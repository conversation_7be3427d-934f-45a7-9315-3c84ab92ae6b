{% extends 'base.html' %}

{% block content %}
{# 移除页面主标题 <h1>{{ title }}</h1> #}

<div class="row" data-highlight-minutes="{{ highlight_minutes }}">
    {# --- 左栏：东京出发 (HND -> Americas) --- #}
    <div class="col-6" id="outbound-column">
        {# 移除 <h2>东京 → 美洲</h2> #}
        {% if sorted_outbound_groups %}
            {% for destination, flights in sorted_outbound_groups %}
            <div class="table-responsive mt-0 mb-0" data-city="{{ destination }}">
                <table class="table table-fixed table-hover table-sm mt-0 mb-0">
                    <colgroup>
                        <col style="width:15%">
                        <col style="width:15%">
                        <col style="width:15%">
                        <col style="width:15%">
                        <col style="width:15%">
                        <col style="width:25%">
                    </colgroup>
                    <!-- 移除表头 -->
                    <tbody id="tbody-outbound-{{ destination }}">
                        <tr class="table-secondary">
                            <td class="p-0"></td>
                            <td class="p-0"><h6 class="fw-bold mt-0 mb-0" style="font-size:0.75rem;">{{ destination }}</h6></td>
                            <td class="p-0"></td>
                            <td class="p-0"></td>
                            <td class="p-0"></td>
                            <td class="p-0"></td>
                        </tr>
                        {# RESTORE JINJA LOOP START #}
                        {% for flight in flights %}
                        {# 时间计算和高亮逻辑 - 兼容两种时间格式 #}
                        {% set timestamp_str = flight.last_updated_timestamp %}
                        {% set last_updated = None %}
                        {% set display_timestamp = timestamp_str %} {# Default display value #}
                        {% if timestamp_str %}
                            {% set parsed = False %}
                            {% if 'T' in timestamp_str %}
                                {# 尝试解析带 T 的格式，忽略微秒 #}
                                {% set timestamp_to_parse = timestamp_str.split('.')[0] %}
                                {% set last_updated = datetime.strptime(timestamp_to_parse, '%Y-%m-%dT%H:%M:%S') %}
                                {% set parsed = True %}
                            {% else %}
                                {# 尝试解析不带 T 的格式 #}
                                {% set last_updated = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S') %}
                                {% set parsed = True %}
                            {% endif %}
                            {% if parsed and last_updated %}
                                {# 如果解析成功，设置统一的显示格式 #}
                                {% set display_timestamp = last_updated.strftime('%Y-%m-%d %H:%M:%S') %}
                            {% endif %}
                        {% endif %}

                        {# History Key #}
                        {% set flight_key = flight.date ~ '_' ~ flight.origin ~ '_' ~ flight.destination ~ '_' ~ flight.flight_numbers ~ '_' ~ flight.cabin_class %}
                        {% set row_id = 'flight-' ~ flight_key | replace('[^a-zA-Z0-9-_]', '-') %}
                        {# Apply highlight class based on JS later #}
                        <tr id="{{ row_id }}" data-history-key="{{ flight_key }}" data-timestamp="{{ timestamp_str }}"> {# ADD data-timestamp #}
                            <td>{{ flight.date }}</td>
                            <td>{{ flight.origin }}→{{ flight.destination }}</td>
                            <td>{{ flight.flight_numbers }}</td>
                            <td>{{ flight.cabin_class }}</td>
                            <td>{{ flight.current_seats }}</td>
                            {# 显示统一格式的时间戳 #}
                            <td>{{ display_timestamp }}</td>
                        </tr>
                        {% endfor %}
                        {# RESTORE JINJA LOOP END #}
                    </tbody>
                </table>
            </div>
            {% endfor %}
        {% else %}
            <p>没有找到东京出发到美洲的航班数据。</p>
        {% endif %}
    </div>

    {# --- 右栏：到达东京 (Americas -> HND) --- #}
    <div class="col-6" id="inbound-column">
        {# 移除 <h2>美洲 → 东京</h2> #}
        {% if sorted_inbound_groups %}
            {% for origin, flights in sorted_inbound_groups %}
            <div class="table-responsive mt-0 mb-0" data-city="{{ origin }}">
                <table class="table table-fixed table-hover table-sm mt-0 mb-0">
                    <colgroup>
                        <col style="width:15%">
                        <col style="width:15%">
                        <col style="width:15%">
                        <col style="width:15%">
                        <col style="width:15%">
                        <col style="width:25%">
                    </colgroup>
                    <!-- 移除表头 -->
                    <tbody id="tbody-inbound-{{ origin }}">
                        <tr class="table-secondary">
                            <td class="p-0"></td>
                            <td class="p-0"><h6 class="fw-bold mt-0 mb-0" style="font-size:0.75rem;">{{ origin }}</h6></td>
                            <td class="p-0"></td>
                            <td class="p-0"></td>
                            <td class="p-0"></td>
                            <td class="p-0"></td>
                        </tr>
                        {# RESTORE JINJA LOOP START #}
                        {% for flight in flights %}
                        {# 时间计算和高亮逻辑 - 兼容两种时间格式 #}
                        {% set timestamp_str = flight.last_updated_timestamp %}
                        {% set last_updated = None %}
                        {% set display_timestamp = timestamp_str %} {# Default display value #}
                        {% if timestamp_str %}
                            {% set parsed = False %}
                            {% if 'T' in timestamp_str %}
                                {# 尝试解析带 T 的格式，忽略微秒 #}
                                {% set timestamp_to_parse = timestamp_str.split('.')[0] %}
                                {% set last_updated = datetime.strptime(timestamp_to_parse, '%Y-%m-%dT%H:%M:%S') %}
                                {% set parsed = True %}
                            {% else %}
                                {# 尝试解析不带 T 的格式 #}
                                {% set last_updated = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S') %}
                                {% set parsed = True %}
                            {% endif %}
                            {% if parsed and last_updated %}
                                {# 如果解析成功，设置统一的显示格式 #}
                                {% set display_timestamp = last_updated.strftime('%Y-%m-%d %H:%M:%S') %}
                            {% endif %}
                        {% endif %}

                        {# History Key #}
                        {% set flight_key = flight.date ~ '_' ~ flight.origin ~ '_' ~ flight.destination ~ '_' ~ flight.flight_numbers ~ '_' ~ flight.cabin_class %}
                        {% set row_id = 'flight-' ~ flight_key | replace('[^a-zA-Z0-9-_]', '-') %}
                        {# Apply highlight class based on JS later #}
                        <tr id="{{ row_id }}" data-history-key="{{ flight_key }}" data-timestamp="{{ timestamp_str }}"> {# ADD data-timestamp #}
                            <td>{{ flight.date }}</td>
                            <td>{{ flight.origin }}→{{ flight.destination }}</td>
                            <td>{{ flight.flight_numbers }}</td>
                            <td>{{ flight.cabin_class }}</td>
                            <td>{{ flight.current_seats }}</td>
                            {# 显示统一格式的时间戳 #}
                            <td>{{ display_timestamp }}</td>
                        </tr>
                        {% endfor %}
                        {# RESTORE JINJA LOOP END #}
                    </tbody>
                </table>
            </div>
            {% endfor %}
        {% else %}
            <p>没有找到美洲到达东京的航班数据。</p>
        {% endif %}
    </div>
</div>

{# 历史记录提示框容器 #}
<div id="history-tooltip" class="history-tooltip"></div>

{% endblock %}

{% block scripts %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script> {# 确保引入了 Socket.IO 客户端库 #}
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script> {# 确保引入了 jQuery #}
<script>
$(function() { // 使用 $(function() {}) 确保 DOM 加载完毕
    // --- SocketIO 连接 ---
    var socket = io.connect(location.protocol + '//' + document.domain + ':' + location.port);
    socket.on('connect', function() {
        console.log('WebSocket 已连接 (Americas)');
        // 连接成功后立即请求最新数据
        socket.emit('request_initial_data', {page: 'americas'});
    });
    socket.on('disconnect', function() { console.log('WebSocket 已断开 (Americas)'); });
    socket.on('connect_error', (err) => { console.error('WebSocket 连接错误 (Americas):', err); });

    // 从包含 .row 的元素读取高亮分钟数
    const highlightMinutesElement = document.querySelector('.row'); // Get the element containing the row
    const HIGHLIGHT_MINUTES = parseInt(highlightMinutesElement ? highlightMinutesElement.dataset.highlightMinutes : '2', 10); // Default to 2

    // 定时器：每30秒检查一次高亮状态
    setInterval(function() {
        updateHighlightStatus();
    }, 30000); // 30秒检查一次

    const AMERICAS_CITIES = ['JFK', 'LAX', 'ORD', 'SFO', 'IAD', 'IAH', 'YVR', 'SEA']; // 定义城市列表

    // Function to parse timestamp string into display format
    function formatDisplayTimestamp(timestampStr) {
        if (!timestampStr) return 'N/A';
        let displayTimestamp = timestampStr; // Default
        try {
            let dt;
            if (timestampStr.includes('T')) {
                // Handle ISO format with 'T' and optional 'Z' or offset
                dt = new Date(timestampStr + (timestampStr.match(/[+-]\d{2}:?\d{2}|Z$/) ? '' : 'Z'));
            } else {
                // Handle space separator (assuming local or potentially needs tz info)
                dt = new Date(timestampStr.replace(/-/g, '/')); // Basic parsing for 'YYYY-MM-DD HH:MM:SS'
            }

            if (!isNaN(dt)) {
                // Format to local time string 'YYYY-MM-DD HH:MM:SS' (example)
                const pad = (num) => num.toString().padStart(2, '0');
                displayTimestamp = `${dt.getFullYear()}-${pad(dt.getMonth() + 1)}-${pad(dt.getDate())} ${pad(dt.getHours())}:${pad(dt.getMinutes())}:${pad(dt.getSeconds())}`;
            }
        } catch (e) {
            console.warn("Error parsing display timestamp:", timestampStr, e);
            // Keep original string if parsing fails
        }
        return displayTimestamp;
    }

    // Helper function to parse timestamp and check highlight
    function shouldHighlight(timestampStr, highlightMinutes) {
        if (!timestampStr || highlightMinutes <= 0) return false;
        try {
            let dt;
            if (timestampStr.includes('T')) {
                dt = new Date(timestampStr + (timestampStr.match(/[+-]\d{2}:?\d{2}|Z$/) ? '' : 'Z'));
            } else {
                dt = new Date(timestampStr.replace(/-/g, '/'));
            }
            if (isNaN(dt.getTime())) {
                console.warn("Highlight check: Invalid date parsed from:", timestampStr);
                return false;
            }
            const now = new Date();
            const diffMinutes = (now - dt) / (1000 * 60);
            return diffMinutes <= highlightMinutes;
        } catch (e) {
            console.error("Highlight check: Error parsing date:", timestampStr, e);
            return false;
        }
    }

    // --- SocketIO Update Handler ---
    socket.on('update_data', function(msg) {
        if (!msg || !msg.data) return; // Check if msg and msg.data exist

        const flightsToUpdate = Array.isArray(msg.data) ? msg.data : [msg.data]; // Ensure it's an array

        flightsToUpdate.forEach(flightData => {
            // Filter: Only process Americas routes on this page
            const origin = flightData.origin;
            const destination = flightData.destination;
            if (!((AMERICAS_CITIES.includes(origin) || AMERICAS_CITIES.includes(destination)) ||
                 ((origin === 'NRT' || destination === 'NRT') && (AMERICAS_CITIES.includes(destination) || AMERICAS_CITIES.includes(origin))))) {
                return; // Skip non-Americas routes
            }

            updateOrAddFlightRow(flightData);
        });
    });

    // Function to update or add a row
    function updateOrAddFlightRow(flightData) {
        const flightKey = `${flightData.date}_${flightData.origin}_${flightData.destination}_${flightData.flight_numbers}_${flightData.cabin_class}`;
        const rowId = `flight-${flightKey.replace(/[^a-zA-Z0-9-_]/g, '-')}`;
        let row = document.getElementById(rowId);

        // --- Determine target column and city ---
        let targetColumnId = null;
        let targetTbodyId = null;
        let targetCity = null;
        let isOutbound = false; // HND -> Americas

        if ((flightData.origin === 'HND' || flightData.origin === 'NRT') && AMERICAS_CITIES.includes(flightData.destination)) { // Outbound
            targetColumnId = 'outbound-column';
            targetCity = flightData.destination;
            targetTbodyId = `tbody-outbound-${targetCity}`;
            isOutbound = true;
        } else if (AMERICAS_CITIES.includes(flightData.origin) && (flightData.destination === 'HND' || flightData.destination === 'NRT')) { // Inbound
            targetColumnId = 'inbound-column';
            targetCity = flightData.origin;
            targetTbodyId = `tbody-inbound-${targetCity}`;
        } else {
            // This should theoretically not happen due to the filter in socket.on
            console.warn("Skipping irrelevant flight data for Americas page:", flightData);
            return;
        }

        const displayTimestamp = formatDisplayTimestamp(flightData.last_updated_timestamp);
        const highlight = shouldHighlight(flightData.last_updated_timestamp, HIGHLIGHT_MINUTES);

        if (row) { // Row exists, update it
            // Update cells: Date[0], Route[1], Number[2], CabinClass[3], CurrentSeats[4], Timestamp[5]
            row.cells[3].textContent = flightData.cabin_class || ''; // 显示舱位信息
            row.cells[4].textContent = flightData.current_seats !== undefined ? flightData.current_seats : '';
            row.cells[5].textContent = displayTimestamp;
            // 更新时间戳属性
            row.dataset.timestamp = flightData.last_updated_timestamp;
            // Update highlight class
            if (highlight) {
                row.classList.add('table-highlight');
            } else {
                row.classList.remove('table-highlight');
            }

        } else { // Row does not exist, create and add it
            let targetTbody = document.getElementById(targetTbodyId);
            const columnElement = document.getElementById(targetColumnId);

            // If tbody doesn't exist, create the whole group structure
            if (!targetTbody && columnElement) {
                console.log(`Creating new group for city: ${targetCity} in column ${targetColumnId}`);
                const newGroupDiv = document.createElement('div');
                newGroupDiv.className = 'table-responsive mt-0 mb-0';
                newGroupDiv.dataset.city = targetCity;

                const newTable = document.createElement('table');
                newTable.className = 'table table-fixed table-hover table-sm mt-0 mb-0';
                // Add colgroup (important for layout)
                const colgroup = document.createElement('colgroup');
                const widths = ['15%', '15%', '15%', '15%', '15%', '25%']; // 统一左右两侧表格的列宽
                widths.forEach(w => {
                    const col = document.createElement('col');
                    col.style.width = w;
                    colgroup.appendChild(col);
                });
                newTable.appendChild(colgroup);

                // 移除表头创建部分

                targetTbody = document.createElement('tbody');
                targetTbody.id = targetTbodyId;

                // Add header row
                const headerRow = document.createElement('tr');
                headerRow.className = 'table-secondary';
                headerRow.innerHTML = `<td class="p-0"></td><td class="p-0"><h6 class="fw-bold mt-0 mb-0" style="font-size:0.75rem;">${targetCity}</h6></td><td class="p-0"></td><td class="p-0"></td><td class="p-0"></td><td class="p-0"></td>`;
                targetTbody.appendChild(headerRow);

                newTable.appendChild(targetTbody);
                newGroupDiv.appendChild(newTable);
                columnElement.appendChild(newGroupDiv); // Add the new group to the column
            }

            // If tbody exists (or was just created), add the new row
            if (targetTbody) {
                row = targetTbody.insertRow(-1); // Insert at the end
                row.id = rowId;
                row.dataset.historyKey = flightKey;
                row.dataset.timestamp = flightData.last_updated_timestamp; // 添加时间戳属性
                if (highlight) {
                    row.classList.add('table-highlight');
                } else {
                    row.classList.remove('table-highlight');
                }

                row.innerHTML = `
                    <td>${flightData.date || ''}</td>
                    <td>${flightData.origin || ''}→${flightData.destination || ''}</td>
                    <td>${flightData.flight_numbers || ''}</td>
                    <td>${flightData.cabin_class || ''}</td>
                    <td>${flightData.current_seats !== undefined ? flightData.current_seats : ''}</td>
                    <td>${displayTimestamp}</td>
                `;

                // Re-bind click listener for history tooltip on the new row
                // This assumes the bindHistoryClick function is available globally or within scope
                if (typeof bindHistoryClick === 'function') {
                    bindHistoryClick($(row)); // Use jQuery if available
                } else {
                    console.warn("bindHistoryClick function not found for new row.");
                }
            } else {
                console.error(`Could not find or create tbody with id ${targetTbodyId}`);
            }
        }
    }

    // --- History Tooltip Logic (Click-based) ---
    let tooltip = $('#history-tooltip');
    let activeKey = null;

    function bindHistoryClick(selector) {
        $(selector).off('click').on('click', function(e) {
            e.stopPropagation();
            let clickedRow = $(this);
            let historyKey = clickedRow.data('history-key');
            if (!historyKey) return;

            if (activeKey === historyKey) {
                tooltip.hide();
                activeKey = null;
                return;
            }
            activeKey = historyKey;

            tooltip.html('加载中...').show();
            let position = clickedRow.offset();
            let rowHeight = clickedRow.outerHeight();
            let topPos = position.top + rowHeight + 5;
            let leftPos = position.left + 50;

            // Adjust tooltip position
            let tooltipWidth = tooltip.outerWidth() || 250;
            let windowWidth = $(window).width();
            if (leftPos + tooltipWidth > windowWidth - 20) {
                leftPos = windowWidth - tooltipWidth - 20;
            }
            if (leftPos < 0) leftPos = 5;
            if (topPos < 0) topPos = 5;
            tooltip.css({ top: topPos, left: leftPos });

            $.ajax({
                url: `/api/history/${historyKey}`,
                method: 'GET',
                success: function(data) {
                    if (activeKey === historyKey) {
                        if (data && data.length > 0) {
                            let content = '<table class="table table-sm table-borderless mb-0"><tbody>';
                            data.forEach(function(item) {
                                let ts = formatDisplayTimestamp(item.timestamp); // Use consistent formatting
                                content += `<tr><td class="pe-2">${ts}</td><td>座位: ${item.seats}</td></tr>`;
                            });
                            tooltip.html(content + '</tbody></table>');
                        } else {
                            tooltip.text('没有历史记录。');
                        }
                    }
                },
                error: function(xhr, status, error) {
                    console.error("获取历史记录失败:", status, error);
                    if (activeKey === historyKey) {
                        tooltip.text('加载历史记录失败。');
                    }
                }
            });
        });
    }

    // Initial binding for existing rows (now happens after initial_data)
    bindHistoryClick('tbody tr[data-history-key]'); // RESTORE Initial binding here

    // Hide tooltip on document click
    $(document).on('click', function(e) {
        if ($(e.target).closest('tbody tr[data-history-key], #history-tooltip').length === 0) {
            tooltip.hide();
            activeKey = null;
        }
    });

    // 定义更新高亮状态的函数
    function updateHighlightStatus() {
        console.log("正在检查并更新高亮状态...");
        // 遍历所有行，检查是否需要高亮
        const allRows = document.querySelectorAll('tbody tr');
        allRows.forEach(row => {
            // 跳过表头行
            if (row.classList.contains('table-secondary')) return;

            // 获取时间戳
            let timestamp = null;
            if (row.hasAttribute('data-timestamp')) {
                timestamp = row.getAttribute('data-timestamp');
            } else if (row.cells && row.cells.length > 5) {
                // 如果没有data-timestamp属性，尝试从最后一个单元格获取时间戳
                timestamp = row.cells[5].textContent;
            }

            if (timestamp) {
                const highlight = shouldHighlight(timestamp, HIGHLIGHT_MINUTES);
                if (highlight) {
                    row.classList.add('table-highlight');
                } else {
                    // 移除高亮类
                    row.classList.remove('table-highlight');
                }
            }
        });
    }

    // 初始化时应用高亮逻辑
    updateHighlightStatus();
});
</script>
{% endblock %}