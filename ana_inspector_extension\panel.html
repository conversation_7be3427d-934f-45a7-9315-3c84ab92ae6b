<!DOCTYPE html>
<html>
<head>
    <title>ANA 侦测器</title>
    <meta charset="UTF-8">
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="toolbar">
        <h1>ANA 会话侦测器</h1>
        <div id="controls">
            <button id="record-button" class="record-start">开始录制</button>
            <button id="export-button" style="display: none;">导出为 HAR</button>
            <button id="clear-button">清空</button>
        </div>
    </div>
    <div class="instructions">
        <p>1. 点击 <strong>'开始录制'</strong>。</p>
        <p>2. 在浏览器主窗口中，执行您想要捕获的操作 (例如：点击 '延長' 按钮)。</p>
        <p>3. 点击 <strong>'停止录制'</strong>。</p>
        <p>4. 捕获到的请求将显示在下方。可疑的会话延长请求将以绿色高亮显示。</p>
    </div>
    <div id="status"></div>
    <div id="requests"></div>
    <script src="panel.js"></script>
</body>
</html> 