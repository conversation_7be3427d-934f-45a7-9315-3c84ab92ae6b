<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="ja-<PERSON>" xml:lang="ja-<PERSON>">

	
	<!-- skey = 2025/06/19 18:12:40 rei21g bWWHdeNLjg dljdmx+34f  --><head id="j_idt53">
    <base href="about:blank">
    <style>
        /* Hide elements that might try to load external resources, as a fallback. */
        img, svg, video, iframe, link[rel="stylesheet"], script { display: none !important; }
    </style>

		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
			<meta http-equiv="X-UA-Compatible" content="IE=EmulateIE9; IE=EmulateIE10" />
			<meta http-equiv="X-UA-Compatible" content="IE=7" />
			<meta http-equiv="X-UA-Compatible" content="IE=edge" />
		<meta name="description" content="" />
		<meta name="keywords" content="" />
		<meta name="format-detection" content="telephone=no" />
		<script type="text/javascript" src="/international_asw/dynatrace/ruxitagentjs_ICANVfhqrux_10273230920145353.js" data-dtconfig="rid=RID_-71023080|rpid=1514300270|domain=aswbe-i.ana.co.jp|reportUrl=https://bf90419wvr.bf.dynatrace.com/bf|app=78bf0b58acf6ed13|cors=1|owasp=1|featureHash=ICANVfhqrux|xb=www^bs.google-analytics^bs.com^pana^bs.demdex^bs.net^pcollect^bs.tealiumiq^bs.com^pallnipponairways^bs.australia-3^bs.evergage^bs.com^pallnipponairwayscolt^bs.tt^bs.omtrdc^bs.net|rdnt=1|uxrgce=1|cuc=k8vpgh61|mel=100000|expw=1|dpvc=1|md=mdcc1=bAsw.PnrOutput.recordLocator,mdcc2=bAsw.PnrOutput.paymentDetailInfoList[0].fopCode,mdcc3=bAsw.PnrOutput.ticketedFlag,mdcc4=bAsw.PnrOutput.nhPnrFlag,mdcc5=bAsw.PnrOutput.itinerarySummary.departureDateYyyymmdd,mdcc6=bAsw.SearchCriteriaOutput.cffCodeInput,mdcc7=a#main ^rb div.section.sectionNoFlow ^rb table ^rb tbody ^rb tr ^rb td.flightNumber.lastCol ^rb span ^rb span:nth-child(2),mdcc8=cunique|lastModification=1750184416409|tp=500,50,0|agentUri=/international_asw/dynatrace/ruxitagentjs_ICANVfhqrux_10273230920145353.js"></script><link rel="shortcut icon" href="https://www.ana.co.jp/favicon.ico" />
		<title>お客様情報入力 | ANA</title>
		<noscript><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/css/asw_noscript_pc.css?99001fc" />
		</noscript>

		<!--  [ Adobe Target ] -->
		<script type="text/javascript" src="https://www.ana.co.jp/behavior/adobetarget/target_intbe.js"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/jquery-1.8.3.min.js?717d3c0"></script><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/ja/css/asw_initialize.css?99001fc" /><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/css/asw_screen_pc.css?99001fc" /><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/css/asw_common_pc.css?99001fc" /><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/css/asw_forminput_pc.css?99001fc" /><script type="text/javascript">
		var Asw = Asw || {};
		Asw.GlobalWord = Asw.GlobalWord || {};
		
		Asw.GlobalWord.toolTipCloseIconAltText = "閉じる";
		
		</script><script >bazadebezolkohpepadr="1684818009"</script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/akam/13/646c4915"  defer></script></head><body>
		
		<noscript>当サイトをご利用になるためには、JavaScript対応のブラウザが必要です。設定でJavaScriptを有効にしてください。有効にできない場合はANA電話窓口へお問い合わせください。
		</noscript>
		<div id="preLoadingArea"></div>
	
	<div id="cmnHeader" class="platinum">
		<!--===== cmnHeader =====-->
		<div class="headerWrapper">
			<div class="header">
				<p class="logo"><a href="https://www.ana.co.jp/asw/AMCTopServlet?type=a" tabindex="-1" aria-hidden="true"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/ana_logo_navy.png?717d3c0" alt="ANA Inspiration of JAPAN。リンク先はマイレージクラブトップページです。" height="28" width="274" /></a>
				</p>
					<ul class="headerMenu">
						<li><a href="https://www.ana.co.jp/asw/AMCTopServlet?type=a">ANAマイレージクラブトップページ</a>
						</li>
						<li><a href="https://www.ana.co.jp/other/int/meta/0005.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">お問い合わせ<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
						</li>
							<li class="btnLogoutArea"><a href="https://aswbe-i.ana.co.jp/rei21g/international_asw/rest/logout" role="button" class="btnBase btnLogout">ログアウト</a>
							</li>
					</ul>
			</div>
		</div>
			<div class="amcUserInfoWrapper">
				<div class="amcUserInfo">
					<div class="userNameArea">
						<table>
							<tr>	
									<td class="statusIcon"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_status_05.png?717d3c0" alt="プラチナサービスメンバー" height="46" width="60" />
									</td>
								<td class="userName">佐藤 直樹 <span>様</span>
								</td>
							</tr>					
						</table>
					</div>
						<div class="balance">
								<dl class="upgradePointBalance">
									<dt>アップグレードポイント残高
									</dt>
									<dd>
											<dl>
												<dt>(2025年度)
												</dt>
												<dd>
													<em>20
													</em>ポイント
												</dd>
											</dl>
									</dd>
								</dl>
							<dl class="mileBalance">
								<dt>マイル口座残高
								</dt>
								<dd><em>75,663</em><span class="currencyCode">マイル</span>
								</dd>
							</dl>
						</div>
				</div>
					<p class="nowTime">6月19日18時12分現在
					</p>
			</div>
		<!--===== /cmnHeader =====-->
	</div>
		<div id="sessionKeeperContainer" class="sessionKeepingContainer"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/bg_modalWindow.png?717d3c0" alt="" height="0" width="0" />
			<div id="extendSessionExpiredDialog" class="modalExpire modalSmall modalContainer" role="dialog" aria-labelledby="extendSessionExpiredDialogTitle" aria-hidden="true">
<form id="sessionKeeperContainer:j_idt183" name="sessionKeeperContainer:j_idt183" method="post" action="https://aswbe-i.ana.co.jp/rei21g/international_asw/pages/award/reservation/award_mandatory_passenger_information_input.xhtml?aswcid=1&amp;rand=20250619181240bWWHdeNLjg" autocomplete="off" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="sessionKeeperContainer:j_idt183" value="sessionKeeperContainer:j_idt183" />
<input type="hidden" name="sessionKeeperContainer:j_idt183_operationTicket" value="dljdmx+34f9c73d790c45548a6b8e35a9d8984e~qvu0rfuznnY9bEBxywxhTTTpsP22URJhm7jsZCTf!1750324347004.aere-xml-controller-67d4778877-g88ld" /><input type="hidden" name="sessionKeeperContainer:j_idt183_cmnPageTicket" value="4" />
					<h1 id="extendSessionExpiredDialogTitle" class="dialogTitle">まもなくセッションが終了します</h1>
					<div id="extendSessionExpiredDialogMessage" class="dialogMessage" tabindex="-1">このままお手続きを進める場合は、［延長］ボタンを押してください。</div>
					<p class="modalButton"><input id="sessionKeeperContainer:cmnSessionKeepingButton" type="submit" name="sessionKeeperContainer:cmnSessionKeepingButton" value="延長" class="btnBase btnModal btnMainStream" onclick="mojarra.ab(this,event,'action',0,0,{'onevent':function(data) {Asw.doCommonAjaxCallback(data,false, event); Asw.SessionKeeper.doSessionKeep(data);},'onerror':function(data) {Asw.doCommonAjaxErrorCallback(data); }});return false" />
					</p><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:0" value="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" autocomplete="off" />
</form>
			</div>
			<div id="showSessionExpiredDialog" class="modalExpire modalSmall modalContainer" role="dialog" aria-labelledby="showSessionExpiredDialogTitle" aria-hidden="true">
				<h1 id="showSessionExpiredDialogTitle" class="dialogTitle">セッションが終了しました</h1>
				<div id="showSessionExpiredDialogMessage" class="dialogMessage" tabindex="-1">一定時間操作されなかったため、お手続きを進めることができません。お手数ですが、再度トップページからお手続きください。</div>
				<p class="modalButton">
					<input type="submit" value="閉じる" class="btnBase btnModal btnMainStream" onclick="Asw.SessionKeeper.closeTimeoutDialog(); return false;" />
				</p>
			</div>
		</div>
		<!--===== dialogMessages =====--><div id="cmnModalMessages"></div>
		<div id="prebookConfirmDialog" class="modalContainer modalConfirm modalSmall " aria-hidden="true" role="dialog">
<form id="prebookConfirmDialog:j_idt403" name="prebookConfirmDialog:j_idt403" method="post" action="https://aswbe-i.ana.co.jp/rei21g/international_asw/pages/award/reservation/award_mandatory_passenger_information_input.xhtml?aswcid=1&amp;rand=20250619181240bWWHdeNLjg" autocomplete="off" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="prebookConfirmDialog:j_idt403" value="prebookConfirmDialog:j_idt403" />
<input type="hidden" name="prebookConfirmDialog:j_idt403_operationTicket" value="dljdmx+34f9c73d790c45548a6b8e35a9d8984e~qvu0rfuznnY9bEBxywxhTTTpsP22URJhm7jsZCTf!1750324347004.aere-xml-controller-67d4778877-g88ld" /><input type="hidden" name="prebookConfirmDialog:j_idt403_cmnPageTicket" value="4" /><div class="dialogMessage" tabindex="0"><p>この後、名・姓の修正や変更ができません。</p><p>「搭乗者選択」欄の氏名が「名」「姓」の順番で表示されていることを再確認ください。</p><p>例）<strong class="highlight">空野 太郎</strong> 様 → MR <strong class="highlight">TARO SORANO</strong></p><p>保安上の理由により、航空券とパスポートのお名前が同じ表記でない場合はご搭乗いただくことができませんので必ず再度ご確認ください。</p></div>
					
					<ul class="modalBtnHorizontal">
							
							<li><input type="submit" name="prebookConfirmDialog:j_idt419" value="戻る" aria-controls="prebookConfirmDialog" class="btnBase btnModal btnCancel" onclick="Asw.Dialog.getInstance('prebookConfirmDialog').close(event, Asw.DialogSequencer.isActiveSequencerNotDisplayedLastDialog());  return false;" />
							</li>
						
						<li class="btnArrowNext"><input type="submit" name="prebookConfirmDialog:j_idt422" value="OK" aria-controls="prebookConfirmDialog" class="btnBase btnModal btnMainStream " onclick="Asw.Dialog.getInstance('prebookConfirmDialog').callOpener('NORMAL', event); return false;" />
						</li>
					</ul><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:1" value="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" autocomplete="off" />
</form>
		</div>
		<!--===== /dialogMessages =====--><div id="cmnContainer">
		<!--=========== .cmnContainer ============--><div id="cmnEmergencyNotice"></div><div id="cmnTitle" class="hgroup ">
		<h1 class="visuallyHidden">お客様情報入力</h1></div>

		<div id="cmnWrapper">
		<!--===== cmnWrapper =====-->
		<ol class="mainFlow" aria-label="完了までの手順、">
			<li><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_flow_01.png?717d3c0" alt="1" height="20" width="28" />フライト検索</li><!--
			--><li><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_flow_02_on.png?717d3c0" alt="2" height="20" width="28" /><span class="visuallyHidden">現在のステップ、</span>お客様情報入力</li><!--
			--><li><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_flow_03.png?717d3c0" alt="3" height="20" width="28" />お支払い情報</li><!--
			--><li class="flowLast"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_flow4_04.png?717d3c0" alt="4" height="20" width="28" />完了</li>
		</ol>

	<div id="main">
	<!--=========== main =============-->
		<ul class="iconExample">
				<li class="requiredIconExample">
					<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="" height="7" width="7" /></span>必須項目です	
				</li>
		</ul>
<form id="j_idt526" name="j_idt526" method="post" action="https://aswbe-i.ana.co.jp/rei21g/international_asw/pages/award/reservation/award_mandatory_passenger_information_input.xhtml?aswcid=1&amp;rand=20250619181240bWWHdeNLjg" autocomplete="off" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="j_idt526" value="j_idt526" />
<input type="hidden" name="j_idt526_operationTicket" value="dljdmx+34f9c73d790c45548a6b8e35a9d8984e~qvu0rfuznnY9bEBxywxhTTTpsP22URJhm7jsZCTf!1750324347004.aere-xml-controller-67d4778877-g88ld" /><input type="hidden" name="j_idt526_cmnPageTicket" value="4" />

<div class="section">
	<h2>搭乗者選択<a href="https://www.ana.co.jp/other/int/meta/0086.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="特典のご利用に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a></h2>
	<table class="passengerInfo">
		<thead>
			<tr>
				
				<th class="name">氏名</th>
				
				<th class="dateOfBirthWide" id="dateOfBirthHeader">生年月日</th>
				
				<th class="gender lastCol" id="genderHeader">性別<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span></th>
				
			</tr>
		</thead>
		<tbody>
				
				<tr>
					<th class="parent_01" id="parentHeader01">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:0:passenger" type="checkbox" name="passengers:0:passenger" checked="checked" data-number="1" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:0:passenger">NAOKI SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader01">1963年06月05日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader01">				
							<span class="formWrapper">
							<label for="passengers:0:gender" class="visuallyHidden">NAOKI SATOの性別</label><select id="passengers:0:gender" name="passengers:0:gender" size="1">	<option value="">選択してください</option>
	<option value="M" selected="selected">男性</option>
	<option value="F">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_02" id="parentHeader02">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:1:passenger" type="checkbox" name="passengers:1:passenger" data-number="2" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:1:passenger">REO SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader02">1995年05月18日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader02">				
							<span class="formWrapper">
							<label for="passengers:1:gender" class="visuallyHidden">REO SATOの性別</label><select id="passengers:1:gender" name="passengers:1:gender" size="1">	<option value="">選択してください</option>
	<option value="M" selected="selected">男性</option>
	<option value="F">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_03" id="parentHeader03">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:2:passenger" type="checkbox" name="passengers:2:passenger" data-number="3" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:2:passenger">ERI SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader03">1963年08月14日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader03">				
							<span class="formWrapper">
							<label for="passengers:2:gender" class="visuallyHidden">ERI SATOの性別</label><select id="passengers:2:gender" name="passengers:2:gender" size="1">	<option value="">選択してください</option>
	<option value="M">男性</option>
	<option value="F" selected="selected">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_04" id="parentHeader04">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:3:passenger" type="checkbox" name="passengers:3:passenger" data-number="4" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:3:passenger">TAISHI SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader04">1992年12月03日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader04">				
							<span class="formWrapper">
							<label for="passengers:3:gender" class="visuallyHidden">TAISHI SATOの性別</label><select id="passengers:3:gender" name="passengers:3:gender" size="1">	<option value="">選択してください</option>
	<option value="M" selected="selected">男性</option>
	<option value="F">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_05" id="parentHeader05">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:4:passenger" type="checkbox" name="passengers:4:passenger" data-number="5" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:4:passenger">HIROMI FUJIMOTO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader05">1961年11月23日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader05">				
							<span class="formWrapper">
							<label for="passengers:4:gender" class="visuallyHidden">HIROMI FUJIMOTOの性別</label><select id="passengers:4:gender" name="passengers:4:gender" size="1">	<option value="">選択してください</option>
	<option value="M">男性</option>
	<option value="F" selected="selected">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
		</tbody>
	</table>
</div>
		<div class="section">
			<h2 class="midstream">連絡先情報
			</h2>
			<h3>代表者
			</h3>
	
	
	
	<div class="formInput">
		<fieldset>
			<legend><label>代表者</label></legend>
			<dl id="contactEmailAddressInfo">
				<dt class="hasRequiredIcon">メールアドレス<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span><a href="https://www.ana.co.jp/other/int/meta/0057.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="メールの宛先と内容に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a>
				</dt>
				<dd>
					<ul class="verticalList">
						<li class="nonMarginTop"><label for="representativeEmail" class="visuallyHidden">代表者メールアドレス</label><input id="representativeEmail" type="text" name="representativeEmail" value="<EMAIL>" class="widthLarge" placeholder="例：<EMAIL>" />
						</li>
						<li>
							<span class="supplementTxt" id="confirmMailAddressForRepresentative">確認のため再度ご入力ください。
							</span><label for="confirmrepresentativeEmail" class="visuallyHidden">確認用メールアドレス</label><input id="confirmrepresentativeEmail" type="text" name="confirmrepresentativeEmail" value="<EMAIL>" class="widthLarge jsNotPasteInput" placeholder="例：<EMAIL>" aria-describedby="confirmMailAddressForRepresentative" />
						</li>
					</ul>
				</dd>
					<dd style="display:none;"><input id="updateAlwaysInfo" type="checkbox" name="updateAlwaysInfo" /><label for="updateAlwaysInfo">ログイン中の会員情報へこのメールアドレスを登録</label>
					</dd>
			</dl>
			<dl>
				<dt>電話番号<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span>
				</dt>
				<dd class="js-countryWrap">
					<ul>
						<li><label for="representativeTelType" class="visuallyHidden">電話番号種別</label><select id="representativeTelType" name="representativeTelType" class="basicWidth js-typeSelect" size="1" onchange="changeTelType(this,'representativeTel')">	<option value="M1" selected="selected">携帯</option>
	<option value="B1">会社</option>
	<option value="H1">自宅</option>
</select>
						</li>
						<li><label for="representativeTelCountry" class="visuallyHidden">電話番号の国/地区選択</label><select id="representativeTelCountry" name="representativeTelCountry" class="js-countrySelect" size="1">	<option value="" data-sms-possible="true" data-country-code="---">選択してください</option>
	<option value="JP" selected="selected" data-sms-possible="true">日本</option>
	<option value="-" data-sms-possible="true" data-country-code="---">-----------</option>
	<option value="IS" data-sms-possible="true">アイスランド</option>
	<option value="IE" data-sms-possible="true">アイルランド</option>
	<option value="AZ" data-sms-possible="true">アゼルバイジャン</option>
	<option value="AF" data-sms-possible="true">アフガニスタン</option>
	<option value="US" data-sms-possible="true">アメリカ合衆国/カナダ</option>
	<option value="AE" data-sms-possible="true">アラブ首長国連邦</option>
	<option value="DZ" data-sms-possible="true">アルジェリア</option>
	<option value="AR" data-sms-possible="true">アルゼンチン</option>
	<option value="AW" data-sms-possible="true">アルバ</option>
	<option value="AL" data-sms-possible="true">アルバニア</option>
	<option value="AM" data-sms-possible="true">アルメニア</option>
	<option value="AO" data-sms-possible="true">アンゴラ</option>
	<option value="AG" data-sms-possible="true">アンティグア・バーブーダ</option>
	<option value="AD" data-sms-possible="true">アンドラ</option>
	<option value="YE" data-sms-possible="true">イエメン</option>
	<option value="GB" data-sms-possible="true">イギリス</option>
	<option value="IL" data-sms-possible="true">イスラエル</option>
	<option value="IT" data-sms-possible="true">イタリア</option>
	<option value="IQ" data-sms-possible="true">イラク</option>
	<option value="IR" data-sms-possible="true">イラン</option>
	<option value="IN" data-sms-possible="true">インド</option>
	<option value="ID" data-sms-possible="true">インドネシア</option>
	<option value="UG" data-sms-possible="true">ウガンダ</option>
	<option value="UA" data-sms-possible="true">ウクライナ</option>
	<option value="UZ" data-sms-possible="true">ウズベキスタン</option>
	<option value="UY" data-sms-possible="true">ウルグアイ</option>
	<option value="EC" data-sms-possible="true">エクアドル</option>
	<option value="EG" data-sms-possible="true">エジプト</option>
	<option value="EE" data-sms-possible="true">エストニア</option>
	<option value="SZ" data-sms-possible="true">エスワティニ</option>
	<option value="ET" data-sms-possible="true">エチオピア</option>
	<option value="ER" data-sms-possible="true">エリトリア</option>
	<option value="SV" data-sms-possible="true">エルサルバドル</option>
	<option value="AU" data-sms-possible="true">オーストラリア</option>
	<option value="AT" data-sms-possible="true">オーストリア</option>
	<option value="OM" data-sms-possible="true">オマーン</option>
	<option value="NL" data-sms-possible="true">オランダ</option>
	<option value="GH" data-sms-possible="true">ガーナ</option>
	<option value="CV" data-sms-possible="true">カーボベルデ</option>
	<option value="GY" data-sms-possible="true">ガイアナ</option>
	<option value="KZ" data-sms-possible="true">カザフスタン</option>
	<option value="QA" data-sms-possible="true">カタール</option>
	<option value="CA" data-sms-possible="true">カナダ/アメリカ合衆国</option>
	<option value="GA" data-sms-possible="true">ガボン</option>
	<option value="CM" data-sms-possible="true">カメルーン</option>
	<option value="KR" data-sms-possible="true">韓国</option>
	<option value="GM" data-sms-possible="true">ガンビア</option>
	<option value="KH" data-sms-possible="true">カンボジア</option>
	<option value="KP" data-sms-possible="true">北朝鮮</option>
	<option value="MK" data-sms-possible="true">北マケドニア</option>
	<option value="GN" data-sms-possible="true">ギニア</option>
	<option value="GW" data-sms-possible="true">ギニアビサウ</option>
	<option value="CY" data-sms-possible="true">キプロス</option>
	<option value="CU" data-sms-possible="true">キューバ</option>
	<option value="GR" data-sms-possible="true">ギリシャ</option>
	<option value="KI" data-sms-possible="true">キリバス</option>
	<option value="KG" data-sms-possible="true">キルギス</option>
	<option value="GT" data-sms-possible="true">グアテマラ</option>
	<option value="GU" data-sms-possible="true">グアム</option>
	<option value="KW" data-sms-possible="true">クウェート</option>
	<option value="GD" data-sms-possible="true">グレナダ</option>
	<option value="HR" data-sms-possible="true">クロアチア</option>
	<option value="KY" data-sms-possible="true">ケイマン諸島</option>
	<option value="KE" data-sms-possible="true">ケニア</option>
	<option value="CI" data-sms-possible="true">コートジボワール</option>
	<option value="CR" data-sms-possible="true">コスタリカ</option>
	<option value="KM" data-sms-possible="true">コモロ</option>
	<option value="CO" data-sms-possible="true">コロンビア</option>
	<option value="CG" data-sms-possible="true">コンゴ共和国</option>
	<option value="CD" data-sms-possible="true">コンゴ民主共和国</option>
	<option value="SA" data-sms-possible="true">サウジアラビア</option>
	<option value="ZM" data-sms-possible="true">ザンビア</option>
	<option value="SM" data-sms-possible="true">サンマリノ</option>
	<option value="SL" data-sms-possible="true">シエラレオネ</option>
	<option value="DJ" data-sms-possible="true">ジブチ</option>
	<option value="JM" data-sms-possible="true">ジャマイカ</option>
	<option value="GE" data-sms-possible="true">ジョージア</option>
	<option value="SY" data-sms-possible="true">シリア</option>
	<option value="SG" data-sms-possible="true">シンガポール</option>
	<option value="ZW" data-sms-possible="true">ジンバブエ</option>
	<option value="CH" data-sms-possible="true">スイス</option>
	<option value="SE" data-sms-possible="true">スウェーデン</option>
	<option value="SD" data-sms-possible="true">スーダン</option>
	<option value="ES" data-sms-possible="true">スペイン</option>
	<option value="SR" data-sms-possible="true">スリナム</option>
	<option value="LK" data-sms-possible="true">スリランカ</option>
	<option value="SK" data-sms-possible="true">スロバキア</option>
	<option value="SI" data-sms-possible="true">スロベニア</option>
	<option value="SC" data-sms-possible="true">セイシェル</option>
	<option value="GQ" data-sms-possible="true">赤道ギニア</option>
	<option value="SN" data-sms-possible="true">セネガル</option>
	<option value="RS" data-sms-possible="true">セルビア</option>
	<option value="KN" data-sms-possible="true">セントクリストファー・ネイビス</option>
	<option value="LC" data-sms-possible="true">セントルシア</option>
	<option value="SB" data-sms-possible="true">ソロモン諸島</option>
	<option value="TH" data-sms-possible="true">タイ</option>
	<option value="TW" data-sms-possible="true">台湾</option>
	<option value="TJ" data-sms-possible="true">タジキスタン</option>
	<option value="TZ" data-sms-possible="true">タンザニア</option>
	<option value="CZ" data-sms-possible="true">チェコ</option>
	<option value="TD" data-sms-possible="true">チャド</option>
	<option value="CF" data-sms-possible="true">中央アフリカ共和国</option>
	<option value="CN" data-sms-possible="true">中国</option>
	<option value="TN" data-sms-possible="true">チュニジア</option>
	<option value="CL" data-sms-possible="true">チリ</option>
	<option value="TV" data-sms-possible="true">ツバル</option>
	<option value="DK" data-sms-possible="true">デンマーク</option>
	<option value="DE" data-sms-possible="true">ドイツ</option>
	<option value="TG" data-sms-possible="true">トーゴ</option>
	<option value="DO" data-sms-possible="true">ドミニカ共和国</option>
	<option value="DM" data-sms-possible="true">ドミニカ国</option>
	<option value="TT" data-sms-possible="true">トリニダード・トバゴ</option>
	<option value="TM" data-sms-possible="true">トルクメニスタン</option>
	<option value="TR" data-sms-possible="true">トルコ</option>
	<option value="TO" data-sms-possible="true">トンガ</option>
	<option value="NG" data-sms-possible="true">ナイジェリア</option>
	<option value="NR" data-sms-possible="true">ナウル</option>
	<option value="NA" data-sms-possible="true">ナミビア</option>
	<option value="NI" data-sms-possible="true">ニカラグア</option>
	<option value="NE" data-sms-possible="true">ニジェール</option>
	<option value="NZ" data-sms-possible="true">ニュージーランド</option>
	<option value="NP" data-sms-possible="true">ネパール</option>
	<option value="NO" data-sms-possible="true">ノルウェー</option>
	<option value="BH" data-sms-possible="true">バーレーン</option>
	<option value="HT" data-sms-possible="true">ハイチ</option>
	<option value="PK" data-sms-possible="true">パキスタン</option>
	<option value="VA" data-sms-possible="true">バチカン</option>
	<option value="PA" data-sms-possible="true">パナマ</option>
	<option value="VU" data-sms-possible="true">バヌアツ</option>
	<option value="BS" data-sms-possible="true">バハマ</option>
	<option value="PG" data-sms-possible="true">パプアニューギニア</option>
	<option value="BM" data-sms-possible="true">バミューダ</option>
	<option value="PW" data-sms-possible="true">パラオ</option>
	<option value="PY" data-sms-possible="true">パラグアイ</option>
	<option value="BB" data-sms-possible="true">バルバドス</option>
	<option value="PS" data-sms-possible="true">パレスチナ</option>
	<option value="HU" data-sms-possible="true">ハンガリー</option>
	<option value="BD" data-sms-possible="true">バングラデシュ</option>
	<option value="TL" data-sms-possible="true">東ティモール</option>
	<option value="FJ" data-sms-possible="true">フィジー</option>
	<option value="PH" data-sms-possible="true">フィリピン</option>
	<option value="FI" data-sms-possible="true">フィンランド</option>
	<option value="BT" data-sms-possible="true">ブータン</option>
	<option value="PR" data-sms-possible="true">プエルトリコ</option>
	<option value="BR" data-sms-possible="true">ブラジル</option>
	<option value="FR" data-sms-possible="true">フランス</option>
	<option value="PF" data-sms-possible="true">フランス領ポリネシア</option>
	<option value="BG" data-sms-possible="true">ブルガリア</option>
	<option value="BF" data-sms-possible="true">ブルキナファソ</option>
	<option value="BN" data-sms-possible="true">ブルネイ</option>
	<option value="BI" data-sms-possible="true">ブルンジ</option>
	<option value="VN" data-sms-possible="true">ベトナム</option>
	<option value="BJ" data-sms-possible="true">ベナン</option>
	<option value="VE" data-sms-possible="true">ベネズエラ</option>
	<option value="BY" data-sms-possible="true">ベラルーシ</option>
	<option value="BZ" data-sms-possible="true">ベリーズ</option>
	<option value="PE" data-sms-possible="true">ペルー</option>
	<option value="BE" data-sms-possible="true">ベルギー</option>
	<option value="PL" data-sms-possible="true">ポーランド</option>
	<option value="BA" data-sms-possible="true">ボスニア・ヘルツェゴビナ</option>
	<option value="BW" data-sms-possible="true">ボツワナ</option>
	<option value="BO" data-sms-possible="true">ボリビア</option>
	<option value="PT" data-sms-possible="true">ポルトガル</option>
	<option value="HK" data-sms-possible="true">香港</option>
	<option value="HN" data-sms-possible="true">ホンジュラス</option>
	<option value="MH" data-sms-possible="true">マーシャル諸島</option>
	<option value="MO" data-sms-possible="true">マカオ</option>
	<option value="MG" data-sms-possible="true">マダガスカル</option>
	<option value="MW" data-sms-possible="true">マラウイ</option>
	<option value="ML" data-sms-possible="true">マリ</option>
	<option value="MT" data-sms-possible="true">マルタ</option>
	<option value="MY" data-sms-possible="true">マレーシア</option>
	<option value="FM" data-sms-possible="true">ミクロネシア連邦</option>
	<option value="ZA" data-sms-possible="true">南アフリカ</option>
	<option value="MM" data-sms-possible="true">ミャンマー</option>
	<option value="MX" data-sms-possible="true">メキシコ</option>
	<option value="MU" data-sms-possible="true">モーリシャス</option>
	<option value="MR" data-sms-possible="true">モーリタニア</option>
	<option value="MZ" data-sms-possible="true">モザンビーク</option>
	<option value="MC" data-sms-possible="true">モナコ</option>
	<option value="MV" data-sms-possible="true">モルディヴ</option>
	<option value="MD" data-sms-possible="true">モルドバ</option>
	<option value="MA" data-sms-possible="true">モロッコ</option>
	<option value="MN" data-sms-possible="true">モンゴル</option>
	<option value="ME" data-sms-possible="true">モンテネグロ</option>
	<option value="JO" data-sms-possible="true">ヨルダン</option>
	<option value="LA" data-sms-possible="true">ラオス人民民主共和国</option>
	<option value="LV" data-sms-possible="true">ラトビア</option>
	<option value="LT" data-sms-possible="true">リトアニア</option>
	<option value="LY" data-sms-possible="true">リビア</option>
	<option value="LI" data-sms-possible="true">リヒテンシュタイン</option>
	<option value="LR" data-sms-possible="true">リベリア</option>
	<option value="RO" data-sms-possible="true">ルーマニア</option>
	<option value="LU" data-sms-possible="true">ルクセンブルク</option>
	<option value="RW" data-sms-possible="true">ルワンダ</option>
	<option value="LS" data-sms-possible="true">レソト</option>
	<option value="LB" data-sms-possible="true">レバノン</option>
	<option value="RU" data-sms-possible="true">ロシア</option>
</select>
						</li>
						<li class="numberInputWrap">
							<span id="numberInputInfo1" class="supplementTxt marginTop">先頭に0(ゼロ)がある場合、0(ゼロ)を抜いてください(一部、国・地域除く)。携帯電話番号の場合は、携帯電話を契約された国を選択ください。</span>
							<div class="numberRowWrap">
								<div class="block">
									
									<span class="visuallyHidden">国番号</span>
									<span class="viewInput widthMini js-countryCode"></span>
								</div>
								<div class="block"><label for="representativeTel" class="visuallyHidden">電話番号</label><input id="representativeTel" type="text" name="representativeTel" value="9042504176" class="widthMiddle" placeholder="例：9012345678" maxlength="30" aria-describedby="numberInputInfo1" />
								</div>
							</div>
						</li>
					</ul>
				</dd>
			</dl>
		</fieldset>
	</div>
	<div class="titleWrapper">
		<h3>
			<span class="text">eメール・SMSに関するご案内<a href="https://www.ana.co.jp/other/int/meta/0057.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="eメール・SMSに関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a></span>
		</h3>
	</div>
	<div class="formInput">
	<h4 class="formAreaTitle">主に搭乗・運航に関わるeメールの送付先設定（米国へ通知いたします）<a href="https://www.ana.co.jp/other/int/meta/0629.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="米国入国用eメール・SMS・電話番号に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a>
	</h4>
	<div class="paxSettingArea js-mailFormWrap onePax is-open">
			<div class="settingArea">
					<dl>
						<dt class="breakWord">NAOKI SATO
						</dt>
						<dd>
							<fieldset>
								<legend>NAOKI SATOのeメール送付先および米国到着時に提供するメールアドレス</legend>代表者と同じ
							</fieldset>
						</dd>
					</dl>
			</div>
	</div>
	<h4 class="formAreaTitle hasTopLine">主に搭乗・運航に関わるSMSの送付先および米国で受信できる電話番号の設定（米国へ通知いたします）<a href="https://www.ana.co.jp/other/int/meta/0629.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="米国入国用eメール・SMS・電話番号に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a>
	</h4>
	<div class="paxSettingArea js-smsFormWrap onePax is-open">
			<div class="settingArea">
					<dl>
						<dt class="breakWord">NAOKI SATO<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span>
						</dt>
						<dd>
							<fieldset>
								<legend>NAOKI SATOの1つ目の電話番号</legend>
									<ul class="verticalList js-formItem">
										<li><input type="radio" id="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSameAsRepresentativeSms:radioItem" name="contactsSms:0:recipientSmsEachPersonSelection:radioGroup" value="0" checked="checked" /><label for="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSameAsRepresentativeSms:radioItem">代表者電話番号と同じ</label>
										</li>
										<li><input type="radio" id="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSmsPerPassenger:radioItem" name="contactsSms:0:recipientSmsEachPersonSelection:radioGroup" value="1" /><label for="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSmsPerPassenger:radioItem">電話番号を指定</label>
											<div class="indentArea js-formImputArea">
												<span id="confirmTelNumberForRepresentative:0" class="supplementTxt marginTop">先頭に0(ゼロ)がある場合、0(ゼロ)を抜いてください(一部、国・地域除く)。携帯電話番号の場合は、携帯電話を契約された国を選択ください。</span>
												<ul class="countryNumberInput js-countryWrap">
													<li><label for="contactsSms:0:recipientSmsEachPersonSelection:passengerSmsCountry" class="visuallyHidden">電話番号の国/地区選択</label><select id="contactsSms:0:recipientSmsEachPersonSelection:passengerSmsCountry" name="contactsSms:0:recipientSmsEachPersonSelection:passengerSmsCountry" class="countrySelect js-countrySelect" size="1">	<option value="" data-country-code="---">選択してください</option>
	<option value="JP">日本</option>
	<option value="-" data-country-code="---">-----------</option>
	<option value="IE">アイルランド</option>
	<option value="AZ">アゼルバイジャン</option>
	<option value="AF">アフガニスタン</option>
	<option value="US">アメリカ合衆国/カナダ</option>
	<option value="AE">アラブ首長国連邦</option>
	<option value="DZ">アルジェリア</option>
	<option value="AR">アルゼンチン</option>
	<option value="AW">アルバ</option>
	<option value="AL">アルバニア</option>
	<option value="AM">アルメニア</option>
	<option value="AG">アンティグア・バーブーダ</option>
	<option value="AD">アンドラ</option>
	<option value="YE">イエメン</option>
	<option value="GB">イギリス</option>
	<option value="IL">イスラエル</option>
	<option value="IT">イタリア</option>
	<option value="IQ">イラク</option>
	<option value="IR">イラン</option>
	<option value="IN">インド</option>
	<option value="ID">インドネシア</option>
	<option value="UG">ウガンダ</option>
	<option value="UA">ウクライナ</option>
	<option value="UZ">ウズベキスタン</option>
	<option value="UY">ウルグアイ</option>
	<option value="EC">エクアドル</option>
	<option value="EG">エジプト</option>
	<option value="EE">エストニア</option>
	<option value="ER">エリトリア</option>
	<option value="SV">エルサルバドル</option>
	<option value="AU">オーストラリア</option>
	<option value="AT">オーストリア</option>
	<option value="OM">オマーン</option>
	<option value="NL">オランダ</option>
	<option value="GH">ガーナ</option>
	<option value="GY">ガイアナ</option>
	<option value="KZ">カザフスタン</option>
	<option value="QA">カタール</option>
	<option value="CA">カナダ/アメリカ合衆国</option>
	<option value="GA">ガボン</option>
	<option value="CM">カメルーン</option>
	<option value="KR">韓国</option>
	<option value="GM">ガンビア</option>
	<option value="KH">カンボジア</option>
	<option value="GN">ギニア</option>
	<option value="GW">ギニアビサウ</option>
	<option value="CY">キプロス</option>
	<option value="CU">キューバ</option>
	<option value="GR">ギリシャ</option>
	<option value="KG">キルギス</option>
	<option value="GT">グアテマラ</option>
	<option value="GU">グアム</option>
	<option value="KW">クウェート</option>
	<option value="GD">グレナダ</option>
	<option value="HR">クロアチア</option>
	<option value="KY">ケイマン諸島</option>
	<option value="KE">ケニア</option>
	<option value="CI">コートジボワール</option>
	<option value="CR">コスタリカ</option>
	<option value="KM">コモロ</option>
	<option value="CO">コロンビア</option>
	<option value="CD">コンゴ民主共和国</option>
	<option value="SA">サウジアラビア</option>
	<option value="ZM">ザンビア</option>
	<option value="SL">シエラレオネ</option>
	<option value="JM">ジャマイカ</option>
	<option value="GE">ジョージア</option>
	<option value="SY">シリア</option>
	<option value="SG">シンガポール</option>
	<option value="ZW">ジンバブエ</option>
	<option value="CH">スイス</option>
	<option value="SE">スウェーデン</option>
	<option value="SD">スーダン</option>
	<option value="ES">スペイン</option>
	<option value="SR">スリナム</option>
	<option value="LK">スリランカ</option>
	<option value="SK">スロバキア</option>
	<option value="SI">スロベニア</option>
	<option value="SN">セネガル</option>
	<option value="RS">セルビア</option>
	<option value="KN">セントクリストファー・ネイビス</option>
	<option value="LC">セントルシア</option>
	<option value="TH">タイ</option>
	<option value="TW">台湾</option>
	<option value="TJ">タジキスタン</option>
	<option value="TZ">タンザニア</option>
	<option value="CZ">チェコ</option>
	<option value="TD">チャド</option>
	<option value="CF">中央アフリカ共和国</option>
	<option value="CN">中国</option>
	<option value="TN">チュニジア</option>
	<option value="CL">チリ</option>
	<option value="DK">デンマーク</option>
	<option value="DE">ドイツ</option>
	<option value="TG">トーゴ</option>
	<option value="DO">ドミニカ共和国</option>
	<option value="TT">トリニダード・トバゴ</option>
	<option value="TR">トルコ</option>
	<option value="NG">ナイジェリア</option>
	<option value="NI">ニカラグア</option>
	<option value="NE">ニジェール</option>
	<option value="NZ">ニュージーランド</option>
	<option value="NP">ネパール</option>
	<option value="NO">ノルウェー</option>
	<option value="HT">ハイチ</option>
	<option value="PK">パキスタン</option>
	<option value="PA">パナマ</option>
	<option value="BS">バハマ</option>
	<option value="PG">パプアニューギニア</option>
	<option value="BM">バミューダ</option>
	<option value="PW">パラオ</option>
	<option value="PY">パラグアイ</option>
	<option value="BB">バルバドス</option>
	<option value="PS">パレスチナ</option>
	<option value="HU">ハンガリー</option>
	<option value="BD">バングラデシュ</option>
	<option value="TL">東ティモール</option>
	<option value="FJ">フィジー</option>
	<option value="PH">フィリピン</option>
	<option value="FI">フィンランド</option>
	<option value="BT">ブータン</option>
	<option value="PR">プエルトリコ</option>
	<option value="BR">ブラジル</option>
	<option value="FR">フランス</option>
	<option value="PF">フランス領ポリネシア</option>
	<option value="BG">ブルガリア</option>
	<option value="BF">ブルキナファソ</option>
	<option value="BN">ブルネイ</option>
	<option value="BI">ブルンジ</option>
	<option value="VN">ベトナム</option>
	<option value="BJ">ベナン</option>
	<option value="VE">ベネズエラ</option>
	<option value="BY">ベラルーシ</option>
	<option value="PE">ペルー</option>
	<option value="BE">ベルギー</option>
	<option value="PL">ポーランド</option>
	<option value="BA">ボスニア・ヘルツェゴビナ</option>
	<option value="BW">ボツワナ</option>
	<option value="HK">香港</option>
	<option value="MH">マーシャル諸島</option>
	<option value="MO">マカオ</option>
	<option value="MG">マダガスカル</option>
	<option value="MW">マラウイ</option>
	<option value="ML">マリ</option>
	<option value="MT">マルタ</option>
	<option value="MY">マレーシア</option>
	<option value="ZA">南アフリカ</option>
	<option value="MM">ミャンマー</option>
	<option value="MX">メキシコ</option>
	<option value="MU">モーリシャス</option>
	<option value="MR">モーリタニア</option>
	<option value="MZ">モザンビーク</option>
	<option value="MC">モナコ</option>
	<option value="MV">モルディヴ</option>
	<option value="MA">モロッコ</option>
	<option value="MN">モンゴル</option>
	<option value="ME">モンテネグロ</option>
	<option value="JO">ヨルダン</option>
	<option value="LA">ラオス人民民主共和国</option>
	<option value="LV">ラトビア</option>
	<option value="LT">リトアニア</option>
	<option value="LY">リビア</option>
	<option value="LR">リベリア</option>
	<option value="RO">ルーマニア</option>
	<option value="LU">ルクセンブルク</option>
	<option value="RW">ルワンダ</option>
	<option value="LB">レバノン</option>
	<option value="RU">ロシア</option>
</select>
													</li>
													<li>
														
														<div class="numberRowWrap">
															<div class="block">
																<span class="visuallyHidden">国番号</span>
																<span class="countryCode viewInput js-countryCode"></span>
															</div>
															
															<div class="block"><label for="contactsSms:0:recipientSmsEachPersonSelection:flightStatusNotificationContactPointSmsDescription" class="visuallyHidden">電話番号</label><input id="contactsSms:0:recipientSmsEachPersonSelection:flightStatusNotificationContactPointSmsDescription" type="text" name="contactsSms:0:recipientSmsEachPersonSelection:flightStatusNotificationContactPointSmsDescription" class="telNumber" placeholder="例：9012345678" maxlength="30" aria-describedby="confirmTelNumberForRepresentative:0" />
															</div>
														</div>
													</li>
												</ul>
											</div>
										</li>
									</ul>
							</fieldset>
						</dd>
					</dl>
			</div>
	</div>
	</div>
	
	<div class="formInput articleLine">
		<h3>滞在中の連絡先(任意)
		</h3>
		<dl>
			
			<dt><label for="accommodationOne">現地連絡先1</label>
			</dt>
			<dd><input id="accommodationOne" type="text" name="accommodationOne" class="widthLarge" placeholder="例：09012345678 ABC HOTEL" maxlength="28" title="例：09012345678 ABC HOTEL" />
			</dd>
		</dl>
		<dl>
			
			<dt><label for="accommodationTwo">現地連絡先2</label>
			</dt>
			<dd><input id="accommodationTwo" type="text" name="accommodationTwo" class="widthLarge" placeholder="例：09012345678 ABC HOTEL" maxlength="28" title="例：09012345678 ABC HOTEL" />
			</dd>
		</dl>
	</div>
		</div>
		
		<p class="btnAreaSubmit btnArrowNext "><input id="next" type="submit" name="next" value="次へ" class="btnBase btnMainStream btnVerticalMain btnWidthVariable" onclick="return Asw.Dialog.getInstance('prebookConfirmDialog').toggle(event);return Asw.LoadingWindow.open(&quot;NORMAL&quot;, event)" />
		</p><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:2" value="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" autocomplete="off" />
</form><div id="cmnDynamicMessages" class="infoBoxSection">
				<dl class="infoBox importantInfo jsAccordionSwitch">
					<dt><strong>重要なご案内</strong></dt>
					<dd>
						<ul class="jsAccordionSwitchList" id="informationMessages">
								<li>個人情報の利用目的<a href="https://www.ana.co.jp/other/int/meta/0012.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="ANAプライバシーポリシー(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">ANAプライバシーポリシー<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>を必ずお読みいただき、内容に同意いただいた上で、お進みください。</li>
								<li><a href="https://www.ana.co.jp/other/int/meta/0058.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="Secure Flight Programについて(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">米国Secure Flightプログラム<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>に基づき、パスポート名、生年月日、性別などの情報は事前に米国へ通知いたします。</li>
								<li>入力したeメールアドレス、電話番号は入国に必要な情報として米国に通知される可能性があります。</li>
									<li class="jsHiddenFlg toggleContents">お名前のスペルがパスポート名と一致しているかご確認ください。異なる場合はインターネットではお取り扱いできません。<a href="https://www.ana.co.jp/other/int/meta/intbe0001.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="ANAお問い合わせ先(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">ANA<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>へ名前の変更をお申し出ください。</li>
									<li class="jsHiddenFlg toggleContents">二重国籍のお客様は、ご搭乗時に使用されるパスポートのお名前でご予約ください。<br />パスポートごとにお名前が異なる場合は、お客様ご自身で入国管理事務所・外務省・大使館などで各種条件をご確認のうえ、使用されるパスポートを決めてください。<br />なお、入国可否については国によって異なるため、お客様ご自身で各入国管理事務所などでご確認ください。</li>
									<li class="jsHiddenFlg toggleContents">旅行開始日時点で12歳以上の場合は大人、2歳以上12歳未満の場合は小児、2歳未満の場合は幼児の扱いとなります。なお、小児は大人と同マイル数が必要です。</li>
									<li class="jsHiddenFlg toggleContents">運航に関する情報をお客様へ通知するため、提携航空会社にもお客様のメールアドレスが提供されます。あらかじめご了承ください。</li>
									<li class="jsHiddenFlg toggleContents"><dl><dt>携帯電話へのメール送信をご希望のお客様へ</dt><dd>ドメイン指定受信をご利用の場合は、ana.co.jpとamadeus.comからのメールが受信できるようあらかじめ設定をお願いいたします。</dd></dl></li>
									<li class="jsHiddenFlg toggleContents">SMSはスマートフォンの番号に配信されます。一部の国ではSMSでのご案内をご利用いただけません。詳細は<a href="https://www.ana.co.jp/other/int/meta/0737.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="ショートメッセージ(SMS)でのご案内対象外の国について(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">ショートメッセージ(SMS)でのご案内対象外の国について<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>をご覧ください。</li>
									<li class="jsHiddenFlg toggleContents">ログインされている会員ご本人が搭乗しない場合は、再度フライト検索画面より「ログインされている会員ご本人は搭乗しない」のチェックボックスを選択してお手続きください。</li>
						</ul>
							<a href="#" class="toggleSwitch jsTriggerSwitch" role="button" aria-controls="informationMessages" aria-expanded="false"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/toggle_open.png?7bec38a" alt="開閉ボタン" height="25" width="25" class="jsRollOver" />
							</a>
					</dd>
				</dl></div>
	<!--=========== /main =============-->
	</div>

	<!--=========== Script ============-->
	<script type="text/javascript">
	$(window).on('load', function() {
		// アコーディオンのメソッドと設定
		Asw.AccordionInfo('.jsAccordionSwitch', {
			'triggerSwitch': '.jsAccordionSwitch .jsTriggerSwitch',
			'listClass': '.jsAccordionSwitchList',
			'visibilityHidden': 'toggleContents'
		});
	});
	</script>
	<!--=========== Script ============-->
	<script type="text/javascript">
		//プラットフォーム判別（サマリーエリア追従用として）
		var Asw = Asw || {};
	
	  	Asw.ClientInfo = Asw.ClientInfo  || {};
	  	Asw.ClientInfo.DeviceType = Asw.ClientInfo.DeviceType || {};
	
	  	Asw.ClientInfo.DeviceType.PC = 'PC';
	  	Asw.ClientInfo.DeviceType.SP = 'SP';
	  	Asw.ClientInfo.DeviceType.TC = 'TC';
	
	  	Asw.ClientInfo.deviceType = Asw.ClientInfo.DeviceType.PC;
	
	 	if((navigator.userAgent.indexOf('Android') > 0 && navigator.userAgent.indexOf('Mobile') == -1) || navigator.userAgent.indexOf('iPad') > 0){
	    	Asw.ClientInfo.deviceType = Asw.ClientInfo.DeviceType.TC;
	  	}
	</script>
	<!--=========== /Script ============--><div id="summaryArea">
			<div class="flightSummaryArea">
						<div class="flightSummarySection">
							<h2>区間 1
							</h2>
							<p class="flightDate"><em>6</em>月<em class="hasLeft">24</em>日（火）
							</p>
							<div class="flightSummaryRoute">
									<div class="flightSummaryRouteSection">
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation1_1">
													<span>東京(成田)
													</span>
												</p>
												<p class="time">
													<span>17:00
													</span>
												</p>
											</div>
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation1_1">
													<span>シカゴ(ORD)
													</span>
												</p>
												<p class="time">
													<span>14:55
													</span>
												</p>
											</div>
										<p class="flightNumberClass">
												<span>NH012</span><span><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/flight_ana_summaryarea.png?717d3c0" alt="ANA運航便" height="11" width="41" /></span>
												<br />特典ビジネス
										</p>
									</div>
							</div>
						</div>
						<div class="flightSummarySection">
							<h2 class="midstream">区間 2
							</h2>
							<p class="flightDate"><em>7</em>月<em class="hasLeft">23</em>日（水）
							</p>
							<div class="flightSummaryRoute">
									<div class="flightSummaryRouteSection">
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation2_1">
													<span>ホノルル(オアフ島)
													</span>
												</p>
												<p class="time">
													<span>11:35
													</span>
												</p>
											</div>
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation2_1">
													<span>東京(成田)
													</span>
												</p>
												<p class="time">
													<span>14:50
																	<span>翌日
																	</span>
													</span>
												</p>
											</div>
										<p class="flightNumberClass">
												<span>NH183</span><span><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/flight_ana_summaryarea.png?717d3c0" alt="ANA運航便" height="11" width="41" /></span>
												<br />特典エコノミー
										</p>
									</div>
							</div>
						</div>
					<div class="total">
						<p class="passenger">大人 x1
						</p>
						<div class="totalArea">
					  		<dl class="mealAmountWrap">
									<dt>有料機内食サービス
									</dt>
									<dd><em class="mealAmount">0</em><span class="currencyCode">円</span>
									</dd>
							</dl>
									<div>
										<div class="mileageAndTotal">
												<dl class="requiredMileage">
													<dt>必要マイル
													</dt>
													<dd><em class="requiredMileage">74,000</em><span class="currencyCode">マイル</span>
													</dd>
												</dl>
												<dl>
													<dt>総額
													</dt>
													<dd><em class="awardTotalPayment">68,130</em>円
													</dd>
												</dl>
												<p class="includeFareAndFuelCost">※各種税金、燃油特別付加運賃等を含みます。
												</p>
										</div>
									</div>
						</div>
					</div>
				
			</div></div>
		<!--===== /cmnWrapper =====-->
		</div>
			
		<span id="message" class="visuallyHidden" role="alert" aria-live="assertive"></span>
		<span id="politeMessage" class="visuallyHidden" role="log" aria-live="polite"></span><script type="text/javascript">
		var Asw = Asw || {};
		Asw.BaseOutput = {"sessionID":"bWWHdeNLjg","language":"ja","siteCatalystLanguageCode":"J","operationDate":"20250619","operationDateTime":"20250619181240","office":{"officeCode":"TYONH08ZZ","country2letterCode":"JP"},"bookingType":"A","pageID":"A04_P01","siteCatalystPageName":"INT_BE_AWARD_J_A04特典予約_P01旅客情報入力（必須情報）","device":{"deviceType":"PC"}};
	</script><div id="promotionArea">
		<!--===== promotionArea =====-->
		<!--===== /promotionArea =====--></div>
		<!--=========== /cmnContainer ============--></div><div id="cmnBaloonArea" aria-live="polite"></div><div id="cmnPopupArea" aria-live="polite"></div>

	

	<div id="cmnFooterWrapper">
		<!--=========== .cmnFooterWrapper ============-->
		<div id="cmnFooter">
			<!--===== cmnFooter =====-->
			<ul>
				<li><a href="https://www.ana.co.jp/other/int/meta/0012.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">プライバシーポリシー<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
					<li><a href="https://www.ana.co.jp/other/int/meta/0670.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">利用者情報の外部送信について<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
					</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/0052.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">サイト利用規約<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/be0238.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">推奨環境<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/0133.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">サイトマップ<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/0732.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">運送約款<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
					<li class="lastChild"><a href="https://www.ana.co.jp/other/int/meta/0556.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">ウェブアクセシビリティについて<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
					</li>
			</ul>
			<p class="blankText" aria-hidden="true"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="" height="10" width="10" />外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。</p>
			<p class="copyright"><span lang="en" xml:lang="en">Copyright&#169;ANA</span>
			</p>
			<p class="starAllianceLogo"><a href="http://www.staralliance.com/ja/" target="_blank"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/star_alliance_logo.png?0fe73b5" alt="A STAR ALLIANCE MEMBER" height="18" width="175" /><span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
			</p>
			<!--===== cmnFooter =====-->
		</div>
		<!--=========== /cmnFooterWrapper ============-->
	</div>

		
		<div id="cmnLoadingForAjax"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/bg_modalWindow.png?717d3c0" alt="" height="0" width="0" />
			<div class="loadingArea">
				<p class="loadingImage"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/loading.gif?717d3c0" alt="リクエストを処理しています...しばらくお待ちください。" height="33" width="33" />
				</p>
			</div>
		</div>
		
		
		<div id="popupAltSentence" style="display: none" aria-hidden="true">別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。</div>
	<div id="cmnSiteCatalyst" aria-hidden="true"><div id="cmnSiteCatalystParamArea"><script type="text/javascript">
				// ▼SiteCatalyst
				// SiteCatalyst code version: H.2.
				// Copyright 1997-2005 Omniture, Inc. More info available at http://www.omniture.com
				SiteCatalystReportSuites   = "INT";
				SiteCatalystCharSet        = "UTF-8";
				SiteCatalystChannel        = "BE_AWARD_J";
				SiteCatalystPageName       = "INT_BE_AWARD_J_A04\u7279\u5178\u4E88\u7D04_P01\u65C5\u5BA2\u60C5\u5831\u5165\u529B\uFF08\u5FC5\u9808\u60C5\u5831\uFF09";
				SiteCatalystERR            = "";
				SiteCatalystEventNum       = "";
				SiteCatalystPurchase       = "FALSE";
				SiteCatalystProductPrice   = "";
				SiteCatalystCurrencyCode   = "";
				SiteCatalystSeatAvailabilityINT   = "";
				SiteCatalystDepartDate     = "";
				SiteCatalystDepart         = "";
				SiteCatalystArrive         = "";
				SiteCatalystFlightNo       = "";
				SiteCatalystClass          = "";
				SiteCatalystFare           = "";
				SiteCatalystPurchaseID     = "";
				
				SiteCatalystLocater        = "";
				SiteCatalystPayment        = "";
				SiteCatalystIncome         = "";
				
				SiteCatalystPax            = "";
				SiteCatalystManipulateDay  = "";
				SiteCatalystCompartmentClass   = "";
				SiteCatalystOptionInfo     = "";
				SiteCatalystOutboundUpsellPrice   = "";
				SiteCatalystInboundUpsellPrice    = "";
				
				SiteCatalystSearchInput    = "";
				SiteCatalystSegment        = "";
				SiteCatalystOfficeCode     = "";
				SiteCatalystPassengerCount = "";
				SiteCatalystPassenger      = "";
				SiteCatalystAncillaryService = "";
				// End SiteCatalyst code version: H.2.
				// ▲SiteCatalyst
			</script></div><script type="text/javascript"><!-- if(navigator.appVersion.indexOf('MSIE')>=0)document.write(unescape('%3C')+'\!-'+'-') // --></script>
		<!--/DO NOT REMOVE/-->
		

		
		<!-- Google Tag Manager -->
		<script type="text/javascript" charset="Shift_JIS" src="https://www.ana.co.jp/common/js/GTMINTBE.js"></script>
		<!-- End Google Tag Manager -->
		<!-- Yahoo Tag Manager -->
		<script type="text/javascript" charset="Shift_JIS" src="https://www.ana.co.jp/common/js/YTMINTBE.js"></script>
		<!-- End Yahoo Tag Manager -->
		

	</div>

	

	<div class="mboxDefault"></div><script type="text/javascript">mboxCreate("ASW_common_A04_P01");</script>
			<script type="text/javascript" src="https://www.ana.co.jp/common/js/tealium/tealium.js"></script>
			<script type="text/javascript" src="//cdn.evgnet.com/beacon/allnipponairways/nonamcprd/scripts/evergage.min.js"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/javax.faces.resource/jsf.js.xhtml?ln=javax.faces"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/jquery.mousewheel.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/jquery.jscrollpane.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/jquery-ui-1.10.4.custom.min.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/jquery.ui.touch-punch.min.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/asw-common.js?beb6a50"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/asw-common-pc.js?fd88d7d"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/asw-format.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/mask.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/loading.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/asw-loadingwindow-pc.js?9e233c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/asw-loadingwindow.js?51db600"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/dialog-pc.js?25d36a4"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/asw-dialog-sequencer.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/asw-event.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/asw-date-select.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/asw-calendar-pc.js?9b86b8a"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/asw-calendar.js?db624c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/heightLine.js?0e7a1ce"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/asw-summaryarea.js?b21c6d7"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/asw-fingerprints.js?99001fc"></script><script type="text/javascript">
			Asw.init("https://aswbe-i.ana.co.jp/rei21g/international_asw", "ja");
			Asw.getCalendarHolidayList = function() {return [20250721,20250811,20250915,20250923,20251013,20251103,20251123,20251124,20260101,20260112,20260211,20260223,20260320,20260429,20260503,20260504,20260505,20260506,20260720,20260811,20260921,20260922,20260923,20261012,20261103,20261123,20270101,20270111,20270211,20270223,20270321,20270322,20270429,20270503,20270504,20270505,20270719,20270811,20270920,20270923,20271011,20271103,20271123,20280101,20280110,20280211,20280223,20280320,20280429,20280503,20280504,20280505,20280717,20280811,20280918,20280922,20281009,20281103,20281123,20290101,20290108,20290211,20290212,20290223,20290320,20290429,20290430,20290503,20290504,20290505,20290716,20290811,20290917,20290923,20290924,20291008,20291103,20291123];}
			Asw.ClientInfo.deviceType = "PC";
			Asw.ClientInfo.mobileDeviceType = "UNKNOWN";
			Asw.ClientInfo.osVersion = "";
			Asw.Format.init("https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/{1}/{2}/{3}?{0}");
			Asw.overwriteTargetAttributeValue("false");
		</script><script type="text/javascript">
		
		var Asw = Asw || {};
		Asw.ContactInput = Asw.ContactInput || {};
		Asw.ContactInput.Const = {
			/** data-属性名「data-type」 @constant @type {String} @default 'data-type' */
			DATA_ATTRIBUTE_NAME_TYPE: 'data-type',
			/** data-属性名「data-sms-possible」 @constant @type {String} @default 'data-sms-possible' */
			DATA_ATTRIBUTE_NAME_SMS_POSSIBLE: 'data-sms-possible',
			/** data-属性名「data-country-code」 @constant @type {String} @default 'data-country-code' */
			DATA_ATTRIBUTE_NAME_COUNTRY_CODE: 'data-country-code',
			/** data-type属性値「sms」 @constant @type {String} @default 'sms' */
			DATA_TYPE_VALUE_SMS: 'sms'
		};

		
		Asw.ContactInput.countryInfoMap = {"IS":{"internationalCallingCode":"+354","smsPossible":false},"IE":{"internationalCallingCode":"+353","smsPossible":true},"AZ":{"internationalCallingCode":"+994","smsPossible":true},"AF":{"internationalCallingCode":"+93","smsPossible":true},"US":{"internationalCallingCode":"+1","smsPossible":true},"AE":{"internationalCallingCode":"+971","smsPossible":true},"DZ":{"internationalCallingCode":"+213","smsPossible":true},"AR":{"internationalCallingCode":"+54","smsPossible":true},"AW":{"internationalCallingCode":"+297","smsPossible":true},"AL":{"internationalCallingCode":"+355","smsPossible":true},"AM":{"internationalCallingCode":"+374","smsPossible":true},"AO":{"internationalCallingCode":"+244","smsPossible":false},"AG":{"internationalCallingCode":"+1","smsPossible":true},"AD":{"internationalCallingCode":"+376","smsPossible":true},"YE":{"internationalCallingCode":"+967","smsPossible":true},"GB":{"internationalCallingCode":"+44","smsPossible":true},"IL":{"internationalCallingCode":"+972","smsPossible":true},"IT":{"internationalCallingCode":"+39","smsPossible":true},"IQ":{"internationalCallingCode":"+964","smsPossible":true},"IR":{"internationalCallingCode":"+98","smsPossible":true},"IN":{"internationalCallingCode":"+91","smsPossible":true},"ID":{"internationalCallingCode":"+62","smsPossible":true},"UG":{"internationalCallingCode":"+256","smsPossible":true},"UA":{"internationalCallingCode":"+380","smsPossible":true},"UZ":{"internationalCallingCode":"+998","smsPossible":true},"UY":{"internationalCallingCode":"+598","smsPossible":true},"EC":{"internationalCallingCode":"+593","smsPossible":true},"EG":{"internationalCallingCode":"+20","smsPossible":true},"EE":{"internationalCallingCode":"+372","smsPossible":true},"SZ":{"internationalCallingCode":"+268","smsPossible":false},"ET":{"internationalCallingCode":"+251","smsPossible":false},"ER":{"internationalCallingCode":"+291","smsPossible":true},"SV":{"internationalCallingCode":"+503","smsPossible":true},"AU":{"internationalCallingCode":"+61","smsPossible":true},"AT":{"internationalCallingCode":"+43","smsPossible":true},"OM":{"internationalCallingCode":"+968","smsPossible":true},"NL":{"internationalCallingCode":"+31","smsPossible":true},"GH":{"internationalCallingCode":"+233","smsPossible":true},"CV":{"internationalCallingCode":"+238","smsPossible":false},"GY":{"internationalCallingCode":"+592","smsPossible":true},"KZ":{"internationalCallingCode":"+7","smsPossible":true},"QA":{"internationalCallingCode":"+974","smsPossible":true},"CA":{"internationalCallingCode":"+1","smsPossible":true},"GA":{"internationalCallingCode":"+241","smsPossible":true},"CM":{"internationalCallingCode":"+237","smsPossible":true},"KR":{"internationalCallingCode":"+82","smsPossible":true},"KH":{"internationalCallingCode":"+855","smsPossible":true},"GM":{"internationalCallingCode":"+220","smsPossible":true},"KP":{"internationalCallingCode":"+850","smsPossible":false},"MK":{"internationalCallingCode":"+389","smsPossible":false},"GN":{"internationalCallingCode":"+224","smsPossible":true},"GW":{"internationalCallingCode":"+245","smsPossible":true},"CY":{"internationalCallingCode":"+357","smsPossible":true},"CU":{"internationalCallingCode":"+53","smsPossible":true},"GR":{"internationalCallingCode":"+30","smsPossible":true},"KI":{"internationalCallingCode":"+686","smsPossible":false},"KG":{"internationalCallingCode":"+996","smsPossible":true},"GT":{"internationalCallingCode":"+502","smsPossible":true},"GU":{"internationalCallingCode":"+1","smsPossible":true},"KW":{"internationalCallingCode":"+965","smsPossible":true},"KY":{"internationalCallingCode":"+1","smsPossible":true},"GD":{"internationalCallingCode":"+1","smsPossible":true},"HR":{"internationalCallingCode":"+385","smsPossible":true},"KE":{"internationalCallingCode":"+254","smsPossible":true},"CI":{"internationalCallingCode":"+225","smsPossible":true},"CR":{"internationalCallingCode":"+506","smsPossible":true},"SA":{"internationalCallingCode":"+966","smsPossible":true},"KM":{"internationalCallingCode":"+269","smsPossible":true},"CO":{"internationalCallingCode":"+57","smsPossible":true},"CG":{"internationalCallingCode":"+242","smsPossible":false},"CD":{"internationalCallingCode":"+243","smsPossible":true},"ZM":{"internationalCallingCode":"+260","smsPossible":true},"SM":{"internationalCallingCode":"+378","smsPossible":false},"SL":{"internationalCallingCode":"+232","smsPossible":true},"DJ":{"internationalCallingCode":"+253","smsPossible":false},"JM":{"internationalCallingCode":"+1","smsPossible":true},"CH":{"internationalCallingCode":"+41","smsPossible":true},"GE":{"internationalCallingCode":"+995","smsPossible":true},"SE":{"internationalCallingCode":"+46","smsPossible":true},"SY":{"internationalCallingCode":"+963","smsPossible":true},"ES":{"internationalCallingCode":"+34","smsPossible":true},"SG":{"internationalCallingCode":"+65","smsPossible":true},"LK":{"internationalCallingCode":"+94","smsPossible":true},"SK":{"internationalCallingCode":"+421","smsPossible":true},"SI":{"internationalCallingCode":"+386","smsPossible":true},"ZW":{"internationalCallingCode":"+263","smsPossible":true},"SD":{"internationalCallingCode":"+249","smsPossible":true},"KN":{"internationalCallingCode":"+1","smsPossible":true},"SR":{"internationalCallingCode":"+597","smsPossible":true},"LC":{"internationalCallingCode":"+1","smsPossible":true},"SC":{"internationalCallingCode":"+248","smsPossible":false},"SN":{"internationalCallingCode":"+221","smsPossible":true},"SB":{"internationalCallingCode":"+677","smsPossible":false},"RS":{"internationalCallingCode":"+381","smsPossible":true},"TH":{"internationalCallingCode":"+66","smsPossible":true},"TW":{"internationalCallingCode":"+886","smsPossible":true},"TJ":{"internationalCallingCode":"+992","smsPossible":true},"TZ":{"internationalCallingCode":"+255","smsPossible":true},"CZ":{"internationalCallingCode":"+420","smsPossible":true},"TD":{"internationalCallingCode":"+235","smsPossible":true},"CF":{"internationalCallingCode":"+236","smsPossible":true},"TN":{"internationalCallingCode":"+216","smsPossible":true},"CL":{"internationalCallingCode":"+56","smsPossible":true},"TV":{"internationalCallingCode":"+688","smsPossible":false},"DK":{"internationalCallingCode":"+45","smsPossible":true},"DE":{"internationalCallingCode":"+49","smsPossible":true},"TG":{"internationalCallingCode":"+228","smsPossible":true},"DO":{"internationalCallingCode":"+1","smsPossible":true},"DM":{"internationalCallingCode":"+1","smsPossible":false},"TT":{"internationalCallingCode":"+1","smsPossible":true},"TM":{"internationalCallingCode":"+993","smsPossible":false},"TR":{"internationalCallingCode":"+90","smsPossible":true},"TO":{"internationalCallingCode":"+676","smsPossible":false},"NG":{"internationalCallingCode":"+234","smsPossible":true},"NR":{"internationalCallingCode":"+674","smsPossible":false},"NA":{"internationalCallingCode":"+264","smsPossible":false},"NI":{"internationalCallingCode":"+505","smsPossible":true},"BH":{"internationalCallingCode":"+973","smsPossible":false},"NE":{"internationalCallingCode":"+227","smsPossible":true},"PK":{"internationalCallingCode":"+92","smsPossible":true},"JP":{"internationalCallingCode":"+81","smsPossible":true},"BS":{"internationalCallingCode":"+1","smsPossible":true},"NZ":{"internationalCallingCode":"+64","smsPossible":true},"BM":{"internationalCallingCode":"+1","smsPossible":true},"NP":{"internationalCallingCode":"+977","smsPossible":true},"BB":{"internationalCallingCode":"+246","smsPossible":true},"NO":{"internationalCallingCode":"+47","smsPossible":true},"HT":{"internationalCallingCode":"+509","smsPossible":true},"BD":{"internationalCallingCode":"+880","smsPossible":true},"VA":{"internationalCallingCode":"+39","smsPossible":false},"PA":{"internationalCallingCode":"+507","smsPossible":true},"VU":{"internationalCallingCode":"+678","smsPossible":false},"PG":{"internationalCallingCode":"+675","smsPossible":true},"PW":{"internationalCallingCode":"+680","smsPossible":true},"PY":{"internationalCallingCode":"+595","smsPossible":true},"PS":{"internationalCallingCode":"+970","smsPossible":true},"BR":{"internationalCallingCode":"+55","smsPossible":true},"HU":{"internationalCallingCode":"+36","smsPossible":true},"TL":{"internationalCallingCode":"+670","smsPossible":true},"BG":{"internationalCallingCode":"+359","smsPossible":true},"BF":{"internationalCallingCode":"+226","smsPossible":true},"BN":{"internationalCallingCode":"+673","smsPossible":true},"FJ":{"internationalCallingCode":"+679","smsPossible":true},"PH":{"internationalCallingCode":"+63","smsPossible":true},"FI":{"internationalCallingCode":"+358","smsPossible":true},"BT":{"internationalCallingCode":"+975","smsPossible":true},"BY":{"internationalCallingCode":"+375","smsPossible":true},"BZ":{"internationalCallingCode":"+501","smsPossible":false},"PR":{"internationalCallingCode":"+1","smsPossible":true},"BE":{"internationalCallingCode":"+32","smsPossible":true},"BA":{"internationalCallingCode":"+387","smsPossible":true},"FR":{"internationalCallingCode":"+33","smsPossible":true},"BO":{"internationalCallingCode":"+591","smsPossible":false},"PF":{"internationalCallingCode":"+689","smsPossible":true},"BI":{"internationalCallingCode":"+257","smsPossible":true},"VN":{"internationalCallingCode":"+84","smsPossible":true},"BJ":{"internationalCallingCode":"+229","smsPossible":true},"VE":{"internationalCallingCode":"+58","smsPossible":true},"PE":{"internationalCallingCode":"+51","smsPossible":true},"PL":{"internationalCallingCode":"+48","smsPossible":true},"BW":{"internationalCallingCode":"+267","smsPossible":true},"PT":{"internationalCallingCode":"+351","smsPossible":false},"HK":{"internationalCallingCode":"+852","smsPossible":true},"HN":{"internationalCallingCode":"+504","smsPossible":false},"MH":{"internationalCallingCode":"+692","smsPossible":true},"MO":{"internationalCallingCode":"+853","smsPossible":true},"MG":{"internationalCallingCode":"+261","smsPossible":true},"MW":{"internationalCallingCode":"+265","smsPossible":true},"ML":{"internationalCallingCode":"+223","smsPossible":true},"MT":{"internationalCallingCode":"+356","smsPossible":true},"MY":{"internationalCallingCode":"+60","smsPossible":true},"FM":{"internationalCallingCode":"+691","smsPossible":false},"MM":{"internationalCallingCode":"+95","smsPossible":true},"MX":{"internationalCallingCode":"+52","smsPossible":true},"MU":{"internationalCallingCode":"+230","smsPossible":true},"MR":{"internationalCallingCode":"+222","smsPossible":true},"MZ":{"internationalCallingCode":"+258","smsPossible":true},"MC":{"internationalCallingCode":"+377","smsPossible":true},"MV":{"internationalCallingCode":"+960","smsPossible":true},"MD":{"internationalCallingCode":"+373","smsPossible":false},"RO":{"internationalCallingCode":"+40","smsPossible":true},"MA":{"internationalCallingCode":"+212","smsPossible":true},"MN":{"internationalCallingCode":"+976","smsPossible":true},"ME":{"internationalCallingCode":"+382","smsPossible":true},"JO":{"internationalCallingCode":"+962","smsPossible":true},"LA":{"internationalCallingCode":"+856","smsPossible":true},"RU":{"internationalCallingCode":"+7","smsPossible":true},"LV":{"internationalCallingCode":"+371","smsPossible":true},"LT":{"internationalCallingCode":"+370","smsPossible":true},"LY":{"internationalCallingCode":"+218","smsPossible":true},"GQ":{"internationalCallingCode":"+240","smsPossible":false},"LI":{"internationalCallingCode":"+423","smsPossible":false},"LR":{"internationalCallingCode":"+231","smsPossible":true},"CN":{"internationalCallingCode":"+86","smsPossible":true},"LU":{"internationalCallingCode":"+352","smsPossible":true},"ZA":{"internationalCallingCode":"+27","smsPossible":true},"RW":{"internationalCallingCode":"+250","smsPossible":true},"LS":{"internationalCallingCode":"+266","smsPossible":false},"LB":{"internationalCallingCode":"+961","smsPossible":true}};

		$(function () {
			contactInfoInit();
		});
		
		function contactInfoInit() {
			
			
			Asw.get('representativeTelType').find('option[value="M1"]').attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_TYPE,
					Asw.ContactInput.Const.DATA_TYPE_VALUE_SMS);
			Asw.get('representativeTelCountry').children('option').each(function(i,option){
				var countryInfo = Asw.ContactInput.countryInfoMap[option.value];
				if (countryInfo === void 0) {
					
					return true;
				}
				$(option).attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_SMS_POSSIBLE, countryInfo.smsPossible);
				$(option).attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_COUNTRY_CODE, countryInfo.internationalCallingCode);
			});
			
			var smsCountrySelectSelector = 'select.js-countrySelect:not(#representativeTelCountry)'
			$(smsCountrySelectSelector).eq(0).children('option').each(function(i,option){
				var countryInfo = Asw.ContactInput.countryInfoMap[option.value];
				if (countryInfo === void 0) {
					
					return true;
				}
				$(smsCountrySelectSelector + ' option:nth-child(' + (i+1) + ')').attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_COUNTRY_CODE,
						countryInfo.internationalCallingCode);
			});
			
			var emergencyCountrySelectSelector = 'select.passengerEmergencyCountry.js-countrySelect:not(#representativeTelCountry)'
			$(emergencyCountrySelectSelector).eq(0).children('option').each(function(i,option){
				var countryInfo = Asw.ContactInput.countryInfoMap[option.value];
				if (countryInfo === void 0) {
					
					return true;
				}
				$(emergencyCountrySelectSelector + ' option:nth-child(' + (i+1) + ')').attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_COUNTRY_CODE,
						countryInfo.internationalCallingCode);
			});
		}
	</script><script type="text/javascript">
		var isInit = true;
		function changeTelType(_this, telNoInputId) {
			var $telNoInput = Asw.get(telNoInputId);
			var $telNoKind = Asw.get("representativeTelType");
			var telNo = Asw.getValueComparedToPlaceholder(telNoInputId);
			var telNoCountry = Asw.get("representativeTelCountry").val();

			if(true && !isInit) {
				if(telInputCheck(telNo, telNoCountry)) {
					if (_this.value == "M1") {
						telNo = exceptDomesticPrefix("09042504176", telNoCountry);
					} else if (_this.value == "B1") {
						telNo = exceptDomesticPrefix("03-5405-2172", telNoCountry);
					} else {
						telNo = exceptDomesticPrefix("090-4250-4176", telNoCountry);
					}
					$telNoInput.val(telNo);
				}
			}
			isInit = false;
		}
		
		function telInputCheck(telNo, telNoCountry) {
	        checkResult = false;
	        
	        var amcHndyTelNo = exceptDomesticPrefix("09042504176", telNoCountry);
	        var amcCmpTelNo = exceptDomesticPrefix("03-5405-2172", telNoCountry);
	        var amcTelNo = exceptDomesticPrefix("090-4250-4176", telNoCountry);
	
	        if(telNo == "" || telNo == amcHndyTelNo || telNo == amcCmpTelNo || telNo == amcTelNo) {
	        	checkResult = true;
	        }
	        return checkResult;
    	}
    	
    	function exceptDomesticPrefix(telNo, telNoCountry) {
    		if(telNoCountry == 'JP') {
    			if(telNo.substr(0, 1) === '0') {
    				return telNo.slice(1);
    			}
    		}
    		return telNo;
    	}
	</script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/asw-customerinfomationform.js?6e91953"></script><script type="text/javascript">
		$(document).ready(function(){
				Asw.enableTextWhenCheckBox('.cascadedSwitch');
				Asw.notPasteInput('.jsNotPasteInput');
			$(function(){
				Asw.alignHeightToParentTdElement( '.passengerInfoInputArea' );
				
					Asw.alignHeightToParentTdElement( '.passengerInfoInputText' );
				
				
				$('select.js-countrySelect').trigger('change');
			});
		});
	</script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/asw-session-keeper.js?6ec0730"></script><script type="text/javascript">
			$(document).ready(function() {
				Asw.SessionKeeper.initAndStart("sessionKeeperContainer", "520", "570", "PC");
			});
			</script><script type="text/javascript">
		Asw.addAjaxCallback(function(data) {
			// アコーディオンのメソッドと設定 
			if($('.jsAccordionSwitch .jsTriggerSwitch').length > 0){
				var events = $._data($('.jsAccordionSwitch .jsTriggerSwitch').get(0), "events");
				if(events === undefined || events.click === undefined) {
					Asw.AccordionInfo('.jsAccordionSwitch', {
						'triggerSwitch': '.jsAccordionSwitch .jsTriggerSwitch',
						'listClass': '.jsAccordionSwitchList',
						'visibilityHidden': 'toggleContents'
					});
				}
			}
		});
	</script><script type="text/javascript">
	Asw.Price = Asw.Price || {};
	// 整数の区切り文字
	Asw.Price.separateString = ',';
	
	// 整数の区切り桁数
	Asw.Price.separateDigitNumber = 3;
	
	// 小数点
	Asw.Price.decimalPointString = '.';
	
	// 小数点以降の有効桁数
	Asw.Price.significantDigit = 0;
	
	// 表示金額の単位
	Asw.Price.currencyUnit = '円';
	</script><script type="text/javascript">
			$(document).ready(function() {
				$('#preLoadingArea').css('visibility', 'hidden');
			});
		</script><noscript><img src="https://aswbe-i.ana.co.jp/akam/13/pixel_646c4915?a=dD1lNThjNDUxMjk1YjA3NTI5MmFmNDQyYWFjMjhlYjk4MGNiMDJmYzZkJmpzPW9mZg==" style="visibility: hidden; position: absolute; left: -999px; top: -999px;" /></noscript><script type="text/javascript"  src="/RcS9yzCRgrNnM/gY/v0Wf9fQwtpdQ/7NE3X2ruOE2SL9YE/HyNpQmYB/RWt/XOHcnHAU"></script></body>
</html>