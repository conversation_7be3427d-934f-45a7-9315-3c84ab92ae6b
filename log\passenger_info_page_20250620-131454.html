<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="ja-<PERSON>" xml:lang="ja-<PERSON>">

	
	<!-- skey = 2025/06/20 14:14:54 rei22c UqWLwq7sU9 dljdmx+56b  --><head id="j_idt53">
    <base href="about:blank">
    <style>
        /* Hide elements that might try to load external resources, as a fallback. */
        img, svg, video, iframe, link[rel="stylesheet"], script { display: none !important; }
    </style>

		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
			<meta http-equiv="X-UA-Compatible" content="IE=EmulateIE9; IE=EmulateIE10" />
			<meta http-equiv="X-UA-Compatible" content="IE=7" />
			<meta http-equiv="X-UA-Compatible" content="IE=edge" />
		<meta name="description" content="" />
		<meta name="keywords" content="" />
		<meta name="format-detection" content="telephone=no" />
		<script type="text/javascript" src="/international_asw/dynatrace/ruxitagentjs_ICANVfhqrux_10273230920145353.js" data-dtconfig="rid=RID_403291245|rpid=-1520539800|domain=aswbe-i.ana.co.jp|reportUrl=https://bf90419wvr.bf.dynatrace.com/bf|app=78bf0b58acf6ed13|cors=1|owasp=1|featureHash=ICANVfhqrux|xb=www^bs.google-analytics^bs.com^pana^bs.demdex^bs.net^pcollect^bs.tealiumiq^bs.com^pallnipponairways^bs.australia-3^bs.evergage^bs.com^pallnipponairwayscolt^bs.tt^bs.omtrdc^bs.net|rdnt=1|uxrgce=1|cuc=k8vpgh61|mel=100000|expw=1|dpvc=1|md=mdcc1=bAsw.PnrOutput.recordLocator,mdcc2=bAsw.PnrOutput.paymentDetailInfoList[0].fopCode,mdcc3=bAsw.PnrOutput.ticketedFlag,mdcc4=bAsw.PnrOutput.nhPnrFlag,mdcc5=bAsw.PnrOutput.itinerarySummary.departureDateYyyymmdd,mdcc6=bAsw.SearchCriteriaOutput.cffCodeInput,mdcc7=a#main ^rb div.section.sectionNoFlow ^rb table ^rb tbody ^rb tr ^rb td.flightNumber.lastCol ^rb span ^rb span:nth-child(2),mdcc8=cunique|lastModification=1750184416409|tp=500,50,0|agentUri=/international_asw/dynatrace/ruxitagentjs_ICANVfhqrux_10273230920145353.js"></script><link rel="shortcut icon" href="https://www.ana.co.jp/favicon.ico" />
		<title>お客様情報入力 | ANA</title>
		<noscript><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/css/asw_noscript_pc.css?99001fc" />
		</noscript>

		<!--  [ Adobe Target ] -->
		<script type="text/javascript" src="https://www.ana.co.jp/behavior/adobetarget/target_intbe.js"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/script/jquery-1.8.3.min.js?717d3c0"></script><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/ja/css/asw_initialize.css?99001fc" /><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/css/asw_screen_pc.css?99001fc" /><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/css/asw_common_pc.css?99001fc" /><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/css/asw_forminput_pc.css?99001fc" /><script type="text/javascript">
		var Asw = Asw || {};
		Asw.GlobalWord = Asw.GlobalWord || {};
		
		Asw.GlobalWord.toolTipCloseIconAltText = "閉じる";
		
		</script><script >bazadebezolkohpepadr="2044841255"</script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/akam/13/79e1ce58"  defer></script></head><body>
		
		<noscript>当サイトをご利用になるためには、JavaScript対応のブラウザが必要です。設定でJavaScriptを有効にしてください。有効にできない場合はANA電話窓口へお問い合わせください。
		</noscript>
		<div id="preLoadingArea"></div>
	
	<div id="cmnHeader" class="platinum">
		<!--===== cmnHeader =====-->
		<div class="headerWrapper">
			<div class="header">
				<p class="logo"><a href="https://www.ana.co.jp/asw/AMCTopServlet?type=a" tabindex="-1" aria-hidden="true"><img src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/image/ana_logo_navy.png?717d3c0" alt="ANA Inspiration of JAPAN。リンク先はマイレージクラブトップページです。" height="28" width="274" /></a>
				</p>
					<ul class="headerMenu">
						<li><a href="https://www.ana.co.jp/asw/AMCTopServlet?type=a">ANAマイレージクラブトップページ</a>
						</li>
						<li><a href="https://www.ana.co.jp/other/int/meta/0005.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">お問い合わせ<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
						</li>
							<li class="btnLogoutArea"><a href="https://aswbe-i.ana.co.jp/rei22c/international_asw/rest/logout" role="button" class="btnBase btnLogout">ログアウト</a>
							</li>
					</ul>
			</div>
		</div>
			<div class="amcUserInfoWrapper">
				<div class="amcUserInfo">
					<div class="userNameArea">
						<table>
							<tr>	
									<td class="statusIcon"><img src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/image/icon_status_05.png?717d3c0" alt="プラチナサービスメンバー" height="46" width="60" />
									</td>
								<td class="userName">佐藤 直樹 <span>様</span>
								</td>
							</tr>					
						</table>
					</div>
						<div class="balance">
								<dl class="upgradePointBalance">
									<dt>アップグレードポイント残高
									</dt>
									<dd>
											<dl>
												<dt>(2025年度)
												</dt>
												<dd>
													<em>20
													</em>ポイント
												</dd>
											</dl>
									</dd>
								</dl>
							<dl class="mileBalance">
								<dt>マイル口座残高
								</dt>
								<dd><em>75,663</em><span class="currencyCode">マイル</span>
								</dd>
							</dl>
						</div>
				</div>
					<p class="nowTime">6月20日14時14分現在
					</p>
			</div>
		<!--===== /cmnHeader =====-->
	</div>
		<div id="sessionKeeperContainer" class="sessionKeepingContainer"><img src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/image/bg_modalWindow.png?717d3c0" alt="" height="0" width="0" />
			<div id="extendSessionExpiredDialog" class="modalExpire modalSmall modalContainer" role="dialog" aria-labelledby="extendSessionExpiredDialogTitle" aria-hidden="true">
<form id="sessionKeeperContainer:j_idt183" name="sessionKeeperContainer:j_idt183" method="post" action="https://aswbe-i.ana.co.jp/rei22c/international_asw/pages/award/reservation/award_mandatory_passenger_information_input.xhtml?aswcid=1&amp;rand=20250620141454UqWLwq7sU9" autocomplete="off" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="sessionKeeperContainer:j_idt183" value="sessionKeeperContainer:j_idt183" />
<input type="hidden" name="sessionKeeperContainer:j_idt183_operationTicket" value="dljdmx+56bdfe9f9ba636b530a9c9bdb5cacf86~njyCQgxui5hCdmqecIlpI6EvBoQcYVGLNm5FuNIM!1750396484952.aere-xml-controller-67d4778877-jgdjz" /><input type="hidden" name="sessionKeeperContainer:j_idt183_cmnPageTicket" value="4" />
					<h1 id="extendSessionExpiredDialogTitle" class="dialogTitle">まもなくセッションが終了します</h1>
					<div id="extendSessionExpiredDialogMessage" class="dialogMessage" tabindex="-1">このままお手続きを進める場合は、［延長］ボタンを押してください。</div>
					<p class="modalButton"><input id="sessionKeeperContainer:cmnSessionKeepingButton" type="submit" name="sessionKeeperContainer:cmnSessionKeepingButton" value="延長" class="btnBase btnModal btnMainStream" onclick="mojarra.ab(this,event,'action',0,0,{'onevent':function(data) {Asw.doCommonAjaxCallback(data,false, event); Asw.SessionKeeper.doSessionKeep(data);},'onerror':function(data) {Asw.doCommonAjaxErrorCallback(data); }});return false" />
					</p><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:0" value="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" autocomplete="off" />
</form>
			</div>
			<div id="showSessionExpiredDialog" class="modalExpire modalSmall modalContainer" role="dialog" aria-labelledby="showSessionExpiredDialogTitle" aria-hidden="true">
				<h1 id="showSessionExpiredDialogTitle" class="dialogTitle">セッションが終了しました</h1>
				<div id="showSessionExpiredDialogMessage" class="dialogMessage" tabindex="-1">一定時間操作されなかったため、お手続きを進めることができません。お手数ですが、再度トップページからお手続きください。</div>
				<p class="modalButton">
					<input type="submit" value="閉じる" class="btnBase btnModal btnMainStream" onclick="Asw.SessionKeeper.closeTimeoutDialog(); return false;" />
				</p>
			</div>
		</div>
		<!--===== dialogMessages =====--><div id="cmnModalMessages"></div>
		<div id="prebookConfirmDialog" class="modalContainer modalConfirm modalSmall " aria-hidden="true" role="dialog">
<form id="prebookConfirmDialog:j_idt403" name="prebookConfirmDialog:j_idt403" method="post" action="https://aswbe-i.ana.co.jp/rei22c/international_asw/pages/award/reservation/award_mandatory_passenger_information_input.xhtml?aswcid=1&amp;rand=20250620141454UqWLwq7sU9" autocomplete="off" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="prebookConfirmDialog:j_idt403" value="prebookConfirmDialog:j_idt403" />
<input type="hidden" name="prebookConfirmDialog:j_idt403_operationTicket" value="dljdmx+56bdfe9f9ba636b530a9c9bdb5cacf86~njyCQgxui5hCdmqecIlpI6EvBoQcYVGLNm5FuNIM!1750396484952.aere-xml-controller-67d4778877-jgdjz" /><input type="hidden" name="prebookConfirmDialog:j_idt403_cmnPageTicket" value="4" /><div class="dialogMessage" tabindex="0"><p>この後、名・姓の修正や変更ができません。</p><p>「搭乗者選択」欄の氏名が「名」「姓」の順番で表示されていることを再確認ください。</p><p>例）<strong class="highlight">空野 太郎</strong> 様 → MR <strong class="highlight">TARO SORANO</strong></p><p>保安上の理由により、航空券とパスポートのお名前が同じ表記でない場合はご搭乗いただくことができませんので必ず再度ご確認ください。</p></div>
					
					<ul class="modalBtnHorizontal">
							
							<li><input type="submit" name="prebookConfirmDialog:j_idt419" value="戻る" aria-controls="prebookConfirmDialog" class="btnBase btnModal btnCancel" onclick="Asw.Dialog.getInstance('prebookConfirmDialog').close(event, Asw.DialogSequencer.isActiveSequencerNotDisplayedLastDialog());  return false;" />
							</li>
						
						<li class="btnArrowNext"><input type="submit" name="prebookConfirmDialog:j_idt422" value="OK" aria-controls="prebookConfirmDialog" class="btnBase btnModal btnMainStream " onclick="Asw.Dialog.getInstance('prebookConfirmDialog').callOpener('NORMAL', event); return false;" />
						</li>
					</ul><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:1" value="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****************************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" autocomplete="off" />
</form>
		</div>
		<!--===== /dialogMessages =====--><div id="cmnContainer">
		<!--=========== .cmnContainer ============--><div id="cmnEmergencyNotice"></div><div id="cmnTitle" class="hgroup ">
		<h1 class="visuallyHidden">お客様情報入力</h1></div>

		<div id="cmnWrapper">
		<!--===== cmnWrapper =====-->
		<ol class="mainFlow" aria-label="完了までの手順、">
			<li><img src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/image/icon_flow_01.png?717d3c0" alt="1" height="20" width="28" />フライト検索</li><!--
			--><li><img src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/image/icon_flow_02_on.png?717d3c0" alt="2" height="20" width="28" /><span class="visuallyHidden">現在のステップ、</span>お客様情報入力</li><!--
			--><li><img src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/image/icon_flow_03.png?717d3c0" alt="3" height="20" width="28" />お支払い情報</li><!--
			--><li class="flowLast"><img src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/image/icon_flow4_04.png?717d3c0" alt="4" height="20" width="28" />完了</li>
		</ol>

	<div id="main">
	<!--=========== main =============-->
		<ul class="iconExample">
				<li class="requiredIconExample">
					<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="" height="7" width="7" /></span>必須項目です	
				</li>
		</ul>
<form id="j_idt526" name="j_idt526" method="post" action="https://aswbe-i.ana.co.jp/rei22c/international_asw/pages/award/reservation/award_mandatory_passenger_information_input.xhtml?aswcid=1&amp;rand=20250620141454UqWLwq7sU9" autocomplete="off" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="j_idt526" value="j_idt526" />
<input type="hidden" name="j_idt526_operationTicket" value="dljdmx+56bdfe9f9ba636b530a9c9bdb5cacf86~njyCQgxui5hCdmqecIlpI6EvBoQcYVGLNm5FuNIM!1750396484952.aere-xml-controller-67d4778877-jgdjz" /><input type="hidden" name="j_idt526_cmnPageTicket" value="4" />

<div class="section">
	<h2>搭乗者選択<a href="https://www.ana.co.jp/other/int/meta/0086.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="特典のご利用に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a></h2>
	<table class="passengerInfo">
		<thead>
			<tr>
				
				<th class="name">氏名</th>
				
				<th class="dateOfBirthWide" id="dateOfBirthHeader">生年月日</th>
				
				<th class="gender lastCol" id="genderHeader">性別<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span></th>
				
			</tr>
		</thead>
		<tbody>
				
				<tr>
					<th class="parent_01" id="parentHeader01">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:0:passenger" type="checkbox" name="passengers:0:passenger" checked="checked" data-number="1" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:0:passenger">NAOKI SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader01">1963年06月05日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader01">				
							<span class="formWrapper">
							<label for="passengers:0:gender" class="visuallyHidden">NAOKI SATOの性別</label><select id="passengers:0:gender" name="passengers:0:gender" size="1">	<option value="">選択してください</option>
	<option value="M" selected="selected">男性</option>
	<option value="F">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_02" id="parentHeader02">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:1:passenger" type="checkbox" name="passengers:1:passenger" data-number="2" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:1:passenger">REO SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader02">1995年05月18日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader02">				
							<span class="formWrapper">
							<label for="passengers:1:gender" class="visuallyHidden">REO SATOの性別</label><select id="passengers:1:gender" name="passengers:1:gender" size="1">	<option value="">選択してください</option>
	<option value="M" selected="selected">男性</option>
	<option value="F">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_03" id="parentHeader03">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:2:passenger" type="checkbox" name="passengers:2:passenger" data-number="3" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:2:passenger">ERI SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader03">1963年08月14日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader03">				
							<span class="formWrapper">
							<label for="passengers:2:gender" class="visuallyHidden">ERI SATOの性別</label><select id="passengers:2:gender" name="passengers:2:gender" size="1">	<option value="">選択してください</option>
	<option value="M">男性</option>
	<option value="F" selected="selected">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_04" id="parentHeader04">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:3:passenger" type="checkbox" name="passengers:3:passenger" data-number="4" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:3:passenger">TAISHI SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader04">1992年12月03日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader04">				
							<span class="formWrapper">
							<label for="passengers:3:gender" class="visuallyHidden">TAISHI SATOの性別</label><select id="passengers:3:gender" name="passengers:3:gender" size="1">	<option value="">選択してください</option>
	<option value="M" selected="selected">男性</option>
	<option value="F">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_05" id="parentHeader05">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:4:passenger" type="checkbox" name="passengers:4:passenger" data-number="5" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:4:passenger">HIROMI FUJIMOTO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader05">1961年11月23日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader05">				
							<span class="formWrapper">
							<label for="passengers:4:gender" class="visuallyHidden">HIROMI FUJIMOTOの性別</label><select id="passengers:4:gender" name="passengers:4:gender" size="1">	<option value="">選択してください</option>
	<option value="M">男性</option>
	<option value="F" selected="selected">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
		</tbody>
	</table>
</div>
		<div class="section">
			<h2 class="midstream">連絡先情報
			</h2>
			<h3>代表者
			</h3>
	
	
	
	<div class="formInput">
		<fieldset>
			<legend><label>代表者</label></legend>
			<dl id="contactEmailAddressInfo">
				<dt class="hasRequiredIcon">メールアドレス<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span><a href="https://www.ana.co.jp/other/int/meta/0057.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="メールの宛先と内容に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a>
				</dt>
				<dd>
					<ul class="verticalList">
						<li class="nonMarginTop"><label for="representativeEmail" class="visuallyHidden">代表者メールアドレス</label><input id="representativeEmail" type="text" name="representativeEmail" value="<EMAIL>" class="widthLarge" placeholder="例：<EMAIL>" />
						</li>
						<li>
							<span class="supplementTxt" id="confirmMailAddressForRepresentative">確認のため再度ご入力ください。
							</span><label for="confirmrepresentativeEmail" class="visuallyHidden">確認用メールアドレス</label><input id="confirmrepresentativeEmail" type="text" name="confirmrepresentativeEmail" value="<EMAIL>" class="widthLarge jsNotPasteInput" placeholder="例：<EMAIL>" aria-describedby="confirmMailAddressForRepresentative" />
						</li>
					</ul>
				</dd>
					<dd style="display:none;"><input id="updateAlwaysInfo" type="checkbox" name="updateAlwaysInfo" /><label for="updateAlwaysInfo">ログイン中の会員情報へこのメールアドレスを登録</label>
					</dd>
			</dl>
			<dl>
				<dt>電話番号<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span>
				</dt>
				<dd class="js-countryWrap">
					<ul>
						<li><label for="representativeTelType" class="visuallyHidden">電話番号種別</label><select id="representativeTelType" name="representativeTelType" class="basicWidth js-typeSelect" size="1" onchange="changeTelType(this,'representativeTel')">	<option value="M1" selected="selected">携帯</option>
	<option value="B1">会社</option>
	<option value="H1">自宅</option>
</select>
						</li>
						<li><label for="representativeTelCountry" class="visuallyHidden">電話番号の国/地区選択</label><select id="representativeTelCountry" name="representativeTelCountry" class="js-countrySelect" size="1">	<option value="" data-sms-possible="true" data-country-code="---">選択してください</option>
	<option value="JP" selected="selected" data-sms-possible="true">日本</option>
	<option value="-" data-sms-possible="true" data-country-code="---">-----------</option>
	<option value="IS" data-sms-possible="true">アイスランド</option>
	<option value="IE" data-sms-possible="true">アイルランド</option>
	<option value="AZ" data-sms-possible="true">アゼルバイジャン</option>
	<option value="AF" data-sms-possible="true">アフガニスタン</option>
	<option value="US" data-sms-possible="true">アメリカ合衆国/カナダ</option>
	<option value="AE" data-sms-possible="true">アラブ首長国連邦</option>
	<option value="DZ" data-sms-possible="true">アルジェリア</option>
	<option value="AR" data-sms-possible="true">アルゼンチン</option>
	<option value="AW" data-sms-possible="true">アルバ</option>
	<option value="AL" data-sms-possible="true">アルバニア</option>
	<option value="AM" data-sms-possible="true">アルメニア</option>
	<option value="AO" data-sms-possible="true">アンゴラ</option>
	<option value="AG" data-sms-possible="true">アンティグア・バーブーダ</option>
	<option value="AD" data-sms-possible="true">アンドラ</option>
	<option value="YE" data-sms-possible="true">イエメン</option>
	<option value="GB" data-sms-possible="true">イギリス</option>
	<option value="IL" data-sms-possible="true">イスラエル</option>
	<option value="IT" data-sms-possible="true">イタリア</option>
	<option value="IQ" data-sms-possible="true">イラク</option>
	<option value="IR" data-sms-possible="true">イラン</option>
	<option value="IN" data-sms-possible="true">インド</option>
	<option value="ID" data-sms-possible="true">インドネシア</option>
	<option value="UG" data-sms-possible="true">ウガンダ</option>
	<option value="UA" data-sms-possible="true">ウクライナ</option>
	<option value="UZ" data-sms-possible="true">ウズベキスタン</option>
	<option value="UY" data-sms-possible="true">ウルグアイ</option>
	<option value="EC" data-sms-possible="true">エクアドル</option>
	<option value="EG" data-sms-possible="true">エジプト</option>
	<option value="EE" data-sms-possible="true">エストニア</option>
	<option value="SZ" data-sms-possible="true">エスワティニ</option>
	<option value="ET" data-sms-possible="true">エチオピア</option>
	<option value="ER" data-sms-possible="true">エリトリア</option>
	<option value="SV" data-sms-possible="true">エルサルバドル</option>
	<option value="AU" data-sms-possible="true">オーストラリア</option>
	<option value="AT" data-sms-possible="true">オーストリア</option>
	<option value="OM" data-sms-possible="true">オマーン</option>
	<option value="NL" data-sms-possible="true">オランダ</option>
	<option value="GH" data-sms-possible="true">ガーナ</option>
	<option value="CV" data-sms-possible="true">カーボベルデ</option>
	<option value="GY" data-sms-possible="true">ガイアナ</option>
	<option value="KZ" data-sms-possible="true">カザフスタン</option>
	<option value="QA" data-sms-possible="true">カタール</option>
	<option value="CA" data-sms-possible="true">カナダ/アメリカ合衆国</option>
	<option value="GA" data-sms-possible="true">ガボン</option>
	<option value="CM" data-sms-possible="true">カメルーン</option>
	<option value="KR" data-sms-possible="true">韓国</option>
	<option value="GM" data-sms-possible="true">ガンビア</option>
	<option value="KH" data-sms-possible="true">カンボジア</option>
	<option value="KP" data-sms-possible="true">北朝鮮</option>
	<option value="MK" data-sms-possible="true">北マケドニア</option>
	<option value="GN" data-sms-possible="true">ギニア</option>
	<option value="GW" data-sms-possible="true">ギニアビサウ</option>
	<option value="CY" data-sms-possible="true">キプロス</option>
	<option value="CU" data-sms-possible="true">キューバ</option>
	<option value="GR" data-sms-possible="true">ギリシャ</option>
	<option value="KI" data-sms-possible="true">キリバス</option>
	<option value="KG" data-sms-possible="true">キルギス</option>
	<option value="GT" data-sms-possible="true">グアテマラ</option>
	<option value="GU" data-sms-possible="true">グアム</option>
	<option value="KW" data-sms-possible="true">クウェート</option>
	<option value="GD" data-sms-possible="true">グレナダ</option>
	<option value="HR" data-sms-possible="true">クロアチア</option>
	<option value="KY" data-sms-possible="true">ケイマン諸島</option>
	<option value="KE" data-sms-possible="true">ケニア</option>
	<option value="CI" data-sms-possible="true">コートジボワール</option>
	<option value="CR" data-sms-possible="true">コスタリカ</option>
	<option value="KM" data-sms-possible="true">コモロ</option>
	<option value="CO" data-sms-possible="true">コロンビア</option>
	<option value="CG" data-sms-possible="true">コンゴ共和国</option>
	<option value="CD" data-sms-possible="true">コンゴ民主共和国</option>
	<option value="SA" data-sms-possible="true">サウジアラビア</option>
	<option value="ZM" data-sms-possible="true">ザンビア</option>
	<option value="SM" data-sms-possible="true">サンマリノ</option>
	<option value="SL" data-sms-possible="true">シエラレオネ</option>
	<option value="DJ" data-sms-possible="true">ジブチ</option>
	<option value="JM" data-sms-possible="true">ジャマイカ</option>
	<option value="GE" data-sms-possible="true">ジョージア</option>
	<option value="SY" data-sms-possible="true">シリア</option>
	<option value="SG" data-sms-possible="true">シンガポール</option>
	<option value="ZW" data-sms-possible="true">ジンバブエ</option>
	<option value="CH" data-sms-possible="true">スイス</option>
	<option value="SE" data-sms-possible="true">スウェーデン</option>
	<option value="SD" data-sms-possible="true">スーダン</option>
	<option value="ES" data-sms-possible="true">スペイン</option>
	<option value="SR" data-sms-possible="true">スリナム</option>
	<option value="LK" data-sms-possible="true">スリランカ</option>
	<option value="SK" data-sms-possible="true">スロバキア</option>
	<option value="SI" data-sms-possible="true">スロベニア</option>
	<option value="SC" data-sms-possible="true">セイシェル</option>
	<option value="GQ" data-sms-possible="true">赤道ギニア</option>
	<option value="SN" data-sms-possible="true">セネガル</option>
	<option value="RS" data-sms-possible="true">セルビア</option>
	<option value="KN" data-sms-possible="true">セントクリストファー・ネイビス</option>
	<option value="LC" data-sms-possible="true">セントルシア</option>
	<option value="SB" data-sms-possible="true">ソロモン諸島</option>
	<option value="TH" data-sms-possible="true">タイ</option>
	<option value="TW" data-sms-possible="true">台湾</option>
	<option value="TJ" data-sms-possible="true">タジキスタン</option>
	<option value="TZ" data-sms-possible="true">タンザニア</option>
	<option value="CZ" data-sms-possible="true">チェコ</option>
	<option value="TD" data-sms-possible="true">チャド</option>
	<option value="CF" data-sms-possible="true">中央アフリカ共和国</option>
	<option value="CN" data-sms-possible="true">中国</option>
	<option value="TN" data-sms-possible="true">チュニジア</option>
	<option value="CL" data-sms-possible="true">チリ</option>
	<option value="TV" data-sms-possible="true">ツバル</option>
	<option value="DK" data-sms-possible="true">デンマーク</option>
	<option value="DE" data-sms-possible="true">ドイツ</option>
	<option value="TG" data-sms-possible="true">トーゴ</option>
	<option value="DO" data-sms-possible="true">ドミニカ共和国</option>
	<option value="DM" data-sms-possible="true">ドミニカ国</option>
	<option value="TT" data-sms-possible="true">トリニダード・トバゴ</option>
	<option value="TM" data-sms-possible="true">トルクメニスタン</option>
	<option value="TR" data-sms-possible="true">トルコ</option>
	<option value="TO" data-sms-possible="true">トンガ</option>
	<option value="NG" data-sms-possible="true">ナイジェリア</option>
	<option value="NR" data-sms-possible="true">ナウル</option>
	<option value="NA" data-sms-possible="true">ナミビア</option>
	<option value="NI" data-sms-possible="true">ニカラグア</option>
	<option value="NE" data-sms-possible="true">ニジェール</option>
	<option value="NZ" data-sms-possible="true">ニュージーランド</option>
	<option value="NP" data-sms-possible="true">ネパール</option>
	<option value="NO" data-sms-possible="true">ノルウェー</option>
	<option value="BH" data-sms-possible="true">バーレーン</option>
	<option value="HT" data-sms-possible="true">ハイチ</option>
	<option value="PK" data-sms-possible="true">パキスタン</option>
	<option value="VA" data-sms-possible="true">バチカン</option>
	<option value="PA" data-sms-possible="true">パナマ</option>
	<option value="VU" data-sms-possible="true">バヌアツ</option>
	<option value="BS" data-sms-possible="true">バハマ</option>
	<option value="PG" data-sms-possible="true">パプアニューギニア</option>
	<option value="BM" data-sms-possible="true">バミューダ</option>
	<option value="PW" data-sms-possible="true">パラオ</option>
	<option value="PY" data-sms-possible="true">パラグアイ</option>
	<option value="BB" data-sms-possible="true">バルバドス</option>
	<option value="PS" data-sms-possible="true">パレスチナ</option>
	<option value="HU" data-sms-possible="true">ハンガリー</option>
	<option value="BD" data-sms-possible="true">バングラデシュ</option>
	<option value="TL" data-sms-possible="true">東ティモール</option>
	<option value="FJ" data-sms-possible="true">フィジー</option>
	<option value="PH" data-sms-possible="true">フィリピン</option>
	<option value="FI" data-sms-possible="true">フィンランド</option>
	<option value="BT" data-sms-possible="true">ブータン</option>
	<option value="PR" data-sms-possible="true">プエルトリコ</option>
	<option value="BR" data-sms-possible="true">ブラジル</option>
	<option value="FR" data-sms-possible="true">フランス</option>
	<option value="PF" data-sms-possible="true">フランス領ポリネシア</option>
	<option value="BG" data-sms-possible="true">ブルガリア</option>
	<option value="BF" data-sms-possible="true">ブルキナファソ</option>
	<option value="BN" data-sms-possible="true">ブルネイ</option>
	<option value="BI" data-sms-possible="true">ブルンジ</option>
	<option value="VN" data-sms-possible="true">ベトナム</option>
	<option value="BJ" data-sms-possible="true">ベナン</option>
	<option value="VE" data-sms-possible="true">ベネズエラ</option>
	<option value="BY" data-sms-possible="true">ベラルーシ</option>
	<option value="BZ" data-sms-possible="true">ベリーズ</option>
	<option value="PE" data-sms-possible="true">ペルー</option>
	<option value="BE" data-sms-possible="true">ベルギー</option>
	<option value="PL" data-sms-possible="true">ポーランド</option>
	<option value="BA" data-sms-possible="true">ボスニア・ヘルツェゴビナ</option>
	<option value="BW" data-sms-possible="true">ボツワナ</option>
	<option value="BO" data-sms-possible="true">ボリビア</option>
	<option value="PT" data-sms-possible="true">ポルトガル</option>
	<option value="HK" data-sms-possible="true">香港</option>
	<option value="HN" data-sms-possible="true">ホンジュラス</option>
	<option value="MH" data-sms-possible="true">マーシャル諸島</option>
	<option value="MO" data-sms-possible="true">マカオ</option>
	<option value="MG" data-sms-possible="true">マダガスカル</option>
	<option value="MW" data-sms-possible="true">マラウイ</option>
	<option value="ML" data-sms-possible="true">マリ</option>
	<option value="MT" data-sms-possible="true">マルタ</option>
	<option value="MY" data-sms-possible="true">マレーシア</option>
	<option value="FM" data-sms-possible="true">ミクロネシア連邦</option>
	<option value="ZA" data-sms-possible="true">南アフリカ</option>
	<option value="MM" data-sms-possible="true">ミャンマー</option>
	<option value="MX" data-sms-possible="true">メキシコ</option>
	<option value="MU" data-sms-possible="true">モーリシャス</option>
	<option value="MR" data-sms-possible="true">モーリタニア</option>
	<option value="MZ" data-sms-possible="true">モザンビーク</option>
	<option value="MC" data-sms-possible="true">モナコ</option>
	<option value="MV" data-sms-possible="true">モルディヴ</option>
	<option value="MD" data-sms-possible="true">モルドバ</option>
	<option value="MA" data-sms-possible="true">モロッコ</option>
	<option value="MN" data-sms-possible="true">モンゴル</option>
	<option value="ME" data-sms-possible="true">モンテネグロ</option>
	<option value="JO" data-sms-possible="true">ヨルダン</option>
	<option value="LA" data-sms-possible="true">ラオス人民民主共和国</option>
	<option value="LV" data-sms-possible="true">ラトビア</option>
	<option value="LT" data-sms-possible="true">リトアニア</option>
	<option value="LY" data-sms-possible="true">リビア</option>
	<option value="LI" data-sms-possible="true">リヒテンシュタイン</option>
	<option value="LR" data-sms-possible="true">リベリア</option>
	<option value="RO" data-sms-possible="true">ルーマニア</option>
	<option value="LU" data-sms-possible="true">ルクセンブルク</option>
	<option value="RW" data-sms-possible="true">ルワンダ</option>
	<option value="LS" data-sms-possible="true">レソト</option>
	<option value="LB" data-sms-possible="true">レバノン</option>
	<option value="RU" data-sms-possible="true">ロシア</option>
</select>
						</li>
						<li class="numberInputWrap">
							<span id="numberInputInfo1" class="supplementTxt marginTop">先頭に0(ゼロ)がある場合、0(ゼロ)を抜いてください(一部、国・地域除く)。携帯電話番号の場合は、携帯電話を契約された国を選択ください。</span>
							<div class="numberRowWrap">
								<div class="block">
									
									<span class="visuallyHidden">国番号</span>
									<span class="viewInput widthMini js-countryCode"></span>
								</div>
								<div class="block"><label for="representativeTel" class="visuallyHidden">電話番号</label><input id="representativeTel" type="text" name="representativeTel" value="9042504176" class="widthMiddle" placeholder="例：9012345678" maxlength="30" aria-describedby="numberInputInfo1" />
								</div>
							</div>
						</li>
					</ul>
				</dd>
			</dl>
		</fieldset>
	</div>
	<div class="titleWrapper">
		<h3>
			<span class="text">eメール・SMSに関するご案内<a href="https://www.ana.co.jp/other/int/meta/0057.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="eメール・SMSに関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a></span>
		</h3>
	</div>
	<div class="formInput">
	<h4 class="formAreaTitle">主に搭乗・運航に関わるeメールの送付先設定（米国へ通知いたします）<a href="https://www.ana.co.jp/other/int/meta/0629.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="米国入国用eメール・SMS・電話番号に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a>
	</h4>
	<div class="paxSettingArea js-mailFormWrap onePax is-open">
			<div class="settingArea">
					<dl>
						<dt class="breakWord">NAOKI SATO
						</dt>
						<dd>
							<fieldset>
								<legend>NAOKI SATOのeメール送付先および米国到着時に提供するメールアドレス</legend>代表者と同じ
							</fieldset>
						</dd>
					</dl>
			</div>
	</div>
	<h4 class="formAreaTitle hasTopLine">主に搭乗・運航に関わるSMSの送付先および米国で受信できる電話番号の設定（米国へ通知いたします）<a href="https://www.ana.co.jp/other/int/meta/0629.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="米国入国用eメール・SMS・電話番号に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a>
	</h4>
	<div class="paxSettingArea js-smsFormWrap onePax is-open">
			<div class="settingArea">
					<dl>
						<dt class="breakWord">NAOKI SATO<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span>
						</dt>
						<dd>
							<fieldset>
								<legend>NAOKI SATOの1つ目の電話番号</legend>
									<ul class="verticalList js-formItem">
										<li><input type="radio" id="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSameAsRepresentativeSms:radioItem" name="contactsSms:0:recipientSmsEachPersonSelection:radioGroup" value="0" checked="checked" /><label for="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSameAsRepresentativeSms:radioItem">代表者電話番号と同じ</label>
										</li>
										<li><input type="radio" id="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSmsPerPassenger:radioItem" name="contactsSms:0:recipientSmsEachPersonSelection:radioGroup" value="1" /><label for="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSmsPerPassenger:radioItem">電話番号を指定</label>
											<div class="indentArea js-formImputArea">
												<span id="confirmTelNumberForRepresentative:0" class="supplementTxt marginTop">先頭に0(ゼロ)がある場合、0(ゼロ)を抜いてください(一部、国・地域除く)。携帯電話番号の場合は、携帯電話を契約された国を選択ください。</span>
												<ul class="countryNumberInput js-countryWrap">
													<li><label for="contactsSms:0:recipientSmsEachPersonSelection:passengerSmsCountry" class="visuallyHidden">電話番号の国/地区選択</label><select id="contactsSms:0:recipientSmsEachPersonSelection:passengerSmsCountry" name="contactsSms:0:recipientSmsEachPersonSelection:passengerSmsCountry" class="countrySelect js-countrySelect" size="1">	<option value="" data-country-code="---">選択してください</option>
	<option value="JP">日本</option>
	<option value="-" data-country-code="---">-----------</option>
	<option value="IE">アイルランド</option>
	<option value="AZ">アゼルバイジャン</option>
	<option value="AF">アフガニスタン</option>
	<option value="US">アメリカ合衆国/カナダ</option>
	<option value="AE">アラブ首長国連邦</option>
	<option value="DZ">アルジェリア</option>
	<option value="AR">アルゼンチン</option>
	<option value="AW">アルバ</option>
	<option value="AL">アルバニア</option>
	<option value="AM">アルメニア</option>
	<option value="AG">アンティグア・バーブーダ</option>
	<option value="AD">アンドラ</option>
	<option value="YE">イエメン</option>
	<option value="GB">イギリス</option>
	<option value="IL">イスラエル</option>
	<option value="IT">イタリア</option>
	<option value="IQ">イラク</option>
	<option value="IR">イラン</option>
	<option value="IN">インド</option>
	<option value="ID">インドネシア</option>
	<option value="UG">ウガンダ</option>
	<option value="UA">ウクライナ</option>
	<option value="UZ">ウズベキスタン</option>
	<option value="UY">ウルグアイ</option>
	<option value="EC">エクアドル</option>
	<option value="EG">エジプト</option>
	<option value="EE">エストニア</option>
	<option value="ER">エリトリア</option>
	<option value="SV">エルサルバドル</option>
	<option value="AU">オーストラリア</option>
	<option value="AT">オーストリア</option>
	<option value="OM">オマーン</option>
	<option value="NL">オランダ</option>
	<option value="GH">ガーナ</option>
	<option value="GY">ガイアナ</option>
	<option value="KZ">カザフスタン</option>
	<option value="QA">カタール</option>
	<option value="CA">カナダ/アメリカ合衆国</option>
	<option value="GA">ガボン</option>
	<option value="CM">カメルーン</option>
	<option value="KR">韓国</option>
	<option value="GM">ガンビア</option>
	<option value="KH">カンボジア</option>
	<option value="GN">ギニア</option>
	<option value="GW">ギニアビサウ</option>
	<option value="CY">キプロス</option>
	<option value="CU">キューバ</option>
	<option value="GR">ギリシャ</option>
	<option value="KG">キルギス</option>
	<option value="GT">グアテマラ</option>
	<option value="GU">グアム</option>
	<option value="KW">クウェート</option>
	<option value="GD">グレナダ</option>
	<option value="HR">クロアチア</option>
	<option value="KY">ケイマン諸島</option>
	<option value="KE">ケニア</option>
	<option value="CI">コートジボワール</option>
	<option value="CR">コスタリカ</option>
	<option value="KM">コモロ</option>
	<option value="CO">コロンビア</option>
	<option value="CD">コンゴ民主共和国</option>
	<option value="SA">サウジアラビア</option>
	<option value="ZM">ザンビア</option>
	<option value="SL">シエラレオネ</option>
	<option value="JM">ジャマイカ</option>
	<option value="GE">ジョージア</option>
	<option value="SY">シリア</option>
	<option value="SG">シンガポール</option>
	<option value="ZW">ジンバブエ</option>
	<option value="CH">スイス</option>
	<option value="SE">スウェーデン</option>
	<option value="SD">スーダン</option>
	<option value="ES">スペイン</option>
	<option value="SR">スリナム</option>
	<option value="LK">スリランカ</option>
	<option value="SK">スロバキア</option>
	<option value="SI">スロベニア</option>
	<option value="SN">セネガル</option>
	<option value="RS">セルビア</option>
	<option value="KN">セントクリストファー・ネイビス</option>
	<option value="LC">セントルシア</option>
	<option value="TH">タイ</option>
	<option value="TW">台湾</option>
	<option value="TJ">タジキスタン</option>
	<option value="TZ">タンザニア</option>
	<option value="CZ">チェコ</option>
	<option value="TD">チャド</option>
	<option value="CF">中央アフリカ共和国</option>
	<option value="CN">中国</option>
	<option value="TN">チュニジア</option>
	<option value="CL">チリ</option>
	<option value="DK">デンマーク</option>
	<option value="DE">ドイツ</option>
	<option value="TG">トーゴ</option>
	<option value="DO">ドミニカ共和国</option>
	<option value="TT">トリニダード・トバゴ</option>
	<option value="TR">トルコ</option>
	<option value="NG">ナイジェリア</option>
	<option value="NI">ニカラグア</option>
	<option value="NE">ニジェール</option>
	<option value="NZ">ニュージーランド</option>
	<option value="NP">ネパール</option>
	<option value="NO">ノルウェー</option>
	<option value="HT">ハイチ</option>
	<option value="PK">パキスタン</option>
	<option value="PA">パナマ</option>
	<option value="BS">バハマ</option>
	<option value="PG">パプアニューギニア</option>
	<option value="BM">バミューダ</option>
	<option value="PW">パラオ</option>
	<option value="PY">パラグアイ</option>
	<option value="BB">バルバドス</option>
	<option value="PS">パレスチナ</option>
	<option value="HU">ハンガリー</option>
	<option value="BD">バングラデシュ</option>
	<option value="TL">東ティモール</option>
	<option value="FJ">フィジー</option>
	<option value="PH">フィリピン</option>
	<option value="FI">フィンランド</option>
	<option value="BT">ブータン</option>
	<option value="PR">プエルトリコ</option>
	<option value="BR">ブラジル</option>
	<option value="FR">フランス</option>
	<option value="PF">フランス領ポリネシア</option>
	<option value="BG">ブルガリア</option>
	<option value="BF">ブルキナファソ</option>
	<option value="BN">ブルネイ</option>
	<option value="BI">ブルンジ</option>
	<option value="VN">ベトナム</option>
	<option value="BJ">ベナン</option>
	<option value="VE">ベネズエラ</option>
	<option value="BY">ベラルーシ</option>
	<option value="PE">ペルー</option>
	<option value="BE">ベルギー</option>
	<option value="PL">ポーランド</option>
	<option value="BA">ボスニア・ヘルツェゴビナ</option>
	<option value="BW">ボツワナ</option>
	<option value="HK">香港</option>
	<option value="MH">マーシャル諸島</option>
	<option value="MO">マカオ</option>
	<option value="MG">マダガスカル</option>
	<option value="MW">マラウイ</option>
	<option value="ML">マリ</option>
	<option value="MT">マルタ</option>
	<option value="MY">マレーシア</option>
	<option value="ZA">南アフリカ</option>
	<option value="MM">ミャンマー</option>
	<option value="MX">メキシコ</option>
	<option value="MU">モーリシャス</option>
	<option value="MR">モーリタニア</option>
	<option value="MZ">モザンビーク</option>
	<option value="MC">モナコ</option>
	<option value="MV">モルディヴ</option>
	<option value="MA">モロッコ</option>
	<option value="MN">モンゴル</option>
	<option value="ME">モンテネグロ</option>
	<option value="JO">ヨルダン</option>
	<option value="LA">ラオス人民民主共和国</option>
	<option value="LV">ラトビア</option>
	<option value="LT">リトアニア</option>
	<option value="LY">リビア</option>
	<option value="LR">リベリア</option>
	<option value="RO">ルーマニア</option>
	<option value="LU">ルクセンブルク</option>
	<option value="RW">ルワンダ</option>
	<option value="LB">レバノン</option>
	<option value="RU">ロシア</option>
</select>
													</li>
													<li>
														
														<div class="numberRowWrap">
															<div class="block">
																<span class="visuallyHidden">国番号</span>
																<span class="countryCode viewInput js-countryCode"></span>
															</div>
															
															<div class="block"><label for="contactsSms:0:recipientSmsEachPersonSelection:flightStatusNotificationContactPointSmsDescription" class="visuallyHidden">電話番号</label><input id="contactsSms:0:recipientSmsEachPersonSelection:flightStatusNotificationContactPointSmsDescription" type="text" name="contactsSms:0:recipientSmsEachPersonSelection:flightStatusNotificationContactPointSmsDescription" class="telNumber" placeholder="例：9012345678" maxlength="30" aria-describedby="confirmTelNumberForRepresentative:0" />
															</div>
														</div>
													</li>
												</ul>
											</div>
										</li>
									</ul>
							</fieldset>
						</dd>
					</dl>
			</div>
	</div>
	</div>
	
	<div class="formInput articleLine">
		<h3>滞在中の連絡先(任意)
		</h3>
		<dl>
			
			<dt><label for="accommodationOne">現地連絡先1</label>
			</dt>
			<dd><input id="accommodationOne" type="text" name="accommodationOne" class="widthLarge" placeholder="例：09012345678 ABC HOTEL" maxlength="28" title="例：09012345678 ABC HOTEL" />
			</dd>
		</dl>
		<dl>
			
			<dt><label for="accommodationTwo">現地連絡先2</label>
			</dt>
			<dd><input id="accommodationTwo" type="text" name="accommodationTwo" class="widthLarge" placeholder="例：09012345678 ABC HOTEL" maxlength="28" title="例：09012345678 ABC HOTEL" />
			</dd>
		</dl>
	</div>
		</div>
		
		<p class="btnAreaSubmit btnArrowNext "><input id="next" type="submit" name="next" value="次へ" class="btnBase btnMainStream btnVerticalMain btnWidthVariable" onclick="return Asw.Dialog.getInstance('prebookConfirmDialog').toggle(event);return Asw.LoadingWindow.open(&quot;NORMAL&quot;, event)" />
		</p><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:2" value="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" autocomplete="off" />
</form><div id="cmnDynamicMessages" class="infoBoxSection">
				<dl class="infoBox importantInfo jsAccordionSwitch">
					<dt><strong>重要なご案内</strong></dt>
					<dd>
						<ul class="jsAccordionSwitchList" id="informationMessages">
								<li>個人情報の利用目的<a href="https://www.ana.co.jp/other/int/meta/0012.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="ANAプライバシーポリシー(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">ANAプライバシーポリシー<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>を必ずお読みいただき、内容に同意いただいた上で、お進みください。</li>
								<li><a href="https://www.ana.co.jp/other/int/meta/0058.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="Secure Flight Programについて(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">米国Secure Flightプログラム<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>に基づき、パスポート名、生年月日、性別などの情報は事前に米国へ通知いたします。</li>
								<li>入力したeメールアドレス、電話番号は入国に必要な情報として米国に通知される可能性があります。</li>
									<li class="jsHiddenFlg toggleContents">お名前のスペルがパスポート名と一致しているかご確認ください。異なる場合はインターネットではお取り扱いできません。<a href="https://www.ana.co.jp/other/int/meta/intbe0001.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="ANAお問い合わせ先(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">ANA<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>へ名前の変更をお申し出ください。</li>
									<li class="jsHiddenFlg toggleContents">二重国籍のお客様は、ご搭乗時に使用されるパスポートのお名前でご予約ください。<br />パスポートごとにお名前が異なる場合は、お客様ご自身で入国管理事務所・外務省・大使館などで各種条件をご確認のうえ、使用されるパスポートを決めてください。<br />なお、入国可否については国によって異なるため、お客様ご自身で各入国管理事務所などでご確認ください。</li>
									<li class="jsHiddenFlg toggleContents">旅行開始日時点で12歳以上の場合は大人、2歳以上12歳未満の場合は小児、2歳未満の場合は幼児の扱いとなります。なお、小児は大人と同マイル数が必要です。</li>
									<li class="jsHiddenFlg toggleContents">運航に関する情報をお客様へ通知するため、提携航空会社にもお客様のメールアドレスが提供されます。あらかじめご了承ください。</li>
									<li class="jsHiddenFlg toggleContents"><dl><dt>携帯電話へのメール送信をご希望のお客様へ</dt><dd>ドメイン指定受信をご利用の場合は、ana.co.jpとamadeus.comからのメールが受信できるようあらかじめ設定をお願いいたします。</dd></dl></li>
									<li class="jsHiddenFlg toggleContents">SMSはスマートフォンの番号に配信されます。一部の国ではSMSでのご案内をご利用いただけません。詳細は<a href="https://www.ana.co.jp/other/int/meta/0737.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="ショートメッセージ(SMS)でのご案内対象外の国について(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">ショートメッセージ(SMS)でのご案内対象外の国について<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>をご覧ください。</li>
									<li class="jsHiddenFlg toggleContents">ログインされている会員ご本人が搭乗しない場合は、再度フライト検索画面より「ログインされている会員ご本人は搭乗しない」のチェックボックスを選択してお手続きください。</li>
						</ul>
							<a href="#" class="toggleSwitch jsTriggerSwitch" role="button" aria-controls="informationMessages" aria-expanded="false"><img src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/image/toggle_open.png?7bec38a" alt="開閉ボタン" height="25" width="25" class="jsRollOver" />
							</a>
					</dd>
				</dl></div>
	<!--=========== /main =============-->
	</div>

	<!--=========== Script ============-->
	<script type="text/javascript">
	$(window).on('load', function() {
		// アコーディオンのメソッドと設定
		Asw.AccordionInfo('.jsAccordionSwitch', {
			'triggerSwitch': '.jsAccordionSwitch .jsTriggerSwitch',
			'listClass': '.jsAccordionSwitchList',
			'visibilityHidden': 'toggleContents'
		});
	});
	</script>
	<!--=========== Script ============-->
	<script type="text/javascript">
		//プラットフォーム判別（サマリーエリア追従用として）
		var Asw = Asw || {};
	
	  	Asw.ClientInfo = Asw.ClientInfo  || {};
	  	Asw.ClientInfo.DeviceType = Asw.ClientInfo.DeviceType || {};
	
	  	Asw.ClientInfo.DeviceType.PC = 'PC';
	  	Asw.ClientInfo.DeviceType.SP = 'SP';
	  	Asw.ClientInfo.DeviceType.TC = 'TC';
	
	  	Asw.ClientInfo.deviceType = Asw.ClientInfo.DeviceType.PC;
	
	 	if((navigator.userAgent.indexOf('Android') > 0 && navigator.userAgent.indexOf('Mobile') == -1) || navigator.userAgent.indexOf('iPad') > 0){
	    	Asw.ClientInfo.deviceType = Asw.ClientInfo.DeviceType.TC;
	  	}
	</script>
	<!--=========== /Script ============--><div id="summaryArea">
			<div class="flightSummaryArea">
						<div class="flightSummarySection">
							<h2>区間 1
							</h2>
							<p class="flightDate"><em>6</em>月<em class="hasLeft">28</em>日（土）
							</p>
							<div class="flightSummaryRoute">
									<div class="flightSummaryRouteSection">
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation1_1">
													<span>東京(成田)
													</span>
												</p>
												<p class="time">
													<span>17:00
													</span>
												</p>
											</div>
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation1_1">
													<span>シカゴ(ORD)
													</span>
												</p>
												<p class="time">
													<span>14:55
													</span>
												</p>
											</div>
										<p class="flightNumberClass">
												<span>NH012</span><span><img src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/image/flight_ana_summaryarea.png?717d3c0" alt="ANA運航便" height="11" width="41" /></span>
												<br />特典ビジネス
										</p>
									</div>
							</div>
						</div>
						<div class="flightSummarySection">
							<h2 class="midstream">区間 2
							</h2>
							<p class="flightDate"><em>7</em>月<em class="hasLeft">23</em>日（水）
							</p>
							<div class="flightSummaryRoute">
									<div class="flightSummaryRouteSection">
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation2_1">
													<span>ホノルル(オアフ島)
													</span>
												</p>
												<p class="time">
													<span>11:35
													</span>
												</p>
											</div>
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation2_1">
													<span>東京(成田)
													</span>
												</p>
												<p class="time">
													<span>14:50
																	<span>翌日
																	</span>
													</span>
												</p>
											</div>
										<p class="flightNumberClass">
												<span>NH183</span><span><img src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/image/flight_ana_summaryarea.png?717d3c0" alt="ANA運航便" height="11" width="41" /></span>
												<br />特典エコノミー
										</p>
									</div>
							</div>
						</div>
					<div class="total">
						<p class="passenger">大人 x1
						</p>
						<div class="totalArea">
					  		<dl class="mealAmountWrap">
									<dt>有料機内食サービス
									</dt>
									<dd><em class="mealAmount">0</em><span class="currencyCode">円</span>
									</dd>
							</dl>
									<div>
										<div class="mileageAndTotal">
												<dl class="requiredMileage">
													<dt>必要マイル
													</dt>
													<dd><em class="requiredMileage">74,000</em><span class="currencyCode">マイル</span>
													</dd>
												</dl>
												<dl>
													<dt>総額
													</dt>
													<dd><em class="awardTotalPayment">68,130</em>円
													</dd>
												</dl>
												<p class="includeFareAndFuelCost">※各種税金、燃油特別付加運賃等を含みます。
												</p>
										</div>
									</div>
						</div>
					</div>
				
			</div></div>
		<!--===== /cmnWrapper =====-->
		</div>
			
		<span id="message" class="visuallyHidden" role="alert" aria-live="assertive"></span>
		<span id="politeMessage" class="visuallyHidden" role="log" aria-live="polite"></span><script type="text/javascript">
		var Asw = Asw || {};
		Asw.BaseOutput = {"sessionID":"UqWLwq7sU9","language":"ja","siteCatalystLanguageCode":"J","operationDate":"20250620","operationDateTime":"20250620141454","office":{"officeCode":"TYONH08ZZ","country2letterCode":"JP"},"bookingType":"A","pageID":"A04_P01","siteCatalystPageName":"INT_BE_AWARD_J_A04特典予約_P01旅客情報入力（必須情報）","device":{"deviceType":"PC"}};
	</script><div id="promotionArea">
		<!--===== promotionArea =====-->
		<!--===== /promotionArea =====--></div>
		<!--=========== /cmnContainer ============--></div><div id="cmnBaloonArea" aria-live="polite"></div><div id="cmnPopupArea" aria-live="polite"></div>

	

	<div id="cmnFooterWrapper">
		<!--=========== .cmnFooterWrapper ============-->
		<div id="cmnFooter">
			<!--===== cmnFooter =====-->
			<ul>
				<li><a href="https://www.ana.co.jp/other/int/meta/0012.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">プライバシーポリシー<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
					<li><a href="https://www.ana.co.jp/other/int/meta/0670.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">利用者情報の外部送信について<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
					</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/0052.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">サイト利用規約<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/be0238.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">推奨環境<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/0133.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">サイトマップ<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/0732.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">運送約款<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
					<li class="lastChild"><a href="https://www.ana.co.jp/other/int/meta/0556.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">ウェブアクセシビリティについて<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
					</li>
			</ul>
			<p class="blankText" aria-hidden="true"><img src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="" height="10" width="10" />外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。</p>
			<p class="copyright"><span lang="en" xml:lang="en">Copyright&#169;ANA</span>
			</p>
			<p class="starAllianceLogo"><a href="http://www.staralliance.com/ja/" target="_blank"><img src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/image/star_alliance_logo.png?0fe73b5" alt="A STAR ALLIANCE MEMBER" height="18" width="175" /><span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
			</p>
			<!--===== cmnFooter =====-->
		</div>
		<!--=========== /cmnFooterWrapper ============-->
	</div>

		
		<div id="cmnLoadingForAjax"><img src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/image/bg_modalWindow.png?717d3c0" alt="" height="0" width="0" />
			<div class="loadingArea">
				<p class="loadingImage"><img src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/image/loading.gif?717d3c0" alt="リクエストを処理しています...しばらくお待ちください。" height="33" width="33" />
				</p>
			</div>
		</div>
		
		
		<div id="popupAltSentence" style="display: none" aria-hidden="true">別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。</div>
	<div id="cmnSiteCatalyst" aria-hidden="true"><div id="cmnSiteCatalystParamArea"><script type="text/javascript">
				// ▼SiteCatalyst
				// SiteCatalyst code version: H.2.
				// Copyright 1997-2005 Omniture, Inc. More info available at http://www.omniture.com
				SiteCatalystReportSuites   = "INT";
				SiteCatalystCharSet        = "UTF-8";
				SiteCatalystChannel        = "BE_AWARD_J";
				SiteCatalystPageName       = "INT_BE_AWARD_J_A04\u7279\u5178\u4E88\u7D04_P01\u65C5\u5BA2\u60C5\u5831\u5165\u529B\uFF08\u5FC5\u9808\u60C5\u5831\uFF09";
				SiteCatalystERR            = "";
				SiteCatalystEventNum       = "";
				SiteCatalystPurchase       = "FALSE";
				SiteCatalystProductPrice   = "";
				SiteCatalystCurrencyCode   = "";
				SiteCatalystSeatAvailabilityINT   = "";
				SiteCatalystDepartDate     = "";
				SiteCatalystDepart         = "";
				SiteCatalystArrive         = "";
				SiteCatalystFlightNo       = "";
				SiteCatalystClass          = "";
				SiteCatalystFare           = "";
				SiteCatalystPurchaseID     = "";
				
				SiteCatalystLocater        = "";
				SiteCatalystPayment        = "";
				SiteCatalystIncome         = "";
				
				SiteCatalystPax            = "";
				SiteCatalystManipulateDay  = "";
				SiteCatalystCompartmentClass   = "";
				SiteCatalystOptionInfo     = "";
				SiteCatalystOutboundUpsellPrice   = "";
				SiteCatalystInboundUpsellPrice    = "";
				
				SiteCatalystSearchInput    = "";
				SiteCatalystSegment        = "";
				SiteCatalystOfficeCode     = "";
				SiteCatalystPassengerCount = "";
				SiteCatalystPassenger      = "";
				SiteCatalystAncillaryService = "";
				// End SiteCatalyst code version: H.2.
				// ▲SiteCatalyst
			</script></div><script type="text/javascript"><!-- if(navigator.appVersion.indexOf('MSIE')>=0)document.write(unescape('%3C')+'\!-'+'-') // --></script>
		<!--/DO NOT REMOVE/-->
		

		
		<!-- Google Tag Manager -->
		<script type="text/javascript" charset="Shift_JIS" src="https://www.ana.co.jp/common/js/GTMINTBE.js"></script>
		<!-- End Google Tag Manager -->
		<!-- Yahoo Tag Manager -->
		<script type="text/javascript" charset="Shift_JIS" src="https://www.ana.co.jp/common/js/YTMINTBE.js"></script>
		<!-- End Yahoo Tag Manager -->
		

	</div>

	

	<div class="mboxDefault"></div><script type="text/javascript">mboxCreate("ASW_common_A04_P01");</script>
			<script type="text/javascript" src="https://www.ana.co.jp/common/js/tealium/tealium.js"></script>
			<script type="text/javascript" src="//cdn.evgnet.com/beacon/allnipponairways/nonamcprd/scripts/evergage.min.js"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22c/international_asw/javax.faces.resource/jsf.js.xhtml?ln=javax.faces"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/script/jquery.mousewheel.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/script/jquery.jscrollpane.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/script/jquery-ui-1.10.4.custom.min.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/script/jquery.ui.touch-punch.min.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/script/asw-common.js?beb6a50"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/script/asw-common-pc.js?fd88d7d"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/script/asw-format.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/script/mask.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/script/loading.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/script/asw-loadingwindow-pc.js?9e233c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/script/asw-loadingwindow.js?51db600"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/script/dialog-pc.js?25d36a4"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/script/asw-dialog-sequencer.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/script/asw-event.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/script/asw-date-select.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/script/asw-calendar-pc.js?9b86b8a"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/script/asw-calendar.js?db624c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/script/heightLine.js?0e7a1ce"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/script/asw-summaryarea.js?b21c6d7"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/script/asw-fingerprints.js?99001fc"></script><script type="text/javascript">
			Asw.init("https://aswbe-i.ana.co.jp/rei22c/international_asw", "ja");
			Asw.getCalendarHolidayList = function() {return [20250721,20250811,20250915,20250923,20251013,20251103,20251123,20251124,20260101,20260112,20260211,20260223,20260320,20260429,20260503,20260504,20260505,20260506,20260720,20260811,20260921,20260922,20260923,20261012,20261103,20261123,20270101,20270111,20270211,20270223,20270321,20270322,20270429,20270503,20270504,20270505,20270719,20270811,20270920,20270923,20271011,20271103,20271123,20280101,20280110,20280211,20280223,20280320,20280429,20280503,20280504,20280505,20280717,20280811,20280918,20280922,20281009,20281103,20281123,20290101,20290108,20290211,20290212,20290223,20290320,20290429,20290430,20290503,20290504,20290505,20290716,20290811,20290917,20290923,20290924,20291008,20291103,20291123];}
			Asw.ClientInfo.deviceType = "PC";
			Asw.ClientInfo.mobileDeviceType = "UNKNOWN";
			Asw.ClientInfo.osVersion = "";
			Asw.Format.init("https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/{1}/{2}/{3}?{0}");
			Asw.overwriteTargetAttributeValue("false");
		</script><script type="text/javascript">
		
		var Asw = Asw || {};
		Asw.ContactInput = Asw.ContactInput || {};
		Asw.ContactInput.Const = {
			/** data-属性名「data-type」 @constant @type {String} @default 'data-type' */
			DATA_ATTRIBUTE_NAME_TYPE: 'data-type',
			/** data-属性名「data-sms-possible」 @constant @type {String} @default 'data-sms-possible' */
			DATA_ATTRIBUTE_NAME_SMS_POSSIBLE: 'data-sms-possible',
			/** data-属性名「data-country-code」 @constant @type {String} @default 'data-country-code' */
			DATA_ATTRIBUTE_NAME_COUNTRY_CODE: 'data-country-code',
			/** data-type属性値「sms」 @constant @type {String} @default 'sms' */
			DATA_TYPE_VALUE_SMS: 'sms'
		};

		
		Asw.ContactInput.countryInfoMap = {"IS":{"internationalCallingCode":"+354","smsPossible":false},"IE":{"internationalCallingCode":"+353","smsPossible":true},"AZ":{"internationalCallingCode":"+994","smsPossible":true},"AF":{"internationalCallingCode":"+93","smsPossible":true},"US":{"internationalCallingCode":"+1","smsPossible":true},"AE":{"internationalCallingCode":"+971","smsPossible":true},"DZ":{"internationalCallingCode":"+213","smsPossible":true},"AR":{"internationalCallingCode":"+54","smsPossible":true},"AW":{"internationalCallingCode":"+297","smsPossible":true},"AL":{"internationalCallingCode":"+355","smsPossible":true},"AM":{"internationalCallingCode":"+374","smsPossible":true},"AO":{"internationalCallingCode":"+244","smsPossible":false},"AG":{"internationalCallingCode":"+1","smsPossible":true},"AD":{"internationalCallingCode":"+376","smsPossible":true},"YE":{"internationalCallingCode":"+967","smsPossible":true},"GB":{"internationalCallingCode":"+44","smsPossible":true},"IL":{"internationalCallingCode":"+972","smsPossible":true},"IT":{"internationalCallingCode":"+39","smsPossible":true},"IQ":{"internationalCallingCode":"+964","smsPossible":true},"IR":{"internationalCallingCode":"+98","smsPossible":true},"IN":{"internationalCallingCode":"+91","smsPossible":true},"ID":{"internationalCallingCode":"+62","smsPossible":true},"UG":{"internationalCallingCode":"+256","smsPossible":true},"UA":{"internationalCallingCode":"+380","smsPossible":true},"UZ":{"internationalCallingCode":"+998","smsPossible":true},"UY":{"internationalCallingCode":"+598","smsPossible":true},"EC":{"internationalCallingCode":"+593","smsPossible":true},"EG":{"internationalCallingCode":"+20","smsPossible":true},"EE":{"internationalCallingCode":"+372","smsPossible":true},"SZ":{"internationalCallingCode":"+268","smsPossible":false},"ET":{"internationalCallingCode":"+251","smsPossible":false},"ER":{"internationalCallingCode":"+291","smsPossible":true},"SV":{"internationalCallingCode":"+503","smsPossible":true},"AU":{"internationalCallingCode":"+61","smsPossible":true},"AT":{"internationalCallingCode":"+43","smsPossible":true},"OM":{"internationalCallingCode":"+968","smsPossible":true},"NL":{"internationalCallingCode":"+31","smsPossible":true},"GH":{"internationalCallingCode":"+233","smsPossible":true},"CV":{"internationalCallingCode":"+238","smsPossible":false},"GY":{"internationalCallingCode":"+592","smsPossible":true},"KZ":{"internationalCallingCode":"+7","smsPossible":true},"QA":{"internationalCallingCode":"+974","smsPossible":true},"CA":{"internationalCallingCode":"+1","smsPossible":true},"GA":{"internationalCallingCode":"+241","smsPossible":true},"CM":{"internationalCallingCode":"+237","smsPossible":true},"KR":{"internationalCallingCode":"+82","smsPossible":true},"KH":{"internationalCallingCode":"+855","smsPossible":true},"GM":{"internationalCallingCode":"+220","smsPossible":true},"KP":{"internationalCallingCode":"+850","smsPossible":false},"MK":{"internationalCallingCode":"+389","smsPossible":false},"GN":{"internationalCallingCode":"+224","smsPossible":true},"GW":{"internationalCallingCode":"+245","smsPossible":true},"CY":{"internationalCallingCode":"+357","smsPossible":true},"CU":{"internationalCallingCode":"+53","smsPossible":true},"GR":{"internationalCallingCode":"+30","smsPossible":true},"KI":{"internationalCallingCode":"+686","smsPossible":false},"KG":{"internationalCallingCode":"+996","smsPossible":true},"GT":{"internationalCallingCode":"+502","smsPossible":true},"GU":{"internationalCallingCode":"+1","smsPossible":true},"KW":{"internationalCallingCode":"+965","smsPossible":true},"KY":{"internationalCallingCode":"+1","smsPossible":true},"GD":{"internationalCallingCode":"+1","smsPossible":true},"HR":{"internationalCallingCode":"+385","smsPossible":true},"KE":{"internationalCallingCode":"+254","smsPossible":true},"CI":{"internationalCallingCode":"+225","smsPossible":true},"CR":{"internationalCallingCode":"+506","smsPossible":true},"SA":{"internationalCallingCode":"+966","smsPossible":true},"KM":{"internationalCallingCode":"+269","smsPossible":true},"CO":{"internationalCallingCode":"+57","smsPossible":true},"CG":{"internationalCallingCode":"+242","smsPossible":false},"CD":{"internationalCallingCode":"+243","smsPossible":true},"ZM":{"internationalCallingCode":"+260","smsPossible":true},"SM":{"internationalCallingCode":"+378","smsPossible":false},"SL":{"internationalCallingCode":"+232","smsPossible":true},"DJ":{"internationalCallingCode":"+253","smsPossible":false},"JM":{"internationalCallingCode":"+1","smsPossible":true},"CH":{"internationalCallingCode":"+41","smsPossible":true},"GE":{"internationalCallingCode":"+995","smsPossible":true},"SE":{"internationalCallingCode":"+46","smsPossible":true},"SY":{"internationalCallingCode":"+963","smsPossible":true},"ES":{"internationalCallingCode":"+34","smsPossible":true},"SG":{"internationalCallingCode":"+65","smsPossible":true},"LK":{"internationalCallingCode":"+94","smsPossible":true},"SK":{"internationalCallingCode":"+421","smsPossible":true},"SI":{"internationalCallingCode":"+386","smsPossible":true},"ZW":{"internationalCallingCode":"+263","smsPossible":true},"SD":{"internationalCallingCode":"+249","smsPossible":true},"KN":{"internationalCallingCode":"+1","smsPossible":true},"SR":{"internationalCallingCode":"+597","smsPossible":true},"LC":{"internationalCallingCode":"+1","smsPossible":true},"SC":{"internationalCallingCode":"+248","smsPossible":false},"SN":{"internationalCallingCode":"+221","smsPossible":true},"SB":{"internationalCallingCode":"+677","smsPossible":false},"RS":{"internationalCallingCode":"+381","smsPossible":true},"TH":{"internationalCallingCode":"+66","smsPossible":true},"TW":{"internationalCallingCode":"+886","smsPossible":true},"TJ":{"internationalCallingCode":"+992","smsPossible":true},"TZ":{"internationalCallingCode":"+255","smsPossible":true},"CZ":{"internationalCallingCode":"+420","smsPossible":true},"TD":{"internationalCallingCode":"+235","smsPossible":true},"CF":{"internationalCallingCode":"+236","smsPossible":true},"TN":{"internationalCallingCode":"+216","smsPossible":true},"CL":{"internationalCallingCode":"+56","smsPossible":true},"TV":{"internationalCallingCode":"+688","smsPossible":false},"DK":{"internationalCallingCode":"+45","smsPossible":true},"DE":{"internationalCallingCode":"+49","smsPossible":true},"TG":{"internationalCallingCode":"+228","smsPossible":true},"DO":{"internationalCallingCode":"+1","smsPossible":true},"DM":{"internationalCallingCode":"+1","smsPossible":false},"TT":{"internationalCallingCode":"+1","smsPossible":true},"TM":{"internationalCallingCode":"+993","smsPossible":false},"TR":{"internationalCallingCode":"+90","smsPossible":true},"TO":{"internationalCallingCode":"+676","smsPossible":false},"NG":{"internationalCallingCode":"+234","smsPossible":true},"NR":{"internationalCallingCode":"+674","smsPossible":false},"NA":{"internationalCallingCode":"+264","smsPossible":false},"NI":{"internationalCallingCode":"+505","smsPossible":true},"BH":{"internationalCallingCode":"+973","smsPossible":false},"NE":{"internationalCallingCode":"+227","smsPossible":true},"PK":{"internationalCallingCode":"+92","smsPossible":true},"JP":{"internationalCallingCode":"+81","smsPossible":true},"BS":{"internationalCallingCode":"+1","smsPossible":true},"NZ":{"internationalCallingCode":"+64","smsPossible":true},"BM":{"internationalCallingCode":"+1","smsPossible":true},"NP":{"internationalCallingCode":"+977","smsPossible":true},"BB":{"internationalCallingCode":"+246","smsPossible":true},"NO":{"internationalCallingCode":"+47","smsPossible":true},"HT":{"internationalCallingCode":"+509","smsPossible":true},"BD":{"internationalCallingCode":"+880","smsPossible":true},"VA":{"internationalCallingCode":"+39","smsPossible":false},"PA":{"internationalCallingCode":"+507","smsPossible":true},"VU":{"internationalCallingCode":"+678","smsPossible":false},"PG":{"internationalCallingCode":"+675","smsPossible":true},"PW":{"internationalCallingCode":"+680","smsPossible":true},"PY":{"internationalCallingCode":"+595","smsPossible":true},"PS":{"internationalCallingCode":"+970","smsPossible":true},"BR":{"internationalCallingCode":"+55","smsPossible":true},"HU":{"internationalCallingCode":"+36","smsPossible":true},"TL":{"internationalCallingCode":"+670","smsPossible":true},"BG":{"internationalCallingCode":"+359","smsPossible":true},"BF":{"internationalCallingCode":"+226","smsPossible":true},"BN":{"internationalCallingCode":"+673","smsPossible":true},"FJ":{"internationalCallingCode":"+679","smsPossible":true},"PH":{"internationalCallingCode":"+63","smsPossible":true},"FI":{"internationalCallingCode":"+358","smsPossible":true},"BT":{"internationalCallingCode":"+975","smsPossible":true},"BY":{"internationalCallingCode":"+375","smsPossible":true},"BZ":{"internationalCallingCode":"+501","smsPossible":false},"PR":{"internationalCallingCode":"+1","smsPossible":true},"BE":{"internationalCallingCode":"+32","smsPossible":true},"BA":{"internationalCallingCode":"+387","smsPossible":true},"FR":{"internationalCallingCode":"+33","smsPossible":true},"BO":{"internationalCallingCode":"+591","smsPossible":false},"PF":{"internationalCallingCode":"+689","smsPossible":true},"BI":{"internationalCallingCode":"+257","smsPossible":true},"VN":{"internationalCallingCode":"+84","smsPossible":true},"BJ":{"internationalCallingCode":"+229","smsPossible":true},"VE":{"internationalCallingCode":"+58","smsPossible":true},"PE":{"internationalCallingCode":"+51","smsPossible":true},"PL":{"internationalCallingCode":"+48","smsPossible":true},"BW":{"internationalCallingCode":"+267","smsPossible":true},"PT":{"internationalCallingCode":"+351","smsPossible":false},"HK":{"internationalCallingCode":"+852","smsPossible":true},"HN":{"internationalCallingCode":"+504","smsPossible":false},"MH":{"internationalCallingCode":"+692","smsPossible":true},"MO":{"internationalCallingCode":"+853","smsPossible":true},"MG":{"internationalCallingCode":"+261","smsPossible":true},"MW":{"internationalCallingCode":"+265","smsPossible":true},"ML":{"internationalCallingCode":"+223","smsPossible":true},"MT":{"internationalCallingCode":"+356","smsPossible":true},"MY":{"internationalCallingCode":"+60","smsPossible":true},"FM":{"internationalCallingCode":"+691","smsPossible":false},"MM":{"internationalCallingCode":"+95","smsPossible":true},"MX":{"internationalCallingCode":"+52","smsPossible":true},"MU":{"internationalCallingCode":"+230","smsPossible":true},"MR":{"internationalCallingCode":"+222","smsPossible":true},"MZ":{"internationalCallingCode":"+258","smsPossible":true},"MC":{"internationalCallingCode":"+377","smsPossible":true},"MV":{"internationalCallingCode":"+960","smsPossible":true},"MD":{"internationalCallingCode":"+373","smsPossible":false},"RO":{"internationalCallingCode":"+40","smsPossible":true},"MA":{"internationalCallingCode":"+212","smsPossible":true},"MN":{"internationalCallingCode":"+976","smsPossible":true},"ME":{"internationalCallingCode":"+382","smsPossible":true},"JO":{"internationalCallingCode":"+962","smsPossible":true},"LA":{"internationalCallingCode":"+856","smsPossible":true},"RU":{"internationalCallingCode":"+7","smsPossible":true},"LV":{"internationalCallingCode":"+371","smsPossible":true},"LT":{"internationalCallingCode":"+370","smsPossible":true},"LY":{"internationalCallingCode":"+218","smsPossible":true},"GQ":{"internationalCallingCode":"+240","smsPossible":false},"LI":{"internationalCallingCode":"+423","smsPossible":false},"LR":{"internationalCallingCode":"+231","smsPossible":true},"CN":{"internationalCallingCode":"+86","smsPossible":true},"LU":{"internationalCallingCode":"+352","smsPossible":true},"ZA":{"internationalCallingCode":"+27","smsPossible":true},"RW":{"internationalCallingCode":"+250","smsPossible":true},"LS":{"internationalCallingCode":"+266","smsPossible":false},"LB":{"internationalCallingCode":"+961","smsPossible":true}};

		$(function () {
			contactInfoInit();
		});
		
		function contactInfoInit() {
			
			
			Asw.get('representativeTelType').find('option[value="M1"]').attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_TYPE,
					Asw.ContactInput.Const.DATA_TYPE_VALUE_SMS);
			Asw.get('representativeTelCountry').children('option').each(function(i,option){
				var countryInfo = Asw.ContactInput.countryInfoMap[option.value];
				if (countryInfo === void 0) {
					
					return true;
				}
				$(option).attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_SMS_POSSIBLE, countryInfo.smsPossible);
				$(option).attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_COUNTRY_CODE, countryInfo.internationalCallingCode);
			});
			
			var smsCountrySelectSelector = 'select.js-countrySelect:not(#representativeTelCountry)'
			$(smsCountrySelectSelector).eq(0).children('option').each(function(i,option){
				var countryInfo = Asw.ContactInput.countryInfoMap[option.value];
				if (countryInfo === void 0) {
					
					return true;
				}
				$(smsCountrySelectSelector + ' option:nth-child(' + (i+1) + ')').attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_COUNTRY_CODE,
						countryInfo.internationalCallingCode);
			});
			
			var emergencyCountrySelectSelector = 'select.passengerEmergencyCountry.js-countrySelect:not(#representativeTelCountry)'
			$(emergencyCountrySelectSelector).eq(0).children('option').each(function(i,option){
				var countryInfo = Asw.ContactInput.countryInfoMap[option.value];
				if (countryInfo === void 0) {
					
					return true;
				}
				$(emergencyCountrySelectSelector + ' option:nth-child(' + (i+1) + ')').attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_COUNTRY_CODE,
						countryInfo.internationalCallingCode);
			});
		}
	</script><script type="text/javascript">
		var isInit = true;
		function changeTelType(_this, telNoInputId) {
			var $telNoInput = Asw.get(telNoInputId);
			var $telNoKind = Asw.get("representativeTelType");
			var telNo = Asw.getValueComparedToPlaceholder(telNoInputId);
			var telNoCountry = Asw.get("representativeTelCountry").val();

			if(true && !isInit) {
				if(telInputCheck(telNo, telNoCountry)) {
					if (_this.value == "M1") {
						telNo = exceptDomesticPrefix("09042504176", telNoCountry);
					} else if (_this.value == "B1") {
						telNo = exceptDomesticPrefix("03-5405-2172", telNoCountry);
					} else {
						telNo = exceptDomesticPrefix("090-4250-4176", telNoCountry);
					}
					$telNoInput.val(telNo);
				}
			}
			isInit = false;
		}
		
		function telInputCheck(telNo, telNoCountry) {
	        checkResult = false;
	        
	        var amcHndyTelNo = exceptDomesticPrefix("09042504176", telNoCountry);
	        var amcCmpTelNo = exceptDomesticPrefix("03-5405-2172", telNoCountry);
	        var amcTelNo = exceptDomesticPrefix("090-4250-4176", telNoCountry);
	
	        if(telNo == "" || telNo == amcHndyTelNo || telNo == amcCmpTelNo || telNo == amcTelNo) {
	        	checkResult = true;
	        }
	        return checkResult;
    	}
    	
    	function exceptDomesticPrefix(telNo, telNoCountry) {
    		if(telNoCountry == 'JP') {
    			if(telNo.substr(0, 1) === '0') {
    				return telNo.slice(1);
    			}
    		}
    		return telNo;
    	}
	</script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/script/asw-customerinfomationform.js?6e91953"></script><script type="text/javascript">
		$(document).ready(function(){
				Asw.enableTextWhenCheckBox('.cascadedSwitch');
				Asw.notPasteInput('.jsNotPasteInput');
			$(function(){
				Asw.alignHeightToParentTdElement( '.passengerInfoInputArea' );
				
					Asw.alignHeightToParentTdElement( '.passengerInfoInputText' );
				
				
				$('select.js-countrySelect').trigger('change');
			});
		});
	</script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/script/asw-session-keeper.js?6ec0730"></script><script type="text/javascript">
			$(document).ready(function() {
				Asw.SessionKeeper.initAndStart("sessionKeeperContainer", "520", "570", "PC");
			});
			</script><script type="text/javascript">
		Asw.addAjaxCallback(function(data) {
			// アコーディオンのメソッドと設定 
			if($('.jsAccordionSwitch .jsTriggerSwitch').length > 0){
				var events = $._data($('.jsAccordionSwitch .jsTriggerSwitch').get(0), "events");
				if(events === undefined || events.click === undefined) {
					Asw.AccordionInfo('.jsAccordionSwitch', {
						'triggerSwitch': '.jsAccordionSwitch .jsTriggerSwitch',
						'listClass': '.jsAccordionSwitchList',
						'visibilityHidden': 'toggleContents'
					});
				}
			}
		});
	</script><script type="text/javascript">
	Asw.Price = Asw.Price || {};
	// 整数の区切り文字
	Asw.Price.separateString = ',';
	
	// 整数の区切り桁数
	Asw.Price.separateDigitNumber = 3;
	
	// 小数点
	Asw.Price.decimalPointString = '.';
	
	// 小数点以降の有効桁数
	Asw.Price.significantDigit = 0;
	
	// 表示金額の単位
	Asw.Price.currencyUnit = '円';
	</script><script type="text/javascript">
			$(document).ready(function() {
				$('#preLoadingArea').css('visibility', 'hidden');
			});
		</script><noscript><img src="https://aswbe-i.ana.co.jp/akam/13/pixel_79e1ce58?a=dD1iZjk4NTE5NTM3MmQyZmRiMjAxNjYyYmNkMmJhN2U3NTg1NDAxNmQyJmpzPW9mZg==" style="visibility: hidden; position: absolute; left: -999px; top: -999px;" /></noscript><script type="text/javascript"  src="/Q1XS4o/Cn_ls/KfDO7/xA/t9OfhmEwSiGQaG/NG4dCQE/bw/QJBChQIQEB"></script></body>
</html>