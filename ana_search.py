from lxml import etree
from loguru import logger
# import requests # 使用 curl_cffi 替换 requests
from curl_cffi.requests import Session, exceptions
import os
import time
from urllib.parse import urljoin, urlparse
import random
import re
import json
from datetime import datetime
import sys
from bs4 import BeautifulSoup
from requests.exceptions import ProxyError, SSLError

# --- 配置开关 ---
# 设置为 True 以使用代理，设置为 False 则不使用代理直接连接。
USE_PROXY = False   


class StringUtil:
    @classmethod
    def extract_between(cls, text, start, end):
        """
        从文本中提取开始和结束标识符之间的内容。

        :param text: 要从中提取内容的原始文本。
        :param start: 开始标识符。
        :param end: 结束标识符。
        :return: 开始和结束标识符之间的内容。
        """
        start_index = text.find(start)
        if start_index == -1:
            return None  # 如果未找到开始标识符，返回 None

        start_index += len(start)  # 移动到开始标识符之后
        end_index = text.find(end, start_index)
        if end_index == -1:
            return None  # 如果未找到结束标识符，返回 None

        return text[start_index:end_index]

    @staticmethod
    def get_string_between(text, start, end):
        start_index = text.find(start) + len(start)
        end_index = text.find(end, start_index)
        return text[start_index:end_index]


AIRPORT_CODES = {
    "TYO": "東京(全て)",
    "HND": "東京(羽田)",
    "NRT": "東京(成田)",
    "JFK": "ニューヨーク(JFK)",
    "LAX": "ロサンゼルス(LAX)",
    "SFO": "サンフランシスコ",
    "ORD": "シカゴ(ORD)",
    "IAH": "ヒューストン",
    "IAD": "ワシントンD.C.(IAD)",
    "SEA": "シアトル",
    "YVR": "バンクーバー",
    "HNL": "ホノルル(オアフ島)",
    "SHA": "上海(全て)",
    "PEK": "北京(首都)",
    "CAN": "広州",
    "HKG": "香港",
    "TAO": "青島"
}


def format_japanese_date(date_str_yyyymmdd):
    """
    将 'YYYYMMDD' 格式的日期字符串转换为 'YYYY年MM月DD日(曜)' 的日文格式。
    """
    try:
        dt_obj = datetime.strptime(date_str_yyyymmdd, '%Y%m%d')
        year = dt_obj.year
        month = dt_obj.month
        day = dt_obj.day
        # 星期映射: 0=月, 1=火, 2=水, 3=木, 4=金, 5=土, 6=日
        weekday_map = ['月', '火', '水', '木', '金', '土', '日']
        weekday_char = weekday_map[dt_obj.weekday()]
        return f"{year}年{month:02d}月{day:02d}日({weekday_char})"
    except ValueError:
        logger.error(f"日期格式不正确: {date_str_yyyymmdd}. 应为 YYYYMMDD。")
        return ""


def get_random_proxy():
    """从proxies.json中读取并随机选择一个代理，并将其格式化为requests库可用的格式。"""
    try:
        with open('proxies.json', 'r', encoding='utf-8') as f:
            proxies_list = json.load(f)

        if not proxies_list:
            logger.error("代理文件 'proxies.json' 为空或格式错误。")
            return None

        random_proxy_str = random.choice(proxies_list)

        # 解析代理字符串 host:port:user:pass
        parts = random_proxy_str.split(':')
        if len(parts) != 4:
            logger.error(f"代理字符串格式不正确: {random_proxy_str}")
            return None

        host, port, user, password = parts

        proxy_url = f"http://{user}:{password}@{host}:{port}"

        return {
            "http": proxy_url,
            "https": proxy_url,
        }
    except FileNotFoundError:
        logger.error("代理文件 'proxies.json' 未找到。")
        return None
    except json.JSONDecodeError:
        logger.error("解析 'proxies.json' 失败，请检查文件是否为有效的JSON格式。")
        return None
    except Exception as e:
        logger.error(f"处理代理文件时发生错误: {e}")
        return None


# 使用 curl_cffi 的 Session 并模拟 Chrome 120 浏览器
# 修复：增加超时时间以解决会话延长超时问题
session = Session(
    impersonate="chrome120",
    timeout=120  # 增加超时时间到120秒
)


def save_html_for_debugging(directory, filename_prefix, response_obj):
    """将HTML响应保存为文件，以便进行快速的本地查看和调试。"""
    if not os.path.exists(directory):
        os.makedirs(directory)
    
    timestamp = time.strftime("%Y%m%d-%H%M%S")
    filepath = os.path.join(directory, f'{filename_prefix}_{timestamp}.html')
    
    # 注入的代码会阻止浏览器加载外部资源，从而加快本地文件的渲染速度。
    enhancements = b'''<!-- Injected by script for fast local debugging -->
<base href="about:blank">
<style>img, svg, video, link[rel="stylesheet"], script { display: none !important; }</style>
'''
    
    with open(filepath, 'wb') as f:
        f.write(enhancements)
        f.write(response_obj.content)
        
    return filepath


def save_response_to_file(response, log_dir, prefix):
    """将requests的响应内容保存到HTML文件，并注入代码以加速本地渲染。"""
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    timestamp = time.strftime("%Y%m%d-%H%M%S")
    filepath = os.path.join(log_dir, f'{prefix}_{timestamp}.html')

    # 注入代码以阻止外部资源加载，<base>标签是关键
    injection_code = """
    <base href="about:blank">
    <style>
        /* Hide elements that might try to load external resources, as a fallback. */
        img, svg, video, iframe, link[rel="stylesheet"], script { display: none !important; }
    </style>
"""
    try:
        content = response.content.decode('utf-8')
    except (UnicodeDecodeError, AttributeError):
        content = response.text

    # 使用正则表达式以不区分大小写的方式查找<head>标签并注入代码
    head_tag_pattern = re.compile(r'<head.*?>', re.IGNORECASE | re.DOTALL)
    match = head_tag_pattern.search(content)

    if match:
        # 在<head>标签后注入代码
        head_tag = match.group(0)
        content = content.replace(head_tag, head_tag + injection_code, 1)
    else:
        # 如果没有<head>标签，则手动创建一个
        content = f'<html><head>{injection_code}</head><body>{content}</body></html>'

    with open(filepath, 'w', encoding='utf-8') as f:
        f.write(content)
        
    return filepath


def akmcookies_get(proxy_dict):
    """获取akm cookie，必须使用指定的代理。"""
    import requests

    if not proxy_dict or 'http' not in proxy_dict:
        logger.error("akmcookies_get调用时未提供有效代理。")
        return {}

    try:
        # 服务需要特定格式的代理字符串: host:port:user:pass
        parsed = urlparse(proxy_dict['http'])
        if not all([parsed.hostname, parsed.port, parsed.username, parsed.password]):
                raise ValueError("代理字典格式不完整或缺少认证信息。")
        proxy_str = f"{parsed.hostname}:{parsed.port}:{parsed.username}:{parsed.password}"
    except (KeyError, AttributeError, ValueError) as e:
        logger.error(f"无法从字典解析代理: {e}。")
        return {}

    data = {
        "url": "https://www.ana.co.jp/",
        "proxy": proxy_str,
        "type": "F134"
    }
    try:
        response = requests.post(url="http://**********:1235/getabck", json=data, timeout=30)
        response.raise_for_status()
        cookie_str = response.text
        # 增加 .strip() 来处理可能的空白字符，使解析更健壮
        cookie_dict = dict(item.strip().split("=", 1) for item in cookie_str.split(";") if "=" in item)
        return cookie_dict
    except requests.RequestException as e:
        logger.error(f"获取akm cookie时发生网络错误: {e}")
        return {}


def keep_session_alive(initial_response):
    """
    保持会话活跃，通过刷新页面来防止会话过期
    每8分钟刷新一次，每次延长10分钟，最多延长15次（总共125分钟）
    如果连续3次刷新失败，则重新开始占位流程
    """
    logger.success("★★★★★ 占位成功，进入会话保持模式。将通过页面刷新维持会话15次。 ★★★★★")

    current_page_url = initial_response.url
    extension_count = 0
    max_extensions = 15  # 15次 × 8分钟 = 120分钟（接近125分钟）
    consecutive_failures = 0
    max_consecutive_failures = 3

    while extension_count < max_extensions:
        try:
            extension_count += 1
            logger.info(f"--- [会话维持 {extension_count}/{max_extensions}] ---")

            # 等待30分钟后刷新页面（测试用）
            logger.info("开始等待 30分钟，然后刷新页面维持会话...")
            wait_time = 30 * 60  # 30分钟

            # 显示倒计时
            for remaining in range(wait_time, 0, -10):
                minutes = remaining // 60
                seconds = remaining % 60
                print(f"\r下次刷新倒计时: {minutes:02d}:{seconds:02d}  ", end="", flush=True)
                time.sleep(10)

            print("\r倒计时结束，正在刷新页面维持会话...      ")

            # 刷新页面来维持会话（带重试机制）
            logger.info("正在刷新页面以维持会话...")

            success = refresh_page_to_extend_session(current_page_url)

            if success:
                logger.success(f"--- [会话维持 {extension_count}/{max_extensions}] 成功刷新页面，会话延长10分钟！ ---")
                consecutive_failures = 0  # 重置连续失败计数
            else:
                consecutive_failures += 1
                logger.warning(f"--- [会话维持 {extension_count}/{max_extensions}] 页面刷新失败，连续失败次数: {consecutive_failures}/{max_consecutive_failures} ---")

                # 如果连续失败次数达到上限，提前结束会话保持
                if consecutive_failures >= max_consecutive_failures:
                    logger.error(f"连续{max_consecutive_failures}次刷新失败，可能会话已失效，提前结束会话保持...")
                    break

        except Exception as e:
            logger.error(f"会话维持过程中发生错误: {e}")
            consecutive_failures += 1
            if consecutive_failures >= max_consecutive_failures:
                logger.error(f"连续{max_consecutive_failures}次异常，提前结束会话保持...")
                break

    logger.info("会话保持结束，准备重新开始占位循环...")
    return True


def refresh_page_to_extend_session(page_url):
    """
    通过刷新页面来延长会话
    模拟真实浏览器的F5刷新行为
    """
    try:
        logger.info(f"刷新页面: {page_url}")

        # 模拟真实浏览器刷新的请求头
        headers = {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Cache-Control': 'max-age=0',  # 强制刷新，不使用缓存
            'Connection': 'keep-alive',
            'Host': 'aswbe-i.ana.co.jp',
            'Referer': page_url,  # 设置Referer为当前页面
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-User': '?1',  # 表示用户发起的请求
            'Upgrade-Insecure-Requests': '1',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"'
        }

        # 使用较短的超时时间，避免长时间等待
        response = session.get(page_url, headers=headers, timeout=15)

        if response.status_code == 200:
            logger.info("页面刷新成功，会话已延长")

            # 检查响应内容是否包含预期的页面元素
            if "password_input" in response.text or "パスワード" in response.text:
                logger.info("确认页面内容正常，会话延长成功")
                return True
            else:
                logger.warning("页面刷新成功但内容异常，可能会话已失效")
                return False
        else:
            logger.warning(f"页面刷新返回状态码: {response.status_code}")
            return False

    except Exception as e:
        logger.error(f"页面刷新失败: {e}")
        return False




def login():
    ua = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"
    headers = {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
        'Cache-Control': 'max-age=0',
        'Connection': 'keep-alive',
        'Content-Type': 'application/x-www-form-urlencoded',
        'Origin': 'https://cam.ana.co.jp',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'same-origin',
        'Sec-Fetch-User': '?1',
        'Upgrade-Insecure-Requests': '1',
        'User-Agent': ua,
        'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
    }
    params = {
        'rand': '20230317010551',
        'CONNECTION_KIND': 'JPN',
        'LANG': 'ja',
    }
    data = 'LOGIN=on&KINOU=MailInput&cusnum=2910298161&logpass=2114oreo&login=%A5%ED%A5%B0%A5%A4%A5%F3+%28%B2%F1%B0%F7%C0%EC%CD%D1%B5%A1%C7%BD%29'
    # 登录请求
    login_response = session.post(url='https://cam.ana.co.jp/psz/tokutencal/form.jsp', headers=headers, data=data,
                                  params=params)
    logger.success("登录成功！！！！")
    # 跳转首页
    headers = {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
        'Connection': 'keep-alive',
        'If-Modified-Since': 'Wed, 04 Jun 2025 09:48:04 GMT',
        'If-None-Match': '"b272b-636bbe4b97be6-gzip"',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1',
        'Upgrade-Insecure-Requests': '1',
        'User-Agent': ua,
        'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
    }
    home_page = session.get('https://www.ana.co.jp/ja/jp/', headers=headers)
    logger.success("跳转首页成功！！！！")
    # 跳转查询页面
    headers = {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
        'Connection': 'keep-alive',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1',
        'Upgrade-Insecure-Requests': '1',
        'User-Agent': ua,
        'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
    }
    award_search_roundtrip_input_response = session.get(
        'https://aswbe-i.ana.co.jp/international_asw/pages/award/search/roundtrip/award_search_roundtrip_input.xhtml?rand=20230330183820&CONNECTION_KIND=JPN&LANG=ja',
        headers=headers,
    )
    view_state = ''.join(etree.HTML(
        award_search_roundtrip_input_response.text.replace('<?xml version="1.0" encoding="UTF-8" ?>',
                                                           '').strip()).xpath(
        '//input[@id="j_id1:javax.faces.ViewState:4"]/@value'))
    url = ''.join(etree.HTML(
        award_search_roundtrip_input_response.text.replace('<?xml version="1.0" encoding="UTF-8" ?>',
                                                           '').strip()).xpath('//form[@id="conditionInput"]/@action'))

    logger.success("跳转查询页面成功！！！！")

    headers = {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
        'Cache-Control': 'max-age=0',
        'Connection': 'keep-alive',
        'Content-Type': 'application/x-www-form-urlencoded',
        'Origin': 'https://aswbe-i.ana.co.jp',
        'Referer': award_search_roundtrip_input_response.url,
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'same-origin',
        'Sec-Fetch-User': '?1',
        'Upgrade-Insecure-Requests': '1',
        'User-Agent': ua,
        'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
    }
    data = {
        'conditionInput': 'conditionInput',
        'conditionInput_operationTicket': '',
        'conditionInput_cmnPageTicket': '',
        'hiddenSearchMode': 'ROUND_TRIP',
        'itineraryButtonCheck': 'roundTrip',
        'hiddenAction': 'AwardRoundTripSearchInputAction',
        'roundTripOpenJawSelected': '0',
        'hiddenRoundtripOpenJawSelected': '0',
        'departureAirportCode:field': '',
        'departureAirportCode:field_pctext': '',
        'arrivalAirportCode:field': '',
        'arrivalAirportCode:field_pctext': '',
        'awardDepartureDate:field': '',
        'awardDepartureDate:field_pctext': '',
        'awardReturnDate:field': '',
        'awardReturnDate:field_pctext': '',
        'hiddenBoardingClassType': '0',
        'boardingClass': 'CFF1',
        'adult:count': '1',
        'youngAdult:count': '0',
        'child:count': '0',
        'hiddenDomesticChildAge': 'false',
        'infant:count': '0',
        'javax.faces.ViewState': view_state,
        'j_idt674': 'j_idt674',
    }
    award_search_roundtrip_input_response_111 = session.post(
        url=url,
        headers=headers,
        data=data,
    )
    view_state = ''.join(etree.HTML(
        award_search_roundtrip_input_response_111.text.replace('<?xml version="1.0" encoding="UTF-8" ?>',
                                                               '').strip()).xpath(
        '//input[@id="j_id1:javax.faces.ViewState:3"]/@value'))
    search_url = ''.join(etree.HTML(
        award_search_roundtrip_input_response_111.text.replace('<?xml version="1.0" encoding="UTF-8" ?>',
                                                               '').strip()).xpath(
        '//form[@id="conditionInput"]/@action'))
    headers = {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
        'Cache-Control': 'max-age=0',
        'Connection': 'keep-alive',
        'Content-Type': 'application/x-www-form-urlencoded',
        'Origin': 'https://aswbe-i.ana.co.jp',
        'Referer': url,
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'same-origin',
        'Sec-Fetch-User': '?1',
        'Upgrade-Insecure-Requests': '1',
        'User-Agent': ua,
        'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
    }

    departure_date = "20250628"
    return_date = "20250723"

    data = {
        'conditionInput': 'conditionInput',
        'conditionInput_operationTicket': '',
        'conditionInput_cmnPageTicket': '',
        'requestedSegment:0:departureDate:field': departure_date,
        'requestedSegment:0:departureDate:field_pctext': format_japanese_date(departure_date),
        'requestedSegment:0:departureAirportCode:field': 'NRT',
        'requestedSegment:0:departureAirportCode:field_pctext': AIRPORT_CODES['NRT'],
        'requestedSegment:0:arrivalAirportCode:field': 'ORD',
        'requestedSegment:0:arrivalAirportCode:field_pctext': AIRPORT_CODES['ORD'],
        'requestedSegment:1:departureDate:field': return_date,
        'requestedSegment:1:departureDate:field_pctext': format_japanese_date(return_date),
        'requestedSegment:1:departureAirportCode:field': 'HNL',
        'requestedSegment:1:departureAirportCode:field_pctext': AIRPORT_CODES['HNL'],
        'requestedSegment:1:arrivalAirportCode:field': 'TYO',
        'requestedSegment:1:arrivalAirportCode:field_pctext': AIRPORT_CODES['TYO'],
        'requestedSegment:2:departureDate:field': '',
        'requestedSegment:2:departureDate:field_pctext': '',
        'requestedSegment:2:departureAirportCode:field': '',
        'requestedSegment:2:departureAirportCode:field_pctext': '',
        'requestedSegment:2:arrivalAirportCode:field': '',
        'requestedSegment:2:arrivalAirportCode:field_pctext': '',
        'requestedSegment:3:departureDate:field': '',
        'requestedSegment:3:departureDate:field_pctext': '',
        'requestedSegment:3:departureAirportCode:field': '',
        'requestedSegment:3:departureAirportCode:field_pctext': '',
        'requestedSegment:3:arrivalAirportCode:field': '',
        'requestedSegment:3:arrivalAirportCode:field_pctext': '',
        'requestedSegment:4:departureDate:field': '',
        'requestedSegment:4:departureDate:field_pctext': '',
        'requestedSegment:4:departureAirportCode:field': '',
        'requestedSegment:4:departureAirportCode:field_pctext': '',
        'requestedSegment:4:arrivalAirportCode:field': '',
        'requestedSegment:4:arrivalAirportCode:field_pctext': '',
        'requestedSegment:5:departureDate:field': '',
        'requestedSegment:5:departureDate:field_pctext': '',
        'requestedSegment:5:departureAirportCode:field': '',
        'requestedSegment:5:departureAirportCode:field_pctext': '',
        'requestedSegment:5:arrivalAirportCode:field': '',
        'requestedSegment:5:arrivalAirportCode:field_pctext': '',
        'requestedSegment:6:departureDate:field': '',
        'requestedSegment:6:departureDate:field_pctext': '',
        'requestedSegment:6:departureAirportCode:field': '',
        'requestedSegment:6:departureAirportCode:field_pctext': '',
        'requestedSegment:6:arrivalAirportCode:field': '',
        'requestedSegment:6:arrivalAirportCode:field_pctext': '',
        'requestedSegment:7:departureDate:field': '',
        'requestedSegment:7:departureDate:field_pctext': '',
        'requestedSegment:7:departureAirportCode:field': '',
        'requestedSegment:7:departureAirportCode:field_pctext': '',
        'requestedSegment:7:arrivalAirportCode:field': '',
        'requestedSegment:7:arrivalAirportCode:field_pctext': '',
        'adult:count': '1',
        'youngAdult:count': '0',
        'child:count': '0',
        'infant:count': '0',
        'j_idt816': '検索する',
        'hiddenAction': 'AwardComplexSearchInputAction',
        'hiddenSearchMode': 'MULTI_DESTINATION',
        'javax.faces.ViewState': view_state,
    }

    logger.info(f"提交最终搜索请求到: {search_url}")
    response = session.post(
        url=search_url,
        headers=headers,
        data=data
    )
    if '空席状況はご予約を完了されるまでに変わる可能性があります' not in response.text:
        logger.error("查询失败！！！！")
        filepath = save_response_to_file(response, 'log', 'search_error')
        logger.error(f"响应内容已保存到文件: {filepath}")
        logger.error(f"响应状态码: {response.status_code}")
        logger.error(f"响应头: {response.headers}")
        return False
    else:
        logger.success("查询成功！！！！")

        # --- 后续航班选择逻辑 ---
        # 阶段一: 选择去程航班
        logger.success("阶段一: 开始选择去程航班...")
        outbound_page_html = etree.HTML(response.content)

        target_flight_number = "NH12"
        target_class_name = "ビジネスクラス"  
        class_map = {
            "エコノミークラス": "E",
            "プレミアムエコノミークラス": "N",
            "ビジネスクラス": "B",
            "ファーストクラス": "F"
        }
        target_class_code = class_map.get(target_class_name)
        selected_flight_radio_button = None

        # 航班信息现在位于 <tr class="oneWayDisplayPlan">
        all_flight_rows = outbound_page_html.xpath('//tr[@class="oneWayDisplayPlan"]')
        if not all_flight_rows:
            logger.error("在去程页面上未找到任何航班结果行 (tr.oneWayDisplayPlan)。")
            return False
        
        logger.info(f"在去程页面上找到 {len(all_flight_rows)} 个航班。")

        for flight_row in all_flight_rows:
            flight_number_nodes = flight_row.xpath('.//th[@class="timeSchedule"]//div[contains(@class, "segTableCell") and contains(., "NH")]')
            if flight_number_nodes:
                flight_number_text = "".join(flight_number_nodes[0].itertext()).strip()
                try:
                    # 比较航班号数字部分
                    current_flight_number_digits = int(''.join(filter(str.isdigit, flight_number_text)))
                    target_flight_number_digits = int(''.join(filter(str.isdigit, target_flight_number)))
                except ValueError:
                    continue

                if current_flight_number_digits == target_flight_number_digits:
                    logger.info(f"找到目标航班: {flight_number_text}")
                    
                    if not target_class_code:
                        logger.error(f"未知的目标舱位名称: {target_class_name}")
                        continue

                    # 通过ID后缀找到对应的舱位单元格
                    class_cell_xpath = f'.//td[contains(@id, "_{target_class_code}")]'
                    class_cells = flight_row.xpath(class_cell_xpath)
                    
                    if not class_cells:
                        logger.warning(f"航班 {flight_number_text} 未找到舱位 {target_class_name} (Code: {target_class_code}) 的单元格。")
                        continue
                    
                    class_cell = class_cells[0]

                    # 检查座位可用性
                    seat_status_nodes = class_cell.xpath('.//span[contains(@class, "selectArea")]')
                    if seat_status_nodes and "空席あり" in "".join(seat_status_nodes[0].itertext()):
                        # 新的选择按钮是一个<i>标签
                        radio_button = class_cell.xpath('.//i[@role="button" and contains(@class, "radio")]')
                        if radio_button:
                            selected_flight_radio_button = radio_button[0]
                            logger.success(f"找到并选中去程航班 {target_flight_number} 的 {target_class_name}。")
                            break
            
            if selected_flight_radio_button is not None:
                break
        
        if selected_flight_radio_button is None:
            logger.error(f"未能在去程页面上找到目标航班 {target_flight_number} ({target_class_name}) 的可用座位。")
            return False

        # --- 阶段二: 提交选择并进入下一步 ---
        logger.success("阶段二: 提交所选航班并继续...")

        # 从页面获取 ViewState 和其他表单数据
        view_state_nodes = outbound_page_html.xpath('//input[@name="javax.faces.ViewState"]/@value')
        if not view_state_nodes:
            logger.error("在航班选择页面上找不到 'javax.faces.ViewState'。")
            return False
        view_state = view_state_nodes[0]

        form_action_nodes = outbound_page_html.xpath('//form[.//input[@id="nextButton"]]/@action')
        if not form_action_nodes:
            logger.error("在航班选择页面上找不到包含'下一步'按钮的表单'action' URL。")
            return False
        
        next_page_url = urljoin(response.url, form_action_nodes[0])

        # 获取已选航班的 data-value
        selected_flight_value = selected_flight_radio_button.get('data-value')
        if not selected_flight_value:
            logger.error("未能从所选航班按钮获取 'data-value'。")
            return False

        logger.info(f"选中的航班值为: {selected_flight_value}")

        # 构建POST数据
        # 核心数据：viewstate, 航班选择, 按钮点击
        next_page_data = {
            'javax.faces.ViewState': view_state,
            'departureRadioGroup:radioGroup': selected_flight_value,
            'nextButton': '次へ',
        }

        # 寻找并添加所有隐藏的input值，以确保表单的完整性
        form_element_list = outbound_page_html.xpath('//form[.//input[@id="nextButton"]]')
        if form_element_list:
            form_element = form_element_list[0]
            hidden_inputs = form_element.xpath('.//input[@type="hidden"]')
            for hidden in hidden_inputs:
                name = hidden.get('name')
                value = hidden.get('value', '')
                if name and name not in next_page_data:
                    next_page_data[name] = value
        
        logger.info(f"准备提交到下一步: {next_page_url}")
        logger.debug(f"提交的数据: {next_page_data}")

        # 更新 Referer 并发送请求
        headers['Referer'] = response.url
        next_page_response = session.post(
            url=next_page_url,
            headers=headers,
            data=next_page_data
        )

        # 检查和保存响应
        if "区間 2" in next_page_response.text:
            logger.success("成功进入返程航班选择页面 (区間 2)。")
            return_page_html = etree.HTML(next_page_response.content)

            # --- 阶段三: 选择返程航班 ---
            logger.success("阶段三: 开始选择返程航班...")
            
            selected_return_flight_button = None
            
            all_return_flight_rows = return_page_html.xpath('//tr[@class="oneWayDisplayPlan"]')
            if not all_return_flight_rows:
                logger.error("在返程页面上未找到任何航班结果行 (tr.oneWayDisplayPlan)。")
                save_response_to_file(next_page_response, 'log', 'return_select_error.html')
                return False
                
            logger.info(f"在返程页面上找到 {len(all_return_flight_rows)} 个航班。")

            # 寻找任意一个有空位的经济舱
            for flight_row in all_return_flight_rows:
                # 经济舱的单元格ID通常以下划线E结尾 (e.g., ..._E)
                eco_class_cell = flight_row.xpath('.//td[contains(@id, "_E")]')
                if not eco_class_cell:
                    continue

                if "空席あり" in "".join(eco_class_cell[0].itertext()):
                    radio_button = eco_class_cell[0].xpath('.//i[@role="button" and contains(@class, "radio")]')
                    if radio_button:
                        selected_return_flight_button = radio_button[0]
                        logger.success("找到并选中一个可用的返程经济舱航班。")
                        break
            
            if selected_return_flight_button is None:
                logger.error("未能在返程页面上找到任何可用的经济舱座位。")
                save_response_to_file(next_page_response, 'log', 'return_select_no_seat_error.html')
                return False

            # --- 阶段四: 提交返程选择 ---
            logger.success("阶段四: 提交所选返程航班并继续...")

            view_state = return_page_html.xpath('//input[@name="javax.faces.ViewState"]/@value')[0]
            form_action = return_page_html.xpath('//form[.//input[@id="nextButton"]]/@action')[0]
            confirmation_page_url = urljoin(next_page_response.url, form_action)

            return_flight_value = selected_return_flight_button.get('data-value')
            radio_group_name = selected_return_flight_button.get('data-radiobuttongroupname')

            if not return_flight_value or not radio_group_name:
                logger.error("未能从所选返程航班按钮获取 'data-value' 或 'data-radiobuttongroupname'。")
                return False

            confirmation_data = {
                'javax.faces.ViewState': view_state,
                radio_group_name: return_flight_value,
                'nextButton': '次へ',
            }

            form_element = return_page_html.xpath('//form[.//input[@id="nextButton"]]')[0]
            hidden_inputs = form_element.xpath('.//input[@type="hidden"]')
            for hidden in hidden_inputs:
                name = hidden.get('name')
                value = hidden.get('value', '')
                if name and name not in confirmation_data:
                    confirmation_data[name] = value

            logger.info(f"准备提交到行程确认页面: {confirmation_page_url}")

            headers['Referer'] = next_page_response.url
            confirmation_response = session.post(url=confirmation_page_url, headers=headers, data=confirmation_data)
            
            # 使用更可靠的关键字 "特典種別" (Award Type) 来验证是否到达了行程核对页面
            if "特典種別" in confirmation_response.text:
                logger.success("成功进入行程核对页面！")
                
                # --- 阶段五: 确认弹窗并提交最终行程 ---
                logger.success("阶段五: 开始确认行程并进入客户信息输入页面...")
                confirmation_html = etree.HTML(confirmation_response.content)

                final_view_state_nodes = confirmation_html.xpath('//input[@name="javax.faces.ViewState"]/@value')
                final_form_action_nodes = confirmation_html.xpath('//form[.//input[@value="次へ"]]/@action')

                if not final_view_state_nodes or not final_form_action_nodes:
                    logger.error("在确认页面上找不到 ViewState 或 form action。")
                    save_response_to_file(confirmation_response, 'log', 'final_page_parse_error')
                    return False

                final_view_state = final_view_state_nodes[0]
                final_page_url = urljoin(confirmation_response.url, final_form_action_nodes[0])

                # 查找页面上的"下一步"按钮和弹窗的"确认"按钮
                next_button = confirmation_html.xpath('//input[@value="次へ"]')
                confirm_popup_button = confirmation_html.xpath('//input[@value="確認"]')

                if not next_button:
                    logger.error("在行程确认页面找不到最终的'下一步'按钮。")
                    save_response_to_file(confirmation_response, 'log', 'final_submit_error')
                    return False
                
                # 构建最终提交数据
                final_data = {
                    'javax.faces.ViewState': final_view_state,
                    # 添加"下一步"按钮的数据
                    next_button[0].get('name'): next_button[0].get('value')
                }

                # 如果页面上有弹窗确认按钮，也将其数据添加进去
                if confirm_popup_button:
                    logger.info("在页面上找到弹窗确认按钮，将其信息一并提交。")
                    final_data[confirm_popup_button[0].get('name')] = confirm_popup_button[0].get('value')
                
                # 添加所有其他隐藏字段
                form_element = confirmation_html.xpath('//form[.//input[@value="次へ"]]')[0]
                hidden_inputs = form_element.xpath('.//input[@type="hidden"]')
                for hidden in hidden_inputs:
                    name = hidden.get('name')
                    value = hidden.get('value', '')
                    if name and name not in final_data:
                        final_data[name] = value

                logger.info(f"准备提交到客户信息输入页面: {final_page_url}")
                logger.debug(f"最终提交的数据: {final_data}")

                # --- 强制设置电话号码以避免验证错误 ---
                logger.info("强制设置SMS通知电话号码以完成预订...")
                
                # 1. 选择 "Specify phone number" 选项
                final_data['contactsSms:0:recipientSmsEachPersonSelection:radioGroup'] = '1'
                
                # 2. 将国家设置为日本
                final_data['contactsSms:0:recipientSmsEachPersonSelection:passengerSmsCountry'] = 'JP'
                
                # 3. 生成并设置一个随机的日本手机号码
                random_phone_number = f"90{''.join(random.choices('0123456789', k=8))}"
                final_data['contactsSms:0:recipientSmsEachPersonSelection:flightStatusNotificationContactPointSmsDescription'] = random_phone_number
                logger.info(f"已设置随机日本电话号码: {random_phone_number}")

                logger.info(f"准备提交最终预订请求到: {final_page_url}")
                logger.debug(f"最终预订数据: {final_data}")

                headers['Referer'] = confirmation_response.url
                final_response = session.post(url=final_page_url, headers=headers, data=final_data)

                if "お客様情報入力" in final_response.text:
                    logger.success("成功进入客户信息输入页面！")
                    filepath = save_response_to_file(final_response, 'log', 'passenger_info_page')
                    logger.info(f"乘客信息输入页面已保存到: {filepath}")

                    # --- 阶段六: 提交乘客信息并完成预订 ---
                    logger.success("阶段六: 提交乘客信息，完成最终预订...")
                    passenger_page_html = etree.HTML(final_response.content)

                    # 找到包含"下一步"按钮的表单
                    final_form_nodes = passenger_page_html.xpath('//form[.//input[@value="次へ"]]')
                    if not final_form_nodes:
                        logger.error("在乘客信息页面找不到最终的提交表单。")
                        return False

                    final_form = final_form_nodes[0]
                    booking_action_url = urljoin(final_response.url, final_form.get('action'))

                    # 动态收集所有表单数据
                    booking_data = {}
                    inputs = final_form.xpath('.//input')
                    for i in inputs:
                        name = i.get('name')
                        if not name:
                            continue
                        value = i.get('value', '')
                        input_type = i.get('type', 'text').lower()

                        if input_type in ('checkbox', 'radio'):
                            if i.get('checked') is not None:
                                booking_data[name] = value
                        else:
                            booking_data[name] = value

                    selects = final_form.xpath('.//select')
                    for s in selects:
                        name = s.get('name')
                        if not name:
                            continue
                        selected_option = s.xpath('.//option[@selected]')
                        if selected_option:
                            booking_data[name] = selected_option[0].get('value', '')
                        else:
                            first_option = s.xpath('.//option')
                            if first_option:
                                booking_data[name] = first_option[0].get('value', '')

                    textareas = final_form.xpath('.//textarea')
                    for t in textareas:
                        name = t.get('name')
                        if name:
                            booking_data[name] = t.text or ''
                    
                    # --- 强制设置电话号码以避免验证错误 ---
                    logger.info("强制设置SMS通知电话号码以完成预订...")
                    
                    # 1. 选择 "Specify phone number" 选项
                    booking_data['contactsSms:0:recipientSmsEachPersonSelection:radioGroup'] = '1'
                    
                    # 2. 将国家设置为日本
                    booking_data['contactsSms:0:recipientSmsEachPersonSelection:passengerSmsCountry'] = 'JP'
                    
                    # 3. 生成并设置一个随机的日本手机号码
                    random_phone_number = f"90{''.join(random.choices('0123456789', k=8))}"
                    booking_data['contactsSms:0:recipientSmsEachPersonSelection:flightStatusNotificationContactPointSmsDescription'] = random_phone_number
                    logger.info(f"已设置随机日本电话号码: {random_phone_number}")

                    logger.info(f"准备提交最终预订请求到: {booking_action_url}")
                    logger.debug(f"最终预订数据: {booking_data}")

                    headers['Referer'] = final_response.url
                    booking_response = session.post(
                        url=booking_action_url,
                        headers=headers,
                        data=booking_data
                    )

                    # 验证最终结果：检查是否存在会话保持按钮，这标志着占位成功
                    if "sessionKeeperContainer:cmnSessionKeepingButton" in booking_response.text:
                        logger.success("★★★★★ 占位成功! 进入会话保持阶段。 ★★★★★")
                        filepath = save_response_to_file(booking_response, 'log', 'booking_hold_page')
                        logger.info(f"占位成功页面已保存到: {filepath}")
                        
                        # 调用心跳函数来保持会话
                        keep_session_alive(booking_response)
                        return True # 占位周期完成
                    else:
                        logger.error("最终占位失败。未能进入支付/二次密码页面。")
                        filepath = save_response_to_file(booking_response, 'log', 'booking_failed_final')
                        logger.error(f"最终失败响应已保存到: {filepath}")
                        return False

                else:
                    logger.error("最终提交失败，未能进入客户信息输入页面。")
                    filepath = save_response_to_file(final_response, 'log', 'final_submit_error')
                    logger.error(f"错误响应已保存到: {filepath}")
                    return False

            else:
                logger.error('点击第二次"下一步"失败，未能进入行程确认页面。')
                # 修正文件名错误
                filepath = save_response_to_file(confirmation_response, 'log', 'confirmation_page_error')
                logger.error(f"错误响应已保存到: {filepath}")
                return False
        else:
            logger.error('点击"下一步"失败，未进入预期的返程航班选择页面。')
            # 修正文件名错误
            filepath = save_response_to_file(next_page_response, 'log', 'return_page_error')
            logger.error(f"错误响应已保存到: {filepath}")
            logger.error(f"响应状态码: {next_page_response.status_code}")
            return False


if __name__ == "__main__":
    while True: # 主循环，用于自动重试
        try:
            logger.info("----- 开始新一轮占位流程 -----")

            # 1. 总是为akm获取一个代理
            # 注意：即使后续请求不使用代理，akm cookie的获取也必须通过代理，这是硬性要求。
            proxy_for_akm = get_random_proxy()
            if not proxy_for_akm:
                logger.error("为akm服务获取代理失败，将在10秒后重试...")
                time.sleep(10)
                continue
            logger.info(f"已为akm服务获取代理: {proxy_for_akm.get('http')}")
            
            # 2. 使用该代理获取akm cookie
            akm_cookies = akmcookies_get(proxy_for_akm)
            if not akm_cookies:
                logger.error(f"使用代理 {proxy_for_akm.get('http')} 获取akm cookie失败，10秒后重试。")
                time.sleep(10)
                continue
            logger.info(f"成功获取akm cookie: {akm_cookies}")
            
            # 3. 清理并设置会话
            session.cookies.clear()
            session.cookies.update(akm_cookies)
            
            # 根据 USE_PROXY 开关决定后续请求是否使用代理
            if USE_PROXY:
                logger.info("代理已启用，将为后续所有请求设置相同的代理。")
                session.proxies = proxy_for_akm
            else:
                # 如果后续不使用代理，也要明确清除，以防会话中残留旧设置
                logger.info("代理已禁用，后续请求将不使用代理。")
                session.proxies = {}

            # 4. 执行登录、搜索、占位的完整流程
            login()
            logger.info("本轮占位会话已正常结束或超时，准备开始新一轮尝试。")

        # 使用 curl_cffi.requests.exceptions 中正确的异常类
        except (exceptions.Timeout, exceptions.ProxyError, exceptions.CurlError) as e:
            logger.error(f"发生可恢复的网络错误 (超时/代理/Curl): {e}。")
            # 超时或代理错误的 e 对象可能没有 response 属性，安全地处理
            if hasattr(e, 'response') and e.response is not None:
                save_response_to_file(e.response, 'log', 'timeout_or_proxy_error')
        except Exception as e:
            logger.error(f"本轮尝试期间出现意外错误 ({type(e).__name__}): {e}", exc_info=True)
            
        logger.info("等待 3 秒后重试...")
        time.sleep(3)
