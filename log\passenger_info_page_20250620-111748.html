<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="ja-<PERSON>" xml:lang="ja-<PERSON>">

	
	<!-- skey = 2025/06/20 12:17:48 rei22f eJuLV3l2MG dljdmx+56b  --><head id="j_idt53">
    <base href="about:blank">
    <style>
        /* Hide elements that might try to load external resources, as a fallback. */
        img, svg, video, iframe, link[rel="stylesheet"], script { display: none !important; }
    </style>

		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
			<meta http-equiv="X-UA-Compatible" content="IE=EmulateIE9; IE=EmulateIE10" />
			<meta http-equiv="X-UA-Compatible" content="IE=7" />
			<meta http-equiv="X-UA-Compatible" content="IE=edge" />
		<meta name="description" content="" />
		<meta name="keywords" content="" />
		<meta name="format-detection" content="telephone=no" />
		<script type="text/javascript" src="/international_asw/dynatrace/ruxitagentjs_ICANVfhqrux_10273230920145353.js" data-dtconfig="rid=RID_-1006584617|rpid=1063172751|domain=aswbe-i.ana.co.jp|reportUrl=https://bf90419wvr.bf.dynatrace.com/bf|app=78bf0b58acf6ed13|cors=1|owasp=1|featureHash=ICANVfhqrux|xb=www^bs.google-analytics^bs.com^pana^bs.demdex^bs.net^pcollect^bs.tealiumiq^bs.com^pallnipponairways^bs.australia-3^bs.evergage^bs.com^pallnipponairwayscolt^bs.tt^bs.omtrdc^bs.net|rdnt=1|uxrgce=1|cuc=k8vpgh61|mel=100000|expw=1|dpvc=1|md=mdcc1=bAsw.PnrOutput.recordLocator,mdcc2=bAsw.PnrOutput.paymentDetailInfoList[0].fopCode,mdcc3=bAsw.PnrOutput.ticketedFlag,mdcc4=bAsw.PnrOutput.nhPnrFlag,mdcc5=bAsw.PnrOutput.itinerarySummary.departureDateYyyymmdd,mdcc6=bAsw.SearchCriteriaOutput.cffCodeInput,mdcc7=a#main ^rb div.section.sectionNoFlow ^rb table ^rb tbody ^rb tr ^rb td.flightNumber.lastCol ^rb span ^rb span:nth-child(2),mdcc8=cunique|lastModification=1750184416409|tp=500,50,0|agentUri=/international_asw/dynatrace/ruxitagentjs_ICANVfhqrux_10273230920145353.js"></script><link rel="shortcut icon" href="https://www.ana.co.jp/favicon.ico" />
		<title>お客様情報入力 | ANA</title>
		<noscript><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei22f/international_asw/contents/cmn/css/asw_noscript_pc.css?99001fc" />
		</noscript>

		<!--  [ Adobe Target ] -->
		<script type="text/javascript" src="https://www.ana.co.jp/behavior/adobetarget/target_intbe.js"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22f/international_asw/contents/cmn/script/jquery-1.8.3.min.js?717d3c0"></script><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei22f/international_asw/contents/ja/css/asw_initialize.css?99001fc" /><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei22f/international_asw/contents/cmn/css/asw_screen_pc.css?99001fc" /><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei22f/international_asw/contents/cmn/css/asw_common_pc.css?99001fc" /><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei22f/international_asw/contents/cmn/css/asw_forminput_pc.css?99001fc" /><script type="text/javascript">
		var Asw = Asw || {};
		Asw.GlobalWord = Asw.GlobalWord || {};
		
		Asw.GlobalWord.toolTipCloseIconAltText = "閉じる";
		
		</script><script >bazadebezolkohpepadr="132486686"</script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/akam/13/7e597f4"  defer></script></head><body>
		
		<noscript>当サイトをご利用になるためには、JavaScript対応のブラウザが必要です。設定でJavaScriptを有効にしてください。有効にできない場合はANA電話窓口へお問い合わせください。
		</noscript>
		<div id="preLoadingArea"></div>
	
	<div id="cmnHeader" class="platinum">
		<!--===== cmnHeader =====-->
		<div class="headerWrapper">
			<div class="header">
				<p class="logo"><a href="https://www.ana.co.jp/asw/AMCTopServlet?type=a" tabindex="-1" aria-hidden="true"><img src="https://aswbe-i.ana.co.jp/rei22f/international_asw/contents/cmn/image/ana_logo_navy.png?717d3c0" alt="ANA Inspiration of JAPAN。リンク先はマイレージクラブトップページです。" height="28" width="274" /></a>
				</p>
					<ul class="headerMenu">
						<li><a href="https://www.ana.co.jp/asw/AMCTopServlet?type=a">ANAマイレージクラブトップページ</a>
						</li>
						<li><a href="https://www.ana.co.jp/other/int/meta/0005.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">お問い合わせ<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22f/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
						</li>
							<li class="btnLogoutArea"><a href="https://aswbe-i.ana.co.jp/rei22f/international_asw/rest/logout" role="button" class="btnBase btnLogout">ログアウト</a>
							</li>
					</ul>
			</div>
		</div>
			<div class="amcUserInfoWrapper">
				<div class="amcUserInfo">
					<div class="userNameArea">
						<table>
							<tr>	
									<td class="statusIcon"><img src="https://aswbe-i.ana.co.jp/rei22f/international_asw/contents/cmn/image/icon_status_05.png?717d3c0" alt="プラチナサービスメンバー" height="46" width="60" />
									</td>
								<td class="userName">佐藤 直樹 <span>様</span>
								</td>
							</tr>					
						</table>
					</div>
						<div class="balance">
								<dl class="upgradePointBalance">
									<dt>アップグレードポイント残高
									</dt>
									<dd>
											<dl>
												<dt>(2025年度)
												</dt>
												<dd>
													<em>20
													</em>ポイント
												</dd>
											</dl>
									</dd>
								</dl>
							<dl class="mileBalance">
								<dt>マイル口座残高
								</dt>
								<dd><em>75,663</em><span class="currencyCode">マイル</span>
								</dd>
							</dl>
						</div>
				</div>
					<p class="nowTime">6月20日12時17分現在
					</p>
			</div>
		<!--===== /cmnHeader =====-->
	</div>
		<div id="sessionKeeperContainer" class="sessionKeepingContainer"><img src="https://aswbe-i.ana.co.jp/rei22f/international_asw/contents/cmn/image/bg_modalWindow.png?717d3c0" alt="" height="0" width="0" />
			<div id="extendSessionExpiredDialog" class="modalExpire modalSmall modalContainer" role="dialog" aria-labelledby="extendSessionExpiredDialogTitle" aria-hidden="true">
<form id="sessionKeeperContainer:j_idt183" name="sessionKeeperContainer:j_idt183" method="post" action="https://aswbe-i.ana.co.jp/rei22f/international_asw/pages/award/reservation/award_mandatory_passenger_information_input.xhtml?aswcid=1&amp;rand=20250620121748eJuLV3l2MG" autocomplete="off" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="sessionKeeperContainer:j_idt183" value="sessionKeeperContainer:j_idt183" />
<input type="hidden" name="sessionKeeperContainer:j_idt183_operationTicket" value="dljdmx+56bdfe9f9ba636b530a9c9bdb5cacf86~z27ts0czQ8Gs_UeUB2Lajqi4IuYYsFvMem29XoAC!1750389458956.aere-xml-controller-67d4778877-jgdjz" /><input type="hidden" name="sessionKeeperContainer:j_idt183_cmnPageTicket" value="4" />
					<h1 id="extendSessionExpiredDialogTitle" class="dialogTitle">まもなくセッションが終了します</h1>
					<div id="extendSessionExpiredDialogMessage" class="dialogMessage" tabindex="-1">このままお手続きを進める場合は、［延長］ボタンを押してください。</div>
					<p class="modalButton"><input id="sessionKeeperContainer:cmnSessionKeepingButton" type="submit" name="sessionKeeperContainer:cmnSessionKeepingButton" value="延長" class="btnBase btnModal btnMainStream" onclick="mojarra.ab(this,event,'action',0,0,{'onevent':function(data) {Asw.doCommonAjaxCallback(data,false, event); Asw.SessionKeeper.doSessionKeep(data);},'onerror':function(data) {Asw.doCommonAjaxErrorCallback(data); }});return false" />
					</p><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:0" value="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" autocomplete="off" />
</form>
			</div>
			<div id="showSessionExpiredDialog" class="modalExpire modalSmall modalContainer" role="dialog" aria-labelledby="showSessionExpiredDialogTitle" aria-hidden="true">
				<h1 id="showSessionExpiredDialogTitle" class="dialogTitle">セッションが終了しました</h1>
				<div id="showSessionExpiredDialogMessage" class="dialogMessage" tabindex="-1">一定時間操作されなかったため、お手続きを進めることができません。お手数ですが、再度トップページからお手続きください。</div>
				<p class="modalButton">
					<input type="submit" value="閉じる" class="btnBase btnModal btnMainStream" onclick="Asw.SessionKeeper.closeTimeoutDialog(); return false;" />
				</p>
			</div>
		</div>
		<!--===== dialogMessages =====--><div id="cmnModalMessages"></div>
		<div id="prebookConfirmDialog" class="modalContainer modalConfirm modalSmall " aria-hidden="true" role="dialog">
<form id="prebookConfirmDialog:j_idt403" name="prebookConfirmDialog:j_idt403" method="post" action="https://aswbe-i.ana.co.jp/rei22f/international_asw/pages/award/reservation/award_mandatory_passenger_information_input.xhtml?aswcid=1&amp;rand=20250620121748eJuLV3l2MG" autocomplete="off" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="prebookConfirmDialog:j_idt403" value="prebookConfirmDialog:j_idt403" />
<input type="hidden" name="prebookConfirmDialog:j_idt403_operationTicket" value="dljdmx+56bdfe9f9ba636b530a9c9bdb5cacf86~z27ts0czQ8Gs_UeUB2Lajqi4IuYYsFvMem29XoAC!1750389458956.aere-xml-controller-67d4778877-jgdjz" /><input type="hidden" name="prebookConfirmDialog:j_idt403_cmnPageTicket" value="4" /><div class="dialogMessage" tabindex="0"><p>この後、名・姓の修正や変更ができません。</p><p>「搭乗者選択」欄の氏名が「名」「姓」の順番で表示されていることを再確認ください。</p><p>例）<strong class="highlight">空野 太郎</strong> 様 → MR <strong class="highlight">TARO SORANO</strong></p><p>保安上の理由により、航空券とパスポートのお名前が同じ表記でない場合はご搭乗いただくことができませんので必ず再度ご確認ください。</p></div>
					
					<ul class="modalBtnHorizontal">
							
							<li><input type="submit" name="prebookConfirmDialog:j_idt419" value="戻る" aria-controls="prebookConfirmDialog" class="btnBase btnModal btnCancel" onclick="Asw.Dialog.getInstance('prebookConfirmDialog').close(event, Asw.DialogSequencer.isActiveSequencerNotDisplayedLastDialog());  return false;" />
							</li>
						
						<li class="btnArrowNext"><input type="submit" name="prebookConfirmDialog:j_idt422" value="OK" aria-controls="prebookConfirmDialog" class="btnBase btnModal btnMainStream " onclick="Asw.Dialog.getInstance('prebookConfirmDialog').callOpener('NORMAL', event); return false;" />
						</li>
					</ul><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:1" value="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" autocomplete="off" />
</form>
		</div>
		<!--===== /dialogMessages =====--><div id="cmnContainer">
		<!--=========== .cmnContainer ============--><div id="cmnEmergencyNotice"></div><div id="cmnTitle" class="hgroup ">
		<h1 class="visuallyHidden">お客様情報入力</h1></div>

		<div id="cmnWrapper">
		<!--===== cmnWrapper =====-->
		<ol class="mainFlow" aria-label="完了までの手順、">
			<li><img src="https://aswbe-i.ana.co.jp/rei22f/international_asw/contents/cmn/image/icon_flow_01.png?717d3c0" alt="1" height="20" width="28" />フライト検索</li><!--
			--><li><img src="https://aswbe-i.ana.co.jp/rei22f/international_asw/contents/cmn/image/icon_flow_02_on.png?717d3c0" alt="2" height="20" width="28" /><span class="visuallyHidden">現在のステップ、</span>お客様情報入力</li><!--
			--><li><img src="https://aswbe-i.ana.co.jp/rei22f/international_asw/contents/cmn/image/icon_flow_03.png?717d3c0" alt="3" height="20" width="28" />お支払い情報</li><!--
			--><li class="flowLast"><img src="https://aswbe-i.ana.co.jp/rei22f/international_asw/contents/cmn/image/icon_flow4_04.png?717d3c0" alt="4" height="20" width="28" />完了</li>
		</ol>

	<div id="main">
	<!--=========== main =============-->
		<ul class="iconExample">
				<li class="requiredIconExample">
					<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei22f/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="" height="7" width="7" /></span>必須項目です	
				</li>
		</ul>
<form id="j_idt526" name="j_idt526" method="post" action="https://aswbe-i.ana.co.jp/rei22f/international_asw/pages/award/reservation/award_mandatory_passenger_information_input.xhtml?aswcid=1&amp;rand=20250620121748eJuLV3l2MG" autocomplete="off" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="j_idt526" value="j_idt526" />
<input type="hidden" name="j_idt526_operationTicket" value="dljdmx+56bdfe9f9ba636b530a9c9bdb5cacf86~z27ts0czQ8Gs_UeUB2Lajqi4IuYYsFvMem29XoAC!1750389458956.aere-xml-controller-67d4778877-jgdjz" /><input type="hidden" name="j_idt526_cmnPageTicket" value="4" />

<div class="section">
	<h2>搭乗者選択<a href="https://www.ana.co.jp/other/int/meta/0086.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="特典のご利用に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei22f/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a></h2>
	<table class="passengerInfo">
		<thead>
			<tr>
				
				<th class="name">氏名</th>
				
				<th class="dateOfBirthWide" id="dateOfBirthHeader">生年月日</th>
				
				<th class="gender lastCol" id="genderHeader">性別<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei22f/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span></th>
				
			</tr>
		</thead>
		<tbody>
				
				<tr>
					<th class="parent_01" id="parentHeader01">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:0:passenger" type="checkbox" name="passengers:0:passenger" checked="checked" data-number="1" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:0:passenger">NAOKI SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader01">1963年06月05日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader01">				
							<span class="formWrapper">
							<label for="passengers:0:gender" class="visuallyHidden">NAOKI SATOの性別</label><select id="passengers:0:gender" name="passengers:0:gender" size="1">	<option value="">選択してください</option>
	<option value="M" selected="selected">男性</option>
	<option value="F">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_02" id="parentHeader02">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:1:passenger" type="checkbox" name="passengers:1:passenger" data-number="2" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:1:passenger">REO SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader02">1995年05月18日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader02">				
							<span class="formWrapper">
							<label for="passengers:1:gender" class="visuallyHidden">REO SATOの性別</label><select id="passengers:1:gender" name="passengers:1:gender" size="1">	<option value="">選択してください</option>
	<option value="M" selected="selected">男性</option>
	<option value="F">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_03" id="parentHeader03">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:2:passenger" type="checkbox" name="passengers:2:passenger" data-number="3" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:2:passenger">ERI SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader03">1963年08月14日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader03">				
							<span class="formWrapper">
							<label for="passengers:2:gender" class="visuallyHidden">ERI SATOの性別</label><select id="passengers:2:gender" name="passengers:2:gender" size="1">	<option value="">選択してください</option>
	<option value="M">男性</option>
	<option value="F" selected="selected">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_04" id="parentHeader04">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:3:passenger" type="checkbox" name="passengers:3:passenger" data-number="4" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:3:passenger">TAISHI SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader04">1992年12月03日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader04">				
							<span class="formWrapper">
							<label for="passengers:3:gender" class="visuallyHidden">TAISHI SATOの性別</label><select id="passengers:3:gender" name="passengers:3:gender" size="1">	<option value="">選択してください</option>
	<option value="M" selected="selected">男性</option>
	<option value="F">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_05" id="parentHeader05">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:4:passenger" type="checkbox" name="passengers:4:passenger" data-number="5" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:4:passenger">HIROMI FUJIMOTO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader05">1961年11月23日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader05">				
							<span class="formWrapper">
							<label for="passengers:4:gender" class="visuallyHidden">HIROMI FUJIMOTOの性別</label><select id="passengers:4:gender" name="passengers:4:gender" size="1">	<option value="">選択してください</option>
	<option value="M">男性</option>
	<option value="F" selected="selected">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
		</tbody>
	</table>
</div>
		<div class="section">
			<h2 class="midstream">連絡先情報
			</h2>
			<h3>代表者
			</h3>
	
	
	
	<div class="formInput">
		<fieldset>
			<legend><label>代表者</label></legend>
			<dl id="contactEmailAddressInfo">
				<dt class="hasRequiredIcon">メールアドレス<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei22f/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span><a href="https://www.ana.co.jp/other/int/meta/0057.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="メールの宛先と内容に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei22f/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a>
				</dt>
				<dd>
					<ul class="verticalList">
						<li class="nonMarginTop"><label for="representativeEmail" class="visuallyHidden">代表者メールアドレス</label><input id="representativeEmail" type="text" name="representativeEmail" value="<EMAIL>" class="widthLarge" placeholder="例：<EMAIL>" />
						</li>
						<li>
							<span class="supplementTxt" id="confirmMailAddressForRepresentative">確認のため再度ご入力ください。
							</span><label for="confirmrepresentativeEmail" class="visuallyHidden">確認用メールアドレス</label><input id="confirmrepresentativeEmail" type="text" name="confirmrepresentativeEmail" value="<EMAIL>" class="widthLarge jsNotPasteInput" placeholder="例：<EMAIL>" aria-describedby="confirmMailAddressForRepresentative" />
						</li>
					</ul>
				</dd>
					<dd style="display:none;"><input id="updateAlwaysInfo" type="checkbox" name="updateAlwaysInfo" /><label for="updateAlwaysInfo">ログイン中の会員情報へこのメールアドレスを登録</label>
					</dd>
			</dl>
			<dl>
				<dt>電話番号<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei22f/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span>
				</dt>
				<dd class="js-countryWrap">
					<ul>
						<li><label for="representativeTelType" class="visuallyHidden">電話番号種別</label><select id="representativeTelType" name="representativeTelType" class="basicWidth js-typeSelect" size="1" onchange="changeTelType(this,'representativeTel')">	<option value="M1" selected="selected">携帯</option>
	<option value="B1">会社</option>
	<option value="H1">自宅</option>
</select>
						</li>
						<li><label for="representativeTelCountry" class="visuallyHidden">電話番号の国/地区選択</label><select id="representativeTelCountry" name="representativeTelCountry" class="js-countrySelect" size="1">	<option value="" data-sms-possible="true" data-country-code="---">選択してください</option>
	<option value="JP" selected="selected" data-sms-possible="true">日本</option>
	<option value="-" data-sms-possible="true" data-country-code="---">-----------</option>
	<option value="IS" data-sms-possible="true">アイスランド</option>
	<option value="IE" data-sms-possible="true">アイルランド</option>
	<option value="AZ" data-sms-possible="true">アゼルバイジャン</option>
	<option value="AF" data-sms-possible="true">アフガニスタン</option>
	<option value="US" data-sms-possible="true">アメリカ合衆国/カナダ</option>
	<option value="AE" data-sms-possible="true">アラブ首長国連邦</option>
	<option value="DZ" data-sms-possible="true">アルジェリア</option>
	<option value="AR" data-sms-possible="true">アルゼンチン</option>
	<option value="AW" data-sms-possible="true">アルバ</option>
	<option value="AL" data-sms-possible="true">アルバニア</option>
	<option value="AM" data-sms-possible="true">アルメニア</option>
	<option value="AO" data-sms-possible="true">アンゴラ</option>
	<option value="AG" data-sms-possible="true">アンティグア・バーブーダ</option>
	<option value="AD" data-sms-possible="true">アンドラ</option>
	<option value="YE" data-sms-possible="true">イエメン</option>
	<option value="GB" data-sms-possible="true">イギリス</option>
	<option value="IL" data-sms-possible="true">イスラエル</option>
	<option value="IT" data-sms-possible="true">イタリア</option>
	<option value="IQ" data-sms-possible="true">イラク</option>
	<option value="IR" data-sms-possible="true">イラン</option>
	<option value="IN" data-sms-possible="true">インド</option>
	<option value="ID" data-sms-possible="true">インドネシア</option>
	<option value="UG" data-sms-possible="true">ウガンダ</option>
	<option value="UA" data-sms-possible="true">ウクライナ</option>
	<option value="UZ" data-sms-possible="true">ウズベキスタン</option>
	<option value="UY" data-sms-possible="true">ウルグアイ</option>
	<option value="EC" data-sms-possible="true">エクアドル</option>
	<option value="EG" data-sms-possible="true">エジプト</option>
	<option value="EE" data-sms-possible="true">エストニア</option>
	<option value="SZ" data-sms-possible="true">エスワティニ</option>
	<option value="ET" data-sms-possible="true">エチオピア</option>
	<option value="ER" data-sms-possible="true">エリトリア</option>
	<option value="SV" data-sms-possible="true">エルサルバドル</option>
	<option value="AU" data-sms-possible="true">オーストラリア</option>
	<option value="AT" data-sms-possible="true">オーストリア</option>
	<option value="OM" data-sms-possible="true">オマーン</option>
	<option value="NL" data-sms-possible="true">オランダ</option>
	<option value="GH" data-sms-possible="true">ガーナ</option>
	<option value="CV" data-sms-possible="true">カーボベルデ</option>
	<option value="GY" data-sms-possible="true">ガイアナ</option>
	<option value="KZ" data-sms-possible="true">カザフスタン</option>
	<option value="QA" data-sms-possible="true">カタール</option>
	<option value="CA" data-sms-possible="true">カナダ/アメリカ合衆国</option>
	<option value="GA" data-sms-possible="true">ガボン</option>
	<option value="CM" data-sms-possible="true">カメルーン</option>
	<option value="KR" data-sms-possible="true">韓国</option>
	<option value="GM" data-sms-possible="true">ガンビア</option>
	<option value="KH" data-sms-possible="true">カンボジア</option>
	<option value="KP" data-sms-possible="true">北朝鮮</option>
	<option value="MK" data-sms-possible="true">北マケドニア</option>
	<option value="GN" data-sms-possible="true">ギニア</option>
	<option value="GW" data-sms-possible="true">ギニアビサウ</option>
	<option value="CY" data-sms-possible="true">キプロス</option>
	<option value="CU" data-sms-possible="true">キューバ</option>
	<option value="GR" data-sms-possible="true">ギリシャ</option>
	<option value="KI" data-sms-possible="true">キリバス</option>
	<option value="KG" data-sms-possible="true">キルギス</option>
	<option value="GT" data-sms-possible="true">グアテマラ</option>
	<option value="GU" data-sms-possible="true">グアム</option>
	<option value="KW" data-sms-possible="true">クウェート</option>
	<option value="GD" data-sms-possible="true">グレナダ</option>
	<option value="HR" data-sms-possible="true">クロアチア</option>
	<option value="KY" data-sms-possible="true">ケイマン諸島</option>
	<option value="KE" data-sms-possible="true">ケニア</option>
	<option value="CI" data-sms-possible="true">コートジボワール</option>
	<option value="CR" data-sms-possible="true">コスタリカ</option>
	<option value="KM" data-sms-possible="true">コモロ</option>
	<option value="CO" data-sms-possible="true">コロンビア</option>
	<option value="CG" data-sms-possible="true">コンゴ共和国</option>
	<option value="CD" data-sms-possible="true">コンゴ民主共和国</option>
	<option value="SA" data-sms-possible="true">サウジアラビア</option>
	<option value="ZM" data-sms-possible="true">ザンビア</option>
	<option value="SM" data-sms-possible="true">サンマリノ</option>
	<option value="SL" data-sms-possible="true">シエラレオネ</option>
	<option value="DJ" data-sms-possible="true">ジブチ</option>
	<option value="JM" data-sms-possible="true">ジャマイカ</option>
	<option value="GE" data-sms-possible="true">ジョージア</option>
	<option value="SY" data-sms-possible="true">シリア</option>
	<option value="SG" data-sms-possible="true">シンガポール</option>
	<option value="ZW" data-sms-possible="true">ジンバブエ</option>
	<option value="CH" data-sms-possible="true">スイス</option>
	<option value="SE" data-sms-possible="true">スウェーデン</option>
	<option value="SD" data-sms-possible="true">スーダン</option>
	<option value="ES" data-sms-possible="true">スペイン</option>
	<option value="SR" data-sms-possible="true">スリナム</option>
	<option value="LK" data-sms-possible="true">スリランカ</option>
	<option value="SK" data-sms-possible="true">スロバキア</option>
	<option value="SI" data-sms-possible="true">スロベニア</option>
	<option value="SC" data-sms-possible="true">セイシェル</option>
	<option value="GQ" data-sms-possible="true">赤道ギニア</option>
	<option value="SN" data-sms-possible="true">セネガル</option>
	<option value="RS" data-sms-possible="true">セルビア</option>
	<option value="KN" data-sms-possible="true">セントクリストファー・ネイビス</option>
	<option value="LC" data-sms-possible="true">セントルシア</option>
	<option value="SB" data-sms-possible="true">ソロモン諸島</option>
	<option value="TH" data-sms-possible="true">タイ</option>
	<option value="TW" data-sms-possible="true">台湾</option>
	<option value="TJ" data-sms-possible="true">タジキスタン</option>
	<option value="TZ" data-sms-possible="true">タンザニア</option>
	<option value="CZ" data-sms-possible="true">チェコ</option>
	<option value="TD" data-sms-possible="true">チャド</option>
	<option value="CF" data-sms-possible="true">中央アフリカ共和国</option>
	<option value="CN" data-sms-possible="true">中国</option>
	<option value="TN" data-sms-possible="true">チュニジア</option>
	<option value="CL" data-sms-possible="true">チリ</option>
	<option value="TV" data-sms-possible="true">ツバル</option>
	<option value="DK" data-sms-possible="true">デンマーク</option>
	<option value="DE" data-sms-possible="true">ドイツ</option>
	<option value="TG" data-sms-possible="true">トーゴ</option>
	<option value="DO" data-sms-possible="true">ドミニカ共和国</option>
	<option value="DM" data-sms-possible="true">ドミニカ国</option>
	<option value="TT" data-sms-possible="true">トリニダード・トバゴ</option>
	<option value="TM" data-sms-possible="true">トルクメニスタン</option>
	<option value="TR" data-sms-possible="true">トルコ</option>
	<option value="TO" data-sms-possible="true">トンガ</option>
	<option value="NG" data-sms-possible="true">ナイジェリア</option>
	<option value="NR" data-sms-possible="true">ナウル</option>
	<option value="NA" data-sms-possible="true">ナミビア</option>
	<option value="NI" data-sms-possible="true">ニカラグア</option>
	<option value="NE" data-sms-possible="true">ニジェール</option>
	<option value="NZ" data-sms-possible="true">ニュージーランド</option>
	<option value="NP" data-sms-possible="true">ネパール</option>
	<option value="NO" data-sms-possible="true">ノルウェー</option>
	<option value="BH" data-sms-possible="true">バーレーン</option>
	<option value="HT" data-sms-possible="true">ハイチ</option>
	<option value="PK" data-sms-possible="true">パキスタン</option>
	<option value="VA" data-sms-possible="true">バチカン</option>
	<option value="PA" data-sms-possible="true">パナマ</option>
	<option value="VU" data-sms-possible="true">バヌアツ</option>
	<option value="BS" data-sms-possible="true">バハマ</option>
	<option value="PG" data-sms-possible="true">パプアニューギニア</option>
	<option value="BM" data-sms-possible="true">バミューダ</option>
	<option value="PW" data-sms-possible="true">パラオ</option>
	<option value="PY" data-sms-possible="true">パラグアイ</option>
	<option value="BB" data-sms-possible="true">バルバドス</option>
	<option value="PS" data-sms-possible="true">パレスチナ</option>
	<option value="HU" data-sms-possible="true">ハンガリー</option>
	<option value="BD" data-sms-possible="true">バングラデシュ</option>
	<option value="TL" data-sms-possible="true">東ティモール</option>
	<option value="FJ" data-sms-possible="true">フィジー</option>
	<option value="PH" data-sms-possible="true">フィリピン</option>
	<option value="FI" data-sms-possible="true">フィンランド</option>
	<option value="BT" data-sms-possible="true">ブータン</option>
	<option value="PR" data-sms-possible="true">プエルトリコ</option>
	<option value="BR" data-sms-possible="true">ブラジル</option>
	<option value="FR" data-sms-possible="true">フランス</option>
	<option value="PF" data-sms-possible="true">フランス領ポリネシア</option>
	<option value="BG" data-sms-possible="true">ブルガリア</option>
	<option value="BF" data-sms-possible="true">ブルキナファソ</option>
	<option value="BN" data-sms-possible="true">ブルネイ</option>
	<option value="BI" data-sms-possible="true">ブルンジ</option>
	<option value="VN" data-sms-possible="true">ベトナム</option>
	<option value="BJ" data-sms-possible="true">ベナン</option>
	<option value="VE" data-sms-possible="true">ベネズエラ</option>
	<option value="BY" data-sms-possible="true">ベラルーシ</option>
	<option value="BZ" data-sms-possible="true">ベリーズ</option>
	<option value="PE" data-sms-possible="true">ペルー</option>
	<option value="BE" data-sms-possible="true">ベルギー</option>
	<option value="PL" data-sms-possible="true">ポーランド</option>
	<option value="BA" data-sms-possible="true">ボスニア・ヘルツェゴビナ</option>
	<option value="BW" data-sms-possible="true">ボツワナ</option>
	<option value="BO" data-sms-possible="true">ボリビア</option>
	<option value="PT" data-sms-possible="true">ポルトガル</option>
	<option value="HK" data-sms-possible="true">香港</option>
	<option value="HN" data-sms-possible="true">ホンジュラス</option>
	<option value="MH" data-sms-possible="true">マーシャル諸島</option>
	<option value="MO" data-sms-possible="true">マカオ</option>
	<option value="MG" data-sms-possible="true">マダガスカル</option>
	<option value="MW" data-sms-possible="true">マラウイ</option>
	<option value="ML" data-sms-possible="true">マリ</option>
	<option value="MT" data-sms-possible="true">マルタ</option>
	<option value="MY" data-sms-possible="true">マレーシア</option>
	<option value="FM" data-sms-possible="true">ミクロネシア連邦</option>
	<option value="ZA" data-sms-possible="true">南アフリカ</option>
	<option value="MM" data-sms-possible="true">ミャンマー</option>
	<option value="MX" data-sms-possible="true">メキシコ</option>
	<option value="MU" data-sms-possible="true">モーリシャス</option>
	<option value="MR" data-sms-possible="true">モーリタニア</option>
	<option value="MZ" data-sms-possible="true">モザンビーク</option>
	<option value="MC" data-sms-possible="true">モナコ</option>
	<option value="MV" data-sms-possible="true">モルディヴ</option>
	<option value="MD" data-sms-possible="true">モルドバ</option>
	<option value="MA" data-sms-possible="true">モロッコ</option>
	<option value="MN" data-sms-possible="true">モンゴル</option>
	<option value="ME" data-sms-possible="true">モンテネグロ</option>
	<option value="JO" data-sms-possible="true">ヨルダン</option>
	<option value="LA" data-sms-possible="true">ラオス人民民主共和国</option>
	<option value="LV" data-sms-possible="true">ラトビア</option>
	<option value="LT" data-sms-possible="true">リトアニア</option>
	<option value="LY" data-sms-possible="true">リビア</option>
	<option value="LI" data-sms-possible="true">リヒテンシュタイン</option>
	<option value="LR" data-sms-possible="true">リベリア</option>
	<option value="RO" data-sms-possible="true">ルーマニア</option>
	<option value="LU" data-sms-possible="true">ルクセンブルク</option>
	<option value="RW" data-sms-possible="true">ルワンダ</option>
	<option value="LS" data-sms-possible="true">レソト</option>
	<option value="LB" data-sms-possible="true">レバノン</option>
	<option value="RU" data-sms-possible="true">ロシア</option>
</select>
						</li>
						<li class="numberInputWrap">
							<span id="numberInputInfo1" class="supplementTxt marginTop">先頭に0(ゼロ)がある場合、0(ゼロ)を抜いてください(一部、国・地域除く)。携帯電話番号の場合は、携帯電話を契約された国を選択ください。</span>
							<div class="numberRowWrap">
								<div class="block">
									
									<span class="visuallyHidden">国番号</span>
									<span class="viewInput widthMini js-countryCode"></span>
								</div>
								<div class="block"><label for="representativeTel" class="visuallyHidden">電話番号</label><input id="representativeTel" type="text" name="representativeTel" value="9042504176" class="widthMiddle" placeholder="例：9012345678" maxlength="30" aria-describedby="numberInputInfo1" />
								</div>
							</div>
						</li>
					</ul>
				</dd>
			</dl>
		</fieldset>
	</div>
	<div class="titleWrapper">
		<h3>
			<span class="text">eメール・SMSに関するご案内<a href="https://www.ana.co.jp/other/int/meta/0057.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="eメール・SMSに関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei22f/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a></span>
		</h3>
	</div>
	<div class="formInput">
	<h4 class="formAreaTitle">主に搭乗・運航に関わるeメールの送付先設定（米国へ通知いたします）<a href="https://www.ana.co.jp/other/int/meta/0629.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="米国入国用eメール・SMS・電話番号に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei22f/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a>
	</h4>
	<div class="paxSettingArea js-mailFormWrap onePax is-open">
			<div class="settingArea">
					<dl>
						<dt class="breakWord">NAOKI SATO
						</dt>
						<dd>
							<fieldset>
								<legend>NAOKI SATOのeメール送付先および米国到着時に提供するメールアドレス</legend>代表者と同じ
							</fieldset>
						</dd>
					</dl>
			</div>
	</div>
	<h4 class="formAreaTitle hasTopLine">主に搭乗・運航に関わるSMSの送付先および米国で受信できる電話番号の設定（米国へ通知いたします）<a href="https://www.ana.co.jp/other/int/meta/0629.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="米国入国用eメール・SMS・電話番号に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei22f/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a>
	</h4>
	<div class="paxSettingArea js-smsFormWrap onePax is-open">
			<div class="settingArea">
					<dl>
						<dt class="breakWord">NAOKI SATO<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei22f/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span>
						</dt>
						<dd>
							<fieldset>
								<legend>NAOKI SATOの1つ目の電話番号</legend>
									<ul class="verticalList js-formItem">
										<li><input type="radio" id="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSameAsRepresentativeSms:radioItem" name="contactsSms:0:recipientSmsEachPersonSelection:radioGroup" value="0" checked="checked" /><label for="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSameAsRepresentativeSms:radioItem">代表者電話番号と同じ</label>
										</li>
										<li><input type="radio" id="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSmsPerPassenger:radioItem" name="contactsSms:0:recipientSmsEachPersonSelection:radioGroup" value="1" /><label for="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSmsPerPassenger:radioItem">電話番号を指定</label>
											<div class="indentArea js-formImputArea">
												<span id="confirmTelNumberForRepresentative:0" class="supplementTxt marginTop">先頭に0(ゼロ)がある場合、0(ゼロ)を抜いてください(一部、国・地域除く)。携帯電話番号の場合は、携帯電話を契約された国を選択ください。</span>
												<ul class="countryNumberInput js-countryWrap">
													<li><label for="contactsSms:0:recipientSmsEachPersonSelection:passengerSmsCountry" class="visuallyHidden">電話番号の国/地区選択</label><select id="contactsSms:0:recipientSmsEachPersonSelection:passengerSmsCountry" name="contactsSms:0:recipientSmsEachPersonSelection:passengerSmsCountry" class="countrySelect js-countrySelect" size="1">	<option value="" data-country-code="---">選択してください</option>
	<option value="JP">日本</option>
	<option value="-" data-country-code="---">-----------</option>
	<option value="IE">アイルランド</option>
	<option value="AZ">アゼルバイジャン</option>
	<option value="AF">アフガニスタン</option>
	<option value="US">アメリカ合衆国/カナダ</option>
	<option value="AE">アラブ首長国連邦</option>
	<option value="DZ">アルジェリア</option>
	<option value="AR">アルゼンチン</option>
	<option value="AW">アルバ</option>
	<option value="AL">アルバニア</option>
	<option value="AM">アルメニア</option>
	<option value="AG">アンティグア・バーブーダ</option>
	<option value="AD">アンドラ</option>
	<option value="YE">イエメン</option>
	<option value="GB">イギリス</option>
	<option value="IL">イスラエル</option>
	<option value="IT">イタリア</option>
	<option value="IQ">イラク</option>
	<option value="IR">イラン</option>
	<option value="IN">インド</option>
	<option value="ID">インドネシア</option>
	<option value="UG">ウガンダ</option>
	<option value="UA">ウクライナ</option>
	<option value="UZ">ウズベキスタン</option>
	<option value="UY">ウルグアイ</option>
	<option value="EC">エクアドル</option>
	<option value="EG">エジプト</option>
	<option value="EE">エストニア</option>
	<option value="ER">エリトリア</option>
	<option value="SV">エルサルバドル</option>
	<option value="AU">オーストラリア</option>
	<option value="AT">オーストリア</option>
	<option value="OM">オマーン</option>
	<option value="NL">オランダ</option>
	<option value="GH">ガーナ</option>
	<option value="GY">ガイアナ</option>
	<option value="KZ">カザフスタン</option>
	<option value="QA">カタール</option>
	<option value="CA">カナダ/アメリカ合衆国</option>
	<option value="GA">ガボン</option>
	<option value="CM">カメルーン</option>
	<option value="KR">韓国</option>
	<option value="GM">ガンビア</option>
	<option value="KH">カンボジア</option>
	<option value="GN">ギニア</option>
	<option value="GW">ギニアビサウ</option>
	<option value="CY">キプロス</option>
	<option value="CU">キューバ</option>
	<option value="GR">ギリシャ</option>
	<option value="KG">キルギス</option>
	<option value="GT">グアテマラ</option>
	<option value="GU">グアム</option>
	<option value="KW">クウェート</option>
	<option value="GD">グレナダ</option>
	<option value="HR">クロアチア</option>
	<option value="KY">ケイマン諸島</option>
	<option value="KE">ケニア</option>
	<option value="CI">コートジボワール</option>
	<option value="CR">コスタリカ</option>
	<option value="KM">コモロ</option>
	<option value="CO">コロンビア</option>
	<option value="CD">コンゴ民主共和国</option>
	<option value="SA">サウジアラビア</option>
	<option value="ZM">ザンビア</option>
	<option value="SL">シエラレオネ</option>
	<option value="JM">ジャマイカ</option>
	<option value="GE">ジョージア</option>
	<option value="SY">シリア</option>
	<option value="SG">シンガポール</option>
	<option value="ZW">ジンバブエ</option>
	<option value="CH">スイス</option>
	<option value="SE">スウェーデン</option>
	<option value="SD">スーダン</option>
	<option value="ES">スペイン</option>
	<option value="SR">スリナム</option>
	<option value="LK">スリランカ</option>
	<option value="SK">スロバキア</option>
	<option value="SI">スロベニア</option>
	<option value="SN">セネガル</option>
	<option value="RS">セルビア</option>
	<option value="KN">セントクリストファー・ネイビス</option>
	<option value="LC">セントルシア</option>
	<option value="TH">タイ</option>
	<option value="TW">台湾</option>
	<option value="TJ">タジキスタン</option>
	<option value="TZ">タンザニア</option>
	<option value="CZ">チェコ</option>
	<option value="TD">チャド</option>
	<option value="CF">中央アフリカ共和国</option>
	<option value="CN">中国</option>
	<option value="TN">チュニジア</option>
	<option value="CL">チリ</option>
	<option value="DK">デンマーク</option>
	<option value="DE">ドイツ</option>
	<option value="TG">トーゴ</option>
	<option value="DO">ドミニカ共和国</option>
	<option value="TT">トリニダード・トバゴ</option>
	<option value="TR">トルコ</option>
	<option value="NG">ナイジェリア</option>
	<option value="NI">ニカラグア</option>
	<option value="NE">ニジェール</option>
	<option value="NZ">ニュージーランド</option>
	<option value="NP">ネパール</option>
	<option value="NO">ノルウェー</option>
	<option value="HT">ハイチ</option>
	<option value="PK">パキスタン</option>
	<option value="PA">パナマ</option>
	<option value="BS">バハマ</option>
	<option value="PG">パプアニューギニア</option>
	<option value="BM">バミューダ</option>
	<option value="PW">パラオ</option>
	<option value="PY">パラグアイ</option>
	<option value="BB">バルバドス</option>
	<option value="PS">パレスチナ</option>
	<option value="HU">ハンガリー</option>
	<option value="BD">バングラデシュ</option>
	<option value="TL">東ティモール</option>
	<option value="FJ">フィジー</option>
	<option value="PH">フィリピン</option>
	<option value="FI">フィンランド</option>
	<option value="BT">ブータン</option>
	<option value="PR">プエルトリコ</option>
	<option value="BR">ブラジル</option>
	<option value="FR">フランス</option>
	<option value="PF">フランス領ポリネシア</option>
	<option value="BG">ブルガリア</option>
	<option value="BF">ブルキナファソ</option>
	<option value="BN">ブルネイ</option>
	<option value="BI">ブルンジ</option>
	<option value="VN">ベトナム</option>
	<option value="BJ">ベナン</option>
	<option value="VE">ベネズエラ</option>
	<option value="BY">ベラルーシ</option>
	<option value="PE">ペルー</option>
	<option value="BE">ベルギー</option>
	<option value="PL">ポーランド</option>
	<option value="BA">ボスニア・ヘルツェゴビナ</option>
	<option value="BW">ボツワナ</option>
	<option value="HK">香港</option>
	<option value="MH">マーシャル諸島</option>
	<option value="MO">マカオ</option>
	<option value="MG">マダガスカル</option>
	<option value="MW">マラウイ</option>
	<option value="ML">マリ</option>
	<option value="MT">マルタ</option>
	<option value="MY">マレーシア</option>
	<option value="ZA">南アフリカ</option>
	<option value="MM">ミャンマー</option>
	<option value="MX">メキシコ</option>
	<option value="MU">モーリシャス</option>
	<option value="MR">モーリタニア</option>
	<option value="MZ">モザンビーク</option>
	<option value="MC">モナコ</option>
	<option value="MV">モルディヴ</option>
	<option value="MA">モロッコ</option>
	<option value="MN">モンゴル</option>
	<option value="ME">モンテネグロ</option>
	<option value="JO">ヨルダン</option>
	<option value="LA">ラオス人民民主共和国</option>
	<option value="LV">ラトビア</option>
	<option value="LT">リトアニア</option>
	<option value="LY">リビア</option>
	<option value="LR">リベリア</option>
	<option value="RO">ルーマニア</option>
	<option value="LU">ルクセンブルク</option>
	<option value="RW">ルワンダ</option>
	<option value="LB">レバノン</option>
	<option value="RU">ロシア</option>
</select>
													</li>
													<li>
														
														<div class="numberRowWrap">
															<div class="block">
																<span class="visuallyHidden">国番号</span>
																<span class="countryCode viewInput js-countryCode"></span>
															</div>
															
															<div class="block"><label for="contactsSms:0:recipientSmsEachPersonSelection:flightStatusNotificationContactPointSmsDescription" class="visuallyHidden">電話番号</label><input id="contactsSms:0:recipientSmsEachPersonSelection:flightStatusNotificationContactPointSmsDescription" type="text" name="contactsSms:0:recipientSmsEachPersonSelection:flightStatusNotificationContactPointSmsDescription" class="telNumber" placeholder="例：9012345678" maxlength="30" aria-describedby="confirmTelNumberForRepresentative:0" />
															</div>
														</div>
													</li>
												</ul>
											</div>
										</li>
									</ul>
							</fieldset>
						</dd>
					</dl>
			</div>
	</div>
	</div>
	
	<div class="formInput articleLine">
		<h3>滞在中の連絡先(任意)
		</h3>
		<dl>
			
			<dt><label for="accommodationOne">現地連絡先1</label>
			</dt>
			<dd><input id="accommodationOne" type="text" name="accommodationOne" class="widthLarge" placeholder="例：09012345678 ABC HOTEL" maxlength="28" title="例：09012345678 ABC HOTEL" />
			</dd>
		</dl>
		<dl>
			
			<dt><label for="accommodationTwo">現地連絡先2</label>
			</dt>
			<dd><input id="accommodationTwo" type="text" name="accommodationTwo" class="widthLarge" placeholder="例：09012345678 ABC HOTEL" maxlength="28" title="例：09012345678 ABC HOTEL" />
			</dd>
		</dl>
	</div>
		</div>
		
		<p class="btnAreaSubmit btnArrowNext "><input id="next" type="submit" name="next" value="次へ" class="btnBase btnMainStream btnVerticalMain btnWidthVariable" onclick="return Asw.Dialog.getInstance('prebookConfirmDialog').toggle(event);return Asw.LoadingWindow.open(&quot;NORMAL&quot;, event)" />
		</p><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:2" value="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" autocomplete="off" />
</form><div id="cmnDynamicMessages" class="infoBoxSection">
				<dl class="infoBox importantInfo jsAccordionSwitch">
					<dt><strong>重要なご案内</strong></dt>
					<dd>
						<ul class="jsAccordionSwitchList" id="informationMessages">
								<li>個人情報の利用目的<a href="https://www.ana.co.jp/other/int/meta/0012.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="ANAプライバシーポリシー(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">ANAプライバシーポリシー<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei22f/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>を必ずお読みいただき、内容に同意いただいた上で、お進みください。</li>
								<li><a href="https://www.ana.co.jp/other/int/meta/0058.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="Secure Flight Programについて(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">米国Secure Flightプログラム<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei22f/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>に基づき、パスポート名、生年月日、性別などの情報は事前に米国へ通知いたします。</li>
								<li>入力したeメールアドレス、電話番号は入国に必要な情報として米国に通知される可能性があります。</li>
									<li class="jsHiddenFlg toggleContents">お名前のスペルがパスポート名と一致しているかご確認ください。異なる場合はインターネットではお取り扱いできません。<a href="https://www.ana.co.jp/other/int/meta/intbe0001.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="ANAお問い合わせ先(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">ANA<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei22f/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>へ名前の変更をお申し出ください。</li>
									<li class="jsHiddenFlg toggleContents">二重国籍のお客様は、ご搭乗時に使用されるパスポートのお名前でご予約ください。<br />パスポートごとにお名前が異なる場合は、お客様ご自身で入国管理事務所・外務省・大使館などで各種条件をご確認のうえ、使用されるパスポートを決めてください。<br />なお、入国可否については国によって異なるため、お客様ご自身で各入国管理事務所などでご確認ください。</li>
									<li class="jsHiddenFlg toggleContents">旅行開始日時点で12歳以上の場合は大人、2歳以上12歳未満の場合は小児、2歳未満の場合は幼児の扱いとなります。なお、小児は大人と同マイル数が必要です。</li>
									<li class="jsHiddenFlg toggleContents">運航に関する情報をお客様へ通知するため、提携航空会社にもお客様のメールアドレスが提供されます。あらかじめご了承ください。</li>
									<li class="jsHiddenFlg toggleContents"><dl><dt>携帯電話へのメール送信をご希望のお客様へ</dt><dd>ドメイン指定受信をご利用の場合は、ana.co.jpとamadeus.comからのメールが受信できるようあらかじめ設定をお願いいたします。</dd></dl></li>
									<li class="jsHiddenFlg toggleContents">SMSはスマートフォンの番号に配信されます。一部の国ではSMSでのご案内をご利用いただけません。詳細は<a href="https://www.ana.co.jp/other/int/meta/0737.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="ショートメッセージ(SMS)でのご案内対象外の国について(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">ショートメッセージ(SMS)でのご案内対象外の国について<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei22f/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>をご覧ください。</li>
									<li class="jsHiddenFlg toggleContents">ログインされている会員ご本人が搭乗しない場合は、再度フライト検索画面より「ログインされている会員ご本人は搭乗しない」のチェックボックスを選択してお手続きください。</li>
						</ul>
							<a href="#" class="toggleSwitch jsTriggerSwitch" role="button" aria-controls="informationMessages" aria-expanded="false"><img src="https://aswbe-i.ana.co.jp/rei22f/international_asw/contents/cmn/image/toggle_open.png?7bec38a" alt="開閉ボタン" height="25" width="25" class="jsRollOver" />
							</a>
					</dd>
				</dl></div>
	<!--=========== /main =============-->
	</div>

	<!--=========== Script ============-->
	<script type="text/javascript">
	$(window).on('load', function() {
		// アコーディオンのメソッドと設定
		Asw.AccordionInfo('.jsAccordionSwitch', {
			'triggerSwitch': '.jsAccordionSwitch .jsTriggerSwitch',
			'listClass': '.jsAccordionSwitchList',
			'visibilityHidden': 'toggleContents'
		});
	});
	</script>
	<!--=========== Script ============-->
	<script type="text/javascript">
		//プラットフォーム判別（サマリーエリア追従用として）
		var Asw = Asw || {};
	
	  	Asw.ClientInfo = Asw.ClientInfo  || {};
	  	Asw.ClientInfo.DeviceType = Asw.ClientInfo.DeviceType || {};
	
	  	Asw.ClientInfo.DeviceType.PC = 'PC';
	  	Asw.ClientInfo.DeviceType.SP = 'SP';
	  	Asw.ClientInfo.DeviceType.TC = 'TC';
	
	  	Asw.ClientInfo.deviceType = Asw.ClientInfo.DeviceType.PC;
	
	 	if((navigator.userAgent.indexOf('Android') > 0 && navigator.userAgent.indexOf('Mobile') == -1) || navigator.userAgent.indexOf('iPad') > 0){
	    	Asw.ClientInfo.deviceType = Asw.ClientInfo.DeviceType.TC;
	  	}
	</script>
	<!--=========== /Script ============--><div id="summaryArea">
			<div class="flightSummaryArea">
						<div class="flightSummarySection">
							<h2>区間 1
							</h2>
							<p class="flightDate"><em>6</em>月<em class="hasLeft">28</em>日（土）
							</p>
							<div class="flightSummaryRoute">
									<div class="flightSummaryRouteSection">
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation1_1">
													<span>東京(成田)
													</span>
												</p>
												<p class="time">
													<span>17:00
													</span>
												</p>
											</div>
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation1_1">
													<span>シカゴ(ORD)
													</span>
												</p>
												<p class="time">
													<span>14:55
													</span>
												</p>
											</div>
										<p class="flightNumberClass">
												<span>NH012</span><span><img src="https://aswbe-i.ana.co.jp/rei22f/international_asw/contents/cmn/image/flight_ana_summaryarea.png?717d3c0" alt="ANA運航便" height="11" width="41" /></span>
												<br />特典ビジネス
										</p>
									</div>
							</div>
						</div>
						<div class="flightSummarySection">
							<h2 class="midstream">区間 2
							</h2>
							<p class="flightDate"><em>7</em>月<em class="hasLeft">23</em>日（水）
							</p>
							<div class="flightSummaryRoute">
									<div class="flightSummaryRouteSection">
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation2_1">
													<span>ホノルル(オアフ島)
													</span>
												</p>
												<p class="time">
													<span>11:35
													</span>
												</p>
											</div>
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation2_1">
													<span>東京(成田)
													</span>
												</p>
												<p class="time">
													<span>14:50
																	<span>翌日
																	</span>
													</span>
												</p>
											</div>
										<p class="flightNumberClass">
												<span>NH183</span><span><img src="https://aswbe-i.ana.co.jp/rei22f/international_asw/contents/cmn/image/flight_ana_summaryarea.png?717d3c0" alt="ANA運航便" height="11" width="41" /></span>
												<br />特典エコノミー
										</p>
									</div>
							</div>
						</div>
					<div class="total">
						<p class="passenger">大人 x1
						</p>
						<div class="totalArea">
					  		<dl class="mealAmountWrap">
									<dt>有料機内食サービス
									</dt>
									<dd><em class="mealAmount">0</em><span class="currencyCode">円</span>
									</dd>
							</dl>
									<div>
										<div class="mileageAndTotal">
												<dl class="requiredMileage">
													<dt>必要マイル
													</dt>
													<dd><em class="requiredMileage">74,000</em><span class="currencyCode">マイル</span>
													</dd>
												</dl>
												<dl>
													<dt>総額
													</dt>
													<dd><em class="awardTotalPayment">68,130</em>円
													</dd>
												</dl>
												<p class="includeFareAndFuelCost">※各種税金、燃油特別付加運賃等を含みます。
												</p>
										</div>
									</div>
						</div>
					</div>
				
			</div></div>
		<!--===== /cmnWrapper =====-->
		</div>
			
		<span id="message" class="visuallyHidden" role="alert" aria-live="assertive"></span>
		<span id="politeMessage" class="visuallyHidden" role="log" aria-live="polite"></span><script type="text/javascript">
		var Asw = Asw || {};
		Asw.BaseOutput = {"sessionID":"eJuLV3l2MG","language":"ja","siteCatalystLanguageCode":"J","operationDate":"20250620","operationDateTime":"20250620121748","office":{"officeCode":"TYONH08ZZ","country2letterCode":"JP"},"bookingType":"A","pageID":"A04_P01","siteCatalystPageName":"INT_BE_AWARD_J_A04特典予約_P01旅客情報入力（必須情報）","device":{"deviceType":"PC"}};
	</script><div id="promotionArea">
		<!--===== promotionArea =====-->
		<!--===== /promotionArea =====--></div>
		<!--=========== /cmnContainer ============--></div><div id="cmnBaloonArea" aria-live="polite"></div><div id="cmnPopupArea" aria-live="polite"></div>

	

	<div id="cmnFooterWrapper">
		<!--=========== .cmnFooterWrapper ============-->
		<div id="cmnFooter">
			<!--===== cmnFooter =====-->
			<ul>
				<li><a href="https://www.ana.co.jp/other/int/meta/0012.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">プライバシーポリシー<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22f/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
					<li><a href="https://www.ana.co.jp/other/int/meta/0670.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">利用者情報の外部送信について<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22f/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
					</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/0052.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">サイト利用規約<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22f/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/be0238.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">推奨環境<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22f/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/0133.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">サイトマップ<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22f/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/0732.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">運送約款<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22f/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
					<li class="lastChild"><a href="https://www.ana.co.jp/other/int/meta/0556.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">ウェブアクセシビリティについて<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22f/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
					</li>
			</ul>
			<p class="blankText" aria-hidden="true"><img src="https://aswbe-i.ana.co.jp/rei22f/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="" height="10" width="10" />外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。</p>
			<p class="copyright"><span lang="en" xml:lang="en">Copyright&#169;ANA</span>
			</p>
			<p class="starAllianceLogo"><a href="http://www.staralliance.com/ja/" target="_blank"><img src="https://aswbe-i.ana.co.jp/rei22f/international_asw/contents/cmn/image/star_alliance_logo.png?0fe73b5" alt="A STAR ALLIANCE MEMBER" height="18" width="175" /><span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22f/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
			</p>
			<!--===== cmnFooter =====-->
		</div>
		<!--=========== /cmnFooterWrapper ============-->
	</div>

		
		<div id="cmnLoadingForAjax"><img src="https://aswbe-i.ana.co.jp/rei22f/international_asw/contents/cmn/image/bg_modalWindow.png?717d3c0" alt="" height="0" width="0" />
			<div class="loadingArea">
				<p class="loadingImage"><img src="https://aswbe-i.ana.co.jp/rei22f/international_asw/contents/cmn/image/loading.gif?717d3c0" alt="リクエストを処理しています...しばらくお待ちください。" height="33" width="33" />
				</p>
			</div>
		</div>
		
		
		<div id="popupAltSentence" style="display: none" aria-hidden="true">別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。</div>
	<div id="cmnSiteCatalyst" aria-hidden="true"><div id="cmnSiteCatalystParamArea"><script type="text/javascript">
				// ▼SiteCatalyst
				// SiteCatalyst code version: H.2.
				// Copyright 1997-2005 Omniture, Inc. More info available at http://www.omniture.com
				SiteCatalystReportSuites   = "INT";
				SiteCatalystCharSet        = "UTF-8";
				SiteCatalystChannel        = "BE_AWARD_J";
				SiteCatalystPageName       = "INT_BE_AWARD_J_A04\u7279\u5178\u4E88\u7D04_P01\u65C5\u5BA2\u60C5\u5831\u5165\u529B\uFF08\u5FC5\u9808\u60C5\u5831\uFF09";
				SiteCatalystERR            = "";
				SiteCatalystEventNum       = "";
				SiteCatalystPurchase       = "FALSE";
				SiteCatalystProductPrice   = "";
				SiteCatalystCurrencyCode   = "";
				SiteCatalystSeatAvailabilityINT   = "";
				SiteCatalystDepartDate     = "";
				SiteCatalystDepart         = "";
				SiteCatalystArrive         = "";
				SiteCatalystFlightNo       = "";
				SiteCatalystClass          = "";
				SiteCatalystFare           = "";
				SiteCatalystPurchaseID     = "";
				
				SiteCatalystLocater        = "";
				SiteCatalystPayment        = "";
				SiteCatalystIncome         = "";
				
				SiteCatalystPax            = "";
				SiteCatalystManipulateDay  = "";
				SiteCatalystCompartmentClass   = "";
				SiteCatalystOptionInfo     = "";
				SiteCatalystOutboundUpsellPrice   = "";
				SiteCatalystInboundUpsellPrice    = "";
				
				SiteCatalystSearchInput    = "";
				SiteCatalystSegment        = "";
				SiteCatalystOfficeCode     = "";
				SiteCatalystPassengerCount = "";
				SiteCatalystPassenger      = "";
				SiteCatalystAncillaryService = "";
				// End SiteCatalyst code version: H.2.
				// ▲SiteCatalyst
			</script></div><script type="text/javascript"><!-- if(navigator.appVersion.indexOf('MSIE')>=0)document.write(unescape('%3C')+'\!-'+'-') // --></script>
		<!--/DO NOT REMOVE/-->
		

		
		<!-- Google Tag Manager -->
		<script type="text/javascript" charset="Shift_JIS" src="https://www.ana.co.jp/common/js/GTMINTBE.js"></script>
		<!-- End Google Tag Manager -->
		<!-- Yahoo Tag Manager -->
		<script type="text/javascript" charset="Shift_JIS" src="https://www.ana.co.jp/common/js/YTMINTBE.js"></script>
		<!-- End Yahoo Tag Manager -->
		

	</div>

	

	<div class="mboxDefault"></div><script type="text/javascript">mboxCreate("ASW_common_A04_P01");</script>
			<script type="text/javascript" src="https://www.ana.co.jp/common/js/tealium/tealium.js"></script>
			<script type="text/javascript" src="//cdn.evgnet.com/beacon/allnipponairways/nonamcprd/scripts/evergage.min.js"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22f/international_asw/javax.faces.resource/jsf.js.xhtml?ln=javax.faces"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22f/international_asw/contents/cmn/script/jquery.mousewheel.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22f/international_asw/contents/cmn/script/jquery.jscrollpane.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22f/international_asw/contents/cmn/script/jquery-ui-1.10.4.custom.min.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22f/international_asw/contents/cmn/script/jquery.ui.touch-punch.min.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22f/international_asw/contents/cmn/script/asw-common.js?beb6a50"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22f/international_asw/contents/cmn/script/asw-common-pc.js?fd88d7d"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22f/international_asw/contents/cmn/script/asw-format.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22f/international_asw/contents/cmn/script/mask.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22f/international_asw/contents/cmn/script/loading.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22f/international_asw/contents/cmn/script/asw-loadingwindow-pc.js?9e233c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22f/international_asw/contents/cmn/script/asw-loadingwindow.js?51db600"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22f/international_asw/contents/cmn/script/dialog-pc.js?25d36a4"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22f/international_asw/contents/cmn/script/asw-dialog-sequencer.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22f/international_asw/contents/cmn/script/asw-event.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22f/international_asw/contents/cmn/script/asw-date-select.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22f/international_asw/contents/cmn/script/asw-calendar-pc.js?9b86b8a"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22f/international_asw/contents/cmn/script/asw-calendar.js?db624c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22f/international_asw/contents/cmn/script/heightLine.js?0e7a1ce"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22f/international_asw/contents/cmn/script/asw-summaryarea.js?b21c6d7"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22f/international_asw/contents/cmn/script/asw-fingerprints.js?99001fc"></script><script type="text/javascript">
			Asw.init("https://aswbe-i.ana.co.jp/rei22f/international_asw", "ja");
			Asw.getCalendarHolidayList = function() {return [20250721,20250811,20250915,20250923,20251013,20251103,20251123,20251124,20260101,20260112,20260211,20260223,20260320,20260429,20260503,20260504,20260505,20260506,20260720,20260811,20260921,20260922,20260923,20261012,20261103,20261123,20270101,20270111,20270211,20270223,20270321,20270322,20270429,20270503,20270504,20270505,20270719,20270811,20270920,20270923,20271011,20271103,20271123,20280101,20280110,20280211,20280223,20280320,20280429,20280503,20280504,20280505,20280717,20280811,20280918,20280922,20281009,20281103,20281123,20290101,20290108,20290211,20290212,20290223,20290320,20290429,20290430,20290503,20290504,20290505,20290716,20290811,20290917,20290923,20290924,20291008,20291103,20291123];}
			Asw.ClientInfo.deviceType = "PC";
			Asw.ClientInfo.mobileDeviceType = "UNKNOWN";
			Asw.ClientInfo.osVersion = "";
			Asw.Format.init("https://aswbe-i.ana.co.jp/rei22f/international_asw/contents/{1}/{2}/{3}?{0}");
			Asw.overwriteTargetAttributeValue("false");
		</script><script type="text/javascript">
		
		var Asw = Asw || {};
		Asw.ContactInput = Asw.ContactInput || {};
		Asw.ContactInput.Const = {
			/** data-属性名「data-type」 @constant @type {String} @default 'data-type' */
			DATA_ATTRIBUTE_NAME_TYPE: 'data-type',
			/** data-属性名「data-sms-possible」 @constant @type {String} @default 'data-sms-possible' */
			DATA_ATTRIBUTE_NAME_SMS_POSSIBLE: 'data-sms-possible',
			/** data-属性名「data-country-code」 @constant @type {String} @default 'data-country-code' */
			DATA_ATTRIBUTE_NAME_COUNTRY_CODE: 'data-country-code',
			/** data-type属性値「sms」 @constant @type {String} @default 'sms' */
			DATA_TYPE_VALUE_SMS: 'sms'
		};

		
		Asw.ContactInput.countryInfoMap = {"IS":{"internationalCallingCode":"+354","smsPossible":false},"IE":{"internationalCallingCode":"+353","smsPossible":true},"AZ":{"internationalCallingCode":"+994","smsPossible":true},"AF":{"internationalCallingCode":"+93","smsPossible":true},"US":{"internationalCallingCode":"+1","smsPossible":true},"AE":{"internationalCallingCode":"+971","smsPossible":true},"DZ":{"internationalCallingCode":"+213","smsPossible":true},"AR":{"internationalCallingCode":"+54","smsPossible":true},"AW":{"internationalCallingCode":"+297","smsPossible":true},"AL":{"internationalCallingCode":"+355","smsPossible":true},"AM":{"internationalCallingCode":"+374","smsPossible":true},"AO":{"internationalCallingCode":"+244","smsPossible":false},"AG":{"internationalCallingCode":"+1","smsPossible":true},"AD":{"internationalCallingCode":"+376","smsPossible":true},"YE":{"internationalCallingCode":"+967","smsPossible":true},"GB":{"internationalCallingCode":"+44","smsPossible":true},"IL":{"internationalCallingCode":"+972","smsPossible":true},"IT":{"internationalCallingCode":"+39","smsPossible":true},"IQ":{"internationalCallingCode":"+964","smsPossible":true},"IR":{"internationalCallingCode":"+98","smsPossible":true},"IN":{"internationalCallingCode":"+91","smsPossible":true},"ID":{"internationalCallingCode":"+62","smsPossible":true},"UG":{"internationalCallingCode":"+256","smsPossible":true},"UA":{"internationalCallingCode":"+380","smsPossible":true},"UZ":{"internationalCallingCode":"+998","smsPossible":true},"UY":{"internationalCallingCode":"+598","smsPossible":true},"EC":{"internationalCallingCode":"+593","smsPossible":true},"EG":{"internationalCallingCode":"+20","smsPossible":true},"EE":{"internationalCallingCode":"+372","smsPossible":true},"SZ":{"internationalCallingCode":"+268","smsPossible":false},"ET":{"internationalCallingCode":"+251","smsPossible":false},"ER":{"internationalCallingCode":"+291","smsPossible":true},"SV":{"internationalCallingCode":"+503","smsPossible":true},"AU":{"internationalCallingCode":"+61","smsPossible":true},"AT":{"internationalCallingCode":"+43","smsPossible":true},"OM":{"internationalCallingCode":"+968","smsPossible":true},"NL":{"internationalCallingCode":"+31","smsPossible":true},"GH":{"internationalCallingCode":"+233","smsPossible":true},"CV":{"internationalCallingCode":"+238","smsPossible":false},"GY":{"internationalCallingCode":"+592","smsPossible":true},"KZ":{"internationalCallingCode":"+7","smsPossible":true},"QA":{"internationalCallingCode":"+974","smsPossible":true},"CA":{"internationalCallingCode":"+1","smsPossible":true},"GA":{"internationalCallingCode":"+241","smsPossible":true},"CM":{"internationalCallingCode":"+237","smsPossible":true},"KR":{"internationalCallingCode":"+82","smsPossible":true},"KH":{"internationalCallingCode":"+855","smsPossible":true},"GM":{"internationalCallingCode":"+220","smsPossible":true},"KP":{"internationalCallingCode":"+850","smsPossible":false},"MK":{"internationalCallingCode":"+389","smsPossible":false},"GN":{"internationalCallingCode":"+224","smsPossible":true},"GW":{"internationalCallingCode":"+245","smsPossible":true},"CY":{"internationalCallingCode":"+357","smsPossible":true},"CU":{"internationalCallingCode":"+53","smsPossible":true},"GR":{"internationalCallingCode":"+30","smsPossible":true},"KI":{"internationalCallingCode":"+686","smsPossible":false},"KG":{"internationalCallingCode":"+996","smsPossible":true},"GT":{"internationalCallingCode":"+502","smsPossible":true},"GU":{"internationalCallingCode":"+1","smsPossible":true},"KW":{"internationalCallingCode":"+965","smsPossible":true},"KY":{"internationalCallingCode":"+1","smsPossible":true},"GD":{"internationalCallingCode":"+1","smsPossible":true},"HR":{"internationalCallingCode":"+385","smsPossible":true},"KE":{"internationalCallingCode":"+254","smsPossible":true},"CI":{"internationalCallingCode":"+225","smsPossible":true},"CR":{"internationalCallingCode":"+506","smsPossible":true},"SA":{"internationalCallingCode":"+966","smsPossible":true},"KM":{"internationalCallingCode":"+269","smsPossible":true},"CO":{"internationalCallingCode":"+57","smsPossible":true},"CG":{"internationalCallingCode":"+242","smsPossible":false},"CD":{"internationalCallingCode":"+243","smsPossible":true},"ZM":{"internationalCallingCode":"+260","smsPossible":true},"SM":{"internationalCallingCode":"+378","smsPossible":false},"SL":{"internationalCallingCode":"+232","smsPossible":true},"DJ":{"internationalCallingCode":"+253","smsPossible":false},"JM":{"internationalCallingCode":"+1","smsPossible":true},"CH":{"internationalCallingCode":"+41","smsPossible":true},"GE":{"internationalCallingCode":"+995","smsPossible":true},"SE":{"internationalCallingCode":"+46","smsPossible":true},"SY":{"internationalCallingCode":"+963","smsPossible":true},"ES":{"internationalCallingCode":"+34","smsPossible":true},"SG":{"internationalCallingCode":"+65","smsPossible":true},"LK":{"internationalCallingCode":"+94","smsPossible":true},"SK":{"internationalCallingCode":"+421","smsPossible":true},"SI":{"internationalCallingCode":"+386","smsPossible":true},"ZW":{"internationalCallingCode":"+263","smsPossible":true},"SD":{"internationalCallingCode":"+249","smsPossible":true},"KN":{"internationalCallingCode":"+1","smsPossible":true},"SR":{"internationalCallingCode":"+597","smsPossible":true},"LC":{"internationalCallingCode":"+1","smsPossible":true},"SC":{"internationalCallingCode":"+248","smsPossible":false},"SN":{"internationalCallingCode":"+221","smsPossible":true},"SB":{"internationalCallingCode":"+677","smsPossible":false},"RS":{"internationalCallingCode":"+381","smsPossible":true},"TH":{"internationalCallingCode":"+66","smsPossible":true},"TW":{"internationalCallingCode":"+886","smsPossible":true},"TJ":{"internationalCallingCode":"+992","smsPossible":true},"TZ":{"internationalCallingCode":"+255","smsPossible":true},"CZ":{"internationalCallingCode":"+420","smsPossible":true},"TD":{"internationalCallingCode":"+235","smsPossible":true},"CF":{"internationalCallingCode":"+236","smsPossible":true},"TN":{"internationalCallingCode":"+216","smsPossible":true},"CL":{"internationalCallingCode":"+56","smsPossible":true},"TV":{"internationalCallingCode":"+688","smsPossible":false},"DK":{"internationalCallingCode":"+45","smsPossible":true},"DE":{"internationalCallingCode":"+49","smsPossible":true},"TG":{"internationalCallingCode":"+228","smsPossible":true},"DO":{"internationalCallingCode":"+1","smsPossible":true},"DM":{"internationalCallingCode":"+1","smsPossible":false},"TT":{"internationalCallingCode":"+1","smsPossible":true},"TM":{"internationalCallingCode":"+993","smsPossible":false},"TR":{"internationalCallingCode":"+90","smsPossible":true},"TO":{"internationalCallingCode":"+676","smsPossible":false},"NG":{"internationalCallingCode":"+234","smsPossible":true},"NR":{"internationalCallingCode":"+674","smsPossible":false},"NA":{"internationalCallingCode":"+264","smsPossible":false},"NI":{"internationalCallingCode":"+505","smsPossible":true},"BH":{"internationalCallingCode":"+973","smsPossible":false},"NE":{"internationalCallingCode":"+227","smsPossible":true},"PK":{"internationalCallingCode":"+92","smsPossible":true},"JP":{"internationalCallingCode":"+81","smsPossible":true},"BS":{"internationalCallingCode":"+1","smsPossible":true},"NZ":{"internationalCallingCode":"+64","smsPossible":true},"BM":{"internationalCallingCode":"+1","smsPossible":true},"NP":{"internationalCallingCode":"+977","smsPossible":true},"BB":{"internationalCallingCode":"+246","smsPossible":true},"NO":{"internationalCallingCode":"+47","smsPossible":true},"HT":{"internationalCallingCode":"+509","smsPossible":true},"BD":{"internationalCallingCode":"+880","smsPossible":true},"VA":{"internationalCallingCode":"+39","smsPossible":false},"PA":{"internationalCallingCode":"+507","smsPossible":true},"VU":{"internationalCallingCode":"+678","smsPossible":false},"PG":{"internationalCallingCode":"+675","smsPossible":true},"PW":{"internationalCallingCode":"+680","smsPossible":true},"PY":{"internationalCallingCode":"+595","smsPossible":true},"PS":{"internationalCallingCode":"+970","smsPossible":true},"BR":{"internationalCallingCode":"+55","smsPossible":true},"HU":{"internationalCallingCode":"+36","smsPossible":true},"TL":{"internationalCallingCode":"+670","smsPossible":true},"BG":{"internationalCallingCode":"+359","smsPossible":true},"BF":{"internationalCallingCode":"+226","smsPossible":true},"BN":{"internationalCallingCode":"+673","smsPossible":true},"FJ":{"internationalCallingCode":"+679","smsPossible":true},"PH":{"internationalCallingCode":"+63","smsPossible":true},"FI":{"internationalCallingCode":"+358","smsPossible":true},"BT":{"internationalCallingCode":"+975","smsPossible":true},"BY":{"internationalCallingCode":"+375","smsPossible":true},"BZ":{"internationalCallingCode":"+501","smsPossible":false},"PR":{"internationalCallingCode":"+1","smsPossible":true},"BE":{"internationalCallingCode":"+32","smsPossible":true},"BA":{"internationalCallingCode":"+387","smsPossible":true},"FR":{"internationalCallingCode":"+33","smsPossible":true},"BO":{"internationalCallingCode":"+591","smsPossible":false},"PF":{"internationalCallingCode":"+689","smsPossible":true},"BI":{"internationalCallingCode":"+257","smsPossible":true},"VN":{"internationalCallingCode":"+84","smsPossible":true},"BJ":{"internationalCallingCode":"+229","smsPossible":true},"VE":{"internationalCallingCode":"+58","smsPossible":true},"PE":{"internationalCallingCode":"+51","smsPossible":true},"PL":{"internationalCallingCode":"+48","smsPossible":true},"BW":{"internationalCallingCode":"+267","smsPossible":true},"PT":{"internationalCallingCode":"+351","smsPossible":false},"HK":{"internationalCallingCode":"+852","smsPossible":true},"HN":{"internationalCallingCode":"+504","smsPossible":false},"MH":{"internationalCallingCode":"+692","smsPossible":true},"MO":{"internationalCallingCode":"+853","smsPossible":true},"MG":{"internationalCallingCode":"+261","smsPossible":true},"MW":{"internationalCallingCode":"+265","smsPossible":true},"ML":{"internationalCallingCode":"+223","smsPossible":true},"MT":{"internationalCallingCode":"+356","smsPossible":true},"MY":{"internationalCallingCode":"+60","smsPossible":true},"FM":{"internationalCallingCode":"+691","smsPossible":false},"MM":{"internationalCallingCode":"+95","smsPossible":true},"MX":{"internationalCallingCode":"+52","smsPossible":true},"MU":{"internationalCallingCode":"+230","smsPossible":true},"MR":{"internationalCallingCode":"+222","smsPossible":true},"MZ":{"internationalCallingCode":"+258","smsPossible":true},"MC":{"internationalCallingCode":"+377","smsPossible":true},"MV":{"internationalCallingCode":"+960","smsPossible":true},"MD":{"internationalCallingCode":"+373","smsPossible":false},"RO":{"internationalCallingCode":"+40","smsPossible":true},"MA":{"internationalCallingCode":"+212","smsPossible":true},"MN":{"internationalCallingCode":"+976","smsPossible":true},"ME":{"internationalCallingCode":"+382","smsPossible":true},"JO":{"internationalCallingCode":"+962","smsPossible":true},"LA":{"internationalCallingCode":"+856","smsPossible":true},"RU":{"internationalCallingCode":"+7","smsPossible":true},"LV":{"internationalCallingCode":"+371","smsPossible":true},"LT":{"internationalCallingCode":"+370","smsPossible":true},"LY":{"internationalCallingCode":"+218","smsPossible":true},"GQ":{"internationalCallingCode":"+240","smsPossible":false},"LI":{"internationalCallingCode":"+423","smsPossible":false},"LR":{"internationalCallingCode":"+231","smsPossible":true},"CN":{"internationalCallingCode":"+86","smsPossible":true},"LU":{"internationalCallingCode":"+352","smsPossible":true},"ZA":{"internationalCallingCode":"+27","smsPossible":true},"RW":{"internationalCallingCode":"+250","smsPossible":true},"LS":{"internationalCallingCode":"+266","smsPossible":false},"LB":{"internationalCallingCode":"+961","smsPossible":true}};

		$(function () {
			contactInfoInit();
		});
		
		function contactInfoInit() {
			
			
			Asw.get('representativeTelType').find('option[value="M1"]').attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_TYPE,
					Asw.ContactInput.Const.DATA_TYPE_VALUE_SMS);
			Asw.get('representativeTelCountry').children('option').each(function(i,option){
				var countryInfo = Asw.ContactInput.countryInfoMap[option.value];
				if (countryInfo === void 0) {
					
					return true;
				}
				$(option).attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_SMS_POSSIBLE, countryInfo.smsPossible);
				$(option).attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_COUNTRY_CODE, countryInfo.internationalCallingCode);
			});
			
			var smsCountrySelectSelector = 'select.js-countrySelect:not(#representativeTelCountry)'
			$(smsCountrySelectSelector).eq(0).children('option').each(function(i,option){
				var countryInfo = Asw.ContactInput.countryInfoMap[option.value];
				if (countryInfo === void 0) {
					
					return true;
				}
				$(smsCountrySelectSelector + ' option:nth-child(' + (i+1) + ')').attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_COUNTRY_CODE,
						countryInfo.internationalCallingCode);
			});
			
			var emergencyCountrySelectSelector = 'select.passengerEmergencyCountry.js-countrySelect:not(#representativeTelCountry)'
			$(emergencyCountrySelectSelector).eq(0).children('option').each(function(i,option){
				var countryInfo = Asw.ContactInput.countryInfoMap[option.value];
				if (countryInfo === void 0) {
					
					return true;
				}
				$(emergencyCountrySelectSelector + ' option:nth-child(' + (i+1) + ')').attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_COUNTRY_CODE,
						countryInfo.internationalCallingCode);
			});
		}
	</script><script type="text/javascript">
		var isInit = true;
		function changeTelType(_this, telNoInputId) {
			var $telNoInput = Asw.get(telNoInputId);
			var $telNoKind = Asw.get("representativeTelType");
			var telNo = Asw.getValueComparedToPlaceholder(telNoInputId);
			var telNoCountry = Asw.get("representativeTelCountry").val();

			if(true && !isInit) {
				if(telInputCheck(telNo, telNoCountry)) {
					if (_this.value == "M1") {
						telNo = exceptDomesticPrefix("09042504176", telNoCountry);
					} else if (_this.value == "B1") {
						telNo = exceptDomesticPrefix("03-5405-2172", telNoCountry);
					} else {
						telNo = exceptDomesticPrefix("090-4250-4176", telNoCountry);
					}
					$telNoInput.val(telNo);
				}
			}
			isInit = false;
		}
		
		function telInputCheck(telNo, telNoCountry) {
	        checkResult = false;
	        
	        var amcHndyTelNo = exceptDomesticPrefix("09042504176", telNoCountry);
	        var amcCmpTelNo = exceptDomesticPrefix("03-5405-2172", telNoCountry);
	        var amcTelNo = exceptDomesticPrefix("090-4250-4176", telNoCountry);
	
	        if(telNo == "" || telNo == amcHndyTelNo || telNo == amcCmpTelNo || telNo == amcTelNo) {
	        	checkResult = true;
	        }
	        return checkResult;
    	}
    	
    	function exceptDomesticPrefix(telNo, telNoCountry) {
    		if(telNoCountry == 'JP') {
    			if(telNo.substr(0, 1) === '0') {
    				return telNo.slice(1);
    			}
    		}
    		return telNo;
    	}
	</script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22f/international_asw/contents/cmn/script/asw-customerinfomationform.js?6e91953"></script><script type="text/javascript">
		$(document).ready(function(){
				Asw.enableTextWhenCheckBox('.cascadedSwitch');
				Asw.notPasteInput('.jsNotPasteInput');
			$(function(){
				Asw.alignHeightToParentTdElement( '.passengerInfoInputArea' );
				
					Asw.alignHeightToParentTdElement( '.passengerInfoInputText' );
				
				
				$('select.js-countrySelect').trigger('change');
			});
		});
	</script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22f/international_asw/contents/cmn/script/asw-session-keeper.js?6ec0730"></script><script type="text/javascript">
			$(document).ready(function() {
				Asw.SessionKeeper.initAndStart("sessionKeeperContainer", "520", "570", "PC");
			});
			</script><script type="text/javascript">
		Asw.addAjaxCallback(function(data) {
			// アコーディオンのメソッドと設定 
			if($('.jsAccordionSwitch .jsTriggerSwitch').length > 0){
				var events = $._data($('.jsAccordionSwitch .jsTriggerSwitch').get(0), "events");
				if(events === undefined || events.click === undefined) {
					Asw.AccordionInfo('.jsAccordionSwitch', {
						'triggerSwitch': '.jsAccordionSwitch .jsTriggerSwitch',
						'listClass': '.jsAccordionSwitchList',
						'visibilityHidden': 'toggleContents'
					});
				}
			}
		});
	</script><script type="text/javascript">
	Asw.Price = Asw.Price || {};
	// 整数の区切り文字
	Asw.Price.separateString = ',';
	
	// 整数の区切り桁数
	Asw.Price.separateDigitNumber = 3;
	
	// 小数点
	Asw.Price.decimalPointString = '.';
	
	// 小数点以降の有効桁数
	Asw.Price.significantDigit = 0;
	
	// 表示金額の単位
	Asw.Price.currencyUnit = '円';
	</script><script type="text/javascript">
			$(document).ready(function() {
				$('#preLoadingArea').css('visibility', 'hidden');
			});
		</script><noscript><img src="https://aswbe-i.ana.co.jp/akam/13/pixel_7e597f4?a=dD1mYWI2MmQ2MGU4Zjc3N2MxZmQ3YTFjYjlmMTM5ZDk1YTg1NDhjZmIyJmpzPW9mZg==" style="visibility: hidden; position: absolute; left: -999px; top: -999px;" /></noscript><script type="text/javascript"  src="/Q1XS4o/Cn_ls/KfDO7/xA/t9OfhmEwSiGQaG/NG4dCQE/bw/QJBChQIQEB"></script></body>
</html>