<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="ja-<PERSON>" xml:lang="ja-<PERSON>">

	
	<!-- skey = 2025/06/19 19:05:22 rei21c roGHpkAx-p dljdmx+b90  --><head id="j_idt53">
    <base href="about:blank">
    <style>
        /* Hide elements that might try to load external resources, as a fallback. */
        img, svg, video, iframe, link[rel="stylesheet"], script { display: none !important; }
    </style>

		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
			<meta http-equiv="X-UA-Compatible" content="IE=EmulateIE9; IE=EmulateIE10" />
			<meta http-equiv="X-UA-Compatible" content="IE=7" />
			<meta http-equiv="X-UA-Compatible" content="IE=edge" />
		<meta name="description" content="" />
		<meta name="keywords" content="" />
		<meta name="format-detection" content="telephone=no" />
		<script type="text/javascript" src="/international_asw/dynatrace/ruxitagentjs_ICANVfhqrux_10273230920145353.js" data-dtconfig="rid=RID_1216906599|rpid=-**********|domain=aswbe-i.ana.co.jp|reportUrl=https://bf90419wvr.bf.dynatrace.com/bf|app=78bf0b58acf6ed13|cors=1|owasp=1|featureHash=ICANVfhqrux|xb=www^bs.google-analytics^bs.com^pana^bs.demdex^bs.net^pcollect^bs.tealiumiq^bs.com^pallnipponairways^bs.australia-3^bs.evergage^bs.com^pallnipponairwayscolt^bs.tt^bs.omtrdc^bs.net|rdnt=1|uxrgce=1|cuc=k8vpgh61|mel=100000|expw=1|dpvc=1|md=mdcc1=bAsw.PnrOutput.recordLocator,mdcc2=bAsw.PnrOutput.paymentDetailInfoList[0].fopCode,mdcc3=bAsw.PnrOutput.ticketedFlag,mdcc4=bAsw.PnrOutput.nhPnrFlag,mdcc5=bAsw.PnrOutput.itinerarySummary.departureDateYyyymmdd,mdcc6=bAsw.SearchCriteriaOutput.cffCodeInput,mdcc7=a#main ^rb div.section.sectionNoFlow ^rb table ^rb tbody ^rb tr ^rb td.flightNumber.lastCol ^rb span ^rb span:nth-child(2),mdcc8=cunique|lastModification=1750184416409|tp=500,50,0|agentUri=/international_asw/dynatrace/ruxitagentjs_ICANVfhqrux_10273230920145353.js"></script><link rel="shortcut icon" href="https://www.ana.co.jp/favicon.ico" />
		<title>お客様情報入力 | ANA</title>
		<noscript><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/css/asw_noscript_pc.css?99001fc" />
		</noscript>

		<!--  [ Adobe Target ] -->
		<script type="text/javascript" src="https://www.ana.co.jp/behavior/adobetarget/target_intbe.js"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/jquery-1.8.3.min.js?717d3c0"></script><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/ja/css/asw_initialize.css?99001fc" /><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/css/asw_screen_pc.css?99001fc" /><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/css/asw_common_pc.css?99001fc" /><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/css/asw_forminput_pc.css?99001fc" /><script type="text/javascript">
		var Asw = Asw || {};
		Asw.GlobalWord = Asw.GlobalWord || {};
		
		Asw.GlobalWord.toolTipCloseIconAltText = "閉じる";
		
		</script><script >bazadebezolkohpepadr="20054652"</script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/akam/13/1320083"  defer></script></head><body>
		
		<noscript>当サイトをご利用になるためには、JavaScript対応のブラウザが必要です。設定でJavaScriptを有効にしてください。有効にできない場合はANA電話窓口へお問い合わせください。
		</noscript>
		<div id="preLoadingArea"></div>
	
	<div id="cmnHeader" class="platinum">
		<!--===== cmnHeader =====-->
		<div class="headerWrapper">
			<div class="header">
				<p class="logo"><a href="https://www.ana.co.jp/asw/AMCTopServlet?type=a" tabindex="-1" aria-hidden="true"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/ana_logo_navy.png?717d3c0" alt="ANA Inspiration of JAPAN。リンク先はマイレージクラブトップページです。" height="28" width="274" /></a>
				</p>
					<ul class="headerMenu">
						<li><a href="https://www.ana.co.jp/asw/AMCTopServlet?type=a">ANAマイレージクラブトップページ</a>
						</li>
						<li><a href="https://www.ana.co.jp/other/int/meta/0005.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">お問い合わせ<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
						</li>
							<li class="btnLogoutArea"><a href="https://aswbe-i.ana.co.jp/rei21c/international_asw/rest/logout" role="button" class="btnBase btnLogout">ログアウト</a>
							</li>
					</ul>
			</div>
		</div>
			<div class="amcUserInfoWrapper">
				<div class="amcUserInfo">
					<div class="userNameArea">
						<table>
							<tr>	
									<td class="statusIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_status_05.png?717d3c0" alt="プラチナサービスメンバー" height="46" width="60" />
									</td>
								<td class="userName">佐藤 直樹 <span>様</span>
								</td>
							</tr>					
						</table>
					</div>
						<div class="balance">
								<dl class="upgradePointBalance">
									<dt>アップグレードポイント残高
									</dt>
									<dd>
											<dl>
												<dt>(2025年度)
												</dt>
												<dd>
													<em>20
													</em>ポイント
												</dd>
											</dl>
									</dd>
								</dl>
							<dl class="mileBalance">
								<dt>マイル口座残高
								</dt>
								<dd><em>75,663</em><span class="currencyCode">マイル</span>
								</dd>
							</dl>
						</div>
				</div>
					<p class="nowTime">6月19日19時05分現在
					</p>
			</div>
		<!--===== /cmnHeader =====-->
	</div>
		<div id="sessionKeeperContainer" class="sessionKeepingContainer"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/bg_modalWindow.png?717d3c0" alt="" height="0" width="0" />
			<div id="extendSessionExpiredDialog" class="modalExpire modalSmall modalContainer" role="dialog" aria-labelledby="extendSessionExpiredDialogTitle" aria-hidden="true">
<form id="sessionKeeperContainer:j_idt183" name="sessionKeeperContainer:j_idt183" method="post" action="https://aswbe-i.ana.co.jp/rei21c/international_asw/pages/award/reservation/award_mandatory_passenger_information_input.xhtml?aswcid=1&amp;rand=20250619190522roGHpkAx-p" autocomplete="off" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="sessionKeeperContainer:j_idt183" value="sessionKeeperContainer:j_idt183" />
<input type="hidden" name="sessionKeeperContainer:j_idt183_operationTicket" value="dljdmx+b9015f85aa932eca40815ed63b22e431~zBfkF2iMLJkqgnk7JUNVv-QoyBTZSlujdQt_NW1t!1750327512890.aere-xml-controller-67d4778877-5bbk7" /><input type="hidden" name="sessionKeeperContainer:j_idt183_cmnPageTicket" value="4" />
					<h1 id="extendSessionExpiredDialogTitle" class="dialogTitle">まもなくセッションが終了します</h1>
					<div id="extendSessionExpiredDialogMessage" class="dialogMessage" tabindex="-1">このままお手続きを進める場合は、［延長］ボタンを押してください。</div>
					<p class="modalButton"><input id="sessionKeeperContainer:cmnSessionKeepingButton" type="submit" name="sessionKeeperContainer:cmnSessionKeepingButton" value="延長" class="btnBase btnModal btnMainStream" onclick="mojarra.ab(this,event,'action',0,0,{'onevent':function(data) {Asw.doCommonAjaxCallback(data,false, event); Asw.SessionKeeper.doSessionKeep(data);},'onerror':function(data) {Asw.doCommonAjaxErrorCallback(data); }});return false" />
					</p><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:0" value="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" autocomplete="off" />
</form>
			</div>
			<div id="showSessionExpiredDialog" class="modalExpire modalSmall modalContainer" role="dialog" aria-labelledby="showSessionExpiredDialogTitle" aria-hidden="true">
				<h1 id="showSessionExpiredDialogTitle" class="dialogTitle">セッションが終了しました</h1>
				<div id="showSessionExpiredDialogMessage" class="dialogMessage" tabindex="-1">一定時間操作されなかったため、お手続きを進めることができません。お手数ですが、再度トップページからお手続きください。</div>
				<p class="modalButton">
					<input type="submit" value="閉じる" class="btnBase btnModal btnMainStream" onclick="Asw.SessionKeeper.closeTimeoutDialog(); return false;" />
				</p>
			</div>
		</div>
		<!--===== dialogMessages =====--><div id="cmnModalMessages"></div>
		<div id="prebookConfirmDialog" class="modalContainer modalConfirm modalSmall " aria-hidden="true" role="dialog">
<form id="prebookConfirmDialog:j_idt403" name="prebookConfirmDialog:j_idt403" method="post" action="https://aswbe-i.ana.co.jp/rei21c/international_asw/pages/award/reservation/award_mandatory_passenger_information_input.xhtml?aswcid=1&amp;rand=20250619190522roGHpkAx-p" autocomplete="off" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="prebookConfirmDialog:j_idt403" value="prebookConfirmDialog:j_idt403" />
<input type="hidden" name="prebookConfirmDialog:j_idt403_operationTicket" value="dljdmx+b9015f85aa932eca40815ed63b22e431~zBfkF2iMLJkqgnk7JUNVv-QoyBTZSlujdQt_NW1t!1750327512890.aere-xml-controller-67d4778877-5bbk7" /><input type="hidden" name="prebookConfirmDialog:j_idt403_cmnPageTicket" value="4" /><div class="dialogMessage" tabindex="0"><p>この後、名・姓の修正や変更ができません。</p><p>「搭乗者選択」欄の氏名が「名」「姓」の順番で表示されていることを再確認ください。</p><p>例）<strong class="highlight">空野 太郎</strong> 様 → MR <strong class="highlight">TARO SORANO</strong></p><p>保安上の理由により、航空券とパスポートのお名前が同じ表記でない場合はご搭乗いただくことができませんので必ず再度ご確認ください。</p></div>
					
					<ul class="modalBtnHorizontal">
							
							<li><input type="submit" name="prebookConfirmDialog:j_idt419" value="戻る" aria-controls="prebookConfirmDialog" class="btnBase btnModal btnCancel" onclick="Asw.Dialog.getInstance('prebookConfirmDialog').close(event, Asw.DialogSequencer.isActiveSequencerNotDisplayedLastDialog());  return false;" />
							</li>
						
						<li class="btnArrowNext"><input type="submit" name="prebookConfirmDialog:j_idt422" value="OK" aria-controls="prebookConfirmDialog" class="btnBase btnModal btnMainStream " onclick="Asw.Dialog.getInstance('prebookConfirmDialog').callOpener('NORMAL', event); return false;" />
						</li>
					</ul><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:1" value="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" autocomplete="off" />
</form>
		</div>
		<!--===== /dialogMessages =====--><div id="cmnContainer">
		<!--=========== .cmnContainer ============--><div id="cmnEmergencyNotice"></div><div id="cmnTitle" class="hgroup ">
		<h1 class="visuallyHidden">お客様情報入力</h1></div>

		<div id="cmnWrapper">
		<!--===== cmnWrapper =====-->
		<ol class="mainFlow" aria-label="完了までの手順、">
			<li><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_flow_01.png?717d3c0" alt="1" height="20" width="28" />フライト検索</li><!--
			--><li><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_flow_02_on.png?717d3c0" alt="2" height="20" width="28" /><span class="visuallyHidden">現在のステップ、</span>お客様情報入力</li><!--
			--><li><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_flow_03.png?717d3c0" alt="3" height="20" width="28" />お支払い情報</li><!--
			--><li class="flowLast"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_flow4_04.png?717d3c0" alt="4" height="20" width="28" />完了</li>
		</ol>

	<div id="main">
	<!--=========== main =============-->
		<ul class="iconExample">
				<li class="requiredIconExample">
					<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="" height="7" width="7" /></span>必須項目です	
				</li>
		</ul>
<form id="j_idt526" name="j_idt526" method="post" action="https://aswbe-i.ana.co.jp/rei21c/international_asw/pages/award/reservation/award_mandatory_passenger_information_input.xhtml?aswcid=1&amp;rand=20250619190522roGHpkAx-p" autocomplete="off" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="j_idt526" value="j_idt526" />
<input type="hidden" name="j_idt526_operationTicket" value="dljdmx+b9015f85aa932eca40815ed63b22e431~zBfkF2iMLJkqgnk7JUNVv-QoyBTZSlujdQt_NW1t!1750327512890.aere-xml-controller-67d4778877-5bbk7" /><input type="hidden" name="j_idt526_cmnPageTicket" value="4" />

<div class="section">
	<h2>搭乗者選択<a href="https://www.ana.co.jp/other/int/meta/0086.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="特典のご利用に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a></h2>
	<table class="passengerInfo">
		<thead>
			<tr>
				
				<th class="name">氏名</th>
				
				<th class="dateOfBirthWide" id="dateOfBirthHeader">生年月日</th>
				
				<th class="gender lastCol" id="genderHeader">性別<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span></th>
				
			</tr>
		</thead>
		<tbody>
				
				<tr>
					<th class="parent_01" id="parentHeader01">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:0:passenger" type="checkbox" name="passengers:0:passenger" checked="checked" data-number="1" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:0:passenger">NAOKI SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader01">1963年06月05日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader01">				
							<span class="formWrapper">
							<label for="passengers:0:gender" class="visuallyHidden">NAOKI SATOの性別</label><select id="passengers:0:gender" name="passengers:0:gender" size="1">	<option value="">選択してください</option>
	<option value="M" selected="selected">男性</option>
	<option value="F">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_02" id="parentHeader02">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:1:passenger" type="checkbox" name="passengers:1:passenger" data-number="2" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:1:passenger">REO SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader02">1995年05月18日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader02">				
							<span class="formWrapper">
							<label for="passengers:1:gender" class="visuallyHidden">REO SATOの性別</label><select id="passengers:1:gender" name="passengers:1:gender" size="1">	<option value="">選択してください</option>
	<option value="M" selected="selected">男性</option>
	<option value="F">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_03" id="parentHeader03">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:2:passenger" type="checkbox" name="passengers:2:passenger" data-number="3" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:2:passenger">ERI SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader03">1963年08月14日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader03">				
							<span class="formWrapper">
							<label for="passengers:2:gender" class="visuallyHidden">ERI SATOの性別</label><select id="passengers:2:gender" name="passengers:2:gender" size="1">	<option value="">選択してください</option>
	<option value="M">男性</option>
	<option value="F" selected="selected">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_04" id="parentHeader04">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:3:passenger" type="checkbox" name="passengers:3:passenger" data-number="4" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:3:passenger">TAISHI SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader04">1992年12月03日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader04">				
							<span class="formWrapper">
							<label for="passengers:3:gender" class="visuallyHidden">TAISHI SATOの性別</label><select id="passengers:3:gender" name="passengers:3:gender" size="1">	<option value="">選択してください</option>
	<option value="M" selected="selected">男性</option>
	<option value="F">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_05" id="parentHeader05">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:4:passenger" type="checkbox" name="passengers:4:passenger" data-number="5" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:4:passenger">HIROMI FUJIMOTO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader05">1961年11月23日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader05">				
							<span class="formWrapper">
							<label for="passengers:4:gender" class="visuallyHidden">HIROMI FUJIMOTOの性別</label><select id="passengers:4:gender" name="passengers:4:gender" size="1">	<option value="">選択してください</option>
	<option value="M">男性</option>
	<option value="F" selected="selected">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
		</tbody>
	</table>
</div>
		<div class="section">
			<h2 class="midstream">連絡先情報
			</h2>
			<h3>代表者
			</h3>
	
	
	
	<div class="formInput">
		<fieldset>
			<legend><label>代表者</label></legend>
			<dl id="contactEmailAddressInfo">
				<dt class="hasRequiredIcon">メールアドレス<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span><a href="https://www.ana.co.jp/other/int/meta/0057.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="メールの宛先と内容に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a>
				</dt>
				<dd>
					<ul class="verticalList">
						<li class="nonMarginTop"><label for="representativeEmail" class="visuallyHidden">代表者メールアドレス</label><input id="representativeEmail" type="text" name="representativeEmail" value="<EMAIL>" class="widthLarge" placeholder="例：<EMAIL>" />
						</li>
						<li>
							<span class="supplementTxt" id="confirmMailAddressForRepresentative">確認のため再度ご入力ください。
							</span><label for="confirmrepresentativeEmail" class="visuallyHidden">確認用メールアドレス</label><input id="confirmrepresentativeEmail" type="text" name="confirmrepresentativeEmail" value="<EMAIL>" class="widthLarge jsNotPasteInput" placeholder="例：<EMAIL>" aria-describedby="confirmMailAddressForRepresentative" />
						</li>
					</ul>
				</dd>
					<dd style="display:none;"><input id="updateAlwaysInfo" type="checkbox" name="updateAlwaysInfo" /><label for="updateAlwaysInfo">ログイン中の会員情報へこのメールアドレスを登録</label>
					</dd>
			</dl>
			<dl>
				<dt>電話番号<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span>
				</dt>
				<dd class="js-countryWrap">
					<ul>
						<li><label for="representativeTelType" class="visuallyHidden">電話番号種別</label><select id="representativeTelType" name="representativeTelType" class="basicWidth js-typeSelect" size="1" onchange="changeTelType(this,'representativeTel')">	<option value="M1" selected="selected">携帯</option>
	<option value="B1">会社</option>
	<option value="H1">自宅</option>
</select>
						</li>
						<li><label for="representativeTelCountry" class="visuallyHidden">電話番号の国/地区選択</label><select id="representativeTelCountry" name="representativeTelCountry" class="js-countrySelect" size="1">	<option value="" data-sms-possible="true" data-country-code="---">選択してください</option>
	<option value="JP" selected="selected" data-sms-possible="true">日本</option>
	<option value="-" data-sms-possible="true" data-country-code="---">-----------</option>
	<option value="IS" data-sms-possible="true">アイスランド</option>
	<option value="IE" data-sms-possible="true">アイルランド</option>
	<option value="AZ" data-sms-possible="true">アゼルバイジャン</option>
	<option value="AF" data-sms-possible="true">アフガニスタン</option>
	<option value="US" data-sms-possible="true">アメリカ合衆国/カナダ</option>
	<option value="AE" data-sms-possible="true">アラブ首長国連邦</option>
	<option value="DZ" data-sms-possible="true">アルジェリア</option>
	<option value="AR" data-sms-possible="true">アルゼンチン</option>
	<option value="AW" data-sms-possible="true">アルバ</option>
	<option value="AL" data-sms-possible="true">アルバニア</option>
	<option value="AM" data-sms-possible="true">アルメニア</option>
	<option value="AO" data-sms-possible="true">アンゴラ</option>
	<option value="AG" data-sms-possible="true">アンティグア・バーブーダ</option>
	<option value="AD" data-sms-possible="true">アンドラ</option>
	<option value="YE" data-sms-possible="true">イエメン</option>
	<option value="GB" data-sms-possible="true">イギリス</option>
	<option value="IL" data-sms-possible="true">イスラエル</option>
	<option value="IT" data-sms-possible="true">イタリア</option>
	<option value="IQ" data-sms-possible="true">イラク</option>
	<option value="IR" data-sms-possible="true">イラン</option>
	<option value="IN" data-sms-possible="true">インド</option>
	<option value="ID" data-sms-possible="true">インドネシア</option>
	<option value="UG" data-sms-possible="true">ウガンダ</option>
	<option value="UA" data-sms-possible="true">ウクライナ</option>
	<option value="UZ" data-sms-possible="true">ウズベキスタン</option>
	<option value="UY" data-sms-possible="true">ウルグアイ</option>
	<option value="EC" data-sms-possible="true">エクアドル</option>
	<option value="EG" data-sms-possible="true">エジプト</option>
	<option value="EE" data-sms-possible="true">エストニア</option>
	<option value="SZ" data-sms-possible="true">エスワティニ</option>
	<option value="ET" data-sms-possible="true">エチオピア</option>
	<option value="ER" data-sms-possible="true">エリトリア</option>
	<option value="SV" data-sms-possible="true">エルサルバドル</option>
	<option value="AU" data-sms-possible="true">オーストラリア</option>
	<option value="AT" data-sms-possible="true">オーストリア</option>
	<option value="OM" data-sms-possible="true">オマーン</option>
	<option value="NL" data-sms-possible="true">オランダ</option>
	<option value="GH" data-sms-possible="true">ガーナ</option>
	<option value="CV" data-sms-possible="true">カーボベルデ</option>
	<option value="GY" data-sms-possible="true">ガイアナ</option>
	<option value="KZ" data-sms-possible="true">カザフスタン</option>
	<option value="QA" data-sms-possible="true">カタール</option>
	<option value="CA" data-sms-possible="true">カナダ/アメリカ合衆国</option>
	<option value="GA" data-sms-possible="true">ガボン</option>
	<option value="CM" data-sms-possible="true">カメルーン</option>
	<option value="KR" data-sms-possible="true">韓国</option>
	<option value="GM" data-sms-possible="true">ガンビア</option>
	<option value="KH" data-sms-possible="true">カンボジア</option>
	<option value="KP" data-sms-possible="true">北朝鮮</option>
	<option value="MK" data-sms-possible="true">北マケドニア</option>
	<option value="GN" data-sms-possible="true">ギニア</option>
	<option value="GW" data-sms-possible="true">ギニアビサウ</option>
	<option value="CY" data-sms-possible="true">キプロス</option>
	<option value="CU" data-sms-possible="true">キューバ</option>
	<option value="GR" data-sms-possible="true">ギリシャ</option>
	<option value="KI" data-sms-possible="true">キリバス</option>
	<option value="KG" data-sms-possible="true">キルギス</option>
	<option value="GT" data-sms-possible="true">グアテマラ</option>
	<option value="GU" data-sms-possible="true">グアム</option>
	<option value="KW" data-sms-possible="true">クウェート</option>
	<option value="GD" data-sms-possible="true">グレナダ</option>
	<option value="HR" data-sms-possible="true">クロアチア</option>
	<option value="KY" data-sms-possible="true">ケイマン諸島</option>
	<option value="KE" data-sms-possible="true">ケニア</option>
	<option value="CI" data-sms-possible="true">コートジボワール</option>
	<option value="CR" data-sms-possible="true">コスタリカ</option>
	<option value="KM" data-sms-possible="true">コモロ</option>
	<option value="CO" data-sms-possible="true">コロンビア</option>
	<option value="CG" data-sms-possible="true">コンゴ共和国</option>
	<option value="CD" data-sms-possible="true">コンゴ民主共和国</option>
	<option value="SA" data-sms-possible="true">サウジアラビア</option>
	<option value="ZM" data-sms-possible="true">ザンビア</option>
	<option value="SM" data-sms-possible="true">サンマリノ</option>
	<option value="SL" data-sms-possible="true">シエラレオネ</option>
	<option value="DJ" data-sms-possible="true">ジブチ</option>
	<option value="JM" data-sms-possible="true">ジャマイカ</option>
	<option value="GE" data-sms-possible="true">ジョージア</option>
	<option value="SY" data-sms-possible="true">シリア</option>
	<option value="SG" data-sms-possible="true">シンガポール</option>
	<option value="ZW" data-sms-possible="true">ジンバブエ</option>
	<option value="CH" data-sms-possible="true">スイス</option>
	<option value="SE" data-sms-possible="true">スウェーデン</option>
	<option value="SD" data-sms-possible="true">スーダン</option>
	<option value="ES" data-sms-possible="true">スペイン</option>
	<option value="SR" data-sms-possible="true">スリナム</option>
	<option value="LK" data-sms-possible="true">スリランカ</option>
	<option value="SK" data-sms-possible="true">スロバキア</option>
	<option value="SI" data-sms-possible="true">スロベニア</option>
	<option value="SC" data-sms-possible="true">セイシェル</option>
	<option value="GQ" data-sms-possible="true">赤道ギニア</option>
	<option value="SN" data-sms-possible="true">セネガル</option>
	<option value="RS" data-sms-possible="true">セルビア</option>
	<option value="KN" data-sms-possible="true">セントクリストファー・ネイビス</option>
	<option value="LC" data-sms-possible="true">セントルシア</option>
	<option value="SB" data-sms-possible="true">ソロモン諸島</option>
	<option value="TH" data-sms-possible="true">タイ</option>
	<option value="TW" data-sms-possible="true">台湾</option>
	<option value="TJ" data-sms-possible="true">タジキスタン</option>
	<option value="TZ" data-sms-possible="true">タンザニア</option>
	<option value="CZ" data-sms-possible="true">チェコ</option>
	<option value="TD" data-sms-possible="true">チャド</option>
	<option value="CF" data-sms-possible="true">中央アフリカ共和国</option>
	<option value="CN" data-sms-possible="true">中国</option>
	<option value="TN" data-sms-possible="true">チュニジア</option>
	<option value="CL" data-sms-possible="true">チリ</option>
	<option value="TV" data-sms-possible="true">ツバル</option>
	<option value="DK" data-sms-possible="true">デンマーク</option>
	<option value="DE" data-sms-possible="true">ドイツ</option>
	<option value="TG" data-sms-possible="true">トーゴ</option>
	<option value="DO" data-sms-possible="true">ドミニカ共和国</option>
	<option value="DM" data-sms-possible="true">ドミニカ国</option>
	<option value="TT" data-sms-possible="true">トリニダード・トバゴ</option>
	<option value="TM" data-sms-possible="true">トルクメニスタン</option>
	<option value="TR" data-sms-possible="true">トルコ</option>
	<option value="TO" data-sms-possible="true">トンガ</option>
	<option value="NG" data-sms-possible="true">ナイジェリア</option>
	<option value="NR" data-sms-possible="true">ナウル</option>
	<option value="NA" data-sms-possible="true">ナミビア</option>
	<option value="NI" data-sms-possible="true">ニカラグア</option>
	<option value="NE" data-sms-possible="true">ニジェール</option>
	<option value="NZ" data-sms-possible="true">ニュージーランド</option>
	<option value="NP" data-sms-possible="true">ネパール</option>
	<option value="NO" data-sms-possible="true">ノルウェー</option>
	<option value="BH" data-sms-possible="true">バーレーン</option>
	<option value="HT" data-sms-possible="true">ハイチ</option>
	<option value="PK" data-sms-possible="true">パキスタン</option>
	<option value="VA" data-sms-possible="true">バチカン</option>
	<option value="PA" data-sms-possible="true">パナマ</option>
	<option value="VU" data-sms-possible="true">バヌアツ</option>
	<option value="BS" data-sms-possible="true">バハマ</option>
	<option value="PG" data-sms-possible="true">パプアニューギニア</option>
	<option value="BM" data-sms-possible="true">バミューダ</option>
	<option value="PW" data-sms-possible="true">パラオ</option>
	<option value="PY" data-sms-possible="true">パラグアイ</option>
	<option value="BB" data-sms-possible="true">バルバドス</option>
	<option value="PS" data-sms-possible="true">パレスチナ</option>
	<option value="HU" data-sms-possible="true">ハンガリー</option>
	<option value="BD" data-sms-possible="true">バングラデシュ</option>
	<option value="TL" data-sms-possible="true">東ティモール</option>
	<option value="FJ" data-sms-possible="true">フィジー</option>
	<option value="PH" data-sms-possible="true">フィリピン</option>
	<option value="FI" data-sms-possible="true">フィンランド</option>
	<option value="BT" data-sms-possible="true">ブータン</option>
	<option value="PR" data-sms-possible="true">プエルトリコ</option>
	<option value="BR" data-sms-possible="true">ブラジル</option>
	<option value="FR" data-sms-possible="true">フランス</option>
	<option value="PF" data-sms-possible="true">フランス領ポリネシア</option>
	<option value="BG" data-sms-possible="true">ブルガリア</option>
	<option value="BF" data-sms-possible="true">ブルキナファソ</option>
	<option value="BN" data-sms-possible="true">ブルネイ</option>
	<option value="BI" data-sms-possible="true">ブルンジ</option>
	<option value="VN" data-sms-possible="true">ベトナム</option>
	<option value="BJ" data-sms-possible="true">ベナン</option>
	<option value="VE" data-sms-possible="true">ベネズエラ</option>
	<option value="BY" data-sms-possible="true">ベラルーシ</option>
	<option value="BZ" data-sms-possible="true">ベリーズ</option>
	<option value="PE" data-sms-possible="true">ペルー</option>
	<option value="BE" data-sms-possible="true">ベルギー</option>
	<option value="PL" data-sms-possible="true">ポーランド</option>
	<option value="BA" data-sms-possible="true">ボスニア・ヘルツェゴビナ</option>
	<option value="BW" data-sms-possible="true">ボツワナ</option>
	<option value="BO" data-sms-possible="true">ボリビア</option>
	<option value="PT" data-sms-possible="true">ポルトガル</option>
	<option value="HK" data-sms-possible="true">香港</option>
	<option value="HN" data-sms-possible="true">ホンジュラス</option>
	<option value="MH" data-sms-possible="true">マーシャル諸島</option>
	<option value="MO" data-sms-possible="true">マカオ</option>
	<option value="MG" data-sms-possible="true">マダガスカル</option>
	<option value="MW" data-sms-possible="true">マラウイ</option>
	<option value="ML" data-sms-possible="true">マリ</option>
	<option value="MT" data-sms-possible="true">マルタ</option>
	<option value="MY" data-sms-possible="true">マレーシア</option>
	<option value="FM" data-sms-possible="true">ミクロネシア連邦</option>
	<option value="ZA" data-sms-possible="true">南アフリカ</option>
	<option value="MM" data-sms-possible="true">ミャンマー</option>
	<option value="MX" data-sms-possible="true">メキシコ</option>
	<option value="MU" data-sms-possible="true">モーリシャス</option>
	<option value="MR" data-sms-possible="true">モーリタニア</option>
	<option value="MZ" data-sms-possible="true">モザンビーク</option>
	<option value="MC" data-sms-possible="true">モナコ</option>
	<option value="MV" data-sms-possible="true">モルディヴ</option>
	<option value="MD" data-sms-possible="true">モルドバ</option>
	<option value="MA" data-sms-possible="true">モロッコ</option>
	<option value="MN" data-sms-possible="true">モンゴル</option>
	<option value="ME" data-sms-possible="true">モンテネグロ</option>
	<option value="JO" data-sms-possible="true">ヨルダン</option>
	<option value="LA" data-sms-possible="true">ラオス人民民主共和国</option>
	<option value="LV" data-sms-possible="true">ラトビア</option>
	<option value="LT" data-sms-possible="true">リトアニア</option>
	<option value="LY" data-sms-possible="true">リビア</option>
	<option value="LI" data-sms-possible="true">リヒテンシュタイン</option>
	<option value="LR" data-sms-possible="true">リベリア</option>
	<option value="RO" data-sms-possible="true">ルーマニア</option>
	<option value="LU" data-sms-possible="true">ルクセンブルク</option>
	<option value="RW" data-sms-possible="true">ルワンダ</option>
	<option value="LS" data-sms-possible="true">レソト</option>
	<option value="LB" data-sms-possible="true">レバノン</option>
	<option value="RU" data-sms-possible="true">ロシア</option>
</select>
						</li>
						<li class="numberInputWrap">
							<span id="numberInputInfo1" class="supplementTxt marginTop">先頭に0(ゼロ)がある場合、0(ゼロ)を抜いてください(一部、国・地域除く)。携帯電話番号の場合は、携帯電話を契約された国を選択ください。</span>
							<div class="numberRowWrap">
								<div class="block">
									
									<span class="visuallyHidden">国番号</span>
									<span class="viewInput widthMini js-countryCode"></span>
								</div>
								<div class="block"><label for="representativeTel" class="visuallyHidden">電話番号</label><input id="representativeTel" type="text" name="representativeTel" value="9042504176" class="widthMiddle" placeholder="例：9012345678" maxlength="30" aria-describedby="numberInputInfo1" />
								</div>
							</div>
						</li>
					</ul>
				</dd>
			</dl>
		</fieldset>
	</div>
	<div class="titleWrapper">
		<h3>
			<span class="text">eメール・SMSに関するご案内<a href="https://www.ana.co.jp/other/int/meta/0057.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="eメール・SMSに関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a></span>
		</h3>
	</div>
	<div class="formInput">
	<h4 class="formAreaTitle">主に搭乗・運航に関わるeメールの送付先設定（米国へ通知いたします）<a href="https://www.ana.co.jp/other/int/meta/0629.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="米国入国用eメール・SMS・電話番号に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a>
	</h4>
	<div class="paxSettingArea js-mailFormWrap onePax is-open">
			<div class="settingArea">
					<dl>
						<dt class="breakWord">NAOKI SATO
						</dt>
						<dd>
							<fieldset>
								<legend>NAOKI SATOのeメール送付先および米国到着時に提供するメールアドレス</legend>代表者と同じ
							</fieldset>
						</dd>
					</dl>
			</div>
	</div>
	<h4 class="formAreaTitle hasTopLine">主に搭乗・運航に関わるSMSの送付先および米国で受信できる電話番号の設定（米国へ通知いたします）<a href="https://www.ana.co.jp/other/int/meta/0629.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="米国入国用eメール・SMS・電話番号に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a>
	</h4>
	<div class="paxSettingArea js-smsFormWrap onePax is-open">
			<div class="settingArea">
					<dl>
						<dt class="breakWord">NAOKI SATO<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span>
						</dt>
						<dd>
							<fieldset>
								<legend>NAOKI SATOの1つ目の電話番号</legend>
									<ul class="verticalList js-formItem">
										<li><input type="radio" id="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSameAsRepresentativeSms:radioItem" name="contactsSms:0:recipientSmsEachPersonSelection:radioGroup" value="0" checked="checked" /><label for="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSameAsRepresentativeSms:radioItem">代表者電話番号と同じ</label>
										</li>
										<li><input type="radio" id="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSmsPerPassenger:radioItem" name="contactsSms:0:recipientSmsEachPersonSelection:radioGroup" value="1" /><label for="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSmsPerPassenger:radioItem">電話番号を指定</label>
											<div class="indentArea js-formImputArea">
												<span id="confirmTelNumberForRepresentative:0" class="supplementTxt marginTop">先頭に0(ゼロ)がある場合、0(ゼロ)を抜いてください(一部、国・地域除く)。携帯電話番号の場合は、携帯電話を契約された国を選択ください。</span>
												<ul class="countryNumberInput js-countryWrap">
													<li><label for="contactsSms:0:recipientSmsEachPersonSelection:passengerSmsCountry" class="visuallyHidden">電話番号の国/地区選択</label><select id="contactsSms:0:recipientSmsEachPersonSelection:passengerSmsCountry" name="contactsSms:0:recipientSmsEachPersonSelection:passengerSmsCountry" class="countrySelect js-countrySelect" size="1">	<option value="" data-country-code="---">選択してください</option>
	<option value="JP">日本</option>
	<option value="-" data-country-code="---">-----------</option>
	<option value="IE">アイルランド</option>
	<option value="AZ">アゼルバイジャン</option>
	<option value="AF">アフガニスタン</option>
	<option value="US">アメリカ合衆国/カナダ</option>
	<option value="AE">アラブ首長国連邦</option>
	<option value="DZ">アルジェリア</option>
	<option value="AR">アルゼンチン</option>
	<option value="AW">アルバ</option>
	<option value="AL">アルバニア</option>
	<option value="AM">アルメニア</option>
	<option value="AG">アンティグア・バーブーダ</option>
	<option value="AD">アンドラ</option>
	<option value="YE">イエメン</option>
	<option value="GB">イギリス</option>
	<option value="IL">イスラエル</option>
	<option value="IT">イタリア</option>
	<option value="IQ">イラク</option>
	<option value="IR">イラン</option>
	<option value="IN">インド</option>
	<option value="ID">インドネシア</option>
	<option value="UG">ウガンダ</option>
	<option value="UA">ウクライナ</option>
	<option value="UZ">ウズベキスタン</option>
	<option value="UY">ウルグアイ</option>
	<option value="EC">エクアドル</option>
	<option value="EG">エジプト</option>
	<option value="EE">エストニア</option>
	<option value="ER">エリトリア</option>
	<option value="SV">エルサルバドル</option>
	<option value="AU">オーストラリア</option>
	<option value="AT">オーストリア</option>
	<option value="OM">オマーン</option>
	<option value="NL">オランダ</option>
	<option value="GH">ガーナ</option>
	<option value="GY">ガイアナ</option>
	<option value="KZ">カザフスタン</option>
	<option value="QA">カタール</option>
	<option value="CA">カナダ/アメリカ合衆国</option>
	<option value="GA">ガボン</option>
	<option value="CM">カメルーン</option>
	<option value="KR">韓国</option>
	<option value="GM">ガンビア</option>
	<option value="KH">カンボジア</option>
	<option value="GN">ギニア</option>
	<option value="GW">ギニアビサウ</option>
	<option value="CY">キプロス</option>
	<option value="CU">キューバ</option>
	<option value="GR">ギリシャ</option>
	<option value="KG">キルギス</option>
	<option value="GT">グアテマラ</option>
	<option value="GU">グアム</option>
	<option value="KW">クウェート</option>
	<option value="GD">グレナダ</option>
	<option value="HR">クロアチア</option>
	<option value="KY">ケイマン諸島</option>
	<option value="KE">ケニア</option>
	<option value="CI">コートジボワール</option>
	<option value="CR">コスタリカ</option>
	<option value="KM">コモロ</option>
	<option value="CO">コロンビア</option>
	<option value="CD">コンゴ民主共和国</option>
	<option value="SA">サウジアラビア</option>
	<option value="ZM">ザンビア</option>
	<option value="SL">シエラレオネ</option>
	<option value="JM">ジャマイカ</option>
	<option value="GE">ジョージア</option>
	<option value="SY">シリア</option>
	<option value="SG">シンガポール</option>
	<option value="ZW">ジンバブエ</option>
	<option value="CH">スイス</option>
	<option value="SE">スウェーデン</option>
	<option value="SD">スーダン</option>
	<option value="ES">スペイン</option>
	<option value="SR">スリナム</option>
	<option value="LK">スリランカ</option>
	<option value="SK">スロバキア</option>
	<option value="SI">スロベニア</option>
	<option value="SN">セネガル</option>
	<option value="RS">セルビア</option>
	<option value="KN">セントクリストファー・ネイビス</option>
	<option value="LC">セントルシア</option>
	<option value="TH">タイ</option>
	<option value="TW">台湾</option>
	<option value="TJ">タジキスタン</option>
	<option value="TZ">タンザニア</option>
	<option value="CZ">チェコ</option>
	<option value="TD">チャド</option>
	<option value="CF">中央アフリカ共和国</option>
	<option value="CN">中国</option>
	<option value="TN">チュニジア</option>
	<option value="CL">チリ</option>
	<option value="DK">デンマーク</option>
	<option value="DE">ドイツ</option>
	<option value="TG">トーゴ</option>
	<option value="DO">ドミニカ共和国</option>
	<option value="TT">トリニダード・トバゴ</option>
	<option value="TR">トルコ</option>
	<option value="NG">ナイジェリア</option>
	<option value="NI">ニカラグア</option>
	<option value="NE">ニジェール</option>
	<option value="NZ">ニュージーランド</option>
	<option value="NP">ネパール</option>
	<option value="NO">ノルウェー</option>
	<option value="HT">ハイチ</option>
	<option value="PK">パキスタン</option>
	<option value="PA">パナマ</option>
	<option value="BS">バハマ</option>
	<option value="PG">パプアニューギニア</option>
	<option value="BM">バミューダ</option>
	<option value="PW">パラオ</option>
	<option value="PY">パラグアイ</option>
	<option value="BB">バルバドス</option>
	<option value="PS">パレスチナ</option>
	<option value="HU">ハンガリー</option>
	<option value="BD">バングラデシュ</option>
	<option value="TL">東ティモール</option>
	<option value="FJ">フィジー</option>
	<option value="PH">フィリピン</option>
	<option value="FI">フィンランド</option>
	<option value="BT">ブータン</option>
	<option value="PR">プエルトリコ</option>
	<option value="BR">ブラジル</option>
	<option value="FR">フランス</option>
	<option value="PF">フランス領ポリネシア</option>
	<option value="BG">ブルガリア</option>
	<option value="BF">ブルキナファソ</option>
	<option value="BN">ブルネイ</option>
	<option value="BI">ブルンジ</option>
	<option value="VN">ベトナム</option>
	<option value="BJ">ベナン</option>
	<option value="VE">ベネズエラ</option>
	<option value="BY">ベラルーシ</option>
	<option value="PE">ペルー</option>
	<option value="BE">ベルギー</option>
	<option value="PL">ポーランド</option>
	<option value="BA">ボスニア・ヘルツェゴビナ</option>
	<option value="BW">ボツワナ</option>
	<option value="HK">香港</option>
	<option value="MH">マーシャル諸島</option>
	<option value="MO">マカオ</option>
	<option value="MG">マダガスカル</option>
	<option value="MW">マラウイ</option>
	<option value="ML">マリ</option>
	<option value="MT">マルタ</option>
	<option value="MY">マレーシア</option>
	<option value="ZA">南アフリカ</option>
	<option value="MM">ミャンマー</option>
	<option value="MX">メキシコ</option>
	<option value="MU">モーリシャス</option>
	<option value="MR">モーリタニア</option>
	<option value="MZ">モザンビーク</option>
	<option value="MC">モナコ</option>
	<option value="MV">モルディヴ</option>
	<option value="MA">モロッコ</option>
	<option value="MN">モンゴル</option>
	<option value="ME">モンテネグロ</option>
	<option value="JO">ヨルダン</option>
	<option value="LA">ラオス人民民主共和国</option>
	<option value="LV">ラトビア</option>
	<option value="LT">リトアニア</option>
	<option value="LY">リビア</option>
	<option value="LR">リベリア</option>
	<option value="RO">ルーマニア</option>
	<option value="LU">ルクセンブルク</option>
	<option value="RW">ルワンダ</option>
	<option value="LB">レバノン</option>
	<option value="RU">ロシア</option>
</select>
													</li>
													<li>
														
														<div class="numberRowWrap">
															<div class="block">
																<span class="visuallyHidden">国番号</span>
																<span class="countryCode viewInput js-countryCode"></span>
															</div>
															
															<div class="block"><label for="contactsSms:0:recipientSmsEachPersonSelection:flightStatusNotificationContactPointSmsDescription" class="visuallyHidden">電話番号</label><input id="contactsSms:0:recipientSmsEachPersonSelection:flightStatusNotificationContactPointSmsDescription" type="text" name="contactsSms:0:recipientSmsEachPersonSelection:flightStatusNotificationContactPointSmsDescription" class="telNumber" placeholder="例：9012345678" maxlength="30" aria-describedby="confirmTelNumberForRepresentative:0" />
															</div>
														</div>
													</li>
												</ul>
											</div>
										</li>
									</ul>
							</fieldset>
						</dd>
					</dl>
			</div>
	</div>
	</div>
	
	<div class="formInput articleLine">
		<h3>滞在中の連絡先(任意)
		</h3>
		<dl>
			
			<dt><label for="accommodationOne">現地連絡先1</label>
			</dt>
			<dd><input id="accommodationOne" type="text" name="accommodationOne" class="widthLarge" placeholder="例：09012345678 ABC HOTEL" maxlength="28" title="例：09012345678 ABC HOTEL" />
			</dd>
		</dl>
		<dl>
			
			<dt><label for="accommodationTwo">現地連絡先2</label>
			</dt>
			<dd><input id="accommodationTwo" type="text" name="accommodationTwo" class="widthLarge" placeholder="例：09012345678 ABC HOTEL" maxlength="28" title="例：09012345678 ABC HOTEL" />
			</dd>
		</dl>
	</div>
		</div>
		
		<p class="btnAreaSubmit btnArrowNext "><input id="next" type="submit" name="next" value="次へ" class="btnBase btnMainStream btnVerticalMain btnWidthVariable" onclick="return Asw.Dialog.getInstance('prebookConfirmDialog').toggle(event);return Asw.LoadingWindow.open(&quot;NORMAL&quot;, event)" />
		</p><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:2" value="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" autocomplete="off" />
</form><div id="cmnDynamicMessages" class="infoBoxSection">
				<dl class="infoBox importantInfo jsAccordionSwitch">
					<dt><strong>重要なご案内</strong></dt>
					<dd>
						<ul class="jsAccordionSwitchList" id="informationMessages">
								<li>個人情報の利用目的<a href="https://www.ana.co.jp/other/int/meta/0012.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="ANAプライバシーポリシー(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">ANAプライバシーポリシー<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>を必ずお読みいただき、内容に同意いただいた上で、お進みください。</li>
								<li><a href="https://www.ana.co.jp/other/int/meta/0058.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="Secure Flight Programについて(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">米国Secure Flightプログラム<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>に基づき、パスポート名、生年月日、性別などの情報は事前に米国へ通知いたします。</li>
								<li>入力したeメールアドレス、電話番号は入国に必要な情報として米国に通知される可能性があります。</li>
									<li class="jsHiddenFlg toggleContents">お名前のスペルがパスポート名と一致しているかご確認ください。異なる場合はインターネットではお取り扱いできません。<a href="https://www.ana.co.jp/other/int/meta/intbe0001.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="ANAお問い合わせ先(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">ANA<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>へ名前の変更をお申し出ください。</li>
									<li class="jsHiddenFlg toggleContents">二重国籍のお客様は、ご搭乗時に使用されるパスポートのお名前でご予約ください。<br />パスポートごとにお名前が異なる場合は、お客様ご自身で入国管理事務所・外務省・大使館などで各種条件をご確認のうえ、使用されるパスポートを決めてください。<br />なお、入国可否については国によって異なるため、お客様ご自身で各入国管理事務所などでご確認ください。</li>
									<li class="jsHiddenFlg toggleContents">旅行開始日時点で12歳以上の場合は大人、2歳以上12歳未満の場合は小児、2歳未満の場合は幼児の扱いとなります。なお、小児は大人と同マイル数が必要です。</li>
									<li class="jsHiddenFlg toggleContents">運航に関する情報をお客様へ通知するため、提携航空会社にもお客様のメールアドレスが提供されます。あらかじめご了承ください。</li>
									<li class="jsHiddenFlg toggleContents"><dl><dt>携帯電話へのメール送信をご希望のお客様へ</dt><dd>ドメイン指定受信をご利用の場合は、ana.co.jpとamadeus.comからのメールが受信できるようあらかじめ設定をお願いいたします。</dd></dl></li>
									<li class="jsHiddenFlg toggleContents">SMSはスマートフォンの番号に配信されます。一部の国ではSMSでのご案内をご利用いただけません。詳細は<a href="https://www.ana.co.jp/other/int/meta/0737.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="ショートメッセージ(SMS)でのご案内対象外の国について(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">ショートメッセージ(SMS)でのご案内対象外の国について<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>をご覧ください。</li>
									<li class="jsHiddenFlg toggleContents">ログインされている会員ご本人が搭乗しない場合は、再度フライト検索画面より「ログインされている会員ご本人は搭乗しない」のチェックボックスを選択してお手続きください。</li>
						</ul>
							<a href="#" class="toggleSwitch jsTriggerSwitch" role="button" aria-controls="informationMessages" aria-expanded="false"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/toggle_open.png?7bec38a" alt="開閉ボタン" height="25" width="25" class="jsRollOver" />
							</a>
					</dd>
				</dl></div>
	<!--=========== /main =============-->
	</div>

	<!--=========== Script ============-->
	<script type="text/javascript">
	$(window).on('load', function() {
		// アコーディオンのメソッドと設定
		Asw.AccordionInfo('.jsAccordionSwitch', {
			'triggerSwitch': '.jsAccordionSwitch .jsTriggerSwitch',
			'listClass': '.jsAccordionSwitchList',
			'visibilityHidden': 'toggleContents'
		});
	});
	</script>
	<!--=========== Script ============-->
	<script type="text/javascript">
		//プラットフォーム判別（サマリーエリア追従用として）
		var Asw = Asw || {};
	
	  	Asw.ClientInfo = Asw.ClientInfo  || {};
	  	Asw.ClientInfo.DeviceType = Asw.ClientInfo.DeviceType || {};
	
	  	Asw.ClientInfo.DeviceType.PC = 'PC';
	  	Asw.ClientInfo.DeviceType.SP = 'SP';
	  	Asw.ClientInfo.DeviceType.TC = 'TC';
	
	  	Asw.ClientInfo.deviceType = Asw.ClientInfo.DeviceType.PC;
	
	 	if((navigator.userAgent.indexOf('Android') > 0 && navigator.userAgent.indexOf('Mobile') == -1) || navigator.userAgent.indexOf('iPad') > 0){
	    	Asw.ClientInfo.deviceType = Asw.ClientInfo.DeviceType.TC;
	  	}
	</script>
	<!--=========== /Script ============--><div id="summaryArea">
			<div class="flightSummaryArea">
						<div class="flightSummarySection">
							<h2>区間 1
							</h2>
							<p class="flightDate"><em>6</em>月<em class="hasLeft">24</em>日（火）
							</p>
							<div class="flightSummaryRoute">
									<div class="flightSummaryRouteSection">
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation1_1">
													<span>東京(成田)
													</span>
												</p>
												<p class="time">
													<span>17:00
													</span>
												</p>
											</div>
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation1_1">
													<span>シカゴ(ORD)
													</span>
												</p>
												<p class="time">
													<span>14:55
													</span>
												</p>
											</div>
										<p class="flightNumberClass">
												<span>NH012</span><span><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/flight_ana_summaryarea.png?717d3c0" alt="ANA運航便" height="11" width="41" /></span>
												<br />特典ビジネス
										</p>
									</div>
							</div>
						</div>
						<div class="flightSummarySection">
							<h2 class="midstream">区間 2
							</h2>
							<p class="flightDate"><em>7</em>月<em class="hasLeft">23</em>日（水）
							</p>
							<div class="flightSummaryRoute">
									<div class="flightSummaryRouteSection">
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation2_1">
													<span>ホノルル(オアフ島)
													</span>
												</p>
												<p class="time">
													<span>11:35
													</span>
												</p>
											</div>
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation2_1">
													<span>東京(成田)
													</span>
												</p>
												<p class="time">
													<span>14:50
																	<span>翌日
																	</span>
													</span>
												</p>
											</div>
										<p class="flightNumberClass">
												<span>NH183</span><span><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/flight_ana_summaryarea.png?717d3c0" alt="ANA運航便" height="11" width="41" /></span>
												<br />特典エコノミー
										</p>
									</div>
							</div>
						</div>
					<div class="total">
						<p class="passenger">大人 x1
						</p>
						<div class="totalArea">
					  		<dl class="mealAmountWrap">
									<dt>有料機内食サービス
									</dt>
									<dd><em class="mealAmount">0</em><span class="currencyCode">円</span>
									</dd>
							</dl>
									<div>
										<div class="mileageAndTotal">
												<dl class="requiredMileage">
													<dt>必要マイル
													</dt>
													<dd><em class="requiredMileage">74,000</em><span class="currencyCode">マイル</span>
													</dd>
												</dl>
												<dl>
													<dt>総額
													</dt>
													<dd><em class="awardTotalPayment">68,130</em>円
													</dd>
												</dl>
												<p class="includeFareAndFuelCost">※各種税金、燃油特別付加運賃等を含みます。
												</p>
										</div>
									</div>
						</div>
					</div>
				
			</div></div>
		<!--===== /cmnWrapper =====-->
		</div>
			
		<span id="message" class="visuallyHidden" role="alert" aria-live="assertive"></span>
		<span id="politeMessage" class="visuallyHidden" role="log" aria-live="polite"></span><script type="text/javascript">
		var Asw = Asw || {};
		Asw.BaseOutput = {"sessionID":"roGHpkAx-p","language":"ja","siteCatalystLanguageCode":"J","operationDate":"20250619","operationDateTime":"20250619190522","office":{"officeCode":"TYONH08ZZ","country2letterCode":"JP"},"bookingType":"A","pageID":"A04_P01","siteCatalystPageName":"INT_BE_AWARD_J_A04特典予約_P01旅客情報入力（必須情報）","device":{"deviceType":"PC"}};
	</script><div id="promotionArea">
		<!--===== promotionArea =====-->
		<!--===== /promotionArea =====--></div>
		<!--=========== /cmnContainer ============--></div><div id="cmnBaloonArea" aria-live="polite"></div><div id="cmnPopupArea" aria-live="polite"></div>

	

	<div id="cmnFooterWrapper">
		<!--=========== .cmnFooterWrapper ============-->
		<div id="cmnFooter">
			<!--===== cmnFooter =====-->
			<ul>
				<li><a href="https://www.ana.co.jp/other/int/meta/0012.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">プライバシーポリシー<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
					<li><a href="https://www.ana.co.jp/other/int/meta/0670.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">利用者情報の外部送信について<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
					</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/0052.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">サイト利用規約<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/be0238.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">推奨環境<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/0133.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">サイトマップ<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/0732.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">運送約款<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
					<li class="lastChild"><a href="https://www.ana.co.jp/other/int/meta/0556.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">ウェブアクセシビリティについて<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
					</li>
			</ul>
			<p class="blankText" aria-hidden="true"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="" height="10" width="10" />外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。</p>
			<p class="copyright"><span lang="en" xml:lang="en">Copyright&#169;ANA</span>
			</p>
			<p class="starAllianceLogo"><a href="http://www.staralliance.com/ja/" target="_blank"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/star_alliance_logo.png?0fe73b5" alt="A STAR ALLIANCE MEMBER" height="18" width="175" /><span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
			</p>
			<!--===== cmnFooter =====-->
		</div>
		<!--=========== /cmnFooterWrapper ============-->
	</div>

		
		<div id="cmnLoadingForAjax"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/bg_modalWindow.png?717d3c0" alt="" height="0" width="0" />
			<div class="loadingArea">
				<p class="loadingImage"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/loading.gif?717d3c0" alt="リクエストを処理しています...しばらくお待ちください。" height="33" width="33" />
				</p>
			</div>
		</div>
		
		
		<div id="popupAltSentence" style="display: none" aria-hidden="true">別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。</div>
	<div id="cmnSiteCatalyst" aria-hidden="true"><div id="cmnSiteCatalystParamArea"><script type="text/javascript">
				// ▼SiteCatalyst
				// SiteCatalyst code version: H.2.
				// Copyright 1997-2005 Omniture, Inc. More info available at http://www.omniture.com
				SiteCatalystReportSuites   = "INT";
				SiteCatalystCharSet        = "UTF-8";
				SiteCatalystChannel        = "BE_AWARD_J";
				SiteCatalystPageName       = "INT_BE_AWARD_J_A04\u7279\u5178\u4E88\u7D04_P01\u65C5\u5BA2\u60C5\u5831\u5165\u529B\uFF08\u5FC5\u9808\u60C5\u5831\uFF09";
				SiteCatalystERR            = "";
				SiteCatalystEventNum       = "";
				SiteCatalystPurchase       = "FALSE";
				SiteCatalystProductPrice   = "";
				SiteCatalystCurrencyCode   = "";
				SiteCatalystSeatAvailabilityINT   = "";
				SiteCatalystDepartDate     = "";
				SiteCatalystDepart         = "";
				SiteCatalystArrive         = "";
				SiteCatalystFlightNo       = "";
				SiteCatalystClass          = "";
				SiteCatalystFare           = "";
				SiteCatalystPurchaseID     = "";
				
				SiteCatalystLocater        = "";
				SiteCatalystPayment        = "";
				SiteCatalystIncome         = "";
				
				SiteCatalystPax            = "";
				SiteCatalystManipulateDay  = "";
				SiteCatalystCompartmentClass   = "";
				SiteCatalystOptionInfo     = "";
				SiteCatalystOutboundUpsellPrice   = "";
				SiteCatalystInboundUpsellPrice    = "";
				
				SiteCatalystSearchInput    = "";
				SiteCatalystSegment        = "";
				SiteCatalystOfficeCode     = "";
				SiteCatalystPassengerCount = "";
				SiteCatalystPassenger      = "";
				SiteCatalystAncillaryService = "";
				// End SiteCatalyst code version: H.2.
				// ▲SiteCatalyst
			</script></div><script type="text/javascript"><!-- if(navigator.appVersion.indexOf('MSIE')>=0)document.write(unescape('%3C')+'\!-'+'-') // --></script>
		<!--/DO NOT REMOVE/-->
		

		
		<!-- Google Tag Manager -->
		<script type="text/javascript" charset="Shift_JIS" src="https://www.ana.co.jp/common/js/GTMINTBE.js"></script>
		<!-- End Google Tag Manager -->
		<!-- Yahoo Tag Manager -->
		<script type="text/javascript" charset="Shift_JIS" src="https://www.ana.co.jp/common/js/YTMINTBE.js"></script>
		<!-- End Yahoo Tag Manager -->
		

	</div>

	

	<div class="mboxDefault"></div><script type="text/javascript">mboxCreate("ASW_common_A04_P01");</script>
			<script type="text/javascript" src="https://www.ana.co.jp/common/js/tealium/tealium.js"></script>
			<script type="text/javascript" src="//cdn.evgnet.com/beacon/allnipponairways/nonamcprd/scripts/evergage.min.js"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/javax.faces.resource/jsf.js.xhtml?ln=javax.faces"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/jquery.mousewheel.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/jquery.jscrollpane.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/jquery-ui-1.10.4.custom.min.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/jquery.ui.touch-punch.min.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-common.js?beb6a50"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-common-pc.js?fd88d7d"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-format.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/mask.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/loading.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-loadingwindow-pc.js?9e233c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-loadingwindow.js?51db600"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/dialog-pc.js?25d36a4"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-dialog-sequencer.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-event.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-date-select.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-calendar-pc.js?9b86b8a"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-calendar.js?db624c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/heightLine.js?0e7a1ce"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-summaryarea.js?b21c6d7"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-fingerprints.js?99001fc"></script><script type="text/javascript">
			Asw.init("https://aswbe-i.ana.co.jp/rei21c/international_asw", "ja");
			Asw.getCalendarHolidayList = function() {return [20250721,20250811,20250915,20250923,20251013,20251103,20251123,20251124,20260101,20260112,20260211,20260223,20260320,20260429,20260503,20260504,20260505,20260506,20260720,20260811,20260921,20260922,20260923,20261012,20261103,20261123,20270101,20270111,20270211,20270223,20270321,20270322,20270429,20270503,20270504,20270505,20270719,20270811,20270920,20270923,20271011,20271103,20271123,20280101,20280110,20280211,20280223,20280320,20280429,20280503,20280504,20280505,20280717,20280811,20280918,20280922,20281009,20281103,20281123,20290101,20290108,20290211,20290212,20290223,20290320,20290429,20290430,20290503,20290504,20290505,20290716,20290811,20290917,20290923,20290924,20291008,20291103,20291123];}
			Asw.ClientInfo.deviceType = "PC";
			Asw.ClientInfo.mobileDeviceType = "UNKNOWN";
			Asw.ClientInfo.osVersion = "";
			Asw.Format.init("https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/{1}/{2}/{3}?{0}");
			Asw.overwriteTargetAttributeValue("false");
		</script><script type="text/javascript">
		
		var Asw = Asw || {};
		Asw.ContactInput = Asw.ContactInput || {};
		Asw.ContactInput.Const = {
			/** data-属性名「data-type」 @constant @type {String} @default 'data-type' */
			DATA_ATTRIBUTE_NAME_TYPE: 'data-type',
			/** data-属性名「data-sms-possible」 @constant @type {String} @default 'data-sms-possible' */
			DATA_ATTRIBUTE_NAME_SMS_POSSIBLE: 'data-sms-possible',
			/** data-属性名「data-country-code」 @constant @type {String} @default 'data-country-code' */
			DATA_ATTRIBUTE_NAME_COUNTRY_CODE: 'data-country-code',
			/** data-type属性値「sms」 @constant @type {String} @default 'sms' */
			DATA_TYPE_VALUE_SMS: 'sms'
		};

		
		Asw.ContactInput.countryInfoMap = {"IS":{"internationalCallingCode":"+354","smsPossible":false},"IE":{"internationalCallingCode":"+353","smsPossible":true},"AZ":{"internationalCallingCode":"+994","smsPossible":true},"AF":{"internationalCallingCode":"+93","smsPossible":true},"US":{"internationalCallingCode":"+1","smsPossible":true},"AE":{"internationalCallingCode":"+971","smsPossible":true},"DZ":{"internationalCallingCode":"+213","smsPossible":true},"AR":{"internationalCallingCode":"+54","smsPossible":true},"AW":{"internationalCallingCode":"+297","smsPossible":true},"AL":{"internationalCallingCode":"+355","smsPossible":true},"AM":{"internationalCallingCode":"+374","smsPossible":true},"AO":{"internationalCallingCode":"+244","smsPossible":false},"AG":{"internationalCallingCode":"+1","smsPossible":true},"AD":{"internationalCallingCode":"+376","smsPossible":true},"YE":{"internationalCallingCode":"+967","smsPossible":true},"GB":{"internationalCallingCode":"+44","smsPossible":true},"IL":{"internationalCallingCode":"+972","smsPossible":true},"IT":{"internationalCallingCode":"+39","smsPossible":true},"IQ":{"internationalCallingCode":"+964","smsPossible":true},"IR":{"internationalCallingCode":"+98","smsPossible":true},"IN":{"internationalCallingCode":"+91","smsPossible":true},"ID":{"internationalCallingCode":"+62","smsPossible":true},"UG":{"internationalCallingCode":"+256","smsPossible":true},"UA":{"internationalCallingCode":"+380","smsPossible":true},"UZ":{"internationalCallingCode":"+998","smsPossible":true},"UY":{"internationalCallingCode":"+598","smsPossible":true},"EC":{"internationalCallingCode":"+593","smsPossible":true},"EG":{"internationalCallingCode":"+20","smsPossible":true},"EE":{"internationalCallingCode":"+372","smsPossible":true},"SZ":{"internationalCallingCode":"+268","smsPossible":false},"ET":{"internationalCallingCode":"+251","smsPossible":false},"ER":{"internationalCallingCode":"+291","smsPossible":true},"SV":{"internationalCallingCode":"+503","smsPossible":true},"AU":{"internationalCallingCode":"+61","smsPossible":true},"AT":{"internationalCallingCode":"+43","smsPossible":true},"OM":{"internationalCallingCode":"+968","smsPossible":true},"NL":{"internationalCallingCode":"+31","smsPossible":true},"GH":{"internationalCallingCode":"+233","smsPossible":true},"CV":{"internationalCallingCode":"+238","smsPossible":false},"GY":{"internationalCallingCode":"+592","smsPossible":true},"KZ":{"internationalCallingCode":"+7","smsPossible":true},"QA":{"internationalCallingCode":"+974","smsPossible":true},"CA":{"internationalCallingCode":"+1","smsPossible":true},"GA":{"internationalCallingCode":"+241","smsPossible":true},"CM":{"internationalCallingCode":"+237","smsPossible":true},"KR":{"internationalCallingCode":"+82","smsPossible":true},"KH":{"internationalCallingCode":"+855","smsPossible":true},"GM":{"internationalCallingCode":"+220","smsPossible":true},"KP":{"internationalCallingCode":"+850","smsPossible":false},"MK":{"internationalCallingCode":"+389","smsPossible":false},"GN":{"internationalCallingCode":"+224","smsPossible":true},"GW":{"internationalCallingCode":"+245","smsPossible":true},"CY":{"internationalCallingCode":"+357","smsPossible":true},"CU":{"internationalCallingCode":"+53","smsPossible":true},"GR":{"internationalCallingCode":"+30","smsPossible":true},"KI":{"internationalCallingCode":"+686","smsPossible":false},"KG":{"internationalCallingCode":"+996","smsPossible":true},"GT":{"internationalCallingCode":"+502","smsPossible":true},"GU":{"internationalCallingCode":"+1","smsPossible":true},"KW":{"internationalCallingCode":"+965","smsPossible":true},"KY":{"internationalCallingCode":"+1","smsPossible":true},"GD":{"internationalCallingCode":"+1","smsPossible":true},"HR":{"internationalCallingCode":"+385","smsPossible":true},"KE":{"internationalCallingCode":"+254","smsPossible":true},"CI":{"internationalCallingCode":"+225","smsPossible":true},"CR":{"internationalCallingCode":"+506","smsPossible":true},"SA":{"internationalCallingCode":"+966","smsPossible":true},"KM":{"internationalCallingCode":"+269","smsPossible":true},"CO":{"internationalCallingCode":"+57","smsPossible":true},"CG":{"internationalCallingCode":"+242","smsPossible":false},"CD":{"internationalCallingCode":"+243","smsPossible":true},"ZM":{"internationalCallingCode":"+260","smsPossible":true},"SM":{"internationalCallingCode":"+378","smsPossible":false},"SL":{"internationalCallingCode":"+232","smsPossible":true},"DJ":{"internationalCallingCode":"+253","smsPossible":false},"JM":{"internationalCallingCode":"+1","smsPossible":true},"CH":{"internationalCallingCode":"+41","smsPossible":true},"GE":{"internationalCallingCode":"+995","smsPossible":true},"SE":{"internationalCallingCode":"+46","smsPossible":true},"SY":{"internationalCallingCode":"+963","smsPossible":true},"ES":{"internationalCallingCode":"+34","smsPossible":true},"SG":{"internationalCallingCode":"+65","smsPossible":true},"LK":{"internationalCallingCode":"+94","smsPossible":true},"SK":{"internationalCallingCode":"+421","smsPossible":true},"SI":{"internationalCallingCode":"+386","smsPossible":true},"ZW":{"internationalCallingCode":"+263","smsPossible":true},"SD":{"internationalCallingCode":"+249","smsPossible":true},"KN":{"internationalCallingCode":"+1","smsPossible":true},"SR":{"internationalCallingCode":"+597","smsPossible":true},"LC":{"internationalCallingCode":"+1","smsPossible":true},"SC":{"internationalCallingCode":"+248","smsPossible":false},"SN":{"internationalCallingCode":"+221","smsPossible":true},"SB":{"internationalCallingCode":"+677","smsPossible":false},"RS":{"internationalCallingCode":"+381","smsPossible":true},"TH":{"internationalCallingCode":"+66","smsPossible":true},"TW":{"internationalCallingCode":"+886","smsPossible":true},"TJ":{"internationalCallingCode":"+992","smsPossible":true},"TZ":{"internationalCallingCode":"+255","smsPossible":true},"CZ":{"internationalCallingCode":"+420","smsPossible":true},"TD":{"internationalCallingCode":"+235","smsPossible":true},"CF":{"internationalCallingCode":"+236","smsPossible":true},"TN":{"internationalCallingCode":"+216","smsPossible":true},"CL":{"internationalCallingCode":"+56","smsPossible":true},"TV":{"internationalCallingCode":"+688","smsPossible":false},"DK":{"internationalCallingCode":"+45","smsPossible":true},"DE":{"internationalCallingCode":"+49","smsPossible":true},"TG":{"internationalCallingCode":"+228","smsPossible":true},"DO":{"internationalCallingCode":"+1","smsPossible":true},"DM":{"internationalCallingCode":"+1","smsPossible":false},"TT":{"internationalCallingCode":"+1","smsPossible":true},"TM":{"internationalCallingCode":"+993","smsPossible":false},"TR":{"internationalCallingCode":"+90","smsPossible":true},"TO":{"internationalCallingCode":"+676","smsPossible":false},"NG":{"internationalCallingCode":"+234","smsPossible":true},"NR":{"internationalCallingCode":"+674","smsPossible":false},"NA":{"internationalCallingCode":"+264","smsPossible":false},"NI":{"internationalCallingCode":"+505","smsPossible":true},"BH":{"internationalCallingCode":"+973","smsPossible":false},"NE":{"internationalCallingCode":"+227","smsPossible":true},"PK":{"internationalCallingCode":"+92","smsPossible":true},"JP":{"internationalCallingCode":"+81","smsPossible":true},"BS":{"internationalCallingCode":"+1","smsPossible":true},"NZ":{"internationalCallingCode":"+64","smsPossible":true},"BM":{"internationalCallingCode":"+1","smsPossible":true},"NP":{"internationalCallingCode":"+977","smsPossible":true},"BB":{"internationalCallingCode":"+246","smsPossible":true},"NO":{"internationalCallingCode":"+47","smsPossible":true},"HT":{"internationalCallingCode":"+509","smsPossible":true},"BD":{"internationalCallingCode":"+880","smsPossible":true},"VA":{"internationalCallingCode":"+39","smsPossible":false},"PA":{"internationalCallingCode":"+507","smsPossible":true},"VU":{"internationalCallingCode":"+678","smsPossible":false},"PG":{"internationalCallingCode":"+675","smsPossible":true},"PW":{"internationalCallingCode":"+680","smsPossible":true},"PY":{"internationalCallingCode":"+595","smsPossible":true},"PS":{"internationalCallingCode":"+970","smsPossible":true},"BR":{"internationalCallingCode":"+55","smsPossible":true},"HU":{"internationalCallingCode":"+36","smsPossible":true},"TL":{"internationalCallingCode":"+670","smsPossible":true},"BG":{"internationalCallingCode":"+359","smsPossible":true},"BF":{"internationalCallingCode":"+226","smsPossible":true},"BN":{"internationalCallingCode":"+673","smsPossible":true},"FJ":{"internationalCallingCode":"+679","smsPossible":true},"PH":{"internationalCallingCode":"+63","smsPossible":true},"FI":{"internationalCallingCode":"+358","smsPossible":true},"BT":{"internationalCallingCode":"+975","smsPossible":true},"BY":{"internationalCallingCode":"+375","smsPossible":true},"BZ":{"internationalCallingCode":"+501","smsPossible":false},"PR":{"internationalCallingCode":"+1","smsPossible":true},"BE":{"internationalCallingCode":"+32","smsPossible":true},"BA":{"internationalCallingCode":"+387","smsPossible":true},"FR":{"internationalCallingCode":"+33","smsPossible":true},"BO":{"internationalCallingCode":"+591","smsPossible":false},"PF":{"internationalCallingCode":"+689","smsPossible":true},"BI":{"internationalCallingCode":"+257","smsPossible":true},"VN":{"internationalCallingCode":"+84","smsPossible":true},"BJ":{"internationalCallingCode":"+229","smsPossible":true},"VE":{"internationalCallingCode":"+58","smsPossible":true},"PE":{"internationalCallingCode":"+51","smsPossible":true},"PL":{"internationalCallingCode":"+48","smsPossible":true},"BW":{"internationalCallingCode":"+267","smsPossible":true},"PT":{"internationalCallingCode":"+351","smsPossible":false},"HK":{"internationalCallingCode":"+852","smsPossible":true},"HN":{"internationalCallingCode":"+504","smsPossible":false},"MH":{"internationalCallingCode":"+692","smsPossible":true},"MO":{"internationalCallingCode":"+853","smsPossible":true},"MG":{"internationalCallingCode":"+261","smsPossible":true},"MW":{"internationalCallingCode":"+265","smsPossible":true},"ML":{"internationalCallingCode":"+223","smsPossible":true},"MT":{"internationalCallingCode":"+356","smsPossible":true},"MY":{"internationalCallingCode":"+60","smsPossible":true},"FM":{"internationalCallingCode":"+691","smsPossible":false},"MM":{"internationalCallingCode":"+95","smsPossible":true},"MX":{"internationalCallingCode":"+52","smsPossible":true},"MU":{"internationalCallingCode":"+230","smsPossible":true},"MR":{"internationalCallingCode":"+222","smsPossible":true},"MZ":{"internationalCallingCode":"+258","smsPossible":true},"MC":{"internationalCallingCode":"+377","smsPossible":true},"MV":{"internationalCallingCode":"+960","smsPossible":true},"MD":{"internationalCallingCode":"+373","smsPossible":false},"RO":{"internationalCallingCode":"+40","smsPossible":true},"MA":{"internationalCallingCode":"+212","smsPossible":true},"MN":{"internationalCallingCode":"+976","smsPossible":true},"ME":{"internationalCallingCode":"+382","smsPossible":true},"JO":{"internationalCallingCode":"+962","smsPossible":true},"LA":{"internationalCallingCode":"+856","smsPossible":true},"RU":{"internationalCallingCode":"+7","smsPossible":true},"LV":{"internationalCallingCode":"+371","smsPossible":true},"LT":{"internationalCallingCode":"+370","smsPossible":true},"LY":{"internationalCallingCode":"+218","smsPossible":true},"GQ":{"internationalCallingCode":"+240","smsPossible":false},"LI":{"internationalCallingCode":"+423","smsPossible":false},"LR":{"internationalCallingCode":"+231","smsPossible":true},"CN":{"internationalCallingCode":"+86","smsPossible":true},"LU":{"internationalCallingCode":"+352","smsPossible":true},"ZA":{"internationalCallingCode":"+27","smsPossible":true},"RW":{"internationalCallingCode":"+250","smsPossible":true},"LS":{"internationalCallingCode":"+266","smsPossible":false},"LB":{"internationalCallingCode":"+961","smsPossible":true}};

		$(function () {
			contactInfoInit();
		});
		
		function contactInfoInit() {
			
			
			Asw.get('representativeTelType').find('option[value="M1"]').attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_TYPE,
					Asw.ContactInput.Const.DATA_TYPE_VALUE_SMS);
			Asw.get('representativeTelCountry').children('option').each(function(i,option){
				var countryInfo = Asw.ContactInput.countryInfoMap[option.value];
				if (countryInfo === void 0) {
					
					return true;
				}
				$(option).attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_SMS_POSSIBLE, countryInfo.smsPossible);
				$(option).attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_COUNTRY_CODE, countryInfo.internationalCallingCode);
			});
			
			var smsCountrySelectSelector = 'select.js-countrySelect:not(#representativeTelCountry)'
			$(smsCountrySelectSelector).eq(0).children('option').each(function(i,option){
				var countryInfo = Asw.ContactInput.countryInfoMap[option.value];
				if (countryInfo === void 0) {
					
					return true;
				}
				$(smsCountrySelectSelector + ' option:nth-child(' + (i+1) + ')').attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_COUNTRY_CODE,
						countryInfo.internationalCallingCode);
			});
			
			var emergencyCountrySelectSelector = 'select.passengerEmergencyCountry.js-countrySelect:not(#representativeTelCountry)'
			$(emergencyCountrySelectSelector).eq(0).children('option').each(function(i,option){
				var countryInfo = Asw.ContactInput.countryInfoMap[option.value];
				if (countryInfo === void 0) {
					
					return true;
				}
				$(emergencyCountrySelectSelector + ' option:nth-child(' + (i+1) + ')').attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_COUNTRY_CODE,
						countryInfo.internationalCallingCode);
			});
		}
	</script><script type="text/javascript">
		var isInit = true;
		function changeTelType(_this, telNoInputId) {
			var $telNoInput = Asw.get(telNoInputId);
			var $telNoKind = Asw.get("representativeTelType");
			var telNo = Asw.getValueComparedToPlaceholder(telNoInputId);
			var telNoCountry = Asw.get("representativeTelCountry").val();

			if(true && !isInit) {
				if(telInputCheck(telNo, telNoCountry)) {
					if (_this.value == "M1") {
						telNo = exceptDomesticPrefix("09042504176", telNoCountry);
					} else if (_this.value == "B1") {
						telNo = exceptDomesticPrefix("03-5405-2172", telNoCountry);
					} else {
						telNo = exceptDomesticPrefix("090-4250-4176", telNoCountry);
					}
					$telNoInput.val(telNo);
				}
			}
			isInit = false;
		}
		
		function telInputCheck(telNo, telNoCountry) {
	        checkResult = false;
	        
	        var amcHndyTelNo = exceptDomesticPrefix("09042504176", telNoCountry);
	        var amcCmpTelNo = exceptDomesticPrefix("03-5405-2172", telNoCountry);
	        var amcTelNo = exceptDomesticPrefix("090-4250-4176", telNoCountry);
	
	        if(telNo == "" || telNo == amcHndyTelNo || telNo == amcCmpTelNo || telNo == amcTelNo) {
	        	checkResult = true;
	        }
	        return checkResult;
    	}
    	
    	function exceptDomesticPrefix(telNo, telNoCountry) {
    		if(telNoCountry == 'JP') {
    			if(telNo.substr(0, 1) === '0') {
    				return telNo.slice(1);
    			}
    		}
    		return telNo;
    	}
	</script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-customerinfomationform.js?6e91953"></script><script type="text/javascript">
		$(document).ready(function(){
				Asw.enableTextWhenCheckBox('.cascadedSwitch');
				Asw.notPasteInput('.jsNotPasteInput');
			$(function(){
				Asw.alignHeightToParentTdElement( '.passengerInfoInputArea' );
				
					Asw.alignHeightToParentTdElement( '.passengerInfoInputText' );
				
				
				$('select.js-countrySelect').trigger('change');
			});
		});
	</script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-session-keeper.js?6ec0730"></script><script type="text/javascript">
			$(document).ready(function() {
				Asw.SessionKeeper.initAndStart("sessionKeeperContainer", "520", "570", "PC");
			});
			</script><script type="text/javascript">
		Asw.addAjaxCallback(function(data) {
			// アコーディオンのメソッドと設定 
			if($('.jsAccordionSwitch .jsTriggerSwitch').length > 0){
				var events = $._data($('.jsAccordionSwitch .jsTriggerSwitch').get(0), "events");
				if(events === undefined || events.click === undefined) {
					Asw.AccordionInfo('.jsAccordionSwitch', {
						'triggerSwitch': '.jsAccordionSwitch .jsTriggerSwitch',
						'listClass': '.jsAccordionSwitchList',
						'visibilityHidden': 'toggleContents'
					});
				}
			}
		});
	</script><script type="text/javascript">
	Asw.Price = Asw.Price || {};
	// 整数の区切り文字
	Asw.Price.separateString = ',';
	
	// 整数の区切り桁数
	Asw.Price.separateDigitNumber = 3;
	
	// 小数点
	Asw.Price.decimalPointString = '.';
	
	// 小数点以降の有効桁数
	Asw.Price.significantDigit = 0;
	
	// 表示金額の単位
	Asw.Price.currencyUnit = '円';
	</script><script type="text/javascript">
			$(document).ready(function() {
				$('#preLoadingArea').css('visibility', 'hidden');
			});
		</script><noscript><img src="https://aswbe-i.ana.co.jp/akam/13/pixel_1320083?a=dD02YWRiMzY0YzQxNjU0MTNjNjgzYmFkNjhmNmRiMTdjMGFlNmFjNjI0JmpzPW9mZg==" style="visibility: hidden; position: absolute; left: -999px; top: -999px;" /></noscript><script type="text/javascript"  src="/RcS9yzCRgrNnM/gY/v0Wf9fQwtpdQ/7NE3X2ruOE2SL9YE/HyNpQmYB/RWt/XOHcnHAU"></script></body>
</html>