// --- 调试模式 v1.5 ---
console.log("--- ANA 侦测器 v1.5 [调试模式] ---");

// 强制等待，确保DOM完全就绪
setTimeout(function() {
    console.log("延时后开始初始化...");
    initialize();
}, 100);


function initialize() {
    // --- Element Variables & Debugging ---
    console.log("正在查找界面元素...");
    const recordButton = document.getElementById('record-button');
    console.log("1. '开始录制' 按钮: ", recordButton);

    const exportButton = document.getElementById('export-button');
    console.log("2. '导出' 按钮: ", exportButton);

    const clearButton = document.getElementById('clear-button');
    console.log("3. '清空' 按钮: ", clearButton);

    const requestsList = document.getElementById('requests');
    console.log("4. 请求列表区域: ", requestsList);

    const statusDiv = document.getElementById('status');
    console.log("5. 状态显示区域: ", statusDiv);

    // --- State Variables ---
    let isRecording = false;
    let requests = [];

    // --- Event Listeners ---
    if (recordButton) {
        recordButton.addEventListener('click', toggleRecording);
    } else {
        console.error("关键错误: 'recordButton' 为 null，无法绑定点击事件。");
    }

    if (clearButton) {
        clearButton.addEventListener('click', clearRequests);
    }

    if (exportButton) {
        exportButton.addEventListener('click', exportHar);
    }
    
    console.log("初始化脚本执行完毕。");

    // --- Core Functions ---
    function toggleRecording() {
        isRecording = !isRecording;
        if (isRecording) {
            startRecording();
        } else {
            stopRecording();
        }
    }
    
    function startRecording() {
        requests = [];
        requestsList.innerHTML = '';
        statusDiv.textContent = '录制中... (0 个请求)';
        recordButton.textContent = '停止录制';
        recordButton.classList.replace('record-start', 'record-stop');
        exportButton.style.display = 'none';
        chrome.devtools.network.onRequestFinished.addListener(handleRequest);
    }
    
    function stopRecording() {
        chrome.devtools.network.onRequestFinished.removeListener(handleRequest);
        recordButton.textContent = '开始录制';
        recordButton.classList.replace('record-stop', 'record-start');
        statusDiv.textContent = `录制停止。捕获到 ${requests.length} 个请求。`;
        if (requests.length > 0) {
            exportButton.style.display = 'inline-block';
        }
        renderRequests();
    }

    function clearRequests() {
        requests = [];
        requestsList.innerHTML = '';
        statusDiv.textContent = '';
        exportButton.style.display = 'none';
        if(isRecording) {
            toggleRecording();
        }
    }

    const handleRequest = request => {
        if (!isRecording) return;
        requests.push(request);
        statusDiv.textContent = `录制中... (${requests.length} 个请求)`;
    };

    function renderRequests() {
        requestsList.innerHTML = '';
        requests.forEach(request => {
            request.getContent(() => {
                const postDataText = request.request.postData ? request.request.postData.text : '';
                const isSessionRequest = request.request.method === "POST" && postDataText && postDataText.includes("cmnSessionKeepingButton");
                
                const urlPath = request.request.url.split('?')[0];
                const pageName = urlPath.substring(urlPath.lastIndexOf('/') + 1);
                const title = `[${request.request.method}] ${pageName || urlPath}`;
                
                const entry = createCollapsible(title, request, isSessionRequest);
                requestsList.appendChild(entry);
            });
        });
    }

    function exportHar() {
        const har = { log: { version: '1.2', creator: { name: 'ANA 侦测器', version: '1.5' }, entries: requests } };
        const blob = new Blob([JSON.stringify(har, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `ana-network-log-${new Date().toISOString()}.har`;
        a.click();
        URL.revokeObjectURL(url);
    }

    function createCollapsible(title, requestData, isHighlighted) {
        const entry = document.createElement('div');
        entry.className = isHighlighted ? 'entry session-request' : 'entry';
        const header = document.createElement('div');
        header.className = 'header';
        header.innerHTML = `<span>${title}</span>`;
        
        header.addEventListener('click', function() {
            this.classList.toggle('active');
            const contentDiv = this.nextElementSibling;
            if (!contentDiv) {
                const newContent = createContent(requestData);
                this.parentElement.appendChild(newContent);
                newContent.style.display = 'block';
            } else {
                contentDiv.style.display = contentDiv.style.display === "block" ? "none" : "block";
            }
        });
        entry.appendChild(header);
        return entry;
    }

    function createContent(request) {
        const contentDiv = document.createElement('div');
        contentDiv.className = 'content';
        const requestHeaders = headersArrayToObject(request.request.headers);
        const postDataObject = request.request.postData ? formEncodedToObject(request.request.postData.text) : {};
        contentDiv.innerHTML = `
            <div class="data-block"><h4>请求 URL</h4><div class="url-data">${request.request.url}</div></div>
            ${createDisplayTable('请求头', requestHeaders)}
            ${Object.keys(postDataObject).length > 0 ? createDisplayTable('表单数据', postDataObject) : ''}
            <hr><div class="data-block"><h4>响应状态</h4><div class="url-data">${request.response.status} ${request.response.statusText}</div></div>`;
        return contentDiv;
    }

    function createDisplayTable(title, data) {
        let table = `<div class="data-table"><h4>${title}</h4><table>`;
        for (const key in data) { table += `<tr><td class="key">${key}</td><td class="value">${data[key] || '&nbsp;'}</td></tr>`; }
        return table + '</table></div>';
    }

    function headersArrayToObject(headersArray) {
        if (!headersArray) return {};
        return headersArray.reduce((acc, h) => ({ ...acc, [h.name]: h.value }), {});
    }

    function formEncodedToObject(encodedString) {
        if (!encodedString) return {};
        return Array.from(new URLSearchParams(encodedString).entries()).reduce((acc, [k, v]) => ({ ...acc, [k]: v }), {});
    }
} 