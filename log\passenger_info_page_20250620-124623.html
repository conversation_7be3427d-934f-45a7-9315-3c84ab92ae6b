<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="ja-<PERSON>" xml:lang="ja-<PERSON>">

	
	<!-- skey = 2025/06/20 13:46:23 rei21g IPuLqJe9_e dljdmx+1ad  --><head id="j_idt53">
    <base href="about:blank">
    <style>
        /* Hide elements that might try to load external resources, as a fallback. */
        img, svg, video, iframe, link[rel="stylesheet"], script { display: none !important; }
    </style>

		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
			<meta http-equiv="X-UA-Compatible" content="IE=EmulateIE9; IE=EmulateIE10" />
			<meta http-equiv="X-UA-Compatible" content="IE=7" />
			<meta http-equiv="X-UA-Compatible" content="IE=edge" />
		<meta name="description" content="" />
		<meta name="keywords" content="" />
		<meta name="format-detection" content="telephone=no" />
		<script type="text/javascript" src="/international_asw/dynatrace/ruxitagentjs_ICANVfhqrux_10273230920145353.js" data-dtconfig="rid=RID_1936918436|rpid=-*********|domain=aswbe-i.ana.co.jp|reportUrl=https://bf90419wvr.bf.dynatrace.com/bf|app=78bf0b58acf6ed13|cors=1|owasp=1|featureHash=ICANVfhqrux|xb=www^bs.google-analytics^bs.com^pana^bs.demdex^bs.net^pcollect^bs.tealiumiq^bs.com^pallnipponairways^bs.australia-3^bs.evergage^bs.com^pallnipponairwayscolt^bs.tt^bs.omtrdc^bs.net|rdnt=1|uxrgce=1|cuc=k8vpgh61|mel=100000|expw=1|dpvc=1|md=mdcc1=bAsw.PnrOutput.recordLocator,mdcc2=bAsw.PnrOutput.paymentDetailInfoList[0].fopCode,mdcc3=bAsw.PnrOutput.ticketedFlag,mdcc4=bAsw.PnrOutput.nhPnrFlag,mdcc5=bAsw.PnrOutput.itinerarySummary.departureDateYyyymmdd,mdcc6=bAsw.SearchCriteriaOutput.cffCodeInput,mdcc7=a#main ^rb div.section.sectionNoFlow ^rb table ^rb tbody ^rb tr ^rb td.flightNumber.lastCol ^rb span ^rb span:nth-child(2),mdcc8=cunique|lastModification=1750184416409|tp=500,50,0|agentUri=/international_asw/dynatrace/ruxitagentjs_ICANVfhqrux_10273230920145353.js"></script><link rel="shortcut icon" href="https://www.ana.co.jp/favicon.ico" />
		<title>お客様情報入力 | ANA</title>
		<noscript><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/css/asw_noscript_pc.css?99001fc" />
		</noscript>

		<!--  [ Adobe Target ] -->
		<script type="text/javascript" src="https://www.ana.co.jp/behavior/adobetarget/target_intbe.js"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/jquery-1.8.3.min.js?717d3c0"></script><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/ja/css/asw_initialize.css?99001fc" /><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/css/asw_screen_pc.css?99001fc" /><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/css/asw_common_pc.css?99001fc" /><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/css/asw_forminput_pc.css?99001fc" /><script type="text/javascript">
		var Asw = Asw || {};
		Asw.GlobalWord = Asw.GlobalWord || {};
		
		Asw.GlobalWord.toolTipCloseIconAltText = "閉じる";
		
		</script><script >bazadebezolkohpepadr="77227031"</script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/akam/13/49a665b"  defer></script></head><body>
		
		<noscript>当サイトをご利用になるためには、JavaScript対応のブラウザが必要です。設定でJavaScriptを有効にしてください。有効にできない場合はANA電話窓口へお問い合わせください。
		</noscript>
		<div id="preLoadingArea"></div>
	
	<div id="cmnHeader" class="platinum">
		<!--===== cmnHeader =====-->
		<div class="headerWrapper">
			<div class="header">
				<p class="logo"><a href="https://www.ana.co.jp/asw/AMCTopServlet?type=a" tabindex="-1" aria-hidden="true"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/ana_logo_navy.png?717d3c0" alt="ANA Inspiration of JAPAN。リンク先はマイレージクラブトップページです。" height="28" width="274" /></a>
				</p>
					<ul class="headerMenu">
						<li><a href="https://www.ana.co.jp/asw/AMCTopServlet?type=a">ANAマイレージクラブトップページ</a>
						</li>
						<li><a href="https://www.ana.co.jp/other/int/meta/0005.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">お問い合わせ<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
						</li>
							<li class="btnLogoutArea"><a href="https://aswbe-i.ana.co.jp/rei21g/international_asw/rest/logout" role="button" class="btnBase btnLogout">ログアウト</a>
							</li>
					</ul>
			</div>
		</div>
			<div class="amcUserInfoWrapper">
				<div class="amcUserInfo">
					<div class="userNameArea">
						<table>
							<tr>	
									<td class="statusIcon"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_status_05.png?717d3c0" alt="プラチナサービスメンバー" height="46" width="60" />
									</td>
								<td class="userName">佐藤 直樹 <span>様</span>
								</td>
							</tr>					
						</table>
					</div>
						<div class="balance">
								<dl class="upgradePointBalance">
									<dt>アップグレードポイント残高
									</dt>
									<dd>
											<dl>
												<dt>(2025年度)
												</dt>
												<dd>
													<em>20
													</em>ポイント
												</dd>
											</dl>
									</dd>
								</dl>
							<dl class="mileBalance">
								<dt>マイル口座残高
								</dt>
								<dd><em>75,663</em><span class="currencyCode">マイル</span>
								</dd>
							</dl>
						</div>
				</div>
					<p class="nowTime">6月20日13時46分現在
					</p>
			</div>
		<!--===== /cmnHeader =====-->
	</div>
		<div id="sessionKeeperContainer" class="sessionKeepingContainer"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/bg_modalWindow.png?717d3c0" alt="" height="0" width="0" />
			<div id="extendSessionExpiredDialog" class="modalExpire modalSmall modalContainer" role="dialog" aria-labelledby="extendSessionExpiredDialogTitle" aria-hidden="true">
<form id="sessionKeeperContainer:j_idt183" name="sessionKeeperContainer:j_idt183" method="post" action="https://aswbe-i.ana.co.jp/rei21g/international_asw/pages/award/reservation/award_mandatory_passenger_information_input.xhtml?aswcid=1&amp;rand=20250620134623IPuLqJe9_e" autocomplete="off" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="sessionKeeperContainer:j_idt183" value="sessionKeeperContainer:j_idt183" />
<input type="hidden" name="sessionKeeperContainer:j_idt183_operationTicket" value="dljdmx+1add3e86320053bc2682c12737113d9d~K-JICRgc98IFGV7ztZDEh5bNK10AcTtuRNlOlC5E!1750394774989.aere-xml-controller-67d4778877-hjhfm" /><input type="hidden" name="sessionKeeperContainer:j_idt183_cmnPageTicket" value="4" />
					<h1 id="extendSessionExpiredDialogTitle" class="dialogTitle">まもなくセッションが終了します</h1>
					<div id="extendSessionExpiredDialogMessage" class="dialogMessage" tabindex="-1">このままお手続きを進める場合は、［延長］ボタンを押してください。</div>
					<p class="modalButton"><input id="sessionKeeperContainer:cmnSessionKeepingButton" type="submit" name="sessionKeeperContainer:cmnSessionKeepingButton" value="延長" class="btnBase btnModal btnMainStream" onclick="mojarra.ab(this,event,'action',0,0,{'onevent':function(data) {Asw.doCommonAjaxCallback(data,false, event); Asw.SessionKeeper.doSessionKeep(data);},'onerror':function(data) {Asw.doCommonAjaxErrorCallback(data); }});return false" />
					</p><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:0" value="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" autocomplete="off" />
</form>
			</div>
			<div id="showSessionExpiredDialog" class="modalExpire modalSmall modalContainer" role="dialog" aria-labelledby="showSessionExpiredDialogTitle" aria-hidden="true">
				<h1 id="showSessionExpiredDialogTitle" class="dialogTitle">セッションが終了しました</h1>
				<div id="showSessionExpiredDialogMessage" class="dialogMessage" tabindex="-1">一定時間操作されなかったため、お手続きを進めることができません。お手数ですが、再度トップページからお手続きください。</div>
				<p class="modalButton">
					<input type="submit" value="閉じる" class="btnBase btnModal btnMainStream" onclick="Asw.SessionKeeper.closeTimeoutDialog(); return false;" />
				</p>
			</div>
		</div>
		<!--===== dialogMessages =====--><div id="cmnModalMessages"></div>
		<div id="prebookConfirmDialog" class="modalContainer modalConfirm modalSmall " aria-hidden="true" role="dialog">
<form id="prebookConfirmDialog:j_idt403" name="prebookConfirmDialog:j_idt403" method="post" action="https://aswbe-i.ana.co.jp/rei21g/international_asw/pages/award/reservation/award_mandatory_passenger_information_input.xhtml?aswcid=1&amp;rand=20250620134623IPuLqJe9_e" autocomplete="off" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="prebookConfirmDialog:j_idt403" value="prebookConfirmDialog:j_idt403" />
<input type="hidden" name="prebookConfirmDialog:j_idt403_operationTicket" value="dljdmx+1add3e86320053bc2682c12737113d9d~K-JICRgc98IFGV7ztZDEh5bNK10AcTtuRNlOlC5E!1750394774989.aere-xml-controller-67d4778877-hjhfm" /><input type="hidden" name="prebookConfirmDialog:j_idt403_cmnPageTicket" value="4" /><div class="dialogMessage" tabindex="0"><p>この後、名・姓の修正や変更ができません。</p><p>「搭乗者選択」欄の氏名が「名」「姓」の順番で表示されていることを再確認ください。</p><p>例）<strong class="highlight">空野 太郎</strong> 様 → MR <strong class="highlight">TARO SORANO</strong></p><p>保安上の理由により、航空券とパスポートのお名前が同じ表記でない場合はご搭乗いただくことができませんので必ず再度ご確認ください。</p></div>
					
					<ul class="modalBtnHorizontal">
							
							<li><input type="submit" name="prebookConfirmDialog:j_idt419" value="戻る" aria-controls="prebookConfirmDialog" class="btnBase btnModal btnCancel" onclick="Asw.Dialog.getInstance('prebookConfirmDialog').close(event, Asw.DialogSequencer.isActiveSequencerNotDisplayedLastDialog());  return false;" />
							</li>
						
						<li class="btnArrowNext"><input type="submit" name="prebookConfirmDialog:j_idt422" value="OK" aria-controls="prebookConfirmDialog" class="btnBase btnModal btnMainStream " onclick="Asw.Dialog.getInstance('prebookConfirmDialog').callOpener('NORMAL', event); return false;" />
						</li>
					</ul><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:1" value="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" autocomplete="off" />
</form>
		</div>
		<!--===== /dialogMessages =====--><div id="cmnContainer">
		<!--=========== .cmnContainer ============--><div id="cmnEmergencyNotice"></div><div id="cmnTitle" class="hgroup ">
		<h1 class="visuallyHidden">お客様情報入力</h1></div>

		<div id="cmnWrapper">
		<!--===== cmnWrapper =====-->
		<ol class="mainFlow" aria-label="完了までの手順、">
			<li><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_flow_01.png?717d3c0" alt="1" height="20" width="28" />フライト検索</li><!--
			--><li><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_flow_02_on.png?717d3c0" alt="2" height="20" width="28" /><span class="visuallyHidden">現在のステップ、</span>お客様情報入力</li><!--
			--><li><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_flow_03.png?717d3c0" alt="3" height="20" width="28" />お支払い情報</li><!--
			--><li class="flowLast"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_flow4_04.png?717d3c0" alt="4" height="20" width="28" />完了</li>
		</ol>

	<div id="main">
	<!--=========== main =============-->
		<ul class="iconExample">
				<li class="requiredIconExample">
					<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="" height="7" width="7" /></span>必須項目です	
				</li>
		</ul>
<form id="j_idt526" name="j_idt526" method="post" action="https://aswbe-i.ana.co.jp/rei21g/international_asw/pages/award/reservation/award_mandatory_passenger_information_input.xhtml?aswcid=1&amp;rand=20250620134623IPuLqJe9_e" autocomplete="off" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="j_idt526" value="j_idt526" />
<input type="hidden" name="j_idt526_operationTicket" value="dljdmx+1add3e86320053bc2682c12737113d9d~K-JICRgc98IFGV7ztZDEh5bNK10AcTtuRNlOlC5E!1750394774989.aere-xml-controller-67d4778877-hjhfm" /><input type="hidden" name="j_idt526_cmnPageTicket" value="4" />

<div class="section">
	<h2>搭乗者選択<a href="https://www.ana.co.jp/other/int/meta/0086.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="特典のご利用に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a></h2>
	<table class="passengerInfo">
		<thead>
			<tr>
				
				<th class="name">氏名</th>
				
				<th class="dateOfBirthWide" id="dateOfBirthHeader">生年月日</th>
				
				<th class="gender lastCol" id="genderHeader">性別<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span></th>
				
			</tr>
		</thead>
		<tbody>
				
				<tr>
					<th class="parent_01" id="parentHeader01">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:0:passenger" type="checkbox" name="passengers:0:passenger" checked="checked" data-number="1" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:0:passenger">NAOKI SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader01">1963年06月05日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader01">				
							<span class="formWrapper">
							<label for="passengers:0:gender" class="visuallyHidden">NAOKI SATOの性別</label><select id="passengers:0:gender" name="passengers:0:gender" size="1">	<option value="">選択してください</option>
	<option value="M" selected="selected">男性</option>
	<option value="F">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_02" id="parentHeader02">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:1:passenger" type="checkbox" name="passengers:1:passenger" data-number="2" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:1:passenger">REO SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader02">1995年05月18日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader02">				
							<span class="formWrapper">
							<label for="passengers:1:gender" class="visuallyHidden">REO SATOの性別</label><select id="passengers:1:gender" name="passengers:1:gender" size="1">	<option value="">選択してください</option>
	<option value="M" selected="selected">男性</option>
	<option value="F">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_03" id="parentHeader03">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:2:passenger" type="checkbox" name="passengers:2:passenger" data-number="3" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:2:passenger">ERI SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader03">1963年08月14日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader03">				
							<span class="formWrapper">
							<label for="passengers:2:gender" class="visuallyHidden">ERI SATOの性別</label><select id="passengers:2:gender" name="passengers:2:gender" size="1">	<option value="">選択してください</option>
	<option value="M">男性</option>
	<option value="F" selected="selected">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_04" id="parentHeader04">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:3:passenger" type="checkbox" name="passengers:3:passenger" data-number="4" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:3:passenger">TAISHI SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader04">1992年12月03日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader04">				
							<span class="formWrapper">
							<label for="passengers:3:gender" class="visuallyHidden">TAISHI SATOの性別</label><select id="passengers:3:gender" name="passengers:3:gender" size="1">	<option value="">選択してください</option>
	<option value="M" selected="selected">男性</option>
	<option value="F">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_05" id="parentHeader05">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:4:passenger" type="checkbox" name="passengers:4:passenger" data-number="5" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:4:passenger">HIROMI FUJIMOTO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader05">1961年11月23日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader05">				
							<span class="formWrapper">
							<label for="passengers:4:gender" class="visuallyHidden">HIROMI FUJIMOTOの性別</label><select id="passengers:4:gender" name="passengers:4:gender" size="1">	<option value="">選択してください</option>
	<option value="M">男性</option>
	<option value="F" selected="selected">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
		</tbody>
	</table>
</div>
		<div class="section">
			<h2 class="midstream">連絡先情報
			</h2>
			<h3>代表者
			</h3>
	
	
	
	<div class="formInput">
		<fieldset>
			<legend><label>代表者</label></legend>
			<dl id="contactEmailAddressInfo">
				<dt class="hasRequiredIcon">メールアドレス<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span><a href="https://www.ana.co.jp/other/int/meta/0057.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="メールの宛先と内容に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a>
				</dt>
				<dd>
					<ul class="verticalList">
						<li class="nonMarginTop"><label for="representativeEmail" class="visuallyHidden">代表者メールアドレス</label><input id="representativeEmail" type="text" name="representativeEmail" value="<EMAIL>" class="widthLarge" placeholder="例：<EMAIL>" />
						</li>
						<li>
							<span class="supplementTxt" id="confirmMailAddressForRepresentative">確認のため再度ご入力ください。
							</span><label for="confirmrepresentativeEmail" class="visuallyHidden">確認用メールアドレス</label><input id="confirmrepresentativeEmail" type="text" name="confirmrepresentativeEmail" value="<EMAIL>" class="widthLarge jsNotPasteInput" placeholder="例：<EMAIL>" aria-describedby="confirmMailAddressForRepresentative" />
						</li>
					</ul>
				</dd>
					<dd style="display:none;"><input id="updateAlwaysInfo" type="checkbox" name="updateAlwaysInfo" /><label for="updateAlwaysInfo">ログイン中の会員情報へこのメールアドレスを登録</label>
					</dd>
			</dl>
			<dl>
				<dt>電話番号<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span>
				</dt>
				<dd class="js-countryWrap">
					<ul>
						<li><label for="representativeTelType" class="visuallyHidden">電話番号種別</label><select id="representativeTelType" name="representativeTelType" class="basicWidth js-typeSelect" size="1" onchange="changeTelType(this,'representativeTel')">	<option value="M1" selected="selected">携帯</option>
	<option value="B1">会社</option>
	<option value="H1">自宅</option>
</select>
						</li>
						<li><label for="representativeTelCountry" class="visuallyHidden">電話番号の国/地区選択</label><select id="representativeTelCountry" name="representativeTelCountry" class="js-countrySelect" size="1">	<option value="" data-sms-possible="true" data-country-code="---">選択してください</option>
	<option value="JP" selected="selected" data-sms-possible="true">日本</option>
	<option value="-" data-sms-possible="true" data-country-code="---">-----------</option>
	<option value="IS" data-sms-possible="true">アイスランド</option>
	<option value="IE" data-sms-possible="true">アイルランド</option>
	<option value="AZ" data-sms-possible="true">アゼルバイジャン</option>
	<option value="AF" data-sms-possible="true">アフガニスタン</option>
	<option value="US" data-sms-possible="true">アメリカ合衆国/カナダ</option>
	<option value="AE" data-sms-possible="true">アラブ首長国連邦</option>
	<option value="DZ" data-sms-possible="true">アルジェリア</option>
	<option value="AR" data-sms-possible="true">アルゼンチン</option>
	<option value="AW" data-sms-possible="true">アルバ</option>
	<option value="AL" data-sms-possible="true">アルバニア</option>
	<option value="AM" data-sms-possible="true">アルメニア</option>
	<option value="AO" data-sms-possible="true">アンゴラ</option>
	<option value="AG" data-sms-possible="true">アンティグア・バーブーダ</option>
	<option value="AD" data-sms-possible="true">アンドラ</option>
	<option value="YE" data-sms-possible="true">イエメン</option>
	<option value="GB" data-sms-possible="true">イギリス</option>
	<option value="IL" data-sms-possible="true">イスラエル</option>
	<option value="IT" data-sms-possible="true">イタリア</option>
	<option value="IQ" data-sms-possible="true">イラク</option>
	<option value="IR" data-sms-possible="true">イラン</option>
	<option value="IN" data-sms-possible="true">インド</option>
	<option value="ID" data-sms-possible="true">インドネシア</option>
	<option value="UG" data-sms-possible="true">ウガンダ</option>
	<option value="UA" data-sms-possible="true">ウクライナ</option>
	<option value="UZ" data-sms-possible="true">ウズベキスタン</option>
	<option value="UY" data-sms-possible="true">ウルグアイ</option>
	<option value="EC" data-sms-possible="true">エクアドル</option>
	<option value="EG" data-sms-possible="true">エジプト</option>
	<option value="EE" data-sms-possible="true">エストニア</option>
	<option value="SZ" data-sms-possible="true">エスワティニ</option>
	<option value="ET" data-sms-possible="true">エチオピア</option>
	<option value="ER" data-sms-possible="true">エリトリア</option>
	<option value="SV" data-sms-possible="true">エルサルバドル</option>
	<option value="AU" data-sms-possible="true">オーストラリア</option>
	<option value="AT" data-sms-possible="true">オーストリア</option>
	<option value="OM" data-sms-possible="true">オマーン</option>
	<option value="NL" data-sms-possible="true">オランダ</option>
	<option value="GH" data-sms-possible="true">ガーナ</option>
	<option value="CV" data-sms-possible="true">カーボベルデ</option>
	<option value="GY" data-sms-possible="true">ガイアナ</option>
	<option value="KZ" data-sms-possible="true">カザフスタン</option>
	<option value="QA" data-sms-possible="true">カタール</option>
	<option value="CA" data-sms-possible="true">カナダ/アメリカ合衆国</option>
	<option value="GA" data-sms-possible="true">ガボン</option>
	<option value="CM" data-sms-possible="true">カメルーン</option>
	<option value="KR" data-sms-possible="true">韓国</option>
	<option value="GM" data-sms-possible="true">ガンビア</option>
	<option value="KH" data-sms-possible="true">カンボジア</option>
	<option value="KP" data-sms-possible="true">北朝鮮</option>
	<option value="MK" data-sms-possible="true">北マケドニア</option>
	<option value="GN" data-sms-possible="true">ギニア</option>
	<option value="GW" data-sms-possible="true">ギニアビサウ</option>
	<option value="CY" data-sms-possible="true">キプロス</option>
	<option value="CU" data-sms-possible="true">キューバ</option>
	<option value="GR" data-sms-possible="true">ギリシャ</option>
	<option value="KI" data-sms-possible="true">キリバス</option>
	<option value="KG" data-sms-possible="true">キルギス</option>
	<option value="GT" data-sms-possible="true">グアテマラ</option>
	<option value="GU" data-sms-possible="true">グアム</option>
	<option value="KW" data-sms-possible="true">クウェート</option>
	<option value="GD" data-sms-possible="true">グレナダ</option>
	<option value="HR" data-sms-possible="true">クロアチア</option>
	<option value="KY" data-sms-possible="true">ケイマン諸島</option>
	<option value="KE" data-sms-possible="true">ケニア</option>
	<option value="CI" data-sms-possible="true">コートジボワール</option>
	<option value="CR" data-sms-possible="true">コスタリカ</option>
	<option value="KM" data-sms-possible="true">コモロ</option>
	<option value="CO" data-sms-possible="true">コロンビア</option>
	<option value="CG" data-sms-possible="true">コンゴ共和国</option>
	<option value="CD" data-sms-possible="true">コンゴ民主共和国</option>
	<option value="SA" data-sms-possible="true">サウジアラビア</option>
	<option value="ZM" data-sms-possible="true">ザンビア</option>
	<option value="SM" data-sms-possible="true">サンマリノ</option>
	<option value="SL" data-sms-possible="true">シエラレオネ</option>
	<option value="DJ" data-sms-possible="true">ジブチ</option>
	<option value="JM" data-sms-possible="true">ジャマイカ</option>
	<option value="GE" data-sms-possible="true">ジョージア</option>
	<option value="SY" data-sms-possible="true">シリア</option>
	<option value="SG" data-sms-possible="true">シンガポール</option>
	<option value="ZW" data-sms-possible="true">ジンバブエ</option>
	<option value="CH" data-sms-possible="true">スイス</option>
	<option value="SE" data-sms-possible="true">スウェーデン</option>
	<option value="SD" data-sms-possible="true">スーダン</option>
	<option value="ES" data-sms-possible="true">スペイン</option>
	<option value="SR" data-sms-possible="true">スリナム</option>
	<option value="LK" data-sms-possible="true">スリランカ</option>
	<option value="SK" data-sms-possible="true">スロバキア</option>
	<option value="SI" data-sms-possible="true">スロベニア</option>
	<option value="SC" data-sms-possible="true">セイシェル</option>
	<option value="GQ" data-sms-possible="true">赤道ギニア</option>
	<option value="SN" data-sms-possible="true">セネガル</option>
	<option value="RS" data-sms-possible="true">セルビア</option>
	<option value="KN" data-sms-possible="true">セントクリストファー・ネイビス</option>
	<option value="LC" data-sms-possible="true">セントルシア</option>
	<option value="SB" data-sms-possible="true">ソロモン諸島</option>
	<option value="TH" data-sms-possible="true">タイ</option>
	<option value="TW" data-sms-possible="true">台湾</option>
	<option value="TJ" data-sms-possible="true">タジキスタン</option>
	<option value="TZ" data-sms-possible="true">タンザニア</option>
	<option value="CZ" data-sms-possible="true">チェコ</option>
	<option value="TD" data-sms-possible="true">チャド</option>
	<option value="CF" data-sms-possible="true">中央アフリカ共和国</option>
	<option value="CN" data-sms-possible="true">中国</option>
	<option value="TN" data-sms-possible="true">チュニジア</option>
	<option value="CL" data-sms-possible="true">チリ</option>
	<option value="TV" data-sms-possible="true">ツバル</option>
	<option value="DK" data-sms-possible="true">デンマーク</option>
	<option value="DE" data-sms-possible="true">ドイツ</option>
	<option value="TG" data-sms-possible="true">トーゴ</option>
	<option value="DO" data-sms-possible="true">ドミニカ共和国</option>
	<option value="DM" data-sms-possible="true">ドミニカ国</option>
	<option value="TT" data-sms-possible="true">トリニダード・トバゴ</option>
	<option value="TM" data-sms-possible="true">トルクメニスタン</option>
	<option value="TR" data-sms-possible="true">トルコ</option>
	<option value="TO" data-sms-possible="true">トンガ</option>
	<option value="NG" data-sms-possible="true">ナイジェリア</option>
	<option value="NR" data-sms-possible="true">ナウル</option>
	<option value="NA" data-sms-possible="true">ナミビア</option>
	<option value="NI" data-sms-possible="true">ニカラグア</option>
	<option value="NE" data-sms-possible="true">ニジェール</option>
	<option value="NZ" data-sms-possible="true">ニュージーランド</option>
	<option value="NP" data-sms-possible="true">ネパール</option>
	<option value="NO" data-sms-possible="true">ノルウェー</option>
	<option value="BH" data-sms-possible="true">バーレーン</option>
	<option value="HT" data-sms-possible="true">ハイチ</option>
	<option value="PK" data-sms-possible="true">パキスタン</option>
	<option value="VA" data-sms-possible="true">バチカン</option>
	<option value="PA" data-sms-possible="true">パナマ</option>
	<option value="VU" data-sms-possible="true">バヌアツ</option>
	<option value="BS" data-sms-possible="true">バハマ</option>
	<option value="PG" data-sms-possible="true">パプアニューギニア</option>
	<option value="BM" data-sms-possible="true">バミューダ</option>
	<option value="PW" data-sms-possible="true">パラオ</option>
	<option value="PY" data-sms-possible="true">パラグアイ</option>
	<option value="BB" data-sms-possible="true">バルバドス</option>
	<option value="PS" data-sms-possible="true">パレスチナ</option>
	<option value="HU" data-sms-possible="true">ハンガリー</option>
	<option value="BD" data-sms-possible="true">バングラデシュ</option>
	<option value="TL" data-sms-possible="true">東ティモール</option>
	<option value="FJ" data-sms-possible="true">フィジー</option>
	<option value="PH" data-sms-possible="true">フィリピン</option>
	<option value="FI" data-sms-possible="true">フィンランド</option>
	<option value="BT" data-sms-possible="true">ブータン</option>
	<option value="PR" data-sms-possible="true">プエルトリコ</option>
	<option value="BR" data-sms-possible="true">ブラジル</option>
	<option value="FR" data-sms-possible="true">フランス</option>
	<option value="PF" data-sms-possible="true">フランス領ポリネシア</option>
	<option value="BG" data-sms-possible="true">ブルガリア</option>
	<option value="BF" data-sms-possible="true">ブルキナファソ</option>
	<option value="BN" data-sms-possible="true">ブルネイ</option>
	<option value="BI" data-sms-possible="true">ブルンジ</option>
	<option value="VN" data-sms-possible="true">ベトナム</option>
	<option value="BJ" data-sms-possible="true">ベナン</option>
	<option value="VE" data-sms-possible="true">ベネズエラ</option>
	<option value="BY" data-sms-possible="true">ベラルーシ</option>
	<option value="BZ" data-sms-possible="true">ベリーズ</option>
	<option value="PE" data-sms-possible="true">ペルー</option>
	<option value="BE" data-sms-possible="true">ベルギー</option>
	<option value="PL" data-sms-possible="true">ポーランド</option>
	<option value="BA" data-sms-possible="true">ボスニア・ヘルツェゴビナ</option>
	<option value="BW" data-sms-possible="true">ボツワナ</option>
	<option value="BO" data-sms-possible="true">ボリビア</option>
	<option value="PT" data-sms-possible="true">ポルトガル</option>
	<option value="HK" data-sms-possible="true">香港</option>
	<option value="HN" data-sms-possible="true">ホンジュラス</option>
	<option value="MH" data-sms-possible="true">マーシャル諸島</option>
	<option value="MO" data-sms-possible="true">マカオ</option>
	<option value="MG" data-sms-possible="true">マダガスカル</option>
	<option value="MW" data-sms-possible="true">マラウイ</option>
	<option value="ML" data-sms-possible="true">マリ</option>
	<option value="MT" data-sms-possible="true">マルタ</option>
	<option value="MY" data-sms-possible="true">マレーシア</option>
	<option value="FM" data-sms-possible="true">ミクロネシア連邦</option>
	<option value="ZA" data-sms-possible="true">南アフリカ</option>
	<option value="MM" data-sms-possible="true">ミャンマー</option>
	<option value="MX" data-sms-possible="true">メキシコ</option>
	<option value="MU" data-sms-possible="true">モーリシャス</option>
	<option value="MR" data-sms-possible="true">モーリタニア</option>
	<option value="MZ" data-sms-possible="true">モザンビーク</option>
	<option value="MC" data-sms-possible="true">モナコ</option>
	<option value="MV" data-sms-possible="true">モルディヴ</option>
	<option value="MD" data-sms-possible="true">モルドバ</option>
	<option value="MA" data-sms-possible="true">モロッコ</option>
	<option value="MN" data-sms-possible="true">モンゴル</option>
	<option value="ME" data-sms-possible="true">モンテネグロ</option>
	<option value="JO" data-sms-possible="true">ヨルダン</option>
	<option value="LA" data-sms-possible="true">ラオス人民民主共和国</option>
	<option value="LV" data-sms-possible="true">ラトビア</option>
	<option value="LT" data-sms-possible="true">リトアニア</option>
	<option value="LY" data-sms-possible="true">リビア</option>
	<option value="LI" data-sms-possible="true">リヒテンシュタイン</option>
	<option value="LR" data-sms-possible="true">リベリア</option>
	<option value="RO" data-sms-possible="true">ルーマニア</option>
	<option value="LU" data-sms-possible="true">ルクセンブルク</option>
	<option value="RW" data-sms-possible="true">ルワンダ</option>
	<option value="LS" data-sms-possible="true">レソト</option>
	<option value="LB" data-sms-possible="true">レバノン</option>
	<option value="RU" data-sms-possible="true">ロシア</option>
</select>
						</li>
						<li class="numberInputWrap">
							<span id="numberInputInfo1" class="supplementTxt marginTop">先頭に0(ゼロ)がある場合、0(ゼロ)を抜いてください(一部、国・地域除く)。携帯電話番号の場合は、携帯電話を契約された国を選択ください。</span>
							<div class="numberRowWrap">
								<div class="block">
									
									<span class="visuallyHidden">国番号</span>
									<span class="viewInput widthMini js-countryCode"></span>
								</div>
								<div class="block"><label for="representativeTel" class="visuallyHidden">電話番号</label><input id="representativeTel" type="text" name="representativeTel" value="9042504176" class="widthMiddle" placeholder="例：9012345678" maxlength="30" aria-describedby="numberInputInfo1" />
								</div>
							</div>
						</li>
					</ul>
				</dd>
			</dl>
		</fieldset>
	</div>
	<div class="titleWrapper">
		<h3>
			<span class="text">eメール・SMSに関するご案内<a href="https://www.ana.co.jp/other/int/meta/0057.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="eメール・SMSに関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a></span>
		</h3>
	</div>
	<div class="formInput">
	<h4 class="formAreaTitle">主に搭乗・運航に関わるeメールの送付先設定（米国へ通知いたします）<a href="https://www.ana.co.jp/other/int/meta/0629.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="米国入国用eメール・SMS・電話番号に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a>
	</h4>
	<div class="paxSettingArea js-mailFormWrap onePax is-open">
			<div class="settingArea">
					<dl>
						<dt class="breakWord">NAOKI SATO
						</dt>
						<dd>
							<fieldset>
								<legend>NAOKI SATOのeメール送付先および米国到着時に提供するメールアドレス</legend>代表者と同じ
							</fieldset>
						</dd>
					</dl>
			</div>
	</div>
	<h4 class="formAreaTitle hasTopLine">主に搭乗・運航に関わるSMSの送付先および米国で受信できる電話番号の設定（米国へ通知いたします）<a href="https://www.ana.co.jp/other/int/meta/0629.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="米国入国用eメール・SMS・電話番号に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a>
	</h4>
	<div class="paxSettingArea js-smsFormWrap onePax is-open">
			<div class="settingArea">
					<dl>
						<dt class="breakWord">NAOKI SATO<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span>
						</dt>
						<dd>
							<fieldset>
								<legend>NAOKI SATOの1つ目の電話番号</legend>
									<ul class="verticalList js-formItem">
										<li><input type="radio" id="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSameAsRepresentativeSms:radioItem" name="contactsSms:0:recipientSmsEachPersonSelection:radioGroup" value="0" checked="checked" /><label for="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSameAsRepresentativeSms:radioItem">代表者電話番号と同じ</label>
										</li>
										<li><input type="radio" id="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSmsPerPassenger:radioItem" name="contactsSms:0:recipientSmsEachPersonSelection:radioGroup" value="1" /><label for="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSmsPerPassenger:radioItem">電話番号を指定</label>
											<div class="indentArea js-formImputArea">
												<span id="confirmTelNumberForRepresentative:0" class="supplementTxt marginTop">先頭に0(ゼロ)がある場合、0(ゼロ)を抜いてください(一部、国・地域除く)。携帯電話番号の場合は、携帯電話を契約された国を選択ください。</span>
												<ul class="countryNumberInput js-countryWrap">
													<li><label for="contactsSms:0:recipientSmsEachPersonSelection:passengerSmsCountry" class="visuallyHidden">電話番号の国/地区選択</label><select id="contactsSms:0:recipientSmsEachPersonSelection:passengerSmsCountry" name="contactsSms:0:recipientSmsEachPersonSelection:passengerSmsCountry" class="countrySelect js-countrySelect" size="1">	<option value="" data-country-code="---">選択してください</option>
	<option value="JP">日本</option>
	<option value="-" data-country-code="---">-----------</option>
	<option value="IE">アイルランド</option>
	<option value="AZ">アゼルバイジャン</option>
	<option value="AF">アフガニスタン</option>
	<option value="US">アメリカ合衆国/カナダ</option>
	<option value="AE">アラブ首長国連邦</option>
	<option value="DZ">アルジェリア</option>
	<option value="AR">アルゼンチン</option>
	<option value="AW">アルバ</option>
	<option value="AL">アルバニア</option>
	<option value="AM">アルメニア</option>
	<option value="AG">アンティグア・バーブーダ</option>
	<option value="AD">アンドラ</option>
	<option value="YE">イエメン</option>
	<option value="GB">イギリス</option>
	<option value="IL">イスラエル</option>
	<option value="IT">イタリア</option>
	<option value="IQ">イラク</option>
	<option value="IR">イラン</option>
	<option value="IN">インド</option>
	<option value="ID">インドネシア</option>
	<option value="UG">ウガンダ</option>
	<option value="UA">ウクライナ</option>
	<option value="UZ">ウズベキスタン</option>
	<option value="UY">ウルグアイ</option>
	<option value="EC">エクアドル</option>
	<option value="EG">エジプト</option>
	<option value="EE">エストニア</option>
	<option value="ER">エリトリア</option>
	<option value="SV">エルサルバドル</option>
	<option value="AU">オーストラリア</option>
	<option value="AT">オーストリア</option>
	<option value="OM">オマーン</option>
	<option value="NL">オランダ</option>
	<option value="GH">ガーナ</option>
	<option value="GY">ガイアナ</option>
	<option value="KZ">カザフスタン</option>
	<option value="QA">カタール</option>
	<option value="CA">カナダ/アメリカ合衆国</option>
	<option value="GA">ガボン</option>
	<option value="CM">カメルーン</option>
	<option value="KR">韓国</option>
	<option value="GM">ガンビア</option>
	<option value="KH">カンボジア</option>
	<option value="GN">ギニア</option>
	<option value="GW">ギニアビサウ</option>
	<option value="CY">キプロス</option>
	<option value="CU">キューバ</option>
	<option value="GR">ギリシャ</option>
	<option value="KG">キルギス</option>
	<option value="GT">グアテマラ</option>
	<option value="GU">グアム</option>
	<option value="KW">クウェート</option>
	<option value="GD">グレナダ</option>
	<option value="HR">クロアチア</option>
	<option value="KY">ケイマン諸島</option>
	<option value="KE">ケニア</option>
	<option value="CI">コートジボワール</option>
	<option value="CR">コスタリカ</option>
	<option value="KM">コモロ</option>
	<option value="CO">コロンビア</option>
	<option value="CD">コンゴ民主共和国</option>
	<option value="SA">サウジアラビア</option>
	<option value="ZM">ザンビア</option>
	<option value="SL">シエラレオネ</option>
	<option value="JM">ジャマイカ</option>
	<option value="GE">ジョージア</option>
	<option value="SY">シリア</option>
	<option value="SG">シンガポール</option>
	<option value="ZW">ジンバブエ</option>
	<option value="CH">スイス</option>
	<option value="SE">スウェーデン</option>
	<option value="SD">スーダン</option>
	<option value="ES">スペイン</option>
	<option value="SR">スリナム</option>
	<option value="LK">スリランカ</option>
	<option value="SK">スロバキア</option>
	<option value="SI">スロベニア</option>
	<option value="SN">セネガル</option>
	<option value="RS">セルビア</option>
	<option value="KN">セントクリストファー・ネイビス</option>
	<option value="LC">セントルシア</option>
	<option value="TH">タイ</option>
	<option value="TW">台湾</option>
	<option value="TJ">タジキスタン</option>
	<option value="TZ">タンザニア</option>
	<option value="CZ">チェコ</option>
	<option value="TD">チャド</option>
	<option value="CF">中央アフリカ共和国</option>
	<option value="CN">中国</option>
	<option value="TN">チュニジア</option>
	<option value="CL">チリ</option>
	<option value="DK">デンマーク</option>
	<option value="DE">ドイツ</option>
	<option value="TG">トーゴ</option>
	<option value="DO">ドミニカ共和国</option>
	<option value="TT">トリニダード・トバゴ</option>
	<option value="TR">トルコ</option>
	<option value="NG">ナイジェリア</option>
	<option value="NI">ニカラグア</option>
	<option value="NE">ニジェール</option>
	<option value="NZ">ニュージーランド</option>
	<option value="NP">ネパール</option>
	<option value="NO">ノルウェー</option>
	<option value="HT">ハイチ</option>
	<option value="PK">パキスタン</option>
	<option value="PA">パナマ</option>
	<option value="BS">バハマ</option>
	<option value="PG">パプアニューギニア</option>
	<option value="BM">バミューダ</option>
	<option value="PW">パラオ</option>
	<option value="PY">パラグアイ</option>
	<option value="BB">バルバドス</option>
	<option value="PS">パレスチナ</option>
	<option value="HU">ハンガリー</option>
	<option value="BD">バングラデシュ</option>
	<option value="TL">東ティモール</option>
	<option value="FJ">フィジー</option>
	<option value="PH">フィリピン</option>
	<option value="FI">フィンランド</option>
	<option value="BT">ブータン</option>
	<option value="PR">プエルトリコ</option>
	<option value="BR">ブラジル</option>
	<option value="FR">フランス</option>
	<option value="PF">フランス領ポリネシア</option>
	<option value="BG">ブルガリア</option>
	<option value="BF">ブルキナファソ</option>
	<option value="BN">ブルネイ</option>
	<option value="BI">ブルンジ</option>
	<option value="VN">ベトナム</option>
	<option value="BJ">ベナン</option>
	<option value="VE">ベネズエラ</option>
	<option value="BY">ベラルーシ</option>
	<option value="PE">ペルー</option>
	<option value="BE">ベルギー</option>
	<option value="PL">ポーランド</option>
	<option value="BA">ボスニア・ヘルツェゴビナ</option>
	<option value="BW">ボツワナ</option>
	<option value="HK">香港</option>
	<option value="MH">マーシャル諸島</option>
	<option value="MO">マカオ</option>
	<option value="MG">マダガスカル</option>
	<option value="MW">マラウイ</option>
	<option value="ML">マリ</option>
	<option value="MT">マルタ</option>
	<option value="MY">マレーシア</option>
	<option value="ZA">南アフリカ</option>
	<option value="MM">ミャンマー</option>
	<option value="MX">メキシコ</option>
	<option value="MU">モーリシャス</option>
	<option value="MR">モーリタニア</option>
	<option value="MZ">モザンビーク</option>
	<option value="MC">モナコ</option>
	<option value="MV">モルディヴ</option>
	<option value="MA">モロッコ</option>
	<option value="MN">モンゴル</option>
	<option value="ME">モンテネグロ</option>
	<option value="JO">ヨルダン</option>
	<option value="LA">ラオス人民民主共和国</option>
	<option value="LV">ラトビア</option>
	<option value="LT">リトアニア</option>
	<option value="LY">リビア</option>
	<option value="LR">リベリア</option>
	<option value="RO">ルーマニア</option>
	<option value="LU">ルクセンブルク</option>
	<option value="RW">ルワンダ</option>
	<option value="LB">レバノン</option>
	<option value="RU">ロシア</option>
</select>
													</li>
													<li>
														
														<div class="numberRowWrap">
															<div class="block">
																<span class="visuallyHidden">国番号</span>
																<span class="countryCode viewInput js-countryCode"></span>
															</div>
															
															<div class="block"><label for="contactsSms:0:recipientSmsEachPersonSelection:flightStatusNotificationContactPointSmsDescription" class="visuallyHidden">電話番号</label><input id="contactsSms:0:recipientSmsEachPersonSelection:flightStatusNotificationContactPointSmsDescription" type="text" name="contactsSms:0:recipientSmsEachPersonSelection:flightStatusNotificationContactPointSmsDescription" class="telNumber" placeholder="例：9012345678" maxlength="30" aria-describedby="confirmTelNumberForRepresentative:0" />
															</div>
														</div>
													</li>
												</ul>
											</div>
										</li>
									</ul>
							</fieldset>
						</dd>
					</dl>
			</div>
	</div>
	</div>
	
	<div class="formInput articleLine">
		<h3>滞在中の連絡先(任意)
		</h3>
		<dl>
			
			<dt><label for="accommodationOne">現地連絡先1</label>
			</dt>
			<dd><input id="accommodationOne" type="text" name="accommodationOne" class="widthLarge" placeholder="例：09012345678 ABC HOTEL" maxlength="28" title="例：09012345678 ABC HOTEL" />
			</dd>
		</dl>
		<dl>
			
			<dt><label for="accommodationTwo">現地連絡先2</label>
			</dt>
			<dd><input id="accommodationTwo" type="text" name="accommodationTwo" class="widthLarge" placeholder="例：09012345678 ABC HOTEL" maxlength="28" title="例：09012345678 ABC HOTEL" />
			</dd>
		</dl>
	</div>
		</div>
		
		<p class="btnAreaSubmit btnArrowNext "><input id="next" type="submit" name="next" value="次へ" class="btnBase btnMainStream btnVerticalMain btnWidthVariable" onclick="return Asw.Dialog.getInstance('prebookConfirmDialog').toggle(event);return Asw.LoadingWindow.open(&quot;NORMAL&quot;, event)" />
		</p><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:2" value="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" autocomplete="off" />
</form><div id="cmnDynamicMessages" class="infoBoxSection">
				<dl class="infoBox importantInfo jsAccordionSwitch">
					<dt><strong>重要なご案内</strong></dt>
					<dd>
						<ul class="jsAccordionSwitchList" id="informationMessages">
								<li>個人情報の利用目的<a href="https://www.ana.co.jp/other/int/meta/0012.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="ANAプライバシーポリシー(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">ANAプライバシーポリシー<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>を必ずお読みいただき、内容に同意いただいた上で、お進みください。</li>
								<li><a href="https://www.ana.co.jp/other/int/meta/0058.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="Secure Flight Programについて(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">米国Secure Flightプログラム<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>に基づき、パスポート名、生年月日、性別などの情報は事前に米国へ通知いたします。</li>
								<li>入力したeメールアドレス、電話番号は入国に必要な情報として米国に通知される可能性があります。</li>
									<li class="jsHiddenFlg toggleContents">お名前のスペルがパスポート名と一致しているかご確認ください。異なる場合はインターネットではお取り扱いできません。<a href="https://www.ana.co.jp/other/int/meta/intbe0001.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="ANAお問い合わせ先(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">ANA<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>へ名前の変更をお申し出ください。</li>
									<li class="jsHiddenFlg toggleContents">二重国籍のお客様は、ご搭乗時に使用されるパスポートのお名前でご予約ください。<br />パスポートごとにお名前が異なる場合は、お客様ご自身で入国管理事務所・外務省・大使館などで各種条件をご確認のうえ、使用されるパスポートを決めてください。<br />なお、入国可否については国によって異なるため、お客様ご自身で各入国管理事務所などでご確認ください。</li>
									<li class="jsHiddenFlg toggleContents">旅行開始日時点で12歳以上の場合は大人、2歳以上12歳未満の場合は小児、2歳未満の場合は幼児の扱いとなります。なお、小児は大人と同マイル数が必要です。</li>
									<li class="jsHiddenFlg toggleContents">運航に関する情報をお客様へ通知するため、提携航空会社にもお客様のメールアドレスが提供されます。あらかじめご了承ください。</li>
									<li class="jsHiddenFlg toggleContents"><dl><dt>携帯電話へのメール送信をご希望のお客様へ</dt><dd>ドメイン指定受信をご利用の場合は、ana.co.jpとamadeus.comからのメールが受信できるようあらかじめ設定をお願いいたします。</dd></dl></li>
									<li class="jsHiddenFlg toggleContents">SMSはスマートフォンの番号に配信されます。一部の国ではSMSでのご案内をご利用いただけません。詳細は<a href="https://www.ana.co.jp/other/int/meta/0737.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="ショートメッセージ(SMS)でのご案内対象外の国について(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">ショートメッセージ(SMS)でのご案内対象外の国について<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>をご覧ください。</li>
									<li class="jsHiddenFlg toggleContents">ログインされている会員ご本人が搭乗しない場合は、再度フライト検索画面より「ログインされている会員ご本人は搭乗しない」のチェックボックスを選択してお手続きください。</li>
						</ul>
							<a href="#" class="toggleSwitch jsTriggerSwitch" role="button" aria-controls="informationMessages" aria-expanded="false"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/toggle_open.png?7bec38a" alt="開閉ボタン" height="25" width="25" class="jsRollOver" />
							</a>
					</dd>
				</dl></div>
	<!--=========== /main =============-->
	</div>

	<!--=========== Script ============-->
	<script type="text/javascript">
	$(window).on('load', function() {
		// アコーディオンのメソッドと設定
		Asw.AccordionInfo('.jsAccordionSwitch', {
			'triggerSwitch': '.jsAccordionSwitch .jsTriggerSwitch',
			'listClass': '.jsAccordionSwitchList',
			'visibilityHidden': 'toggleContents'
		});
	});
	</script>
	<!--=========== Script ============-->
	<script type="text/javascript">
		//プラットフォーム判別（サマリーエリア追従用として）
		var Asw = Asw || {};
	
	  	Asw.ClientInfo = Asw.ClientInfo  || {};
	  	Asw.ClientInfo.DeviceType = Asw.ClientInfo.DeviceType || {};
	
	  	Asw.ClientInfo.DeviceType.PC = 'PC';
	  	Asw.ClientInfo.DeviceType.SP = 'SP';
	  	Asw.ClientInfo.DeviceType.TC = 'TC';
	
	  	Asw.ClientInfo.deviceType = Asw.ClientInfo.DeviceType.PC;
	
	 	if((navigator.userAgent.indexOf('Android') > 0 && navigator.userAgent.indexOf('Mobile') == -1) || navigator.userAgent.indexOf('iPad') > 0){
	    	Asw.ClientInfo.deviceType = Asw.ClientInfo.DeviceType.TC;
	  	}
	</script>
	<!--=========== /Script ============--><div id="summaryArea">
			<div class="flightSummaryArea">
						<div class="flightSummarySection">
							<h2>区間 1
							</h2>
							<p class="flightDate"><em>6</em>月<em class="hasLeft">28</em>日（土）
							</p>
							<div class="flightSummaryRoute">
									<div class="flightSummaryRouteSection">
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation1_1">
													<span>東京(成田)
													</span>
												</p>
												<p class="time">
													<span>17:00
													</span>
												</p>
											</div>
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation1_1">
													<span>シカゴ(ORD)
													</span>
												</p>
												<p class="time">
													<span>14:55
													</span>
												</p>
											</div>
										<p class="flightNumberClass">
												<span>NH012</span><span><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/flight_ana_summaryarea.png?717d3c0" alt="ANA運航便" height="11" width="41" /></span>
												<br />特典ビジネス
										</p>
									</div>
							</div>
						</div>
						<div class="flightSummarySection">
							<h2 class="midstream">区間 2
							</h2>
							<p class="flightDate"><em>7</em>月<em class="hasLeft">23</em>日（水）
							</p>
							<div class="flightSummaryRoute">
									<div class="flightSummaryRouteSection">
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation2_1">
													<span>ホノルル(オアフ島)
													</span>
												</p>
												<p class="time">
													<span>11:35
													</span>
												</p>
											</div>
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation2_1">
													<span>東京(成田)
													</span>
												</p>
												<p class="time">
													<span>14:50
																	<span>翌日
																	</span>
													</span>
												</p>
											</div>
										<p class="flightNumberClass">
												<span>NH183</span><span><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/flight_ana_summaryarea.png?717d3c0" alt="ANA運航便" height="11" width="41" /></span>
												<br />特典エコノミー
										</p>
									</div>
							</div>
						</div>
					<div class="total">
						<p class="passenger">大人 x1
						</p>
						<div class="totalArea">
					  		<dl class="mealAmountWrap">
									<dt>有料機内食サービス
									</dt>
									<dd><em class="mealAmount">0</em><span class="currencyCode">円</span>
									</dd>
							</dl>
									<div>
										<div class="mileageAndTotal">
												<dl class="requiredMileage">
													<dt>必要マイル
													</dt>
													<dd><em class="requiredMileage">74,000</em><span class="currencyCode">マイル</span>
													</dd>
												</dl>
												<dl>
													<dt>総額
													</dt>
													<dd><em class="awardTotalPayment">68,130</em>円
													</dd>
												</dl>
												<p class="includeFareAndFuelCost">※各種税金、燃油特別付加運賃等を含みます。
												</p>
										</div>
									</div>
						</div>
					</div>
				
			</div></div>
		<!--===== /cmnWrapper =====-->
		</div>
			
		<span id="message" class="visuallyHidden" role="alert" aria-live="assertive"></span>
		<span id="politeMessage" class="visuallyHidden" role="log" aria-live="polite"></span><script type="text/javascript">
		var Asw = Asw || {};
		Asw.BaseOutput = {"sessionID":"IPuLqJe9_e","language":"ja","siteCatalystLanguageCode":"J","operationDate":"20250620","operationDateTime":"20250620134623","office":{"officeCode":"TYONH08ZZ","country2letterCode":"JP"},"bookingType":"A","pageID":"A04_P01","siteCatalystPageName":"INT_BE_AWARD_J_A04特典予約_P01旅客情報入力（必須情報）","device":{"deviceType":"PC"}};
	</script><div id="promotionArea">
		<!--===== promotionArea =====-->
		<!--===== /promotionArea =====--></div>
		<!--=========== /cmnContainer ============--></div><div id="cmnBaloonArea" aria-live="polite"></div><div id="cmnPopupArea" aria-live="polite"></div>

	

	<div id="cmnFooterWrapper">
		<!--=========== .cmnFooterWrapper ============-->
		<div id="cmnFooter">
			<!--===== cmnFooter =====-->
			<ul>
				<li><a href="https://www.ana.co.jp/other/int/meta/0012.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">プライバシーポリシー<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
					<li><a href="https://www.ana.co.jp/other/int/meta/0670.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">利用者情報の外部送信について<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
					</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/0052.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">サイト利用規約<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/be0238.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">推奨環境<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/0133.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">サイトマップ<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/0732.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">運送約款<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
					<li class="lastChild"><a href="https://www.ana.co.jp/other/int/meta/0556.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">ウェブアクセシビリティについて<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
					</li>
			</ul>
			<p class="blankText" aria-hidden="true"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="" height="10" width="10" />外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。</p>
			<p class="copyright"><span lang="en" xml:lang="en">Copyright&#169;ANA</span>
			</p>
			<p class="starAllianceLogo"><a href="http://www.staralliance.com/ja/" target="_blank"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/star_alliance_logo.png?0fe73b5" alt="A STAR ALLIANCE MEMBER" height="18" width="175" /><span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
			</p>
			<!--===== cmnFooter =====-->
		</div>
		<!--=========== /cmnFooterWrapper ============-->
	</div>

		
		<div id="cmnLoadingForAjax"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/bg_modalWindow.png?717d3c0" alt="" height="0" width="0" />
			<div class="loadingArea">
				<p class="loadingImage"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/loading.gif?717d3c0" alt="リクエストを処理しています...しばらくお待ちください。" height="33" width="33" />
				</p>
			</div>
		</div>
		
		
		<div id="popupAltSentence" style="display: none" aria-hidden="true">別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。</div>
	<div id="cmnSiteCatalyst" aria-hidden="true"><div id="cmnSiteCatalystParamArea"><script type="text/javascript">
				// ▼SiteCatalyst
				// SiteCatalyst code version: H.2.
				// Copyright 1997-2005 Omniture, Inc. More info available at http://www.omniture.com
				SiteCatalystReportSuites   = "INT";
				SiteCatalystCharSet        = "UTF-8";
				SiteCatalystChannel        = "BE_AWARD_J";
				SiteCatalystPageName       = "INT_BE_AWARD_J_A04\u7279\u5178\u4E88\u7D04_P01\u65C5\u5BA2\u60C5\u5831\u5165\u529B\uFF08\u5FC5\u9808\u60C5\u5831\uFF09";
				SiteCatalystERR            = "";
				SiteCatalystEventNum       = "";
				SiteCatalystPurchase       = "FALSE";
				SiteCatalystProductPrice   = "";
				SiteCatalystCurrencyCode   = "";
				SiteCatalystSeatAvailabilityINT   = "";
				SiteCatalystDepartDate     = "";
				SiteCatalystDepart         = "";
				SiteCatalystArrive         = "";
				SiteCatalystFlightNo       = "";
				SiteCatalystClass          = "";
				SiteCatalystFare           = "";
				SiteCatalystPurchaseID     = "";
				
				SiteCatalystLocater        = "";
				SiteCatalystPayment        = "";
				SiteCatalystIncome         = "";
				
				SiteCatalystPax            = "";
				SiteCatalystManipulateDay  = "";
				SiteCatalystCompartmentClass   = "";
				SiteCatalystOptionInfo     = "";
				SiteCatalystOutboundUpsellPrice   = "";
				SiteCatalystInboundUpsellPrice    = "";
				
				SiteCatalystSearchInput    = "";
				SiteCatalystSegment        = "";
				SiteCatalystOfficeCode     = "";
				SiteCatalystPassengerCount = "";
				SiteCatalystPassenger      = "";
				SiteCatalystAncillaryService = "";
				// End SiteCatalyst code version: H.2.
				// ▲SiteCatalyst
			</script></div><script type="text/javascript"><!-- if(navigator.appVersion.indexOf('MSIE')>=0)document.write(unescape('%3C')+'\!-'+'-') // --></script>
		<!--/DO NOT REMOVE/-->
		

		
		<!-- Google Tag Manager -->
		<script type="text/javascript" charset="Shift_JIS" src="https://www.ana.co.jp/common/js/GTMINTBE.js"></script>
		<!-- End Google Tag Manager -->
		<!-- Yahoo Tag Manager -->
		<script type="text/javascript" charset="Shift_JIS" src="https://www.ana.co.jp/common/js/YTMINTBE.js"></script>
		<!-- End Yahoo Tag Manager -->
		

	</div>

	

	<div class="mboxDefault"></div><script type="text/javascript">mboxCreate("ASW_common_A04_P01");</script>
			<script type="text/javascript" src="https://www.ana.co.jp/common/js/tealium/tealium.js"></script>
			<script type="text/javascript" src="//cdn.evgnet.com/beacon/allnipponairways/nonamcprd/scripts/evergage.min.js"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/javax.faces.resource/jsf.js.xhtml?ln=javax.faces"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/jquery.mousewheel.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/jquery.jscrollpane.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/jquery-ui-1.10.4.custom.min.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/jquery.ui.touch-punch.min.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/asw-common.js?beb6a50"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/asw-common-pc.js?fd88d7d"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/asw-format.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/mask.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/loading.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/asw-loadingwindow-pc.js?9e233c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/asw-loadingwindow.js?51db600"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/dialog-pc.js?25d36a4"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/asw-dialog-sequencer.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/asw-event.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/asw-date-select.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/asw-calendar-pc.js?9b86b8a"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/asw-calendar.js?db624c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/heightLine.js?0e7a1ce"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/asw-summaryarea.js?b21c6d7"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/asw-fingerprints.js?99001fc"></script><script type="text/javascript">
			Asw.init("https://aswbe-i.ana.co.jp/rei21g/international_asw", "ja");
			Asw.getCalendarHolidayList = function() {return [20250721,20250811,20250915,20250923,20251013,20251103,20251123,20251124,20260101,20260112,20260211,20260223,20260320,20260429,20260503,20260504,20260505,20260506,20260720,20260811,20260921,20260922,20260923,20261012,20261103,20261123,20270101,20270111,20270211,20270223,20270321,20270322,20270429,20270503,20270504,20270505,20270719,20270811,20270920,20270923,20271011,20271103,20271123,20280101,20280110,20280211,20280223,20280320,20280429,20280503,20280504,20280505,20280717,20280811,20280918,20280922,20281009,20281103,20281123,20290101,20290108,20290211,20290212,20290223,20290320,20290429,20290430,20290503,20290504,20290505,20290716,20290811,20290917,20290923,20290924,20291008,20291103,20291123];}
			Asw.ClientInfo.deviceType = "PC";
			Asw.ClientInfo.mobileDeviceType = "UNKNOWN";
			Asw.ClientInfo.osVersion = "";
			Asw.Format.init("https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/{1}/{2}/{3}?{0}");
			Asw.overwriteTargetAttributeValue("false");
		</script><script type="text/javascript">
		
		var Asw = Asw || {};
		Asw.ContactInput = Asw.ContactInput || {};
		Asw.ContactInput.Const = {
			/** data-属性名「data-type」 @constant @type {String} @default 'data-type' */
			DATA_ATTRIBUTE_NAME_TYPE: 'data-type',
			/** data-属性名「data-sms-possible」 @constant @type {String} @default 'data-sms-possible' */
			DATA_ATTRIBUTE_NAME_SMS_POSSIBLE: 'data-sms-possible',
			/** data-属性名「data-country-code」 @constant @type {String} @default 'data-country-code' */
			DATA_ATTRIBUTE_NAME_COUNTRY_CODE: 'data-country-code',
			/** data-type属性値「sms」 @constant @type {String} @default 'sms' */
			DATA_TYPE_VALUE_SMS: 'sms'
		};

		
		Asw.ContactInput.countryInfoMap = {"IS":{"internationalCallingCode":"+354","smsPossible":false},"IE":{"internationalCallingCode":"+353","smsPossible":true},"AZ":{"internationalCallingCode":"+994","smsPossible":true},"AF":{"internationalCallingCode":"+93","smsPossible":true},"US":{"internationalCallingCode":"+1","smsPossible":true},"AE":{"internationalCallingCode":"+971","smsPossible":true},"DZ":{"internationalCallingCode":"+213","smsPossible":true},"AR":{"internationalCallingCode":"+54","smsPossible":true},"AW":{"internationalCallingCode":"+297","smsPossible":true},"AL":{"internationalCallingCode":"+355","smsPossible":true},"AM":{"internationalCallingCode":"+374","smsPossible":true},"AO":{"internationalCallingCode":"+244","smsPossible":false},"AG":{"internationalCallingCode":"+1","smsPossible":true},"AD":{"internationalCallingCode":"+376","smsPossible":true},"YE":{"internationalCallingCode":"+967","smsPossible":true},"GB":{"internationalCallingCode":"+44","smsPossible":true},"IL":{"internationalCallingCode":"+972","smsPossible":true},"IT":{"internationalCallingCode":"+39","smsPossible":true},"IQ":{"internationalCallingCode":"+964","smsPossible":true},"IR":{"internationalCallingCode":"+98","smsPossible":true},"IN":{"internationalCallingCode":"+91","smsPossible":true},"ID":{"internationalCallingCode":"+62","smsPossible":true},"UG":{"internationalCallingCode":"+256","smsPossible":true},"UA":{"internationalCallingCode":"+380","smsPossible":true},"UZ":{"internationalCallingCode":"+998","smsPossible":true},"UY":{"internationalCallingCode":"+598","smsPossible":true},"EC":{"internationalCallingCode":"+593","smsPossible":true},"EG":{"internationalCallingCode":"+20","smsPossible":true},"EE":{"internationalCallingCode":"+372","smsPossible":true},"SZ":{"internationalCallingCode":"+268","smsPossible":false},"ET":{"internationalCallingCode":"+251","smsPossible":false},"ER":{"internationalCallingCode":"+291","smsPossible":true},"SV":{"internationalCallingCode":"+503","smsPossible":true},"AU":{"internationalCallingCode":"+61","smsPossible":true},"AT":{"internationalCallingCode":"+43","smsPossible":true},"OM":{"internationalCallingCode":"+968","smsPossible":true},"NL":{"internationalCallingCode":"+31","smsPossible":true},"GH":{"internationalCallingCode":"+233","smsPossible":true},"CV":{"internationalCallingCode":"+238","smsPossible":false},"GY":{"internationalCallingCode":"+592","smsPossible":true},"KZ":{"internationalCallingCode":"+7","smsPossible":true},"QA":{"internationalCallingCode":"+974","smsPossible":true},"CA":{"internationalCallingCode":"+1","smsPossible":true},"GA":{"internationalCallingCode":"+241","smsPossible":true},"CM":{"internationalCallingCode":"+237","smsPossible":true},"KR":{"internationalCallingCode":"+82","smsPossible":true},"KH":{"internationalCallingCode":"+855","smsPossible":true},"GM":{"internationalCallingCode":"+220","smsPossible":true},"KP":{"internationalCallingCode":"+850","smsPossible":false},"MK":{"internationalCallingCode":"+389","smsPossible":false},"GN":{"internationalCallingCode":"+224","smsPossible":true},"GW":{"internationalCallingCode":"+245","smsPossible":true},"CY":{"internationalCallingCode":"+357","smsPossible":true},"CU":{"internationalCallingCode":"+53","smsPossible":true},"GR":{"internationalCallingCode":"+30","smsPossible":true},"KI":{"internationalCallingCode":"+686","smsPossible":false},"KG":{"internationalCallingCode":"+996","smsPossible":true},"GT":{"internationalCallingCode":"+502","smsPossible":true},"GU":{"internationalCallingCode":"+1","smsPossible":true},"KW":{"internationalCallingCode":"+965","smsPossible":true},"KY":{"internationalCallingCode":"+1","smsPossible":true},"GD":{"internationalCallingCode":"+1","smsPossible":true},"HR":{"internationalCallingCode":"+385","smsPossible":true},"KE":{"internationalCallingCode":"+254","smsPossible":true},"CI":{"internationalCallingCode":"+225","smsPossible":true},"CR":{"internationalCallingCode":"+506","smsPossible":true},"SA":{"internationalCallingCode":"+966","smsPossible":true},"KM":{"internationalCallingCode":"+269","smsPossible":true},"CO":{"internationalCallingCode":"+57","smsPossible":true},"CG":{"internationalCallingCode":"+242","smsPossible":false},"CD":{"internationalCallingCode":"+243","smsPossible":true},"ZM":{"internationalCallingCode":"+260","smsPossible":true},"SM":{"internationalCallingCode":"+378","smsPossible":false},"SL":{"internationalCallingCode":"+232","smsPossible":true},"DJ":{"internationalCallingCode":"+253","smsPossible":false},"JM":{"internationalCallingCode":"+1","smsPossible":true},"CH":{"internationalCallingCode":"+41","smsPossible":true},"GE":{"internationalCallingCode":"+995","smsPossible":true},"SE":{"internationalCallingCode":"+46","smsPossible":true},"SY":{"internationalCallingCode":"+963","smsPossible":true},"ES":{"internationalCallingCode":"+34","smsPossible":true},"SG":{"internationalCallingCode":"+65","smsPossible":true},"LK":{"internationalCallingCode":"+94","smsPossible":true},"SK":{"internationalCallingCode":"+421","smsPossible":true},"SI":{"internationalCallingCode":"+386","smsPossible":true},"ZW":{"internationalCallingCode":"+263","smsPossible":true},"SD":{"internationalCallingCode":"+249","smsPossible":true},"KN":{"internationalCallingCode":"+1","smsPossible":true},"SR":{"internationalCallingCode":"+597","smsPossible":true},"LC":{"internationalCallingCode":"+1","smsPossible":true},"SC":{"internationalCallingCode":"+248","smsPossible":false},"SN":{"internationalCallingCode":"+221","smsPossible":true},"SB":{"internationalCallingCode":"+677","smsPossible":false},"RS":{"internationalCallingCode":"+381","smsPossible":true},"TH":{"internationalCallingCode":"+66","smsPossible":true},"TW":{"internationalCallingCode":"+886","smsPossible":true},"TJ":{"internationalCallingCode":"+992","smsPossible":true},"TZ":{"internationalCallingCode":"+255","smsPossible":true},"CZ":{"internationalCallingCode":"+420","smsPossible":true},"TD":{"internationalCallingCode":"+235","smsPossible":true},"CF":{"internationalCallingCode":"+236","smsPossible":true},"TN":{"internationalCallingCode":"+216","smsPossible":true},"CL":{"internationalCallingCode":"+56","smsPossible":true},"TV":{"internationalCallingCode":"+688","smsPossible":false},"DK":{"internationalCallingCode":"+45","smsPossible":true},"DE":{"internationalCallingCode":"+49","smsPossible":true},"TG":{"internationalCallingCode":"+228","smsPossible":true},"DO":{"internationalCallingCode":"+1","smsPossible":true},"DM":{"internationalCallingCode":"+1","smsPossible":false},"TT":{"internationalCallingCode":"+1","smsPossible":true},"TM":{"internationalCallingCode":"+993","smsPossible":false},"TR":{"internationalCallingCode":"+90","smsPossible":true},"TO":{"internationalCallingCode":"+676","smsPossible":false},"NG":{"internationalCallingCode":"+234","smsPossible":true},"NR":{"internationalCallingCode":"+674","smsPossible":false},"NA":{"internationalCallingCode":"+264","smsPossible":false},"NI":{"internationalCallingCode":"+505","smsPossible":true},"BH":{"internationalCallingCode":"+973","smsPossible":false},"NE":{"internationalCallingCode":"+227","smsPossible":true},"PK":{"internationalCallingCode":"+92","smsPossible":true},"JP":{"internationalCallingCode":"+81","smsPossible":true},"BS":{"internationalCallingCode":"+1","smsPossible":true},"NZ":{"internationalCallingCode":"+64","smsPossible":true},"BM":{"internationalCallingCode":"+1","smsPossible":true},"NP":{"internationalCallingCode":"+977","smsPossible":true},"BB":{"internationalCallingCode":"+246","smsPossible":true},"NO":{"internationalCallingCode":"+47","smsPossible":true},"HT":{"internationalCallingCode":"+509","smsPossible":true},"BD":{"internationalCallingCode":"+880","smsPossible":true},"VA":{"internationalCallingCode":"+39","smsPossible":false},"PA":{"internationalCallingCode":"+507","smsPossible":true},"VU":{"internationalCallingCode":"+678","smsPossible":false},"PG":{"internationalCallingCode":"+675","smsPossible":true},"PW":{"internationalCallingCode":"+680","smsPossible":true},"PY":{"internationalCallingCode":"+595","smsPossible":true},"PS":{"internationalCallingCode":"+970","smsPossible":true},"BR":{"internationalCallingCode":"+55","smsPossible":true},"HU":{"internationalCallingCode":"+36","smsPossible":true},"TL":{"internationalCallingCode":"+670","smsPossible":true},"BG":{"internationalCallingCode":"+359","smsPossible":true},"BF":{"internationalCallingCode":"+226","smsPossible":true},"BN":{"internationalCallingCode":"+673","smsPossible":true},"FJ":{"internationalCallingCode":"+679","smsPossible":true},"PH":{"internationalCallingCode":"+63","smsPossible":true},"FI":{"internationalCallingCode":"+358","smsPossible":true},"BT":{"internationalCallingCode":"+975","smsPossible":true},"BY":{"internationalCallingCode":"+375","smsPossible":true},"BZ":{"internationalCallingCode":"+501","smsPossible":false},"PR":{"internationalCallingCode":"+1","smsPossible":true},"BE":{"internationalCallingCode":"+32","smsPossible":true},"BA":{"internationalCallingCode":"+387","smsPossible":true},"FR":{"internationalCallingCode":"+33","smsPossible":true},"BO":{"internationalCallingCode":"+591","smsPossible":false},"PF":{"internationalCallingCode":"+689","smsPossible":true},"BI":{"internationalCallingCode":"+257","smsPossible":true},"VN":{"internationalCallingCode":"+84","smsPossible":true},"BJ":{"internationalCallingCode":"+229","smsPossible":true},"VE":{"internationalCallingCode":"+58","smsPossible":true},"PE":{"internationalCallingCode":"+51","smsPossible":true},"PL":{"internationalCallingCode":"+48","smsPossible":true},"BW":{"internationalCallingCode":"+267","smsPossible":true},"PT":{"internationalCallingCode":"+351","smsPossible":false},"HK":{"internationalCallingCode":"+852","smsPossible":true},"HN":{"internationalCallingCode":"+504","smsPossible":false},"MH":{"internationalCallingCode":"+692","smsPossible":true},"MO":{"internationalCallingCode":"+853","smsPossible":true},"MG":{"internationalCallingCode":"+261","smsPossible":true},"MW":{"internationalCallingCode":"+265","smsPossible":true},"ML":{"internationalCallingCode":"+223","smsPossible":true},"MT":{"internationalCallingCode":"+356","smsPossible":true},"MY":{"internationalCallingCode":"+60","smsPossible":true},"FM":{"internationalCallingCode":"+691","smsPossible":false},"MM":{"internationalCallingCode":"+95","smsPossible":true},"MX":{"internationalCallingCode":"+52","smsPossible":true},"MU":{"internationalCallingCode":"+230","smsPossible":true},"MR":{"internationalCallingCode":"+222","smsPossible":true},"MZ":{"internationalCallingCode":"+258","smsPossible":true},"MC":{"internationalCallingCode":"+377","smsPossible":true},"MV":{"internationalCallingCode":"+960","smsPossible":true},"MD":{"internationalCallingCode":"+373","smsPossible":false},"RO":{"internationalCallingCode":"+40","smsPossible":true},"MA":{"internationalCallingCode":"+212","smsPossible":true},"MN":{"internationalCallingCode":"+976","smsPossible":true},"ME":{"internationalCallingCode":"+382","smsPossible":true},"JO":{"internationalCallingCode":"+962","smsPossible":true},"LA":{"internationalCallingCode":"+856","smsPossible":true},"RU":{"internationalCallingCode":"+7","smsPossible":true},"LV":{"internationalCallingCode":"+371","smsPossible":true},"LT":{"internationalCallingCode":"+370","smsPossible":true},"LY":{"internationalCallingCode":"+218","smsPossible":true},"GQ":{"internationalCallingCode":"+240","smsPossible":false},"LI":{"internationalCallingCode":"+423","smsPossible":false},"LR":{"internationalCallingCode":"+231","smsPossible":true},"CN":{"internationalCallingCode":"+86","smsPossible":true},"LU":{"internationalCallingCode":"+352","smsPossible":true},"ZA":{"internationalCallingCode":"+27","smsPossible":true},"RW":{"internationalCallingCode":"+250","smsPossible":true},"LS":{"internationalCallingCode":"+266","smsPossible":false},"LB":{"internationalCallingCode":"+961","smsPossible":true}};

		$(function () {
			contactInfoInit();
		});
		
		function contactInfoInit() {
			
			
			Asw.get('representativeTelType').find('option[value="M1"]').attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_TYPE,
					Asw.ContactInput.Const.DATA_TYPE_VALUE_SMS);
			Asw.get('representativeTelCountry').children('option').each(function(i,option){
				var countryInfo = Asw.ContactInput.countryInfoMap[option.value];
				if (countryInfo === void 0) {
					
					return true;
				}
				$(option).attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_SMS_POSSIBLE, countryInfo.smsPossible);
				$(option).attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_COUNTRY_CODE, countryInfo.internationalCallingCode);
			});
			
			var smsCountrySelectSelector = 'select.js-countrySelect:not(#representativeTelCountry)'
			$(smsCountrySelectSelector).eq(0).children('option').each(function(i,option){
				var countryInfo = Asw.ContactInput.countryInfoMap[option.value];
				if (countryInfo === void 0) {
					
					return true;
				}
				$(smsCountrySelectSelector + ' option:nth-child(' + (i+1) + ')').attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_COUNTRY_CODE,
						countryInfo.internationalCallingCode);
			});
			
			var emergencyCountrySelectSelector = 'select.passengerEmergencyCountry.js-countrySelect:not(#representativeTelCountry)'
			$(emergencyCountrySelectSelector).eq(0).children('option').each(function(i,option){
				var countryInfo = Asw.ContactInput.countryInfoMap[option.value];
				if (countryInfo === void 0) {
					
					return true;
				}
				$(emergencyCountrySelectSelector + ' option:nth-child(' + (i+1) + ')').attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_COUNTRY_CODE,
						countryInfo.internationalCallingCode);
			});
		}
	</script><script type="text/javascript">
		var isInit = true;
		function changeTelType(_this, telNoInputId) {
			var $telNoInput = Asw.get(telNoInputId);
			var $telNoKind = Asw.get("representativeTelType");
			var telNo = Asw.getValueComparedToPlaceholder(telNoInputId);
			var telNoCountry = Asw.get("representativeTelCountry").val();

			if(true && !isInit) {
				if(telInputCheck(telNo, telNoCountry)) {
					if (_this.value == "M1") {
						telNo = exceptDomesticPrefix("09042504176", telNoCountry);
					} else if (_this.value == "B1") {
						telNo = exceptDomesticPrefix("03-5405-2172", telNoCountry);
					} else {
						telNo = exceptDomesticPrefix("090-4250-4176", telNoCountry);
					}
					$telNoInput.val(telNo);
				}
			}
			isInit = false;
		}
		
		function telInputCheck(telNo, telNoCountry) {
	        checkResult = false;
	        
	        var amcHndyTelNo = exceptDomesticPrefix("09042504176", telNoCountry);
	        var amcCmpTelNo = exceptDomesticPrefix("03-5405-2172", telNoCountry);
	        var amcTelNo = exceptDomesticPrefix("090-4250-4176", telNoCountry);
	
	        if(telNo == "" || telNo == amcHndyTelNo || telNo == amcCmpTelNo || telNo == amcTelNo) {
	        	checkResult = true;
	        }
	        return checkResult;
    	}
    	
    	function exceptDomesticPrefix(telNo, telNoCountry) {
    		if(telNoCountry == 'JP') {
    			if(telNo.substr(0, 1) === '0') {
    				return telNo.slice(1);
    			}
    		}
    		return telNo;
    	}
	</script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/asw-customerinfomationform.js?6e91953"></script><script type="text/javascript">
		$(document).ready(function(){
				Asw.enableTextWhenCheckBox('.cascadedSwitch');
				Asw.notPasteInput('.jsNotPasteInput');
			$(function(){
				Asw.alignHeightToParentTdElement( '.passengerInfoInputArea' );
				
					Asw.alignHeightToParentTdElement( '.passengerInfoInputText' );
				
				
				$('select.js-countrySelect').trigger('change');
			});
		});
	</script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/asw-session-keeper.js?6ec0730"></script><script type="text/javascript">
			$(document).ready(function() {
				Asw.SessionKeeper.initAndStart("sessionKeeperContainer", "520", "570", "PC");
			});
			</script><script type="text/javascript">
		Asw.addAjaxCallback(function(data) {
			// アコーディオンのメソッドと設定 
			if($('.jsAccordionSwitch .jsTriggerSwitch').length > 0){
				var events = $._data($('.jsAccordionSwitch .jsTriggerSwitch').get(0), "events");
				if(events === undefined || events.click === undefined) {
					Asw.AccordionInfo('.jsAccordionSwitch', {
						'triggerSwitch': '.jsAccordionSwitch .jsTriggerSwitch',
						'listClass': '.jsAccordionSwitchList',
						'visibilityHidden': 'toggleContents'
					});
				}
			}
		});
	</script><script type="text/javascript">
	Asw.Price = Asw.Price || {};
	// 整数の区切り文字
	Asw.Price.separateString = ',';
	
	// 整数の区切り桁数
	Asw.Price.separateDigitNumber = 3;
	
	// 小数点
	Asw.Price.decimalPointString = '.';
	
	// 小数点以降の有効桁数
	Asw.Price.significantDigit = 0;
	
	// 表示金額の単位
	Asw.Price.currencyUnit = '円';
	</script><script type="text/javascript">
			$(document).ready(function() {
				$('#preLoadingArea').css('visibility', 'hidden');
			});
		</script><noscript><img src="https://aswbe-i.ana.co.jp/akam/13/pixel_49a665b?a=dD1iZWY2ZDI0NGFlNGE4YmY4NWQ0YTI4Y2Y0ZDFmNGY5NDNjMzFiY2UzJmpzPW9mZg==" style="visibility: hidden; position: absolute; left: -999px; top: -999px;" /></noscript><script type="text/javascript"  src="/Q1XS4o/Cn_ls/KfDO7/xA/t9OfhmEwSiGQaG/NG4dCQE/bw/QJBChQIQEB"></script></body>
</html>