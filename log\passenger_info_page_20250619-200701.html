<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="ja-<PERSON>" xml:lang="ja-<PERSON>">

	
	<!-- skey = 2025/06/19 21:07:01 rei22h 4WCIFX56aF dljdmx+f38  --><head id="j_idt53">
    <base href="about:blank">
    <style>
        /* Hide elements that might try to load external resources, as a fallback. */
        img, svg, video, iframe, link[rel="stylesheet"], script { display: none !important; }
    </style>

		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
			<meta http-equiv="X-UA-Compatible" content="IE=EmulateIE9; IE=EmulateIE10" />
			<meta http-equiv="X-UA-Compatible" content="IE=7" />
			<meta http-equiv="X-UA-Compatible" content="IE=edge" />
		<meta name="description" content="" />
		<meta name="keywords" content="" />
		<meta name="format-detection" content="telephone=no" />
		<script type="text/javascript" src="/international_asw/dynatrace/ruxitagentjs_ICANVfhqrux_10273230920145353.js" data-dtconfig="rid=RID_-1741980786|rpid=-86004094|domain=aswbe-i.ana.co.jp|reportUrl=https://bf90419wvr.bf.dynatrace.com/bf|app=78bf0b58acf6ed13|cors=1|owasp=1|featureHash=ICANVfhqrux|xb=www^bs.google-analytics^bs.com^pana^bs.demdex^bs.net^pcollect^bs.tealiumiq^bs.com^pallnipponairways^bs.australia-3^bs.evergage^bs.com^pallnipponairwayscolt^bs.tt^bs.omtrdc^bs.net|rdnt=1|uxrgce=1|cuc=k8vpgh61|mel=100000|expw=1|dpvc=1|md=mdcc1=bAsw.PnrOutput.recordLocator,mdcc2=bAsw.PnrOutput.paymentDetailInfoList[0].fopCode,mdcc3=bAsw.PnrOutput.ticketedFlag,mdcc4=bAsw.PnrOutput.nhPnrFlag,mdcc5=bAsw.PnrOutput.itinerarySummary.departureDateYyyymmdd,mdcc6=bAsw.SearchCriteriaOutput.cffCodeInput,mdcc7=a#main ^rb div.section.sectionNoFlow ^rb table ^rb tbody ^rb tr ^rb td.flightNumber.lastCol ^rb span ^rb span:nth-child(2),mdcc8=cunique|lastModification=1750184416409|tp=500,50,0|agentUri=/international_asw/dynatrace/ruxitagentjs_ICANVfhqrux_10273230920145353.js"></script><link rel="shortcut icon" href="https://www.ana.co.jp/favicon.ico" />
		<title>お客様情報入力 | ANA</title>
		<noscript><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/css/asw_noscript_pc.css?99001fc" />
		</noscript>

		<!--  [ Adobe Target ] -->
		<script type="text/javascript" src="https://www.ana.co.jp/behavior/adobetarget/target_intbe.js"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/jquery-1.8.3.min.js?717d3c0"></script><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/ja/css/asw_initialize.css?99001fc" /><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/css/asw_screen_pc.css?99001fc" /><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/css/asw_common_pc.css?99001fc" /><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/css/asw_forminput_pc.css?99001fc" /><script type="text/javascript">
		var Asw = Asw || {};
		Asw.GlobalWord = Asw.GlobalWord || {};
		
		Asw.GlobalWord.toolTipCloseIconAltText = "閉じる";
		
		</script><script >bazadebezolkohpepadr="2123143214"</script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/akam/13/7e8c99da"  defer></script></head><body>
		
		<noscript>当サイトをご利用になるためには、JavaScript対応のブラウザが必要です。設定でJavaScriptを有効にしてください。有効にできない場合はANA電話窓口へお問い合わせください。
		</noscript>
		<div id="preLoadingArea"></div>
	
	<div id="cmnHeader" class="platinum">
		<!--===== cmnHeader =====-->
		<div class="headerWrapper">
			<div class="header">
				<p class="logo"><a href="https://www.ana.co.jp/asw/AMCTopServlet?type=a" tabindex="-1" aria-hidden="true"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/ana_logo_navy.png?717d3c0" alt="ANA Inspiration of JAPAN。リンク先はマイレージクラブトップページです。" height="28" width="274" /></a>
				</p>
					<ul class="headerMenu">
						<li><a href="https://www.ana.co.jp/asw/AMCTopServlet?type=a">ANAマイレージクラブトップページ</a>
						</li>
						<li><a href="https://www.ana.co.jp/other/int/meta/0005.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">お問い合わせ<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
						</li>
							<li class="btnLogoutArea"><a href="https://aswbe-i.ana.co.jp/rei22h/international_asw/rest/logout" role="button" class="btnBase btnLogout">ログアウト</a>
							</li>
					</ul>
			</div>
		</div>
			<div class="amcUserInfoWrapper">
				<div class="amcUserInfo">
					<div class="userNameArea">
						<table>
							<tr>	
									<td class="statusIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_status_05.png?717d3c0" alt="プラチナサービスメンバー" height="46" width="60" />
									</td>
								<td class="userName">佐藤 直樹 <span>様</span>
								</td>
							</tr>					
						</table>
					</div>
						<div class="balance">
								<dl class="upgradePointBalance">
									<dt>アップグレードポイント残高
									</dt>
									<dd>
											<dl>
												<dt>(2025年度)
												</dt>
												<dd>
													<em>20
													</em>ポイント
												</dd>
											</dl>
									</dd>
								</dl>
							<dl class="mileBalance">
								<dt>マイル口座残高
								</dt>
								<dd><em>75,663</em><span class="currencyCode">マイル</span>
								</dd>
							</dl>
						</div>
				</div>
					<p class="nowTime">6月19日21時07分現在
					</p>
			</div>
		<!--===== /cmnHeader =====-->
	</div>
		<div id="sessionKeeperContainer" class="sessionKeepingContainer"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/bg_modalWindow.png?717d3c0" alt="" height="0" width="0" />
			<div id="extendSessionExpiredDialog" class="modalExpire modalSmall modalContainer" role="dialog" aria-labelledby="extendSessionExpiredDialogTitle" aria-hidden="true">
<form id="sessionKeeperContainer:j_idt183" name="sessionKeeperContainer:j_idt183" method="post" action="https://aswbe-i.ana.co.jp/rei22h/international_asw/pages/award/reservation/award_mandatory_passenger_information_input.xhtml?aswcid=1&amp;rand=202506192107014WCIFX56aF" autocomplete="off" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="sessionKeeperContainer:j_idt183" value="sessionKeeperContainer:j_idt183" />
<input type="hidden" name="sessionKeeperContainer:j_idt183_operationTicket" value="dljdmx+f388a673062149d02162180232d8e161~lthgkIHnG3U_ZL4wpKufWCvI41CGLu5-jJMEXRHh!1750334808864.aere-xml-controller-67d4778877-brvvx" /><input type="hidden" name="sessionKeeperContainer:j_idt183_cmnPageTicket" value="4" />
					<h1 id="extendSessionExpiredDialogTitle" class="dialogTitle">まもなくセッションが終了します</h1>
					<div id="extendSessionExpiredDialogMessage" class="dialogMessage" tabindex="-1">このままお手続きを進める場合は、［延長］ボタンを押してください。</div>
					<p class="modalButton"><input id="sessionKeeperContainer:cmnSessionKeepingButton" type="submit" name="sessionKeeperContainer:cmnSessionKeepingButton" value="延長" class="btnBase btnModal btnMainStream" onclick="mojarra.ab(this,event,'action',0,0,{'onevent':function(data) {Asw.doCommonAjaxCallback(data,false, event); Asw.SessionKeeper.doSessionKeep(data);},'onerror':function(data) {Asw.doCommonAjaxErrorCallback(data); }});return false" />
					</p><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:0" value="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" autocomplete="off" />
</form>
			</div>
			<div id="showSessionExpiredDialog" class="modalExpire modalSmall modalContainer" role="dialog" aria-labelledby="showSessionExpiredDialogTitle" aria-hidden="true">
				<h1 id="showSessionExpiredDialogTitle" class="dialogTitle">セッションが終了しました</h1>
				<div id="showSessionExpiredDialogMessage" class="dialogMessage" tabindex="-1">一定時間操作されなかったため、お手続きを進めることができません。お手数ですが、再度トップページからお手続きください。</div>
				<p class="modalButton">
					<input type="submit" value="閉じる" class="btnBase btnModal btnMainStream" onclick="Asw.SessionKeeper.closeTimeoutDialog(); return false;" />
				</p>
			</div>
		</div>
		<!--===== dialogMessages =====--><div id="cmnModalMessages"></div>
		<div id="prebookConfirmDialog" class="modalContainer modalConfirm modalSmall " aria-hidden="true" role="dialog">
<form id="prebookConfirmDialog:j_idt403" name="prebookConfirmDialog:j_idt403" method="post" action="https://aswbe-i.ana.co.jp/rei22h/international_asw/pages/award/reservation/award_mandatory_passenger_information_input.xhtml?aswcid=1&amp;rand=202506192107014WCIFX56aF" autocomplete="off" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="prebookConfirmDialog:j_idt403" value="prebookConfirmDialog:j_idt403" />
<input type="hidden" name="prebookConfirmDialog:j_idt403_operationTicket" value="dljdmx+f388a673062149d02162180232d8e161~lthgkIHnG3U_ZL4wpKufWCvI41CGLu5-jJMEXRHh!1750334808864.aere-xml-controller-67d4778877-brvvx" /><input type="hidden" name="prebookConfirmDialog:j_idt403_cmnPageTicket" value="4" /><div class="dialogMessage" tabindex="0"><p>この後、名・姓の修正や変更ができません。</p><p>「搭乗者選択」欄の氏名が「名」「姓」の順番で表示されていることを再確認ください。</p><p>例）<strong class="highlight">空野 太郎</strong> 様 → MR <strong class="highlight">TARO SORANO</strong></p><p>保安上の理由により、航空券とパスポートのお名前が同じ表記でない場合はご搭乗いただくことができませんので必ず再度ご確認ください。</p></div>
					
					<ul class="modalBtnHorizontal">
							
							<li><input type="submit" name="prebookConfirmDialog:j_idt419" value="戻る" aria-controls="prebookConfirmDialog" class="btnBase btnModal btnCancel" onclick="Asw.Dialog.getInstance('prebookConfirmDialog').close(event, Asw.DialogSequencer.isActiveSequencerNotDisplayedLastDialog());  return false;" />
							</li>
						
						<li class="btnArrowNext"><input type="submit" name="prebookConfirmDialog:j_idt422" value="OK" aria-controls="prebookConfirmDialog" class="btnBase btnModal btnMainStream " onclick="Asw.Dialog.getInstance('prebookConfirmDialog').callOpener('NORMAL', event); return false;" />
						</li>
					</ul><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:1" value="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" autocomplete="off" />
</form>
		</div>
		<!--===== /dialogMessages =====--><div id="cmnContainer">
		<!--=========== .cmnContainer ============--><div id="cmnEmergencyNotice"></div><div id="cmnTitle" class="hgroup ">
		<h1 class="visuallyHidden">お客様情報入力</h1></div>

		<div id="cmnWrapper">
		<!--===== cmnWrapper =====-->
		<ol class="mainFlow" aria-label="完了までの手順、">
			<li><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_flow_01.png?717d3c0" alt="1" height="20" width="28" />フライト検索</li><!--
			--><li><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_flow_02_on.png?717d3c0" alt="2" height="20" width="28" /><span class="visuallyHidden">現在のステップ、</span>お客様情報入力</li><!--
			--><li><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_flow_03.png?717d3c0" alt="3" height="20" width="28" />お支払い情報</li><!--
			--><li class="flowLast"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_flow4_04.png?717d3c0" alt="4" height="20" width="28" />完了</li>
		</ol>

	<div id="main">
	<!--=========== main =============-->
		<ul class="iconExample">
				<li class="requiredIconExample">
					<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="" height="7" width="7" /></span>必須項目です	
				</li>
		</ul>
<form id="j_idt526" name="j_idt526" method="post" action="https://aswbe-i.ana.co.jp/rei22h/international_asw/pages/award/reservation/award_mandatory_passenger_information_input.xhtml?aswcid=1&amp;rand=202506192107014WCIFX56aF" autocomplete="off" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="j_idt526" value="j_idt526" />
<input type="hidden" name="j_idt526_operationTicket" value="dljdmx+f388a673062149d02162180232d8e161~lthgkIHnG3U_ZL4wpKufWCvI41CGLu5-jJMEXRHh!1750334808864.aere-xml-controller-67d4778877-brvvx" /><input type="hidden" name="j_idt526_cmnPageTicket" value="4" />

<div class="section">
	<h2>搭乗者選択<a href="https://www.ana.co.jp/other/int/meta/0086.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="特典のご利用に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a></h2>
	<table class="passengerInfo">
		<thead>
			<tr>
				
				<th class="name">氏名</th>
				
				<th class="dateOfBirthWide" id="dateOfBirthHeader">生年月日</th>
				
				<th class="gender lastCol" id="genderHeader">性別<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span></th>
				
			</tr>
		</thead>
		<tbody>
				
				<tr>
					<th class="parent_01" id="parentHeader01">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:0:passenger" type="checkbox" name="passengers:0:passenger" checked="checked" data-number="1" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:0:passenger">NAOKI SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader01">1963年06月05日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader01">				
							<span class="formWrapper">
							<label for="passengers:0:gender" class="visuallyHidden">NAOKI SATOの性別</label><select id="passengers:0:gender" name="passengers:0:gender" size="1">	<option value="">選択してください</option>
	<option value="M" selected="selected">男性</option>
	<option value="F">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_02" id="parentHeader02">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:1:passenger" type="checkbox" name="passengers:1:passenger" data-number="2" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:1:passenger">REO SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader02">1995年05月18日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader02">				
							<span class="formWrapper">
							<label for="passengers:1:gender" class="visuallyHidden">REO SATOの性別</label><select id="passengers:1:gender" name="passengers:1:gender" size="1">	<option value="">選択してください</option>
	<option value="M" selected="selected">男性</option>
	<option value="F">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_03" id="parentHeader03">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:2:passenger" type="checkbox" name="passengers:2:passenger" data-number="3" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:2:passenger">ERI SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader03">1963年08月14日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader03">				
							<span class="formWrapper">
							<label for="passengers:2:gender" class="visuallyHidden">ERI SATOの性別</label><select id="passengers:2:gender" name="passengers:2:gender" size="1">	<option value="">選択してください</option>
	<option value="M">男性</option>
	<option value="F" selected="selected">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_04" id="parentHeader04">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:3:passenger" type="checkbox" name="passengers:3:passenger" data-number="4" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:3:passenger">TAISHI SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader04">1992年12月03日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader04">				
							<span class="formWrapper">
							<label for="passengers:3:gender" class="visuallyHidden">TAISHI SATOの性別</label><select id="passengers:3:gender" name="passengers:3:gender" size="1">	<option value="">選択してください</option>
	<option value="M" selected="selected">男性</option>
	<option value="F">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_05" id="parentHeader05">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:4:passenger" type="checkbox" name="passengers:4:passenger" data-number="5" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:4:passenger">HIROMI FUJIMOTO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader05">1961年11月23日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader05">				
							<span class="formWrapper">
							<label for="passengers:4:gender" class="visuallyHidden">HIROMI FUJIMOTOの性別</label><select id="passengers:4:gender" name="passengers:4:gender" size="1">	<option value="">選択してください</option>
	<option value="M">男性</option>
	<option value="F" selected="selected">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
		</tbody>
	</table>
</div>
		<div class="section">
			<h2 class="midstream">連絡先情報
			</h2>
			<h3>代表者
			</h3>
	
	
	
	<div class="formInput">
		<fieldset>
			<legend><label>代表者</label></legend>
			<dl id="contactEmailAddressInfo">
				<dt class="hasRequiredIcon">メールアドレス<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span><a href="https://www.ana.co.jp/other/int/meta/0057.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="メールの宛先と内容に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a>
				</dt>
				<dd>
					<ul class="verticalList">
						<li class="nonMarginTop"><label for="representativeEmail" class="visuallyHidden">代表者メールアドレス</label><input id="representativeEmail" type="text" name="representativeEmail" value="<EMAIL>" class="widthLarge" placeholder="例：<EMAIL>" />
						</li>
						<li>
							<span class="supplementTxt" id="confirmMailAddressForRepresentative">確認のため再度ご入力ください。
							</span><label for="confirmrepresentativeEmail" class="visuallyHidden">確認用メールアドレス</label><input id="confirmrepresentativeEmail" type="text" name="confirmrepresentativeEmail" value="<EMAIL>" class="widthLarge jsNotPasteInput" placeholder="例：<EMAIL>" aria-describedby="confirmMailAddressForRepresentative" />
						</li>
					</ul>
				</dd>
					<dd style="display:none;"><input id="updateAlwaysInfo" type="checkbox" name="updateAlwaysInfo" /><label for="updateAlwaysInfo">ログイン中の会員情報へこのメールアドレスを登録</label>
					</dd>
			</dl>
			<dl>
				<dt>電話番号<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span>
				</dt>
				<dd class="js-countryWrap">
					<ul>
						<li><label for="representativeTelType" class="visuallyHidden">電話番号種別</label><select id="representativeTelType" name="representativeTelType" class="basicWidth js-typeSelect" size="1" onchange="changeTelType(this,'representativeTel')">	<option value="M1" selected="selected">携帯</option>
	<option value="B1">会社</option>
	<option value="H1">自宅</option>
</select>
						</li>
						<li><label for="representativeTelCountry" class="visuallyHidden">電話番号の国/地区選択</label><select id="representativeTelCountry" name="representativeTelCountry" class="js-countrySelect" size="1">	<option value="" data-sms-possible="true" data-country-code="---">選択してください</option>
	<option value="JP" selected="selected" data-sms-possible="true">日本</option>
	<option value="-" data-sms-possible="true" data-country-code="---">-----------</option>
	<option value="IS" data-sms-possible="true">アイスランド</option>
	<option value="IE" data-sms-possible="true">アイルランド</option>
	<option value="AZ" data-sms-possible="true">アゼルバイジャン</option>
	<option value="AF" data-sms-possible="true">アフガニスタン</option>
	<option value="US" data-sms-possible="true">アメリカ合衆国/カナダ</option>
	<option value="AE" data-sms-possible="true">アラブ首長国連邦</option>
	<option value="DZ" data-sms-possible="true">アルジェリア</option>
	<option value="AR" data-sms-possible="true">アルゼンチン</option>
	<option value="AW" data-sms-possible="true">アルバ</option>
	<option value="AL" data-sms-possible="true">アルバニア</option>
	<option value="AM" data-sms-possible="true">アルメニア</option>
	<option value="AO" data-sms-possible="true">アンゴラ</option>
	<option value="AG" data-sms-possible="true">アンティグア・バーブーダ</option>
	<option value="AD" data-sms-possible="true">アンドラ</option>
	<option value="YE" data-sms-possible="true">イエメン</option>
	<option value="GB" data-sms-possible="true">イギリス</option>
	<option value="IL" data-sms-possible="true">イスラエル</option>
	<option value="IT" data-sms-possible="true">イタリア</option>
	<option value="IQ" data-sms-possible="true">イラク</option>
	<option value="IR" data-sms-possible="true">イラン</option>
	<option value="IN" data-sms-possible="true">インド</option>
	<option value="ID" data-sms-possible="true">インドネシア</option>
	<option value="UG" data-sms-possible="true">ウガンダ</option>
	<option value="UA" data-sms-possible="true">ウクライナ</option>
	<option value="UZ" data-sms-possible="true">ウズベキスタン</option>
	<option value="UY" data-sms-possible="true">ウルグアイ</option>
	<option value="EC" data-sms-possible="true">エクアドル</option>
	<option value="EG" data-sms-possible="true">エジプト</option>
	<option value="EE" data-sms-possible="true">エストニア</option>
	<option value="SZ" data-sms-possible="true">エスワティニ</option>
	<option value="ET" data-sms-possible="true">エチオピア</option>
	<option value="ER" data-sms-possible="true">エリトリア</option>
	<option value="SV" data-sms-possible="true">エルサルバドル</option>
	<option value="AU" data-sms-possible="true">オーストラリア</option>
	<option value="AT" data-sms-possible="true">オーストリア</option>
	<option value="OM" data-sms-possible="true">オマーン</option>
	<option value="NL" data-sms-possible="true">オランダ</option>
	<option value="GH" data-sms-possible="true">ガーナ</option>
	<option value="CV" data-sms-possible="true">カーボベルデ</option>
	<option value="GY" data-sms-possible="true">ガイアナ</option>
	<option value="KZ" data-sms-possible="true">カザフスタン</option>
	<option value="QA" data-sms-possible="true">カタール</option>
	<option value="CA" data-sms-possible="true">カナダ/アメリカ合衆国</option>
	<option value="GA" data-sms-possible="true">ガボン</option>
	<option value="CM" data-sms-possible="true">カメルーン</option>
	<option value="KR" data-sms-possible="true">韓国</option>
	<option value="GM" data-sms-possible="true">ガンビア</option>
	<option value="KH" data-sms-possible="true">カンボジア</option>
	<option value="KP" data-sms-possible="true">北朝鮮</option>
	<option value="MK" data-sms-possible="true">北マケドニア</option>
	<option value="GN" data-sms-possible="true">ギニア</option>
	<option value="GW" data-sms-possible="true">ギニアビサウ</option>
	<option value="CY" data-sms-possible="true">キプロス</option>
	<option value="CU" data-sms-possible="true">キューバ</option>
	<option value="GR" data-sms-possible="true">ギリシャ</option>
	<option value="KI" data-sms-possible="true">キリバス</option>
	<option value="KG" data-sms-possible="true">キルギス</option>
	<option value="GT" data-sms-possible="true">グアテマラ</option>
	<option value="GU" data-sms-possible="true">グアム</option>
	<option value="KW" data-sms-possible="true">クウェート</option>
	<option value="GD" data-sms-possible="true">グレナダ</option>
	<option value="HR" data-sms-possible="true">クロアチア</option>
	<option value="KY" data-sms-possible="true">ケイマン諸島</option>
	<option value="KE" data-sms-possible="true">ケニア</option>
	<option value="CI" data-sms-possible="true">コートジボワール</option>
	<option value="CR" data-sms-possible="true">コスタリカ</option>
	<option value="KM" data-sms-possible="true">コモロ</option>
	<option value="CO" data-sms-possible="true">コロンビア</option>
	<option value="CG" data-sms-possible="true">コンゴ共和国</option>
	<option value="CD" data-sms-possible="true">コンゴ民主共和国</option>
	<option value="SA" data-sms-possible="true">サウジアラビア</option>
	<option value="ZM" data-sms-possible="true">ザンビア</option>
	<option value="SM" data-sms-possible="true">サンマリノ</option>
	<option value="SL" data-sms-possible="true">シエラレオネ</option>
	<option value="DJ" data-sms-possible="true">ジブチ</option>
	<option value="JM" data-sms-possible="true">ジャマイカ</option>
	<option value="GE" data-sms-possible="true">ジョージア</option>
	<option value="SY" data-sms-possible="true">シリア</option>
	<option value="SG" data-sms-possible="true">シンガポール</option>
	<option value="ZW" data-sms-possible="true">ジンバブエ</option>
	<option value="CH" data-sms-possible="true">スイス</option>
	<option value="SE" data-sms-possible="true">スウェーデン</option>
	<option value="SD" data-sms-possible="true">スーダン</option>
	<option value="ES" data-sms-possible="true">スペイン</option>
	<option value="SR" data-sms-possible="true">スリナム</option>
	<option value="LK" data-sms-possible="true">スリランカ</option>
	<option value="SK" data-sms-possible="true">スロバキア</option>
	<option value="SI" data-sms-possible="true">スロベニア</option>
	<option value="SC" data-sms-possible="true">セイシェル</option>
	<option value="GQ" data-sms-possible="true">赤道ギニア</option>
	<option value="SN" data-sms-possible="true">セネガル</option>
	<option value="RS" data-sms-possible="true">セルビア</option>
	<option value="KN" data-sms-possible="true">セントクリストファー・ネイビス</option>
	<option value="LC" data-sms-possible="true">セントルシア</option>
	<option value="SB" data-sms-possible="true">ソロモン諸島</option>
	<option value="TH" data-sms-possible="true">タイ</option>
	<option value="TW" data-sms-possible="true">台湾</option>
	<option value="TJ" data-sms-possible="true">タジキスタン</option>
	<option value="TZ" data-sms-possible="true">タンザニア</option>
	<option value="CZ" data-sms-possible="true">チェコ</option>
	<option value="TD" data-sms-possible="true">チャド</option>
	<option value="CF" data-sms-possible="true">中央アフリカ共和国</option>
	<option value="CN" data-sms-possible="true">中国</option>
	<option value="TN" data-sms-possible="true">チュニジア</option>
	<option value="CL" data-sms-possible="true">チリ</option>
	<option value="TV" data-sms-possible="true">ツバル</option>
	<option value="DK" data-sms-possible="true">デンマーク</option>
	<option value="DE" data-sms-possible="true">ドイツ</option>
	<option value="TG" data-sms-possible="true">トーゴ</option>
	<option value="DO" data-sms-possible="true">ドミニカ共和国</option>
	<option value="DM" data-sms-possible="true">ドミニカ国</option>
	<option value="TT" data-sms-possible="true">トリニダード・トバゴ</option>
	<option value="TM" data-sms-possible="true">トルクメニスタン</option>
	<option value="TR" data-sms-possible="true">トルコ</option>
	<option value="TO" data-sms-possible="true">トンガ</option>
	<option value="NG" data-sms-possible="true">ナイジェリア</option>
	<option value="NR" data-sms-possible="true">ナウル</option>
	<option value="NA" data-sms-possible="true">ナミビア</option>
	<option value="NI" data-sms-possible="true">ニカラグア</option>
	<option value="NE" data-sms-possible="true">ニジェール</option>
	<option value="NZ" data-sms-possible="true">ニュージーランド</option>
	<option value="NP" data-sms-possible="true">ネパール</option>
	<option value="NO" data-sms-possible="true">ノルウェー</option>
	<option value="BH" data-sms-possible="true">バーレーン</option>
	<option value="HT" data-sms-possible="true">ハイチ</option>
	<option value="PK" data-sms-possible="true">パキスタン</option>
	<option value="VA" data-sms-possible="true">バチカン</option>
	<option value="PA" data-sms-possible="true">パナマ</option>
	<option value="VU" data-sms-possible="true">バヌアツ</option>
	<option value="BS" data-sms-possible="true">バハマ</option>
	<option value="PG" data-sms-possible="true">パプアニューギニア</option>
	<option value="BM" data-sms-possible="true">バミューダ</option>
	<option value="PW" data-sms-possible="true">パラオ</option>
	<option value="PY" data-sms-possible="true">パラグアイ</option>
	<option value="BB" data-sms-possible="true">バルバドス</option>
	<option value="PS" data-sms-possible="true">パレスチナ</option>
	<option value="HU" data-sms-possible="true">ハンガリー</option>
	<option value="BD" data-sms-possible="true">バングラデシュ</option>
	<option value="TL" data-sms-possible="true">東ティモール</option>
	<option value="FJ" data-sms-possible="true">フィジー</option>
	<option value="PH" data-sms-possible="true">フィリピン</option>
	<option value="FI" data-sms-possible="true">フィンランド</option>
	<option value="BT" data-sms-possible="true">ブータン</option>
	<option value="PR" data-sms-possible="true">プエルトリコ</option>
	<option value="BR" data-sms-possible="true">ブラジル</option>
	<option value="FR" data-sms-possible="true">フランス</option>
	<option value="PF" data-sms-possible="true">フランス領ポリネシア</option>
	<option value="BG" data-sms-possible="true">ブルガリア</option>
	<option value="BF" data-sms-possible="true">ブルキナファソ</option>
	<option value="BN" data-sms-possible="true">ブルネイ</option>
	<option value="BI" data-sms-possible="true">ブルンジ</option>
	<option value="VN" data-sms-possible="true">ベトナム</option>
	<option value="BJ" data-sms-possible="true">ベナン</option>
	<option value="VE" data-sms-possible="true">ベネズエラ</option>
	<option value="BY" data-sms-possible="true">ベラルーシ</option>
	<option value="BZ" data-sms-possible="true">ベリーズ</option>
	<option value="PE" data-sms-possible="true">ペルー</option>
	<option value="BE" data-sms-possible="true">ベルギー</option>
	<option value="PL" data-sms-possible="true">ポーランド</option>
	<option value="BA" data-sms-possible="true">ボスニア・ヘルツェゴビナ</option>
	<option value="BW" data-sms-possible="true">ボツワナ</option>
	<option value="BO" data-sms-possible="true">ボリビア</option>
	<option value="PT" data-sms-possible="true">ポルトガル</option>
	<option value="HK" data-sms-possible="true">香港</option>
	<option value="HN" data-sms-possible="true">ホンジュラス</option>
	<option value="MH" data-sms-possible="true">マーシャル諸島</option>
	<option value="MO" data-sms-possible="true">マカオ</option>
	<option value="MG" data-sms-possible="true">マダガスカル</option>
	<option value="MW" data-sms-possible="true">マラウイ</option>
	<option value="ML" data-sms-possible="true">マリ</option>
	<option value="MT" data-sms-possible="true">マルタ</option>
	<option value="MY" data-sms-possible="true">マレーシア</option>
	<option value="FM" data-sms-possible="true">ミクロネシア連邦</option>
	<option value="ZA" data-sms-possible="true">南アフリカ</option>
	<option value="MM" data-sms-possible="true">ミャンマー</option>
	<option value="MX" data-sms-possible="true">メキシコ</option>
	<option value="MU" data-sms-possible="true">モーリシャス</option>
	<option value="MR" data-sms-possible="true">モーリタニア</option>
	<option value="MZ" data-sms-possible="true">モザンビーク</option>
	<option value="MC" data-sms-possible="true">モナコ</option>
	<option value="MV" data-sms-possible="true">モルディヴ</option>
	<option value="MD" data-sms-possible="true">モルドバ</option>
	<option value="MA" data-sms-possible="true">モロッコ</option>
	<option value="MN" data-sms-possible="true">モンゴル</option>
	<option value="ME" data-sms-possible="true">モンテネグロ</option>
	<option value="JO" data-sms-possible="true">ヨルダン</option>
	<option value="LA" data-sms-possible="true">ラオス人民民主共和国</option>
	<option value="LV" data-sms-possible="true">ラトビア</option>
	<option value="LT" data-sms-possible="true">リトアニア</option>
	<option value="LY" data-sms-possible="true">リビア</option>
	<option value="LI" data-sms-possible="true">リヒテンシュタイン</option>
	<option value="LR" data-sms-possible="true">リベリア</option>
	<option value="RO" data-sms-possible="true">ルーマニア</option>
	<option value="LU" data-sms-possible="true">ルクセンブルク</option>
	<option value="RW" data-sms-possible="true">ルワンダ</option>
	<option value="LS" data-sms-possible="true">レソト</option>
	<option value="LB" data-sms-possible="true">レバノン</option>
	<option value="RU" data-sms-possible="true">ロシア</option>
</select>
						</li>
						<li class="numberInputWrap">
							<span id="numberInputInfo1" class="supplementTxt marginTop">先頭に0(ゼロ)がある場合、0(ゼロ)を抜いてください(一部、国・地域除く)。携帯電話番号の場合は、携帯電話を契約された国を選択ください。</span>
							<div class="numberRowWrap">
								<div class="block">
									
									<span class="visuallyHidden">国番号</span>
									<span class="viewInput widthMini js-countryCode"></span>
								</div>
								<div class="block"><label for="representativeTel" class="visuallyHidden">電話番号</label><input id="representativeTel" type="text" name="representativeTel" value="9042504176" class="widthMiddle" placeholder="例：9012345678" maxlength="30" aria-describedby="numberInputInfo1" />
								</div>
							</div>
						</li>
					</ul>
				</dd>
			</dl>
		</fieldset>
	</div>
	<div class="titleWrapper">
		<h3>
			<span class="text">eメール・SMSに関するご案内<a href="https://www.ana.co.jp/other/int/meta/0057.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="eメール・SMSに関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a></span>
		</h3>
	</div>
	<div class="formInput">
	<h4 class="formAreaTitle">主に搭乗・運航に関わるeメールの送付先設定（米国へ通知いたします）<a href="https://www.ana.co.jp/other/int/meta/0629.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="米国入国用eメール・SMS・電話番号に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a>
	</h4>
	<div class="paxSettingArea js-mailFormWrap onePax is-open">
			<div class="settingArea">
					<dl>
						<dt class="breakWord">NAOKI SATO
						</dt>
						<dd>
							<fieldset>
								<legend>NAOKI SATOのeメール送付先および米国到着時に提供するメールアドレス</legend>代表者と同じ
							</fieldset>
						</dd>
					</dl>
			</div>
	</div>
	<h4 class="formAreaTitle hasTopLine">主に搭乗・運航に関わるSMSの送付先および米国で受信できる電話番号の設定（米国へ通知いたします）<a href="https://www.ana.co.jp/other/int/meta/0629.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="米国入国用eメール・SMS・電話番号に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a>
	</h4>
	<div class="paxSettingArea js-smsFormWrap onePax is-open">
			<div class="settingArea">
					<dl>
						<dt class="breakWord">NAOKI SATO<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span>
						</dt>
						<dd>
							<fieldset>
								<legend>NAOKI SATOの1つ目の電話番号</legend>
									<ul class="verticalList js-formItem">
										<li><input type="radio" id="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSameAsRepresentativeSms:radioItem" name="contactsSms:0:recipientSmsEachPersonSelection:radioGroup" value="0" checked="checked" /><label for="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSameAsRepresentativeSms:radioItem">代表者電話番号と同じ</label>
										</li>
										<li><input type="radio" id="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSmsPerPassenger:radioItem" name="contactsSms:0:recipientSmsEachPersonSelection:radioGroup" value="1" /><label for="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSmsPerPassenger:radioItem">電話番号を指定</label>
											<div class="indentArea js-formImputArea">
												<span id="confirmTelNumberForRepresentative:0" class="supplementTxt marginTop">先頭に0(ゼロ)がある場合、0(ゼロ)を抜いてください(一部、国・地域除く)。携帯電話番号の場合は、携帯電話を契約された国を選択ください。</span>
												<ul class="countryNumberInput js-countryWrap">
													<li><label for="contactsSms:0:recipientSmsEachPersonSelection:passengerSmsCountry" class="visuallyHidden">電話番号の国/地区選択</label><select id="contactsSms:0:recipientSmsEachPersonSelection:passengerSmsCountry" name="contactsSms:0:recipientSmsEachPersonSelection:passengerSmsCountry" class="countrySelect js-countrySelect" size="1">	<option value="" data-country-code="---">選択してください</option>
	<option value="JP">日本</option>
	<option value="-" data-country-code="---">-----------</option>
	<option value="IE">アイルランド</option>
	<option value="AZ">アゼルバイジャン</option>
	<option value="AF">アフガニスタン</option>
	<option value="US">アメリカ合衆国/カナダ</option>
	<option value="AE">アラブ首長国連邦</option>
	<option value="DZ">アルジェリア</option>
	<option value="AR">アルゼンチン</option>
	<option value="AW">アルバ</option>
	<option value="AL">アルバニア</option>
	<option value="AM">アルメニア</option>
	<option value="AG">アンティグア・バーブーダ</option>
	<option value="AD">アンドラ</option>
	<option value="YE">イエメン</option>
	<option value="GB">イギリス</option>
	<option value="IL">イスラエル</option>
	<option value="IT">イタリア</option>
	<option value="IQ">イラク</option>
	<option value="IR">イラン</option>
	<option value="IN">インド</option>
	<option value="ID">インドネシア</option>
	<option value="UG">ウガンダ</option>
	<option value="UA">ウクライナ</option>
	<option value="UZ">ウズベキスタン</option>
	<option value="UY">ウルグアイ</option>
	<option value="EC">エクアドル</option>
	<option value="EG">エジプト</option>
	<option value="EE">エストニア</option>
	<option value="ER">エリトリア</option>
	<option value="SV">エルサルバドル</option>
	<option value="AU">オーストラリア</option>
	<option value="AT">オーストリア</option>
	<option value="OM">オマーン</option>
	<option value="NL">オランダ</option>
	<option value="GH">ガーナ</option>
	<option value="GY">ガイアナ</option>
	<option value="KZ">カザフスタン</option>
	<option value="QA">カタール</option>
	<option value="CA">カナダ/アメリカ合衆国</option>
	<option value="GA">ガボン</option>
	<option value="CM">カメルーン</option>
	<option value="KR">韓国</option>
	<option value="GM">ガンビア</option>
	<option value="KH">カンボジア</option>
	<option value="GN">ギニア</option>
	<option value="GW">ギニアビサウ</option>
	<option value="CY">キプロス</option>
	<option value="CU">キューバ</option>
	<option value="GR">ギリシャ</option>
	<option value="KG">キルギス</option>
	<option value="GT">グアテマラ</option>
	<option value="GU">グアム</option>
	<option value="KW">クウェート</option>
	<option value="GD">グレナダ</option>
	<option value="HR">クロアチア</option>
	<option value="KY">ケイマン諸島</option>
	<option value="KE">ケニア</option>
	<option value="CI">コートジボワール</option>
	<option value="CR">コスタリカ</option>
	<option value="KM">コモロ</option>
	<option value="CO">コロンビア</option>
	<option value="CD">コンゴ民主共和国</option>
	<option value="SA">サウジアラビア</option>
	<option value="ZM">ザンビア</option>
	<option value="SL">シエラレオネ</option>
	<option value="JM">ジャマイカ</option>
	<option value="GE">ジョージア</option>
	<option value="SY">シリア</option>
	<option value="SG">シンガポール</option>
	<option value="ZW">ジンバブエ</option>
	<option value="CH">スイス</option>
	<option value="SE">スウェーデン</option>
	<option value="SD">スーダン</option>
	<option value="ES">スペイン</option>
	<option value="SR">スリナム</option>
	<option value="LK">スリランカ</option>
	<option value="SK">スロバキア</option>
	<option value="SI">スロベニア</option>
	<option value="SN">セネガル</option>
	<option value="RS">セルビア</option>
	<option value="KN">セントクリストファー・ネイビス</option>
	<option value="LC">セントルシア</option>
	<option value="TH">タイ</option>
	<option value="TW">台湾</option>
	<option value="TJ">タジキスタン</option>
	<option value="TZ">タンザニア</option>
	<option value="CZ">チェコ</option>
	<option value="TD">チャド</option>
	<option value="CF">中央アフリカ共和国</option>
	<option value="CN">中国</option>
	<option value="TN">チュニジア</option>
	<option value="CL">チリ</option>
	<option value="DK">デンマーク</option>
	<option value="DE">ドイツ</option>
	<option value="TG">トーゴ</option>
	<option value="DO">ドミニカ共和国</option>
	<option value="TT">トリニダード・トバゴ</option>
	<option value="TR">トルコ</option>
	<option value="NG">ナイジェリア</option>
	<option value="NI">ニカラグア</option>
	<option value="NE">ニジェール</option>
	<option value="NZ">ニュージーランド</option>
	<option value="NP">ネパール</option>
	<option value="NO">ノルウェー</option>
	<option value="HT">ハイチ</option>
	<option value="PK">パキスタン</option>
	<option value="PA">パナマ</option>
	<option value="BS">バハマ</option>
	<option value="PG">パプアニューギニア</option>
	<option value="BM">バミューダ</option>
	<option value="PW">パラオ</option>
	<option value="PY">パラグアイ</option>
	<option value="BB">バルバドス</option>
	<option value="PS">パレスチナ</option>
	<option value="HU">ハンガリー</option>
	<option value="BD">バングラデシュ</option>
	<option value="TL">東ティモール</option>
	<option value="FJ">フィジー</option>
	<option value="PH">フィリピン</option>
	<option value="FI">フィンランド</option>
	<option value="BT">ブータン</option>
	<option value="PR">プエルトリコ</option>
	<option value="BR">ブラジル</option>
	<option value="FR">フランス</option>
	<option value="PF">フランス領ポリネシア</option>
	<option value="BG">ブルガリア</option>
	<option value="BF">ブルキナファソ</option>
	<option value="BN">ブルネイ</option>
	<option value="BI">ブルンジ</option>
	<option value="VN">ベトナム</option>
	<option value="BJ">ベナン</option>
	<option value="VE">ベネズエラ</option>
	<option value="BY">ベラルーシ</option>
	<option value="PE">ペルー</option>
	<option value="BE">ベルギー</option>
	<option value="PL">ポーランド</option>
	<option value="BA">ボスニア・ヘルツェゴビナ</option>
	<option value="BW">ボツワナ</option>
	<option value="HK">香港</option>
	<option value="MH">マーシャル諸島</option>
	<option value="MO">マカオ</option>
	<option value="MG">マダガスカル</option>
	<option value="MW">マラウイ</option>
	<option value="ML">マリ</option>
	<option value="MT">マルタ</option>
	<option value="MY">マレーシア</option>
	<option value="ZA">南アフリカ</option>
	<option value="MM">ミャンマー</option>
	<option value="MX">メキシコ</option>
	<option value="MU">モーリシャス</option>
	<option value="MR">モーリタニア</option>
	<option value="MZ">モザンビーク</option>
	<option value="MC">モナコ</option>
	<option value="MV">モルディヴ</option>
	<option value="MA">モロッコ</option>
	<option value="MN">モンゴル</option>
	<option value="ME">モンテネグロ</option>
	<option value="JO">ヨルダン</option>
	<option value="LA">ラオス人民民主共和国</option>
	<option value="LV">ラトビア</option>
	<option value="LT">リトアニア</option>
	<option value="LY">リビア</option>
	<option value="LR">リベリア</option>
	<option value="RO">ルーマニア</option>
	<option value="LU">ルクセンブルク</option>
	<option value="RW">ルワンダ</option>
	<option value="LB">レバノン</option>
	<option value="RU">ロシア</option>
</select>
													</li>
													<li>
														
														<div class="numberRowWrap">
															<div class="block">
																<span class="visuallyHidden">国番号</span>
																<span class="countryCode viewInput js-countryCode"></span>
															</div>
															
															<div class="block"><label for="contactsSms:0:recipientSmsEachPersonSelection:flightStatusNotificationContactPointSmsDescription" class="visuallyHidden">電話番号</label><input id="contactsSms:0:recipientSmsEachPersonSelection:flightStatusNotificationContactPointSmsDescription" type="text" name="contactsSms:0:recipientSmsEachPersonSelection:flightStatusNotificationContactPointSmsDescription" class="telNumber" placeholder="例：9012345678" maxlength="30" aria-describedby="confirmTelNumberForRepresentative:0" />
															</div>
														</div>
													</li>
												</ul>
											</div>
										</li>
									</ul>
							</fieldset>
						</dd>
					</dl>
			</div>
	</div>
	</div>
	
	<div class="formInput articleLine">
		<h3>滞在中の連絡先(任意)
		</h3>
		<dl>
			
			<dt><label for="accommodationOne">現地連絡先1</label>
			</dt>
			<dd><input id="accommodationOne" type="text" name="accommodationOne" class="widthLarge" placeholder="例：09012345678 ABC HOTEL" maxlength="28" title="例：09012345678 ABC HOTEL" />
			</dd>
		</dl>
		<dl>
			
			<dt><label for="accommodationTwo">現地連絡先2</label>
			</dt>
			<dd><input id="accommodationTwo" type="text" name="accommodationTwo" class="widthLarge" placeholder="例：09012345678 ABC HOTEL" maxlength="28" title="例：09012345678 ABC HOTEL" />
			</dd>
		</dl>
	</div>
		</div>
		
		<p class="btnAreaSubmit btnArrowNext "><input id="next" type="submit" name="next" value="次へ" class="btnBase btnMainStream btnVerticalMain btnWidthVariable" onclick="return Asw.Dialog.getInstance('prebookConfirmDialog').toggle(event);return Asw.LoadingWindow.open(&quot;NORMAL&quot;, event)" />
		</p><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:2" value="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" autocomplete="off" />
</form><div id="cmnDynamicMessages" class="infoBoxSection">
				<dl class="infoBox importantInfo jsAccordionSwitch">
					<dt><strong>重要なご案内</strong></dt>
					<dd>
						<ul class="jsAccordionSwitchList" id="informationMessages">
								<li>個人情報の利用目的<a href="https://www.ana.co.jp/other/int/meta/0012.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="ANAプライバシーポリシー(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">ANAプライバシーポリシー<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>を必ずお読みいただき、内容に同意いただいた上で、お進みください。</li>
								<li><a href="https://www.ana.co.jp/other/int/meta/0058.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="Secure Flight Programについて(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">米国Secure Flightプログラム<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>に基づき、パスポート名、生年月日、性別などの情報は事前に米国へ通知いたします。</li>
								<li>入力したeメールアドレス、電話番号は入国に必要な情報として米国に通知される可能性があります。</li>
									<li class="jsHiddenFlg toggleContents">お名前のスペルがパスポート名と一致しているかご確認ください。異なる場合はインターネットではお取り扱いできません。<a href="https://www.ana.co.jp/other/int/meta/intbe0001.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="ANAお問い合わせ先(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">ANA<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>へ名前の変更をお申し出ください。</li>
									<li class="jsHiddenFlg toggleContents">二重国籍のお客様は、ご搭乗時に使用されるパスポートのお名前でご予約ください。<br />パスポートごとにお名前が異なる場合は、お客様ご自身で入国管理事務所・外務省・大使館などで各種条件をご確認のうえ、使用されるパスポートを決めてください。<br />なお、入国可否については国によって異なるため、お客様ご自身で各入国管理事務所などでご確認ください。</li>
									<li class="jsHiddenFlg toggleContents">旅行開始日時点で12歳以上の場合は大人、2歳以上12歳未満の場合は小児、2歳未満の場合は幼児の扱いとなります。なお、小児は大人と同マイル数が必要です。</li>
									<li class="jsHiddenFlg toggleContents">運航に関する情報をお客様へ通知するため、提携航空会社にもお客様のメールアドレスが提供されます。あらかじめご了承ください。</li>
									<li class="jsHiddenFlg toggleContents"><dl><dt>携帯電話へのメール送信をご希望のお客様へ</dt><dd>ドメイン指定受信をご利用の場合は、ana.co.jpとamadeus.comからのメールが受信できるようあらかじめ設定をお願いいたします。</dd></dl></li>
									<li class="jsHiddenFlg toggleContents">SMSはスマートフォンの番号に配信されます。一部の国ではSMSでのご案内をご利用いただけません。詳細は<a href="https://www.ana.co.jp/other/int/meta/0737.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="ショートメッセージ(SMS)でのご案内対象外の国について(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">ショートメッセージ(SMS)でのご案内対象外の国について<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>をご覧ください。</li>
									<li class="jsHiddenFlg toggleContents">ログインされている会員ご本人が搭乗しない場合は、再度フライト検索画面より「ログインされている会員ご本人は搭乗しない」のチェックボックスを選択してお手続きください。</li>
						</ul>
							<a href="#" class="toggleSwitch jsTriggerSwitch" role="button" aria-controls="informationMessages" aria-expanded="false"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/toggle_open.png?7bec38a" alt="開閉ボタン" height="25" width="25" class="jsRollOver" />
							</a>
					</dd>
				</dl></div>
	<!--=========== /main =============-->
	</div>

	<!--=========== Script ============-->
	<script type="text/javascript">
	$(window).on('load', function() {
		// アコーディオンのメソッドと設定
		Asw.AccordionInfo('.jsAccordionSwitch', {
			'triggerSwitch': '.jsAccordionSwitch .jsTriggerSwitch',
			'listClass': '.jsAccordionSwitchList',
			'visibilityHidden': 'toggleContents'
		});
	});
	</script>
	<!--=========== Script ============-->
	<script type="text/javascript">
		//プラットフォーム判別（サマリーエリア追従用として）
		var Asw = Asw || {};
	
	  	Asw.ClientInfo = Asw.ClientInfo  || {};
	  	Asw.ClientInfo.DeviceType = Asw.ClientInfo.DeviceType || {};
	
	  	Asw.ClientInfo.DeviceType.PC = 'PC';
	  	Asw.ClientInfo.DeviceType.SP = 'SP';
	  	Asw.ClientInfo.DeviceType.TC = 'TC';
	
	  	Asw.ClientInfo.deviceType = Asw.ClientInfo.DeviceType.PC;
	
	 	if((navigator.userAgent.indexOf('Android') > 0 && navigator.userAgent.indexOf('Mobile') == -1) || navigator.userAgent.indexOf('iPad') > 0){
	    	Asw.ClientInfo.deviceType = Asw.ClientInfo.DeviceType.TC;
	  	}
	</script>
	<!--=========== /Script ============--><div id="summaryArea">
			<div class="flightSummaryArea">
						<div class="flightSummarySection">
							<h2>区間 1
							</h2>
							<p class="flightDate"><em>6</em>月<em class="hasLeft">24</em>日（火）
							</p>
							<div class="flightSummaryRoute">
									<div class="flightSummaryRouteSection">
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation1_1">
													<span>東京(成田)
													</span>
												</p>
												<p class="time">
													<span>17:00
													</span>
												</p>
											</div>
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation1_1">
													<span>シカゴ(ORD)
													</span>
												</p>
												<p class="time">
													<span>14:55
													</span>
												</p>
											</div>
										<p class="flightNumberClass">
												<span>NH012</span><span><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/flight_ana_summaryarea.png?717d3c0" alt="ANA運航便" height="11" width="41" /></span>
												<br />特典ビジネス
										</p>
									</div>
							</div>
						</div>
						<div class="flightSummarySection">
							<h2 class="midstream">区間 2
							</h2>
							<p class="flightDate"><em>7</em>月<em class="hasLeft">23</em>日（水）
							</p>
							<div class="flightSummaryRoute">
									<div class="flightSummaryRouteSection">
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation2_1">
													<span>ホノルル(オアフ島)
													</span>
												</p>
												<p class="time">
													<span>11:35
													</span>
												</p>
											</div>
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation2_1">
													<span>東京(成田)
													</span>
												</p>
												<p class="time">
													<span>14:50
																	<span>翌日
																	</span>
													</span>
												</p>
											</div>
										<p class="flightNumberClass">
												<span>NH183</span><span><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/flight_ana_summaryarea.png?717d3c0" alt="ANA運航便" height="11" width="41" /></span>
												<br />特典エコノミー
										</p>
									</div>
							</div>
						</div>
					<div class="total">
						<p class="passenger">大人 x1
						</p>
						<div class="totalArea">
					  		<dl class="mealAmountWrap">
									<dt>有料機内食サービス
									</dt>
									<dd><em class="mealAmount">0</em><span class="currencyCode">円</span>
									</dd>
							</dl>
									<div>
										<div class="mileageAndTotal">
												<dl class="requiredMileage">
													<dt>必要マイル
													</dt>
													<dd><em class="requiredMileage">74,000</em><span class="currencyCode">マイル</span>
													</dd>
												</dl>
												<dl>
													<dt>総額
													</dt>
													<dd><em class="awardTotalPayment">68,130</em>円
													</dd>
												</dl>
												<p class="includeFareAndFuelCost">※各種税金、燃油特別付加運賃等を含みます。
												</p>
										</div>
									</div>
						</div>
					</div>
				
			</div></div>
		<!--===== /cmnWrapper =====-->
		</div>
			
		<span id="message" class="visuallyHidden" role="alert" aria-live="assertive"></span>
		<span id="politeMessage" class="visuallyHidden" role="log" aria-live="polite"></span><script type="text/javascript">
		var Asw = Asw || {};
		Asw.BaseOutput = {"sessionID":"4WCIFX56aF","language":"ja","siteCatalystLanguageCode":"J","operationDate":"20250619","operationDateTime":"20250619210702","office":{"officeCode":"TYONH08ZZ","country2letterCode":"JP"},"bookingType":"A","pageID":"A04_P01","siteCatalystPageName":"INT_BE_AWARD_J_A04特典予約_P01旅客情報入力（必須情報）","device":{"deviceType":"PC"}};
	</script><div id="promotionArea">
		<!--===== promotionArea =====-->
		<!--===== /promotionArea =====--></div>
		<!--=========== /cmnContainer ============--></div><div id="cmnBaloonArea" aria-live="polite"></div><div id="cmnPopupArea" aria-live="polite"></div>

	

	<div id="cmnFooterWrapper">
		<!--=========== .cmnFooterWrapper ============-->
		<div id="cmnFooter">
			<!--===== cmnFooter =====-->
			<ul>
				<li><a href="https://www.ana.co.jp/other/int/meta/0012.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">プライバシーポリシー<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
					<li><a href="https://www.ana.co.jp/other/int/meta/0670.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">利用者情報の外部送信について<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
					</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/0052.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">サイト利用規約<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/be0238.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">推奨環境<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/0133.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">サイトマップ<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/0732.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">運送約款<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
					<li class="lastChild"><a href="https://www.ana.co.jp/other/int/meta/0556.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">ウェブアクセシビリティについて<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
					</li>
			</ul>
			<p class="blankText" aria-hidden="true"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="" height="10" width="10" />外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。</p>
			<p class="copyright"><span lang="en" xml:lang="en">Copyright&#169;ANA</span>
			</p>
			<p class="starAllianceLogo"><a href="http://www.staralliance.com/ja/" target="_blank"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/star_alliance_logo.png?0fe73b5" alt="A STAR ALLIANCE MEMBER" height="18" width="175" /><span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
			</p>
			<!--===== cmnFooter =====-->
		</div>
		<!--=========== /cmnFooterWrapper ============-->
	</div>

		
		<div id="cmnLoadingForAjax"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/bg_modalWindow.png?717d3c0" alt="" height="0" width="0" />
			<div class="loadingArea">
				<p class="loadingImage"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/loading.gif?717d3c0" alt="リクエストを処理しています...しばらくお待ちください。" height="33" width="33" />
				</p>
			</div>
		</div>
		
		
		<div id="popupAltSentence" style="display: none" aria-hidden="true">別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。</div>
	<div id="cmnSiteCatalyst" aria-hidden="true"><div id="cmnSiteCatalystParamArea"><script type="text/javascript">
				// ▼SiteCatalyst
				// SiteCatalyst code version: H.2.
				// Copyright 1997-2005 Omniture, Inc. More info available at http://www.omniture.com
				SiteCatalystReportSuites   = "INT";
				SiteCatalystCharSet        = "UTF-8";
				SiteCatalystChannel        = "BE_AWARD_J";
				SiteCatalystPageName       = "INT_BE_AWARD_J_A04\u7279\u5178\u4E88\u7D04_P01\u65C5\u5BA2\u60C5\u5831\u5165\u529B\uFF08\u5FC5\u9808\u60C5\u5831\uFF09";
				SiteCatalystERR            = "";
				SiteCatalystEventNum       = "";
				SiteCatalystPurchase       = "FALSE";
				SiteCatalystProductPrice   = "";
				SiteCatalystCurrencyCode   = "";
				SiteCatalystSeatAvailabilityINT   = "";
				SiteCatalystDepartDate     = "";
				SiteCatalystDepart         = "";
				SiteCatalystArrive         = "";
				SiteCatalystFlightNo       = "";
				SiteCatalystClass          = "";
				SiteCatalystFare           = "";
				SiteCatalystPurchaseID     = "";
				
				SiteCatalystLocater        = "";
				SiteCatalystPayment        = "";
				SiteCatalystIncome         = "";
				
				SiteCatalystPax            = "";
				SiteCatalystManipulateDay  = "";
				SiteCatalystCompartmentClass   = "";
				SiteCatalystOptionInfo     = "";
				SiteCatalystOutboundUpsellPrice   = "";
				SiteCatalystInboundUpsellPrice    = "";
				
				SiteCatalystSearchInput    = "";
				SiteCatalystSegment        = "";
				SiteCatalystOfficeCode     = "";
				SiteCatalystPassengerCount = "";
				SiteCatalystPassenger      = "";
				SiteCatalystAncillaryService = "";
				// End SiteCatalyst code version: H.2.
				// ▲SiteCatalyst
			</script></div><script type="text/javascript"><!-- if(navigator.appVersion.indexOf('MSIE')>=0)document.write(unescape('%3C')+'\!-'+'-') // --></script>
		<!--/DO NOT REMOVE/-->
		

		
		<!-- Google Tag Manager -->
		<script type="text/javascript" charset="Shift_JIS" src="https://www.ana.co.jp/common/js/GTMINTBE.js"></script>
		<!-- End Google Tag Manager -->
		<!-- Yahoo Tag Manager -->
		<script type="text/javascript" charset="Shift_JIS" src="https://www.ana.co.jp/common/js/YTMINTBE.js"></script>
		<!-- End Yahoo Tag Manager -->
		

	</div>

	

	<div class="mboxDefault"></div><script type="text/javascript">mboxCreate("ASW_common_A04_P01");</script>
			<script type="text/javascript" src="https://www.ana.co.jp/common/js/tealium/tealium.js"></script>
			<script type="text/javascript" src="//cdn.evgnet.com/beacon/allnipponairways/nonamcprd/scripts/evergage.min.js"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/javax.faces.resource/jsf.js.xhtml?ln=javax.faces"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/jquery.mousewheel.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/jquery.jscrollpane.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/jquery-ui-1.10.4.custom.min.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/jquery.ui.touch-punch.min.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/asw-common.js?beb6a50"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/asw-common-pc.js?fd88d7d"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/asw-format.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/mask.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/loading.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/asw-loadingwindow-pc.js?9e233c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/asw-loadingwindow.js?51db600"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/dialog-pc.js?25d36a4"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/asw-dialog-sequencer.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/asw-event.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/asw-date-select.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/asw-calendar-pc.js?9b86b8a"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/asw-calendar.js?db624c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/heightLine.js?0e7a1ce"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/asw-summaryarea.js?b21c6d7"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/asw-fingerprints.js?99001fc"></script><script type="text/javascript">
			Asw.init("https://aswbe-i.ana.co.jp/rei22h/international_asw", "ja");
			Asw.getCalendarHolidayList = function() {return [20250721,20250811,20250915,20250923,20251013,20251103,20251123,20251124,20260101,20260112,20260211,20260223,20260320,20260429,20260503,20260504,20260505,20260506,20260720,20260811,20260921,20260922,20260923,20261012,20261103,20261123,20270101,20270111,20270211,20270223,20270321,20270322,20270429,20270503,20270504,20270505,20270719,20270811,20270920,20270923,20271011,20271103,20271123,20280101,20280110,20280211,20280223,20280320,20280429,20280503,20280504,20280505,20280717,20280811,20280918,20280922,20281009,20281103,20281123,20290101,20290108,20290211,20290212,20290223,20290320,20290429,20290430,20290503,20290504,20290505,20290716,20290811,20290917,20290923,20290924,20291008,20291103,20291123];}
			Asw.ClientInfo.deviceType = "PC";
			Asw.ClientInfo.mobileDeviceType = "UNKNOWN";
			Asw.ClientInfo.osVersion = "";
			Asw.Format.init("https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/{1}/{2}/{3}?{0}");
			Asw.overwriteTargetAttributeValue("false");
		</script><script type="text/javascript">
		
		var Asw = Asw || {};
		Asw.ContactInput = Asw.ContactInput || {};
		Asw.ContactInput.Const = {
			/** data-属性名「data-type」 @constant @type {String} @default 'data-type' */
			DATA_ATTRIBUTE_NAME_TYPE: 'data-type',
			/** data-属性名「data-sms-possible」 @constant @type {String} @default 'data-sms-possible' */
			DATA_ATTRIBUTE_NAME_SMS_POSSIBLE: 'data-sms-possible',
			/** data-属性名「data-country-code」 @constant @type {String} @default 'data-country-code' */
			DATA_ATTRIBUTE_NAME_COUNTRY_CODE: 'data-country-code',
			/** data-type属性値「sms」 @constant @type {String} @default 'sms' */
			DATA_TYPE_VALUE_SMS: 'sms'
		};

		
		Asw.ContactInput.countryInfoMap = {"IS":{"internationalCallingCode":"+354","smsPossible":false},"IE":{"internationalCallingCode":"+353","smsPossible":true},"AZ":{"internationalCallingCode":"+994","smsPossible":true},"AF":{"internationalCallingCode":"+93","smsPossible":true},"US":{"internationalCallingCode":"+1","smsPossible":true},"AE":{"internationalCallingCode":"+971","smsPossible":true},"DZ":{"internationalCallingCode":"+213","smsPossible":true},"AR":{"internationalCallingCode":"+54","smsPossible":true},"AW":{"internationalCallingCode":"+297","smsPossible":true},"AL":{"internationalCallingCode":"+355","smsPossible":true},"AM":{"internationalCallingCode":"+374","smsPossible":true},"AO":{"internationalCallingCode":"+244","smsPossible":false},"AG":{"internationalCallingCode":"+1","smsPossible":true},"AD":{"internationalCallingCode":"+376","smsPossible":true},"YE":{"internationalCallingCode":"+967","smsPossible":true},"GB":{"internationalCallingCode":"+44","smsPossible":true},"IL":{"internationalCallingCode":"+972","smsPossible":true},"IT":{"internationalCallingCode":"+39","smsPossible":true},"IQ":{"internationalCallingCode":"+964","smsPossible":true},"IR":{"internationalCallingCode":"+98","smsPossible":true},"IN":{"internationalCallingCode":"+91","smsPossible":true},"ID":{"internationalCallingCode":"+62","smsPossible":true},"UG":{"internationalCallingCode":"+256","smsPossible":true},"UA":{"internationalCallingCode":"+380","smsPossible":true},"UZ":{"internationalCallingCode":"+998","smsPossible":true},"UY":{"internationalCallingCode":"+598","smsPossible":true},"EC":{"internationalCallingCode":"+593","smsPossible":true},"EG":{"internationalCallingCode":"+20","smsPossible":true},"EE":{"internationalCallingCode":"+372","smsPossible":true},"SZ":{"internationalCallingCode":"+268","smsPossible":false},"ET":{"internationalCallingCode":"+251","smsPossible":false},"ER":{"internationalCallingCode":"+291","smsPossible":true},"SV":{"internationalCallingCode":"+503","smsPossible":true},"AU":{"internationalCallingCode":"+61","smsPossible":true},"AT":{"internationalCallingCode":"+43","smsPossible":true},"OM":{"internationalCallingCode":"+968","smsPossible":true},"NL":{"internationalCallingCode":"+31","smsPossible":true},"GH":{"internationalCallingCode":"+233","smsPossible":true},"CV":{"internationalCallingCode":"+238","smsPossible":false},"GY":{"internationalCallingCode":"+592","smsPossible":true},"KZ":{"internationalCallingCode":"+7","smsPossible":true},"QA":{"internationalCallingCode":"+974","smsPossible":true},"CA":{"internationalCallingCode":"+1","smsPossible":true},"GA":{"internationalCallingCode":"+241","smsPossible":true},"CM":{"internationalCallingCode":"+237","smsPossible":true},"KR":{"internationalCallingCode":"+82","smsPossible":true},"KH":{"internationalCallingCode":"+855","smsPossible":true},"GM":{"internationalCallingCode":"+220","smsPossible":true},"KP":{"internationalCallingCode":"+850","smsPossible":false},"MK":{"internationalCallingCode":"+389","smsPossible":false},"GN":{"internationalCallingCode":"+224","smsPossible":true},"GW":{"internationalCallingCode":"+245","smsPossible":true},"CY":{"internationalCallingCode":"+357","smsPossible":true},"CU":{"internationalCallingCode":"+53","smsPossible":true},"GR":{"internationalCallingCode":"+30","smsPossible":true},"KI":{"internationalCallingCode":"+686","smsPossible":false},"KG":{"internationalCallingCode":"+996","smsPossible":true},"GT":{"internationalCallingCode":"+502","smsPossible":true},"GU":{"internationalCallingCode":"+1","smsPossible":true},"KW":{"internationalCallingCode":"+965","smsPossible":true},"KY":{"internationalCallingCode":"+1","smsPossible":true},"GD":{"internationalCallingCode":"+1","smsPossible":true},"HR":{"internationalCallingCode":"+385","smsPossible":true},"KE":{"internationalCallingCode":"+254","smsPossible":true},"CI":{"internationalCallingCode":"+225","smsPossible":true},"CR":{"internationalCallingCode":"+506","smsPossible":true},"SA":{"internationalCallingCode":"+966","smsPossible":true},"KM":{"internationalCallingCode":"+269","smsPossible":true},"CO":{"internationalCallingCode":"+57","smsPossible":true},"CG":{"internationalCallingCode":"+242","smsPossible":false},"CD":{"internationalCallingCode":"+243","smsPossible":true},"ZM":{"internationalCallingCode":"+260","smsPossible":true},"SM":{"internationalCallingCode":"+378","smsPossible":false},"SL":{"internationalCallingCode":"+232","smsPossible":true},"DJ":{"internationalCallingCode":"+253","smsPossible":false},"JM":{"internationalCallingCode":"+1","smsPossible":true},"CH":{"internationalCallingCode":"+41","smsPossible":true},"GE":{"internationalCallingCode":"+995","smsPossible":true},"SE":{"internationalCallingCode":"+46","smsPossible":true},"SY":{"internationalCallingCode":"+963","smsPossible":true},"ES":{"internationalCallingCode":"+34","smsPossible":true},"SG":{"internationalCallingCode":"+65","smsPossible":true},"LK":{"internationalCallingCode":"+94","smsPossible":true},"SK":{"internationalCallingCode":"+421","smsPossible":true},"SI":{"internationalCallingCode":"+386","smsPossible":true},"ZW":{"internationalCallingCode":"+263","smsPossible":true},"SD":{"internationalCallingCode":"+249","smsPossible":true},"KN":{"internationalCallingCode":"+1","smsPossible":true},"SR":{"internationalCallingCode":"+597","smsPossible":true},"LC":{"internationalCallingCode":"+1","smsPossible":true},"SC":{"internationalCallingCode":"+248","smsPossible":false},"SN":{"internationalCallingCode":"+221","smsPossible":true},"SB":{"internationalCallingCode":"+677","smsPossible":false},"RS":{"internationalCallingCode":"+381","smsPossible":true},"TH":{"internationalCallingCode":"+66","smsPossible":true},"TW":{"internationalCallingCode":"+886","smsPossible":true},"TJ":{"internationalCallingCode":"+992","smsPossible":true},"TZ":{"internationalCallingCode":"+255","smsPossible":true},"CZ":{"internationalCallingCode":"+420","smsPossible":true},"TD":{"internationalCallingCode":"+235","smsPossible":true},"CF":{"internationalCallingCode":"+236","smsPossible":true},"TN":{"internationalCallingCode":"+216","smsPossible":true},"CL":{"internationalCallingCode":"+56","smsPossible":true},"TV":{"internationalCallingCode":"+688","smsPossible":false},"DK":{"internationalCallingCode":"+45","smsPossible":true},"DE":{"internationalCallingCode":"+49","smsPossible":true},"TG":{"internationalCallingCode":"+228","smsPossible":true},"DO":{"internationalCallingCode":"+1","smsPossible":true},"DM":{"internationalCallingCode":"+1","smsPossible":false},"TT":{"internationalCallingCode":"+1","smsPossible":true},"TM":{"internationalCallingCode":"+993","smsPossible":false},"TR":{"internationalCallingCode":"+90","smsPossible":true},"TO":{"internationalCallingCode":"+676","smsPossible":false},"NG":{"internationalCallingCode":"+234","smsPossible":true},"NR":{"internationalCallingCode":"+674","smsPossible":false},"NA":{"internationalCallingCode":"+264","smsPossible":false},"NI":{"internationalCallingCode":"+505","smsPossible":true},"BH":{"internationalCallingCode":"+973","smsPossible":false},"NE":{"internationalCallingCode":"+227","smsPossible":true},"PK":{"internationalCallingCode":"+92","smsPossible":true},"JP":{"internationalCallingCode":"+81","smsPossible":true},"BS":{"internationalCallingCode":"+1","smsPossible":true},"NZ":{"internationalCallingCode":"+64","smsPossible":true},"BM":{"internationalCallingCode":"+1","smsPossible":true},"NP":{"internationalCallingCode":"+977","smsPossible":true},"BB":{"internationalCallingCode":"+246","smsPossible":true},"NO":{"internationalCallingCode":"+47","smsPossible":true},"HT":{"internationalCallingCode":"+509","smsPossible":true},"BD":{"internationalCallingCode":"+880","smsPossible":true},"VA":{"internationalCallingCode":"+39","smsPossible":false},"PA":{"internationalCallingCode":"+507","smsPossible":true},"VU":{"internationalCallingCode":"+678","smsPossible":false},"PG":{"internationalCallingCode":"+675","smsPossible":true},"PW":{"internationalCallingCode":"+680","smsPossible":true},"PY":{"internationalCallingCode":"+595","smsPossible":true},"PS":{"internationalCallingCode":"+970","smsPossible":true},"BR":{"internationalCallingCode":"+55","smsPossible":true},"HU":{"internationalCallingCode":"+36","smsPossible":true},"TL":{"internationalCallingCode":"+670","smsPossible":true},"BG":{"internationalCallingCode":"+359","smsPossible":true},"BF":{"internationalCallingCode":"+226","smsPossible":true},"BN":{"internationalCallingCode":"+673","smsPossible":true},"FJ":{"internationalCallingCode":"+679","smsPossible":true},"PH":{"internationalCallingCode":"+63","smsPossible":true},"FI":{"internationalCallingCode":"+358","smsPossible":true},"BT":{"internationalCallingCode":"+975","smsPossible":true},"BY":{"internationalCallingCode":"+375","smsPossible":true},"BZ":{"internationalCallingCode":"+501","smsPossible":false},"PR":{"internationalCallingCode":"+1","smsPossible":true},"BE":{"internationalCallingCode":"+32","smsPossible":true},"BA":{"internationalCallingCode":"+387","smsPossible":true},"FR":{"internationalCallingCode":"+33","smsPossible":true},"BO":{"internationalCallingCode":"+591","smsPossible":false},"PF":{"internationalCallingCode":"+689","smsPossible":true},"BI":{"internationalCallingCode":"+257","smsPossible":true},"VN":{"internationalCallingCode":"+84","smsPossible":true},"BJ":{"internationalCallingCode":"+229","smsPossible":true},"VE":{"internationalCallingCode":"+58","smsPossible":true},"PE":{"internationalCallingCode":"+51","smsPossible":true},"PL":{"internationalCallingCode":"+48","smsPossible":true},"BW":{"internationalCallingCode":"+267","smsPossible":true},"PT":{"internationalCallingCode":"+351","smsPossible":false},"HK":{"internationalCallingCode":"+852","smsPossible":true},"HN":{"internationalCallingCode":"+504","smsPossible":false},"MH":{"internationalCallingCode":"+692","smsPossible":true},"MO":{"internationalCallingCode":"+853","smsPossible":true},"MG":{"internationalCallingCode":"+261","smsPossible":true},"MW":{"internationalCallingCode":"+265","smsPossible":true},"ML":{"internationalCallingCode":"+223","smsPossible":true},"MT":{"internationalCallingCode":"+356","smsPossible":true},"MY":{"internationalCallingCode":"+60","smsPossible":true},"FM":{"internationalCallingCode":"+691","smsPossible":false},"MM":{"internationalCallingCode":"+95","smsPossible":true},"MX":{"internationalCallingCode":"+52","smsPossible":true},"MU":{"internationalCallingCode":"+230","smsPossible":true},"MR":{"internationalCallingCode":"+222","smsPossible":true},"MZ":{"internationalCallingCode":"+258","smsPossible":true},"MC":{"internationalCallingCode":"+377","smsPossible":true},"MV":{"internationalCallingCode":"+960","smsPossible":true},"MD":{"internationalCallingCode":"+373","smsPossible":false},"RO":{"internationalCallingCode":"+40","smsPossible":true},"MA":{"internationalCallingCode":"+212","smsPossible":true},"MN":{"internationalCallingCode":"+976","smsPossible":true},"ME":{"internationalCallingCode":"+382","smsPossible":true},"JO":{"internationalCallingCode":"+962","smsPossible":true},"LA":{"internationalCallingCode":"+856","smsPossible":true},"RU":{"internationalCallingCode":"+7","smsPossible":true},"LV":{"internationalCallingCode":"+371","smsPossible":true},"LT":{"internationalCallingCode":"+370","smsPossible":true},"LY":{"internationalCallingCode":"+218","smsPossible":true},"GQ":{"internationalCallingCode":"+240","smsPossible":false},"LI":{"internationalCallingCode":"+423","smsPossible":false},"LR":{"internationalCallingCode":"+231","smsPossible":true},"CN":{"internationalCallingCode":"+86","smsPossible":true},"LU":{"internationalCallingCode":"+352","smsPossible":true},"ZA":{"internationalCallingCode":"+27","smsPossible":true},"RW":{"internationalCallingCode":"+250","smsPossible":true},"LS":{"internationalCallingCode":"+266","smsPossible":false},"LB":{"internationalCallingCode":"+961","smsPossible":true}};

		$(function () {
			contactInfoInit();
		});
		
		function contactInfoInit() {
			
			
			Asw.get('representativeTelType').find('option[value="M1"]').attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_TYPE,
					Asw.ContactInput.Const.DATA_TYPE_VALUE_SMS);
			Asw.get('representativeTelCountry').children('option').each(function(i,option){
				var countryInfo = Asw.ContactInput.countryInfoMap[option.value];
				if (countryInfo === void 0) {
					
					return true;
				}
				$(option).attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_SMS_POSSIBLE, countryInfo.smsPossible);
				$(option).attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_COUNTRY_CODE, countryInfo.internationalCallingCode);
			});
			
			var smsCountrySelectSelector = 'select.js-countrySelect:not(#representativeTelCountry)'
			$(smsCountrySelectSelector).eq(0).children('option').each(function(i,option){
				var countryInfo = Asw.ContactInput.countryInfoMap[option.value];
				if (countryInfo === void 0) {
					
					return true;
				}
				$(smsCountrySelectSelector + ' option:nth-child(' + (i+1) + ')').attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_COUNTRY_CODE,
						countryInfo.internationalCallingCode);
			});
			
			var emergencyCountrySelectSelector = 'select.passengerEmergencyCountry.js-countrySelect:not(#representativeTelCountry)'
			$(emergencyCountrySelectSelector).eq(0).children('option').each(function(i,option){
				var countryInfo = Asw.ContactInput.countryInfoMap[option.value];
				if (countryInfo === void 0) {
					
					return true;
				}
				$(emergencyCountrySelectSelector + ' option:nth-child(' + (i+1) + ')').attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_COUNTRY_CODE,
						countryInfo.internationalCallingCode);
			});
		}
	</script><script type="text/javascript">
		var isInit = true;
		function changeTelType(_this, telNoInputId) {
			var $telNoInput = Asw.get(telNoInputId);
			var $telNoKind = Asw.get("representativeTelType");
			var telNo = Asw.getValueComparedToPlaceholder(telNoInputId);
			var telNoCountry = Asw.get("representativeTelCountry").val();

			if(true && !isInit) {
				if(telInputCheck(telNo, telNoCountry)) {
					if (_this.value == "M1") {
						telNo = exceptDomesticPrefix("09042504176", telNoCountry);
					} else if (_this.value == "B1") {
						telNo = exceptDomesticPrefix("03-5405-2172", telNoCountry);
					} else {
						telNo = exceptDomesticPrefix("090-4250-4176", telNoCountry);
					}
					$telNoInput.val(telNo);
				}
			}
			isInit = false;
		}
		
		function telInputCheck(telNo, telNoCountry) {
	        checkResult = false;
	        
	        var amcHndyTelNo = exceptDomesticPrefix("09042504176", telNoCountry);
	        var amcCmpTelNo = exceptDomesticPrefix("03-5405-2172", telNoCountry);
	        var amcTelNo = exceptDomesticPrefix("090-4250-4176", telNoCountry);
	
	        if(telNo == "" || telNo == amcHndyTelNo || telNo == amcCmpTelNo || telNo == amcTelNo) {
	        	checkResult = true;
	        }
	        return checkResult;
    	}
    	
    	function exceptDomesticPrefix(telNo, telNoCountry) {
    		if(telNoCountry == 'JP') {
    			if(telNo.substr(0, 1) === '0') {
    				return telNo.slice(1);
    			}
    		}
    		return telNo;
    	}
	</script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/asw-customerinfomationform.js?6e91953"></script><script type="text/javascript">
		$(document).ready(function(){
				Asw.enableTextWhenCheckBox('.cascadedSwitch');
				Asw.notPasteInput('.jsNotPasteInput');
			$(function(){
				Asw.alignHeightToParentTdElement( '.passengerInfoInputArea' );
				
					Asw.alignHeightToParentTdElement( '.passengerInfoInputText' );
				
				
				$('select.js-countrySelect').trigger('change');
			});
		});
	</script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/asw-session-keeper.js?6ec0730"></script><script type="text/javascript">
			$(document).ready(function() {
				Asw.SessionKeeper.initAndStart("sessionKeeperContainer", "520", "570", "PC");
			});
			</script><script type="text/javascript">
		Asw.addAjaxCallback(function(data) {
			// アコーディオンのメソッドと設定 
			if($('.jsAccordionSwitch .jsTriggerSwitch').length > 0){
				var events = $._data($('.jsAccordionSwitch .jsTriggerSwitch').get(0), "events");
				if(events === undefined || events.click === undefined) {
					Asw.AccordionInfo('.jsAccordionSwitch', {
						'triggerSwitch': '.jsAccordionSwitch .jsTriggerSwitch',
						'listClass': '.jsAccordionSwitchList',
						'visibilityHidden': 'toggleContents'
					});
				}
			}
		});
	</script><script type="text/javascript">
	Asw.Price = Asw.Price || {};
	// 整数の区切り文字
	Asw.Price.separateString = ',';
	
	// 整数の区切り桁数
	Asw.Price.separateDigitNumber = 3;
	
	// 小数点
	Asw.Price.decimalPointString = '.';
	
	// 小数点以降の有効桁数
	Asw.Price.significantDigit = 0;
	
	// 表示金額の単位
	Asw.Price.currencyUnit = '円';
	</script><script type="text/javascript">
			$(document).ready(function() {
				$('#preLoadingArea').css('visibility', 'hidden');
			});
		</script><noscript><img src="https://aswbe-i.ana.co.jp/akam/13/pixel_7e8c99da?a=dD02NDNkMjUxYzk2OGI0OTM2OGQxYmM3NTY0OGRlN2M4NjhhZTU3ODU0JmpzPW9mZg==" style="visibility: hidden; position: absolute; left: -999px; top: -999px;" /></noscript><script type="text/javascript"  src="/RcS9yzCRgrNnM/gY/v0Wf9fQwtpdQ/7NE3X2ruOE2SL9YE/HyNpQmYB/RWt/XOHcnHAU"></script></body>
</html>