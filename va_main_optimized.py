import requests
import os
import sys
import traceback
import json
import time
import datetime
import random
import argparse

from loguru import logger
import threading
import concurrent.futures
from collections import defaultdict
import queue
import urllib3
import redis
import asyncio # <-- Add asyncio
import redis.asyncio as aredis
from curl_cffi import requests as curl_requests # Import curl_cffi requests
from dateutil.relativedelta import relativedelta
from urllib3.exceptions import InsecureRequestWarning
import logging # Add standard logging import
from flask_socketio import SocketIO  # 新增: 用于跨进程实时推送
# Publisher for cross-process real-time updates via Redis (using env vars)
socketio_pub = SocketIO(message_queue=f"redis://{os.environ.get('REDIS_HOST', 'localhost')}:{os.environ.get('REDIS_PORT', 6379)}/{os.environ.get('REDIS_DB', 0)}")

# --- Add InterceptHandler to capture standard logs ---
class InterceptHandler(logging.Handler):
    def emit(self, record):
        # Get corresponding Loguru level if it exists
        try:
            level = logger.level(record.levelname).name
        except ValueError:
            level = record.levelno

        # Find caller from where originated the logged message
        frame, depth = logging.currentframe(), 2
        while frame.f_code.co_filename == logging.__file__:
            frame = frame.f_back
            depth += 1

        logger.opt(depth=depth, exception=record.exc_info).log(level, record.getMessage())
# ----------------------------------------------------

# 添加403错误计数器
failed_403_count = 0

# Redis配置
REDIS_HOST = os.environ.get("REDIS_HOST", "localhost")
REDIS_PORT = int(os.environ.get("REDIS_PORT", 6379))
REDIS_DB = int(os.environ.get("REDIS_DB", 0))
REDIS_PASSWORD = os.environ.get("REDIS_PASSWORD", None)  # 如果有密码，请设置
REDIS_KEY_PREFIX = "reese84:"
# REDIS_TOKEN_EXPIRY = 600 # Token expiry is managed in reese84/main.py

# --- 添加城市定义 ---
AMERICAS_CITIES = ['JFK', 'LAX', 'ORD', 'SFO', 'IAD', 'IAH', 'YVR', 'SEA']
CHINA_JAPAN_CITIES = ['PEK', 'PVG', 'SHA', 'HKG', 'TAO', 'SZX', 'CAN', 'NRT']
# --- 结束城市定义 ---

# 同步 Redis 客户端，用于写入航班更新到 Redis 哈希并发布消息
redis_sync_client = redis.Redis(host=REDIS_HOST, port=REDIS_PORT, db=REDIS_DB, password=REDIS_PASSWORD, decode_responses=True)

# --- 修改 Redis 初始化函数 ---
# 重命名并更新为使用 redis.asyncio
async def initialize_aredis(redis_host=REDIS_HOST, redis_port=REDIS_PORT, redis_db=REDIS_DB, redis_password=REDIS_PASSWORD):
    """Initializes a redis.asyncio client instance."""
    redis_url = f"redis://{':'+redis_password+'@' if redis_password else ''}{redis_host}:{redis_port}/{redis_db}"
    try:
        # redis.asyncio.from_url 通常返回一个可以直接使用的客户端实例
        # decode_responses=True 对字符串操作很重要
        redis_client = aredis.from_url(redis_url, encoding="utf-8", decode_responses=True, max_connections=20)
        await redis_client.ping() # 验证连接
        logger.success(f"Successfully connected to Redis at {redis_url} using redis.asyncio.")
        return redis_client # 直接返回客户端实例
    except redis.exceptions.RedisError as e: # 使用 redis.exceptions
        logger.critical(f"Failed to connect to Redis using redis.asyncio: {e}")
        return None
    except Exception as e:
        logger.critical(f"Unexpected error initializing redis.asyncio client: {e}")
        logger.critical(traceback.format_exc())
        return None
# --- End Redis 初始化 ---

# --- Start: Insert get_redis_key and AsyncTokenFetcher definitions ---

# Add redis key generating function (copied from reese84/main.py for consistency)
def get_redis_key(proxy_str):
    """获取代理对应的Redis键名"""
    if not proxy_str:
        return f"{REDIS_KEY_PREFIX}direct"
    return f"{REDIS_KEY_PREFIX}{proxy_str}"

# Constants for AsyncTokenFetcher (can be defined here or inherited if fetcher is imported)
TOKEN_TTL_THRESHOLD_SECONDS = 180  # Minimum TTL required for a token to be considered valid (与Token服务保持一致)
MAX_RANDOM_PROXY_ATTEMPTS = 5 # Max attempts to find a token from a random proxy if preferred fails

class AsyncTokenFetcher:
    """
    Asynchronously fetches valid tokens from Redis cache populated by the background generator.
    Uses redis.asyncio (aredis). Includes detailed logging.
    """
    def __init__(self, redis_client: aredis.Redis, proxy_manager_instance, logger_instance, loop):
        """
        Initializes the AsyncTokenFetcher.

        Args:
            redis_client: An initialized redis.asyncio Redis client instance.
            proxy_manager_instance: An instance of ProxyManager to get active proxies.
            logger_instance: A configured logger instance (Loguru assumed).
            loop: The asyncio event loop from the main thread.
        """
        if not redis_client:
            # Use logger_instance if available, otherwise fallback to global logger or print
            log = logger_instance or logger
            log.critical("[TokenFetcher Init] CRITICAL: redis.asyncio client is required.")
            raise ValueError("redis.asyncio client is required.")
        if not proxy_manager_instance:
            log = logger_instance or logger
            log.critical("[TokenFetcher Init] CRITICAL: ProxyManager instance is required.")
            raise ValueError("ProxyManager instance is required.")
        if not loop:
            log = logger_instance or logger
            log.critical("[TokenFetcher Init] CRITICAL: Asyncio event loop is required.")
            raise ValueError("Asyncio event loop is required.")

        self.redis_client = redis_client
        self.proxy_manager = proxy_manager_instance
        # Ensure logger is stored, use provided or fallback to global logger
        self.logger = logger_instance or logger
        self.loop = loop

    async def _get_token_data_from_redis(self, proxy_str):
        """Internal helper to get token data and TTL."""
        if not proxy_str:
            proxy_str = "direct"
        # Use the globally defined get_redis_key function within this file
        key = get_redis_key(proxy_str)
        temp_logger = self.logger # Use the instance logger

        try:
            # Ensure self.redis_client is the aredis client
            if not isinstance(self.redis_client, aredis.Redis):
                 temp_logger.critical(f"[TokenFetcher] CRITICAL: aredis client (self.redis_client) is of wrong type")
                 return None, None, None

            async with self.redis_client.pipeline(transaction=False) as pipe:
                await pipe.ttl(key)
                await pipe.get(key)
                results = await pipe.execute()

            ttl = results[0]
            data_json = results[1]

            if data_json and ttl is not None and ttl >= TOKEN_TTL_THRESHOLD_SECONDS:
                try:
                    if not isinstance(data_json, (str, bytes)):
                         return None, None, None

                    data = json.loads(data_json)
                    token = data.get('token')
                    ua = data.get('ua')
                    if token and ua:
                        return token, ua, proxy_str
                    else:
                        return None, None, None
                except (json.JSONDecodeError, TypeError):
                    return None, None, None
            else:
                return None, None, None
        except redis.exceptions.RedisError:
            return None, None, None
        except Exception:
            return None, None, None

    async def get_token(self, preferred_proxy_str=None):
        """Asynchronously fetches a valid token."""
        # 1. Try preferred
        if preferred_proxy_str:
            token, ua, used_proxy = await self._get_token_data_from_redis(preferred_proxy_str)
            if token:
                return token, ua, used_proxy

        # 2. Get all available tokens from Redis first
        try:
            # Get all reese84 keys from Redis
            pattern = "reese84:*"
            keys = await self.redis_client.keys(pattern)

            if not keys:
                return None, None, None

            # Extract proxy strings from keys and check TTL
            available_proxies = []
            for key in keys:
                try:
                    ttl = await self.redis_client.ttl(key)
                    if ttl >= TOKEN_TTL_THRESHOLD_SECONDS:
                        proxy_str = key.replace("reese84:", "")
                        available_proxies.append(proxy_str)
                except Exception:
                    continue

            if not available_proxies:
                return None, None, None

            # Shuffle and try available proxies
            random.shuffle(available_proxies)
            attempts = 0
            for proxy_str_to_try in available_proxies:
                if attempts >= MAX_RANDOM_PROXY_ATTEMPTS:
                    break
                if preferred_proxy_str and proxy_str_to_try == preferred_proxy_str:
                     continue # Already tried

                token, ua, used_proxy = await self._get_token_data_from_redis(proxy_str_to_try)
                if token:
                    return token, ua, used_proxy
                attempts += 1

            return None, None, None
        except Exception:
             return None, None, None

    async def invalidate_token(self, proxy_str_to_invalidate):
         """Removes the token for the specified proxy."""
         if not proxy_str_to_invalidate: proxy_str_to_invalidate = "direct"
         key = get_redis_key(proxy_str_to_invalidate)
         try:
             if not isinstance(self.redis_client, aredis.Redis):
                 return False
             await self.redis_client.delete(key)
             return True
         except redis.exceptions.RedisError:
             return False
         except Exception:
             return False

    # --- Synchronous Wrappers (with logging) ---
    def get_token_sync(self, preferred_proxy_str=None):
        """
        Synchronously calls the async get_token method from a different thread.
        This is the primary method for worker threads to get tokens.
        """
        if not hasattr(self, 'loop') or not self.loop.is_running():
            self.logger.error("[TokenFetcher] Main event loop is not available or not running for async call.")
            self.logger.warning("[TokenFetcher] Falling back to direct synchronous Redis fetch.")
            return self._get_token_sync_direct(preferred_proxy_str)

        try:
            # Schedule the coroutine on the main event loop from this worker thread.
            future = asyncio.run_coroutine_threadsafe(self.get_token(preferred_proxy_str), self.loop)

            # Wait for the result with a reasonable timeout.
            result = future.result(timeout=15)
            return result
        except asyncio.TimeoutError:
            self.logger.warning(f"Token fetch timed out in sync wrapper after 15s (proxy: {preferred_proxy_str}).")
            # Attempt to cancel the future to clean up resources on the event loop.
            self.loop.call_soon_threadsafe(future.cancel)
            return None, None, None
        except Exception as e:
            self.logger.error(f"Token fetch error in sync wrapper: {e}", exc_info=True)
            return None, None, None

    def _get_token_sync_direct(self, preferred_proxy_str=None):
        """
        Direct synchronous token fetch using a temporary sync Redis client.
        Used as a fallback mechanism.
        """
        sync_client = None
        try:
            import redis as sync_redis
            import random

            # Create sync Redis client using environment variables
            redis_config = {
                'host': REDIS_HOST,
                'port': REDIS_PORT,
                'db': REDIS_DB,
                'password': REDIS_PASSWORD,
                'decode_responses': True,
                'socket_timeout': 5,
                'socket_connect_timeout': 5
            }

            sync_client = sync_redis.Redis(**redis_config)
            sync_client.ping()

            # 1. Try preferred proxy first
            if preferred_proxy_str:
                token, ua, used_proxy = self._get_token_from_redis_sync(sync_client, preferred_proxy_str)
                if token:
                    return token, ua, used_proxy

            # 2. Get all available tokens
            keys = sync_client.keys("reese84:*")
            if not keys:
                self.logger.warning("[TokenFetcher-direct] No reese84 token keys found in Redis.")
                return None, None, None

            available_proxies = []
            for key in keys:
                try:
                    if sync_client.ttl(key) >= TOKEN_TTL_THRESHOLD_SECONDS:
                        available_proxies.append(key.replace("reese84:", ""))
                except Exception:
                    continue

            if not available_proxies:
                self.logger.warning(f"[TokenFetcher-direct] No proxies with sufficient TTL (>{TOKEN_TTL_THRESHOLD_SECONDS}s) found.")
                return None, None, None

            random.shuffle(available_proxies)
            for proxy_str_to_try in available_proxies[:10]:
                if preferred_proxy_str and proxy_str_to_try == preferred_proxy_str:
                    continue
                token, ua, used_proxy = self._get_token_from_redis_sync(sync_client, proxy_str_to_try)
                if token:
                    return token, ua, used_proxy

            self.logger.warning(f"[TokenFetcher-direct] Failed to get a valid token after trying {min(10, len(available_proxies))} proxies.")
            return None, None, None
        except Exception as e:
            self.logger.error(f"Direct sync token fetch encountered an exception: {e}", exc_info=True)
            return None, None, None
        finally:
            if sync_client:
                try:
                    sync_client.close()
                except Exception as close_e:
                    self.logger.error(f"Error closing direct sync Redis client: {close_e}")

    def _get_token_from_redis_sync(self, sync_client, proxy_str):
        """Get token from Redis using sync client."""
        try:
            if not proxy_str:
                proxy_str = "direct"

            key = get_redis_key(proxy_str)
            self.logger.debug(f"[TokenFetcher] 检查Redis键: {key}")

            # Get TTL and data
            ttl = sync_client.ttl(key)
            data_json = sync_client.get(key)

            self.logger.debug(f"[TokenFetcher] Redis数据 - TTL: {ttl}, 数据长度: {len(data_json) if data_json else 0}")

            if data_json and ttl is not None and ttl >= TOKEN_TTL_THRESHOLD_SECONDS:  # 使用常量
                try:
                    import json
                    data = json.loads(data_json)
                    token = data.get('token')
                    ua = data.get('ua')
                    self.logger.debug(f"[TokenFetcher] 解析数据 - token存在: {bool(token)}, ua存在: {bool(ua)}")
                    if token and ua:
                        self.logger.debug(f"[TokenFetcher] 成功获取有效token和UA: {proxy_str}")
                        return token, ua, proxy_str
                    else:
                        self.logger.warning(f"[TokenFetcher] token或ua为空: token={bool(token)}, ua={bool(ua)}")
                except (json.JSONDecodeError, TypeError) as e:
                    self.logger.error(f"[TokenFetcher] JSON解析失败: {e}, 数据: {data_json[:100] if data_json else 'None'}")
            else:
                self.logger.debug(f"[TokenFetcher] 条件不满足 - 数据存在: {bool(data_json)}, TTL: {ttl}, TTL>={TOKEN_TTL_THRESHOLD_SECONDS}: {ttl >= TOKEN_TTL_THRESHOLD_SECONDS if ttl is not None else False}")

            return None, None, None

        except Exception as e:
            self.logger.error(f"[TokenFetcher] _get_token_from_redis_sync异常: {e}")
            return None, None, None

    def invalidate_token_sync(self, proxy_str_to_invalidate):
        """Synchronously calls the async invalidate_token method."""
        try:
            if not hasattr(self, 'loop') or not isinstance(self.loop, asyncio.AbstractEventLoop):
                 try:
                      current_loop = asyncio.get_running_loop()
                 except RuntimeError:
                      return False
            else:
                 current_loop = self.loop

            # Call the async invalidate_token method defined in *this* class
            future = asyncio.run_coroutine_threadsafe(self.invalidate_token(proxy_str_to_invalidate), current_loop)
            # Increased timeout slightly
            result = future.result(timeout=7)
            return result
        except asyncio.TimeoutError:
             return False
        except Exception:
            return False

# --- End: Insert get_redis_key and AsyncTokenFetcher definitions ---

# 导入代理池管理系统
from proxy_pool_manager import ProxyManager, setup_logging as setup_proxy_logging

# 使用RLock而不是Lock，允许同一线程多次获取锁
log_file_locks = {
    "proxy_pool": threading.RLock(),
    "va_monitor": threading.RLock(),
    "reese84": threading.RLock()
}

# 禁用不安全请求的警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 配置日志
def setup_logging():
    try:
        # --- Intercept standard logging ---
        logging.basicConfig(handlers=[InterceptHandler()], level=0, force=True)
        # ----------------------------------

        # 获取当前脚本的绝对路径
        current_script = os.path.abspath(__file__)
        current_dir = os.path.dirname(current_script)

        # 构建日志文件的绝对路径
        log_dir = os.path.join(current_dir, "logs")

        # 确保日志目录存在
        os.makedirs(log_dir, exist_ok=True)

        log_file = os.path.join(log_dir, "va_monitor.log")

        # 配置loguru日志输出到文件和控制台
        logger.remove()  # 移除默认的控制台处理器

        # 添加文件处理器 - 详细日志写入文件
        logger.add(
            log_file,
            rotation="1.5 MB", # 将轮换大小修改为 1.5MB
            retention="7 days",
            level="DEBUG", # <--- 文件日志级别已设置为 DEBUG
            backtrace=True,
            diagnose=True,
            enqueue=True,
            serialize=True,
            format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}"
        )

        # 添加控制台处理器 - 恢复为SUCCESS级别，只显示重要信息
        logger.add(
            sys.stderr,
            colorize=True,
            level="SUCCESS", # <--- 恢复为SUCCESS级别，只显示成功和错误信息
            format="{time:HH:mm:ss} | {level} | {message}"
        )

        logger.info("监控程序日志系统初始化完成")

        # 初始化代理池管理系统的日志
        setup_proxy_logging()
        logger.info("代理池日志系统初始化完成")

    except Exception as e:
        print(f"日志配置过程中发生错误: {e}")
        print(traceback.format_exc())
        sys.exit(1)

# 初始化代理池管理器
def init_proxy_manager(config_file="proxy_config.json"): # 默认配置文件在项目根目录
    """初始化代理池管理器，优先从文件加载，若失败则使用内置列表并保存"""
    try:
        # 尝试从配置文件初始化代理池管理器
        proxy_manager = ProxyManager(config_file)
        logger.debug(f"尝试从 {config_file} 加载代理配置...")

        if not proxy_manager.proxies:
            logger.warning(f"未能从 {config_file} 加载代理，没有可用代理，程序将继续运行。")
            # 不再需要内置代理列表
            # builtin_proxy_list = [
            #     "us.arxlabs.io:3010:1pvi61410-region-US-sid-PwCBV2iZ-t-5:ketsns25",
            #     ...
            # ]
            # logger.error(f"错误：未找到或无法加载 {config_file}，且代码中已移除内置代理列表！")
            # logger.error("请确保 proxy_config.json 文件存在于项目根目录并包含有效的代理信息。")
            # return None # 返回 None 以指示初始化失败
        else:
            logger.success(f"成功从 {config_file} 加载了 {len(proxy_manager.proxies)} 个代理")

        # 配置代理池 (这些设置可以放在 ProxyManager 内部，或在此处覆盖)
        proxy_manager.selection_strategy = "weighted_random"
        proxy_manager.min_request_interval = 0.1
        proxy_manager.direct_request_enabled = False
        proxy_manager.direct_request_ratio = 0.0

        logger.debug(f"代理池管理器初始化完成，最终代理数: {len(proxy_manager.proxies)}")
        return proxy_manager

    except Exception as e:
        logger.error(f"初始化代理池管理器失败: {e}")
        logger.error(traceback.format_exc())
        return None

# 获取请求头和cookies
def get_headers_cookies(reese84_token, user_agent):
    """
    Builds headers and cookies using the provided token and user agent.
    Updated to match the working demo format.
    """
    if not reese84_token or not user_agent:
        logger.error("Cannot build headers/cookies: Missing reese84_token or user_agent.")
        return None, None

    cookies = {
        'reese84': reese84_token
    }

    # 使用固定的conversation-id和execution ID（与demo保持一致）
    conversation_id = 'cmbpvelrc7v38e1sm7w6wbhcq'
    execution_id = '40e39d50-c375-4a8d-b190-593eced6b63a'

    headers = {
        'accept': '*/*',
        'accept-language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
        'adrum': 'isAjax:true',
        'application-id': 'SWS1:SBR-DigConShpBk:fd34efe9a9',
        'cache-control': 'no-cache',
        'content-type': 'application/json',
        'conversation-id': conversation_id,
        'dc-url': '',
        'execution': execution_id,
        'origin': 'https://book.virginaustralia.com',
        'pragma': 'no-cache',
        'priority': 'u=1, i',
        'referer': 'https://book.virginaustralia.com/dx/VADX/',
        'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin',
        'ssgtoken': 'undefined',
        'ssotoken': 'undefined',
        'user-agent': "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36",  # 与demo程序保持完全一致
        'x-sabre-storefront': 'VADX',
    }

    return headers, cookies

# 构建请求数据
def build_request_data(origin, destination, date):
    # 保持标准的日期格式 (YYYY-MM-DD)，与demo保持一致
    formatted_date = date

    json_data = {
        'operationName': 'bookingAirSearch',
        'variables': {
            'airSearchInput': {
                'cabinClass': 'First',
                'awardBooking': True,  # 设置为True表示积分兑换
                'promoCodes': [],
                'searchType': 'BRANDED',
                'itineraryParts': [
                    {
                        'from': {
                            'useNearbyLocations': False,
                            'code': origin,
                        },
                        'to': {
                            'useNearbyLocations': False,
                            'code': destination,
                        },
                        'when': {
                            'date': formatted_date,
                        },
                    },
                ],
                'passengers': {
                    'ADT': 1,
                },
            },
        },
        'extensions': {},
        'query': 'query bookingAirSearch($airSearchInput: CustomAirSearchInput) {\n  bookingAirSearch(airSearchInput: $airSearchInput) {\n    originalResponse\n    __typename\n  }\n}',
    }
    logger.debug(f"构建的请求数据: {json_data}")
    return json_data

# 搜索航班 - 使用代理池管理器
def search_flight(origin, destination, date, proxy_manager, token_fetcher, use_proxy=True):
    """
    Searches for flights using a token obtained from token_fetcher.

    Args:
        ... (other args) ...
        token_fetcher: An instance of AsyncTokenFetcher.
        ...
    """
    proxy = None # Initialize proxy to None
    use_direct = False # Initialize use_direct
    reese84_token = None
    ua = None
    used_proxy_str = None # Store the proxy string used for this attempt

    try:
        # 1. Select proxy (still needed for routing the API call itself)
        proxy, use_direct = proxy_manager.select_proxy()

        # If use_proxy=False, force direct connection
        if not use_proxy:
            use_direct = True
            proxy = None

        preferred_proxy_str = proxy.proxy_str if proxy else ("direct" if use_direct else None)
        logger.debug(f"Attempting to fetch token using preferred proxy: {preferred_proxy_str}")

        # 2. Get token using the fetcher (synchronous wrapper)
        if token_fetcher is None:
             logger.critical("CRITICAL: token_fetcher is None in search_flight. Cannot proceed.")
             return None

        # --- 修改开始: 检查 get_token_sync 的返回值 ---
        token_sync_result = token_fetcher.get_token_sync(preferred_proxy_str=preferred_proxy_str)

        # 检查返回的是否为 None (表示获取失败或内部错误)
        if token_sync_result is None:
            logger.error(f"token_fetcher.get_token_sync returned None for {origin}->{destination} on {date} (Preferred: {preferred_proxy_str}). Skipping search.")
            # 标记初始代理失败 (如果适用)
            if preferred_proxy_str is not None:
                 proxy_to_update = proxy if preferred_proxy_str != "direct" else None
                 proxy_manager.update_proxy_result(proxy_to_update, False, 0, "token_fetch_sync_returned_none")
            return None # 无法继续

        # 如果不是 None，则解包
        reese84_token, ua, used_proxy_str = token_sync_result
        # --- 修改结束 ---

        # 添加详细的令牌获取日志
        logger.info(f"🔑 令牌获取成功:")
        logger.info(f"   - 令牌长度: {len(reese84_token) if reese84_token else 0} 字符")
        logger.info(f"   - 令牌前缀: {reese84_token[:50] if reese84_token else 'None'}...")
        logger.info(f"   - 令牌后缀: ...{reese84_token[-20:] if reese84_token and len(reese84_token) > 70 else ''}")
        logger.info(f"   - User-Agent: {ua[:100] if ua else 'None'}...")
        logger.info(f"   - 令牌来源: {used_proxy_str}")

        # 检查令牌格式是否正确
        if reese84_token and reese84_token.startswith('3:'):
            logger.success(f"✅ 令牌格式验证: 令牌以'3:'开头，格式正确")
        else:
            logger.warning(f"⚠️ 令牌格式警告: 令牌不以'3:'开头，可能格式异常")

        # 原有的检查可以保留，以防万一解包后的值仍然是 None 或空
        if not reese84_token or not ua:
            logger.error(f"Token fetch successful (proxy: {used_proxy_str}) but token or ua is invalid for {origin}->{destination} on {date}. Token: {reese84_token}, UA: {ua}")
            # 标记 token 对应的代理为失败
            proxy_manager.update_proxy_result(proxy, False, 0, "invalid_token_data_after_fetch")
            return None # Cannot proceed without a token

        # 删除成功获取令牌的日志输出

        # If the fetcher returned a token for a different proxy than initially selected,
        # update the 'proxy' and 'use_direct' variables to match the one actually used for the token.
        if used_proxy_str != preferred_proxy_str:
            # 删除令牌不匹配的日志输出
            if used_proxy_str == "direct":
                proxy = None
                use_direct = True
            else:
                # Find the matching proxy object
                original_proxy = proxy
                proxy = next((p for p in proxy_manager.proxies if p.proxy_str == used_proxy_str), None)
                if proxy:
                    use_direct = False
                else:
                    proxy = original_proxy # Revert if not found
                    use_direct = (proxy is None)


        # 3. Get headers and cookies using the fetched token and ua
        headers, cookies = get_headers_cookies(reese84_token, ua)
        if not headers or not cookies:
            logger.error("Failed to build headers/cookies even after getting token.")
            # If header build fails, it's not necessarily the proxy's fault, but the token might be involved.
            # Consider whether to mark the proxy used for the token as failed.
            # proxy_manager.update_proxy_result(proxy, False, 0, "header_build_failed") # Maybe too harsh?
            return None

        logger.debug(f"Successfully built headers/cookies: {origin} -> {destination}, 日期: {date}")
        logger.debug(f"Cookie: reese84={cookies.get('reese84', '')[:20]}...")
        logger.debug(f"Headers: {json.dumps(headers, indent=2)}")

        # 4. Build request data (no change needed here)
        json_data = build_request_data(origin, destination, date)
        logger.debug(f"构建的请求数据: {json.dumps(json_data, indent=2)}")

        # 5. Build request arguments
        request_kwargs = {
            'cookies': cookies,
            'headers': headers,
            'json': json_data,
            'timeout': 25,
            'impersonate': "chrome101"  # 与demo程序保持一致
        }

        # Add proxy to request if not direct - Use the proxy determined by token fetch
        current_request_proxy_str = "direct" # Default to direct
        if not use_direct and proxy:
            if hasattr(proxy, 'proxies') and proxy.proxies:
                request_kwargs['proxies'] = proxy.proxies
                current_request_proxy_str = proxy.proxy_str # Log the proxy used for the request
                logger.debug(f"Sending request: {origin}->{destination}, Date:{date}, Using Proxy:{current_request_proxy_str}")
            else:
                logger.error(f"代理对象缺少有效的proxies配置: {proxy.proxy_str}")
                # 标记代理为失败并使用直接连接
                proxy_manager.update_proxy_result(proxy, False, 0, "invalid_proxy_config")
                use_direct = True
                logger.debug(f"Sending request: {origin}->{destination}, Date:{date}, No Proxy (Direct - proxy config invalid)")
        else:
            logger.debug(f"Sending request: {origin}->{destination}, Date:{date}, No Proxy (Direct)")


        # 6. Send the request
        logger.info(f"🚀 发送航班搜索请求: {origin} -> {destination}, 日期: {date}")
        logger.info(f"📋 请求详情:")
        logger.info(f"   - URL: https://book.virginaustralia.com/api/graphql")
        logger.info(f"   - 使用令牌: {reese84_token[:30]}...{reese84_token[-10:] if len(reese84_token) > 40 else reese84_token}")
        logger.info(f"   - 令牌来源代理: {used_proxy_str}")
        logger.info(f"   - 请求路由代理: {current_request_proxy_str}")
        logger.info(f"   - User-Agent: {ua[:80]}...")
        logger.info(f"   - impersonate: {request_kwargs.get('impersonate', 'None')}")
        logger.info(f"   - Cookie reese84: {cookies.get('reese84', '')[:30]}...{cookies.get('reese84', '')[-10:] if len(cookies.get('reese84', '')) > 40 else cookies.get('reese84', '')}")

        # 验证令牌是否正确设置在cookie中
        if cookies.get('reese84') == reese84_token:
            logger.success(f"✅ 令牌验证: Cookie中的reese84与获取的令牌一致")
        else:
            logger.error(f"❌ 令牌验证失败: Cookie中的reese84与获取的令牌不一致!")
            logger.error(f"   获取的令牌: {reese84_token}")
            logger.error(f"   Cookie中的令牌: {cookies.get('reese84', 'None')}")

        start_time = time.time()
        response = curl_requests.post(
            'https://book.virginaustralia.com/api/graphql',
            **request_kwargs
        )
        end_time = time.time()
        response_time = end_time - start_time

        logger.info(f"📥 请求完成: 状态码={response.status_code}, 耗时={response_time:.2f}秒")

        # 7. Process response
        if response.status_code == 200:
            # Use the proxy object associated with the *request routing* for success/failure update
            proxy_manager.update_proxy_result(proxy, True, response_time)
            logger.success(f"🎉 请求成功! 状态码: 200, 耗时: {response_time:.2f}秒")

            response_json = response.json()
            logger.info(f"📄 响应数据预览: {json.dumps(response_json, indent=2)[:500]}...")

            # 检查响应中是否包含预期的数据结构
            if 'data' in response_json and 'bookingAirSearch' in response_json.get('data', {}):
                logger.success(f"✅ 响应结构验证: 包含预期的bookingAirSearch数据")
            else:
                logger.warning(f"⚠️ 响应结构警告: 缺少预期的数据结构")

            if 'errors' in response_json:
                 logger.error(f"❌ API返回错误 (状态码200): {json.dumps(response_json['errors'], indent=2)}")
                 # 修改: 更严格的令牌失效条件，只在明确的令牌相关错误时才失效
                 # 检查是否包含明确的令牌/认证相关错误信息
                 token_related_errors = [
                     "authentication required",
                     "invalid token",
                     "token expired",
                     "unauthorized",
                     "authentication failed"
                 ]
                 is_token_error = any(
                     any(keyword in str(err).lower() for keyword in token_related_errors)
                     for err in response_json['errors']
                 )
                 if is_token_error:
                    logger.warning(f"🔄 检测到明确的令牌相关错误，将使令牌失效: {used_proxy_str}")
                    token_fetcher.invalidate_token_sync(used_proxy_str)
                 else:
                    logger.info(f"ℹ️ API返回错误但非令牌相关，保留令牌: {used_proxy_str}")
            return response_json
        else:
            # Log failure before potentially invalidating token
            logger.error(f"❌ 请求失败!")
            logger.error(f"   - 状态码: {response.status_code}")
            logger.error(f"   - 使用的令牌: {reese84_token[:30]}...{reese84_token[-10:] if len(reese84_token) > 40 else reese84_token}")
            logger.error(f"   - 令牌来源代理: {used_proxy_str}")
            logger.error(f"   - 请求路由代理: {current_request_proxy_str}")
            logger.error(f"   - 响应内容: {response.text[:500]}")

            # Update result for the proxy used for the *request*
            proxy_manager.update_proxy_result(proxy, False, 0, f"status_code_{response.status_code}")

            # --- 修改: 更保守的令牌失效策略 ---
            # 只在明确的认证错误时才使令牌失效，403可能是代理IP问题而非令牌问题
            # 401通常表示认证失败，403可能是IP被封或其他原因
            if response.status_code == 401:
                logger.warning(f"🔄 检测到401认证错误，将使令牌失效: {used_proxy_str}")
                token_fetcher.invalidate_token_sync(used_proxy_str)
            elif response.status_code == 403:
                # 403错误更可能是代理IP问题，不立即使令牌失效
                logger.warning(f"⚠️ 检测到403错误，可能是代理IP被封，暂不使令牌失效: {used_proxy_str}")
                # 可以考虑在连续多次403错误后才使令牌失效
            # --- End token invalidation ---

            # Remove the old retry logic within search_flight, let the caller (monitor_route_date) handle retries.
            return None

    except Exception as e:
        # 简化代理错误日志
        logger.error(f"请求失败: {origin}->{destination} 日期: {date}")
        # Mark proxy failure if an exception occurred during the request phase
        # The proxy used here is the one determined for routing the request ('proxy' variable)
        if proxy:
             proxy_manager.update_proxy_result(proxy, False, 0, str(type(e).__name__))
        elif use_direct:
             # Optionally track direct connection failures if needed
             pass

        # Do NOT invalidate the token on general exceptions unless certain it's token-related.
        return None

# 解析航班数据 - 修改返回逻辑
def parse_flight_data(response_data, flight_numbers, required_miles, date=None, origin=None, destination=None):
    """
    解析航班数据。
    成功解析但无符合航班 -> 返回 []
    解析过程中发生异常 -> 返回 None
    """
    if not response_data:
        logger.error("没有响应数据可解析")
        return None # 返回 None 表示解析失败或无数据

    try:
        # 记录完整的响应数据结构，便于调试
        # logger.debug(f"响应数据结构: {json.dumps(response_data, indent=2)[:1000]}...") # Debug 时可以取消注释

        # 从响应中提取原始数据
        if response_data.get('data') is None:
            logger.error("响应数据结构不完整: data 字段为 None")
            logger.debug(f"完整响应: {json.dumps(response_data, indent=2)}")
            return None # 返回 None 表示解析失败

        booking_air_search = response_data['data'].get('bookingAirSearch')
        if booking_air_search is None:
            logger.error("响应数据结构不完整: bookingAirSearch 为 None")
            logger.debug(f"data字段内容: {json.dumps(response_data['data'], indent=2)}")
            if 'errors' in response_data:
                logger.error(f"API返回错误: {json.dumps(response_data['errors'], indent=2)}")
            return None # 返回 None 表示解析失败或API错误

        original_response = booking_air_search.get('originalResponse')
        if original_response is None:
            # 检查是否有 GraphQL 级别的错误信息
            if 'errors' in response_data:
                 logger.error(f"originalResponse 为 None，但存在 GraphQL 错误: {json.dumps(response_data['errors'], indent=2)}")
            else:
                 logger.error("响应数据结构不完整: originalResponse 字段为 None 或不存在")
                 logger.debug(f"bookingAirSearch字段内容: {json.dumps(booking_air_search, indent=2)}")
            return None # 返回 None 表示解析失败或无数据

        # 检查originalResponse是否是字符串，如果是则解析为JSON
        if isinstance(original_response, str):
            try:
                original_response = json.loads(original_response)
                # logger.debug("成功将originalResponse从字符串解析为JSON") # Debug 时可以取消注释
            except json.JSONDecodeError as e:
                logger.error(f"解析originalResponse字符串失败: {e}")
                logger.debug(f"原始字符串: {original_response[:1000]}...")
                return None # 返回 None 表示解析失败

        # logger.debug(f"原始响应类型: {type(original_response)}") # Debug 时可以取消注释

        # 提取航班信息
        available_flights = []

        # 标准化航班号列表（移除空格）
        normalized_flight_numbers = []
        if flight_numbers:
            for flight_num in flight_numbers:
                normalized_flight_numbers.append(flight_num.replace(" ", ""))

        # 检查 unbundledOffers 字段
        # 使用 .get() 来安全地访问嵌套字典
        unbundled_offers_list = original_response.get('unbundledOffers')
        if unbundled_offers_list and isinstance(unbundled_offers_list, list) and len(unbundled_offers_list) > 0:
            # 假设我们只关心第一个 unbundledOffers 数组
            offers = unbundled_offers_list[0]
            if isinstance(offers, list): # 确保 offers 是一个列表
                 logger.info(f"找到 {len(offers)} 个航班优惠 (offers)")

                 for offer in offers:
                    if not isinstance(offer, dict): # 跳过非字典类型的 offer
                        logger.warning(f"Skipping invalid offer item (not a dict): {type(offer)}")
                        continue

                    cabin_class = offer.get('cabinClass', '')
                    # logger.info(f"Offer 舱位: {cabin_class}") # Debug 时取消注释

                    if cabin_class and cabin_class.upper() in ['BUSINESS', 'FIRST']:
                        # logger.success(f"找到高级舱位: {cabin_class}") # Debug 时取消注释

                        miles = offer.get('requiredMinThreshold', {}).get('amount', 0)
                        # logger.info(f"里程: {miles}, 要求里程: {required_miles}") # Debug 时取消注释

                        flight_match = False
                        segment_flight_numbers = []
                        itinerary_part = offer.get('itineraryPart')

                        if itinerary_part and isinstance(itinerary_part, list) and len(itinerary_part) > 0:
                             # 假设只关心第一个 itineraryPart
                             first_itinerary = itinerary_part[0]
                             if isinstance(first_itinerary, dict):
                                 segments = first_itinerary.get('segments')
                                 if segments and isinstance(segments, list) and len(segments) > 0:
                                     for segment in segments:
                                         if isinstance(segment, dict):
                                             flight_info_seg = segment.get('flight')
                                             if isinstance(flight_info_seg, dict):
                                                  flight_number = flight_info_seg.get('flightNumber', '')
                                                  airline_code = flight_info_seg.get('airlineCode', '')
                                                  if flight_number and airline_code:
                                                       full_flight_number = f"{airline_code}{flight_number}"
                                                       segment_flight_numbers.append(full_flight_number)
                                                       # logger.info(f"检查航班: {full_flight_number}") # Debug 时取消注释

                                                       if not normalized_flight_numbers:
                                                            flight_match = True
                                                       else:
                                                            normalized_flight_num = full_flight_number.replace(" ", "")
                                                            if normalized_flight_num in normalized_flight_numbers:
                                                                 flight_match = True
                                                                 # logger.info(f"航班号匹配: {full_flight_number}") # Debug 时取消注释
                                                                 break # 找到一个匹配即可

                        if flight_match and (required_miles == 0 or miles == required_miles):
                             departure_time = ""
                             arrival_time = ""
                             booking_code = ""
                             seats = 0

                             if segments and len(segments) > 0: # Use segments variable from above
                                 first_segment = segments[0]
                                 last_segment = segments[-1]
                                 if isinstance(first_segment, dict):
                                     departure_time = first_segment.get('departure', '')
                                     booking_code = first_segment.get('bookingClass', '') # 获取第一个 segment 的 booking class
                                 if isinstance(last_segment, dict):
                                     arrival_time = last_segment.get('arrival', '')

                             seats_remaining = offer.get('seatsRemaining')
                             if isinstance(seats_remaining, dict):
                                 seats = seats_remaining.get('count', 0)

                             flight_info = {
                                 'flight_numbers': segment_flight_numbers,
                                 'departure': departure_time,
                                 'arrival': arrival_time,
                                 'cabin_class': cabin_class,
                                 'booking_code': booking_code, # 使用获取到的 booking code
                                 'miles': miles,
                                 'seats': seats,
                                 'max_seats': seats
                             }

                             flight_nums_str = ','.join(segment_flight_numbers)
                             logger.success(f"找到航班: {date} {origin}-{destination} {flight_nums_str} | 舱位:{cabin_class} | 里程:{miles} | 座位:{seats}")
                             available_flights.append(flight_info)
            else:
                 logger.warning(f"Expected 'offers' to be a list, but got {type(offers)}")
        else:
            logger.warning("响应中没有 unbundledOffers 字段、为空或格式不正确")
            # Consider returning None here if this field is absolutely essential
            # return None

        # 如果循环结束没有找到符合条件的航班，返回空列表
        return available_flights

    except Exception as e:
        logger.error(f"解析航班数据时发生严重异常: {e}")
        logger.error(traceback.format_exc())
        # 记录导致问题的响应数据片段，帮助调试
        response_snippet = str(response_data)[:1500] # 增加片段长度
        logger.error(f"异常发生时的响应数据片段: {response_snippet}...")
        return None # 返回 None 表示解析过程中出现异常

# --- 修改后的数据库写入逻辑 --- (Redis 重构)
def save_to_database(date, origin, destination, flight_info):
    """使用新的 Redis 结构存储最新状态并记录历史。"""
    now = datetime.datetime.now()
    now_ts_str = now.strftime('%Y-%m-%d %H:%M:%S')
    now_timestamp_float = now.timestamp() # 用于 ZSET score

    flight_nums_str = ','.join(flight_info['flight_numbers'])
    # 保持 payload 结构，用于推送和历史记录
    payload = {
        'date': date,
        'origin': origin,
        'destination': destination,
        'flight_numbers': flight_nums_str,
        'cabin_class': flight_info['cabin_class'],
        'current_seats': flight_info['seats'],
        'last_updated_timestamp': now_ts_str,
        # --- 添加 max_seats 到 payload ---
        'max_seats': flight_info['seats'] # 初始化 max_seats 为当前座位数
    }
    payload_json = json.dumps(payload)

    # --- 新增: 写入 Redis ZSET 和分片 Hash ---
    flight_key = f"{date}_{origin}_{destination}_{flight_nums_str}_{flight_info['cabin_class']}"
    region_key = None

    if origin in AMERICAS_CITIES or destination in AMERICAS_CITIES:
        region_key = 'flights:americas:latest'
    elif origin in CHINA_JAPAN_CITIES or destination in CHINA_JAPAN_CITIES:
        region_key = 'flights:china_japan:latest'
    else:
        logger.warning(f"未知的航线区域: {origin}-{destination}，跳过写入区域 Hash")
        # 可能需要一个默认或错误的区域键，或者干脆不写入？

    try:
        # 使用 pipeline 保证原子性 (至少针对这三个命令)
        pipe = redis_sync_client.pipeline()

        # 1. 更新活跃航班 ZSET (时间戳为 score)
        pipe.zadd('flights:active', {flight_key: now_timestamp_float})

        # 2. 更新区域 Hash
        if region_key:
            pipe.hset(region_key, flight_key, payload_json)

        # 3. 记录历史 List (保持不变)
        pipe.lpush('flights:history', payload_json)

        # 执行 pipeline
        pipe.execute()

        logger.debug(f"Redis 更新成功: Key={flight_key}, Region={region_key}, Score={now_timestamp_float}")

    except redis.exceptions.RedisError as e:
        logger.error(f"写入 Redis 失败 (key: {flight_key}): {e}")
        logger.error(traceback.format_exc())
        # 此处失败可能导致数据不一致，需要监控

    # --- END 新增 ---

    # 推送前端实时更新 (使用原始 payload)
    socketio_pub.emit('update_data', {'data': [payload]}, namespace='/')

    # 发布更新事件 (使用原始 payload)
    redis_sync_client.publish('flights_update', payload_json)

# --- 修改后的数据库更新逻辑 (移除 route_type) ---
def update_search_timestamp(date, origin, destination):
    """使用 Redis 发布无航班事件。"""
    # 使用本地时间生成时间戳
    now_ts = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    payload = {
        'date': date,
        'origin': origin,
        'destination': destination,
        'status': 'no_flights',
        'timestamp': now_ts
    }
    redis_sync_client.publish('flights_no_results', json.dumps(payload))

# 监控单个日期的航线 (移除 route_type) - 更新处理 parse_flight_data 返回值
# Modify monitor_route_date to accept token_fetcher
def monitor_route_date(origin, destination, flight_numbers, required_miles, search_date, proxy_manager, token_fetcher, max_retries=3):
    """监控单个日期的航线，返回是否成功完成 API 调用并成功解析（即使无航班）"""
    route_str = f"{origin}-{destination}"
    logger.debug(f"开始监控任务: 路由={route_str}, 日期={search_date}")
    retries = 0
    task_successful = False # 标记任务是否最终成功（API 调用成功 + 解析成功）
    parsing_failed = False # 标记解析是否失败

    try:
        while retries <= max_retries:
            response_data = None # 重置 response_data
            parsing_failed = False # 重置解析失败标记
            try:
                # Pass token_fetcher to search_flight
                response_data = search_flight(origin, destination, search_date, proxy_manager, token_fetcher)

                if response_data:
                    # API 调用成功，尝试解析
                    available_flights = parse_flight_data(response_data, flight_numbers, required_miles, search_date, origin, destination)

                    if available_flights is None:
                        # 解析失败 (parse_flight_data 返回 None)
                        logger.error(f"解析航班数据失败: {route_str}, 日期: {search_date}. 这可能是一个临时的 API 结构问题或代码 bug。")
                        parsing_failed = True
                        # 这里不设置 task_successful = True，因为解析失败意味着我们可能错过了数据
                        # 让循环继续，进入重试逻辑（如果还有重试次数）

                    elif available_flights:
                        # 解析成功且找到航班
                        for flight_info in available_flights:
                            save_to_database(search_date, origin, destination, flight_info)
                        task_successful = True # 任务成功
                        break # 成功则跳出重试循环

                    else:
                        # 解析成功但未找到符合条件的航班
                        update_search_timestamp(search_date, origin, destination)
                        logger.warning(f"未找到符合条件的航班 (解析成功): {route_str}, 日期: {search_date}")
                        task_successful = True # API 调用和解析都成功，即使没航班也算成功完成任务
                        break # 成功则跳出重试循环

                else:
                    # search_flight 返回 None (API 调用失败或令牌获取失败)
                    logger.error(f"搜索航班失败 (search_flight returned None): {route_str}, 日期: {search_date}, 尝试 {retries+1}/{max_retries+1}")
                    # task_successful 保持 False

            except Exception as e:
                # 捕获 monitor_route_date 内部的其他异常 (理论上 search_flight 和 parse_flight_data 内部异常已处理)
                logger.error(f"监控子任务内部发生未预料的异常: {route_str}, 日期: {search_date}, 错误: {e}, 尝试 {retries+1}/{max_retries+1}")
                logger.error(traceback.format_exc())
                # task_successful 保持 False

            # --- 重试逻辑 ---
            # 仅在 API 调用失败 或 解析失败 时重试
            if not task_successful and retries < max_retries:
                 retry_reason = "解析失败" if parsing_failed else "API调用失败"
                 logger.warning(f"任务因 '{retry_reason}' 未成功，准备重试 ({retries+1}/{max_retries})...")
                 retries += 1
                 time.sleep(random.uniform(1.5, 4)) # 稍微增加重试间隔
            elif task_successful:
                 break # 如果任务成功，跳出循环
            else: # 重试次数耗尽 或 任务已标记成功
                 retries += 1 # 确保循环能结束

        if not task_successful:
             logger.error(f"任务最终失败 (耗尽重试次数或从未成功): {route_str}, 日期: {search_date}")

    except Exception as outer_e:
         # 捕获 monitor_route_date 循环外部的异常 (极少情况)
         logger.critical(f"监控任务函数 'monitor_route_date' 发生严重顶层异常: {route_str}, 日期: {search_date}, 错误: {outer_e}")
         logger.critical(traceback.format_exc())
         task_successful = False # 确保返回失败状态

    finally:
        status_str = "成功" if task_successful else "失败"
        logger.debug(f"结束监控任务: 路由={route_str}, 日期={search_date}, 最终状态={status_str}")

    return task_successful # 返回任务是否最终成功（API 调用成功 + 解析成功）

# 获取需要监控的日期列表
def get_monitoring_dates():
    """获取需要监控的日期列表，根据北京时间调整起始日期。"""
    # 获取当前日期和时间
    now_local = datetime.datetime.now() # 用于计算日期
    # 获取当前 UTC 时间
    now_utc = datetime.datetime.now(datetime.timezone.utc)
    # 计算北京时间 (UTC+8)
    beijing_tz = datetime.timezone(datetime.timedelta(hours=8))
    now_beijing = now_utc.astimezone(beijing_tz)

    # 检查北京时间是否大于等于凌晨3点
    is_after_3am_beijing = now_beijing.hour >= 3

    # 根据北京时间决定起始天数偏移量
    start_day_offset = 2 if is_after_3am_beijing else 1

    if is_after_3am_beijing:
        logger.debug(f"当前北京时间 {now_beijing.strftime('%H:%M:%S')} >= 03:00，将从第 {start_day_offset} 天开始监控。")
    else:
        logger.debug(f"当前北京时间 {now_beijing.strftime('%H:%M:%S')} < 03:00，将从第 {start_day_offset} 天开始监控。")

    # 生成日期范围 (结束日期仍为+10天)
    monitoring_dates = []
    for day_offset in range(start_day_offset, 11):
        search_date = (now_local + datetime.timedelta(days=day_offset)).strftime('%Y-%m-%d')
        monitoring_dates.append(search_date)

    # 记录详细的日期信息
    date_str = ", ".join(monitoring_dates)
    logger.debug(f"最终确定的监控日期: {date_str}")

    return monitoring_dates

# 航线配置
def get_route_configs():
    """获取需要监控的航线配置 (移除 route_type)"""
    # 移除 route_type 相关的变量定义 (如果之前有)
    # china_routes = [...]
    # americas_routes_major = [...]
    # americas_routes_other = [...]

    route_configs = [
        {'origin':'HND','destination':'JFK','flight_numbers':['NH160','NH110'],'required_miles':0,'interval_seconds':3},
        {'origin':'JFK','destination':'HND','flight_numbers':['NH159','NH109'],'required_miles':0,'interval_seconds':3},
        {'origin':'HND','destination':'LAX','flight_numbers':['NH126','NH106'],'required_miles':0,'interval_seconds':6},
        {'origin':'NRT','destination':'LAX','flight_numbers':['NH6'],'required_miles':0,'interval_seconds':6},
        {'origin':'LAX','destination':'HND','flight_numbers':['NH125','NH105','NH1905'],'required_miles':0,'interval_seconds':6},
        {'origin':'LAX','destination':'NRT','flight_numbers':['NH5'],'required_miles':0,'interval_seconds':6},
        {'origin':'HND','destination':'ORD','flight_numbers':['NH112'],'required_miles':0,'interval_seconds':6},
        {'origin':'NRT','destination':'ORD','flight_numbers':['NH12', 'NH012'],'required_miles':0,'interval_seconds':10},
        {'origin':'HND','destination':'SFO','flight_numbers':['NH108'],'required_miles':0,'interval_seconds':10},
        {'origin':'NRT','destination':'SFO','flight_numbers':['NH8'],'required_miles':0,'interval_seconds':10},
        {'origin':'HND','destination':'IAD','flight_numbers':['NH102'],'required_miles':0,'interval_seconds':6},
        {'origin':'HND','destination':'IAH','flight_numbers':['NH114'],'required_miles':0,'interval_seconds':6},
        {'origin':'HND','destination':'SEA','flight_numbers':['NH118'],'required_miles':0,'interval_seconds':30},
        {'origin':'HND','destination':'YVR','flight_numbers':['NH116'],'required_miles':0,'interval_seconds':30},
        {'origin':'ORD','destination':'HND','flight_numbers':['NH111'],'required_miles':0,'interval_seconds':6},
        {'origin':'ORD','destination':'NRT','flight_numbers':['NH11', 'NH011'],'required_miles':0,'interval_seconds':10},
        {'origin':'SFO','destination':'HND','flight_numbers':['NH107'],'required_miles':0,'interval_seconds':10},
        {'origin':'SFO','destination':'NRT','flight_numbers':['NH7'],'required_miles':0,'interval_seconds':10},
        {'origin':'IAD','destination':'HND','flight_numbers':['NH101'],'required_miles':0,'interval_seconds':6},
        {'origin':'IAH','destination':'HND','flight_numbers':['NH113'],'required_miles':0,'interval_seconds':6},
        {'origin':'SEA','destination':'HND','flight_numbers':['NH117'],'required_miles':0,'interval_seconds':30},
        {'origin':'YVR','destination':'HND','flight_numbers':['NH115'],'required_miles':0,'interval_seconds':30},
        {'origin':'HND','destination':'PEK','flight_numbers':['NH963','NH961'],'required_miles':0,'interval_seconds':180},
        {'origin':'HND','destination':'PVG','flight_numbers':['NH967','NH971'],'required_miles':0,'interval_seconds':180},
        {'origin':'HND','destination':'SHA','flight_numbers':['NH969'],'required_miles':0,'interval_seconds':180},
        {'origin':'HND','destination':'HKG','flight_numbers':['NH859'],'required_miles':0,'interval_seconds':180},
        {'origin':'HND','destination':'TAO','flight_numbers':['NH949'],'required_miles':0,'interval_seconds':180},
        {'origin':'HND','destination':'SZX','flight_numbers':['NH965'],'required_miles':0,'interval_seconds':180},
        {'origin':'HND','destination':'CAN','flight_numbers':['NH923'],'required_miles':0,'interval_seconds':180},
        {'origin':'PEK','destination':'HND','flight_numbers':['NH962','NH964'],'required_miles':0,'interval_seconds':180},
        {'origin':'PVG','destination':'HND','flight_numbers':['NH968','NH972'],'required_miles':0,'interval_seconds':180},
        {'origin':'SHA','destination':'HND','flight_numbers':['NH970'],'required_miles':0,'interval_seconds':180},
        {'origin':'HKG','destination':'HND','flight_numbers':['NH860'],'required_miles':0,'interval_seconds':180},
        {'origin':'TAO','destination':'HND','flight_numbers':['NH950'],'required_miles':0,'interval_seconds':180},
        {'origin':'SZX','destination':'HND','flight_numbers':['NH966'],'required_miles':0,'interval_seconds':180},
        {'origin':'CAN','destination':'HND','flight_numbers':['NH924'],'required_miles':0,'interval_seconds':180},
    ]

    return route_configs

# 显示代理统计信息
def display_proxy_stats(proxy_manager):
    """显示所有代理的统计信息"""
    stats = proxy_manager.get_stats()
    proxy_stats = proxy_manager.get_proxy_stats()

    if not proxy_stats:
        logger.info("没有代理统计信息")
        return

    logger.success("代理池统计信息:")
    logger.success("=" * 80)

    # 显示全局统计信息
    logger.success(f"总请求数: {stats['total_requests']}")
    logger.success(f"成功请求数: {stats['successful_requests']}")
    logger.success(f"失败请求数: {stats['failed_requests']}")
    logger.success(f"直接请求数: {stats['direct_requests']}")
    logger.success(f"代理请求数: {stats['proxy_requests']}")
    logger.success(f"代理数量: {stats['proxy_count']}")
    logger.success(f"可用代理数量: {stats['available_proxy_count']}")
    logger.success(f"禁用代理数量: {stats['disabled_proxy_count']}")

    if stats['total_requests'] > 0:
        logger.success(f"成功率: {stats['success_rate']:.2f}")
        logger.success(f"直接请求比例: {stats['direct_request_ratio_actual']:.2f}")

    logger.success("-" * 80)
    logger.success(f"{'代理':<30} | {'总请求':<8} | {'成功':<8} | {'失败':<8} | {'成功率':<8} | {'健康分数':<8} | {'状态'}")
    logger.success("-" * 80)

    for proxy in proxy_stats:
        host = proxy['host']
        port = proxy['port']
        total = proxy['total_requests']
        success = proxy['successful_requests']
        failed = proxy['failed_requests']
        success_rate = proxy['success_rate'] * 100 if total > 0 else 0
        health_score = proxy['health_score']
        disabled = "禁用" if proxy['disabled'] else "可用"

        # 只有错误率100%（成功率0%）且请求总数大于10的代理才用红色显示
        if total >= 10 and success_rate == 0.0:
            logger.error(f"{host}:{port:<30} | {total:<8} | {success:<8} | {failed:<8} | {success_rate:<8.2f}% | {health_score:<8} | {disabled} | 已标记为不可用")
        else:
            logger.success(f"{host}:{port:<30} | {total:<8} | {success:<8} | {failed:<8} | {success_rate:<8.2f}% | {health_score:<8} | {disabled}")

    logger.success("=" * 80)

# 路由任务类 (移除 route_type)
# Modify RouteTask to accept and store token_fetcher
class RouteTask:
    def __init__(self, config, date, proxy_manager, token_fetcher):
        self.config = config
        self.date = date
        self.proxy_manager = proxy_manager
        self.token_fetcher = token_fetcher
        self.origin = config['origin']
        self.destination = config['destination']
        self.flight_numbers = config['flight_numbers']
        self.required_miles = config['required_miles']

    def execute(self):
        route_id = f"{self.origin}-{self.destination}" # Get route_id for logging
        start_time = time.time()
        logger.debug(f"Task START :: Route={route_id}, Date={self.date}")
        try:
            # Pass token_fetcher to monitor_route_date
            result = monitor_route_date(
                self.origin,
                self.destination,
                self.flight_numbers,
                self.required_miles,
                self.date,
                self.proxy_manager,
                self.token_fetcher # Pass it down
            )
            end_time = time.time()
            duration = end_time - start_time
            logger.debug(f"Task END :: Route={route_id}, Date={self.date}, Success={result}, Duration={duration:.2f}s")
            return result
        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            logger.error(f"Task EXCEPTION :: Route={route_id}, Date={self.date}, Duration={duration:.2f}s, Error: {e}", exc_info=True)
            return False # Ensure task failure is reported if exception occurs here

# 路由调度器类
class RouteScheduler:
    """管理航线任务的调度"""
    def __init__(self, config, proxy_manager, token_fetcher, max_workers=None, scheduler_id=None):
        self.route_id = f"{config['origin']}-{config['destination']}" # Log route_id early
        logger.debug(f"RouteScheduler START: {self.route_id}, scheduler_id={scheduler_id}") # Log init start

        self.config = config
        self.proxy_manager = proxy_manager
        self.token_fetcher = token_fetcher
        self.scheduler_id = scheduler_id or 0
        self.interval_seconds = config['interval_seconds']
        self.failed_tasks = queue.Queue()
        self.running = True
        self.thread = threading.Thread(target=self._scheduler_loop, daemon=True)

        # Log before creating executor
        effective_max_workers = max_workers or 8
        logger.debug(f"RouteScheduler Creating Executor: {self.route_id}, max_workers={effective_max_workers}")
        self.executor = concurrent.futures.ThreadPoolExecutor(max_workers=effective_max_workers)
        logger.debug(f"RouteScheduler Executor Created: {self.route_id}") # Log after creating executor

        self.futures = []
        self.last_task_time = defaultdict(float)
        self.requests_per_minute = 60 / self.interval_seconds
        self.task_results = {}
        self.task_start_time = {}
        self.crazy_mode = config.get('crazy_mode', False)

        self.dates = get_monitoring_dates()
        self.date_count = len(self.dates)
        self.actual_interval = max(0.1, self.interval_seconds)

        self.initial_delays = {}
        if self.date_count > 0:
            delay_step = self.actual_interval / self.date_count
            for i, date in enumerate(self.dates):
                stagger_factor = (i + self.scheduler_id) % self.date_count
                self.initial_delays[date] = delay_step * stagger_factor

        logger.debug(f"路由 {self.route_id} 调度器初始化完成: 目标间隔={self.interval_seconds}秒, 实际间隔={self.actual_interval:.2f}秒, 日期数={self.date_count}, 每分钟请求={self.requests_per_minute:.1f}次")
        logger.debug(f"RouteScheduler END: {self.route_id}") # Log init end

    def start(self):
        """启动调度器"""
        self.thread.start()
        logger.debug(f"路由 {self.route_id} 调度器已启动")
        return self

    def stop(self):
        """停止调度器"""
        self.running = False
        # 等待所有任务完成
        for future in concurrent.futures.as_completed(self.futures):
            try:
                future.result()
            except Exception as e:
                logger.error(f"任务执行异常: {e}")

        self.executor.shutdown(wait=True)
        logger.info(f"路由 {self.route_id} 调度器已停止")

    def _handle_task_result(self, future, date):
        """处理任务执行结果"""
        try:
            result = future.result()
            self.task_results[date] = result

            if not result:
                # 如果任务失败，立即将其添加到失败任务队列
                logger.warning(f"路由 {self.route_id} 日期 {date} 任务失败，添加到失败任务队列")
                self.failed_tasks.put(date)
        except Exception as e:
            logger.error(f"路由 {self.route_id} 日期 {date} 任务异常: {e}")
            # 任务异常也视为失败，添加到失败任务队列
            self.failed_tasks.put(date)

    def _scheduler_loop(self):
        """调度器主循环"""
        try:
            # --- 修改开始: 移除基于 initial_delays 的首次执行时间设定 ---
            # # 初始化每个日期的下一次执行时间，使用计算好的初始延迟
            # next_execution_time = {}
            # now = time.time()
            # for date in self.dates:
            #     # 使用计算好的初始延迟，而不是随机延迟
            #     next_execution_time[date] = now + self.initial_delays[date]
            #     logger.debug(f"路由 {self.route_id} 日期 {date} 初始延迟: {self.initial_delays[date]:.2f}秒, 首次执行时间: {datetime.datetime.fromtimestamp(next_execution_time[date]).strftime('%H:%M:%S.%f')[:-3]}")

            # --- 新增: 设置所有日期任务在启动时立即以微小随机延迟执行首次任务 ---
            next_execution_time = {}
            now = time.time()
            for date in self.dates:
                # 设置一个 0 到 0.5 秒的随机延迟，确保立即启动
                immediate_start_delay = random.uniform(0, 0.5)
                next_execution_time[date] = now + immediate_start_delay
                logger.debug(f"路由 {self.route_id} 日期 {date} 强制首次启动延迟: {immediate_start_delay:.3f}秒, 首次执行时间: {datetime.datetime.fromtimestamp(next_execution_time[date]).strftime('%H:%M:%S.%f')[:-3]}")
            # --- 修改结束 ---

            while self.running:
                # 清理已完成的任务
                self.futures = [f for f in self.futures if not f.done()]

                # 获取当前时间
                now = time.time()

                # 优先处理失败任务队列中的任务 - 立即处理，不等待
                while not self.failed_tasks.empty() and len(self.futures) < self.executor._max_workers:
                    try:
                        date = self.failed_tasks.get_nowait()
                        logger.info(f"路由 {self.route_id} 重试失败任务: 日期={date}")

                        # 创建任务
                        task = RouteTask(self.config, date, self.proxy_manager, self.token_fetcher)

                        # 提交任务到线程池
                        future = self.executor.submit(task.execute)
                        future.add_done_callback(lambda f, d=date: self._handle_task_result(f, d))
                        self.futures.append(future)

                        # 更新最后执行时间
                        self.last_task_time[date] = now
                    except queue.Empty:
                        break

                # 按照下一次执行时间排序日期，优先执行最早需要执行的任务
                sorted_dates = sorted(self.dates, key=lambda d: next_execution_time.get(d, float('inf')))

                # 检查每个日期是否需要执行任务 - 更积极地提交任务
                for date in sorted_dates:
                    # 如果到达或超过了下一次执行时间
                    if now >= next_execution_time[date]:
                        # 检查当前活动任务数是否未达到最大值
                        if len(self.futures) < self.executor._max_workers:
                            # --- 添加日志记录任务提交 ---
                            logger.debug(f"路由 {self.route_id} 提交任务: 日期={date}")
                            # ---------------------------
                            task = RouteTask(self.config, date, self.proxy_manager, self.token_fetcher)
                            future = self.executor.submit(task.execute)
                            future.add_done_callback(lambda f, d=date: self._handle_task_result(f, d))
                            self.futures.append(future)
                            self.last_task_time[date] = now
                            next_execution_time[date] += self.actual_interval
                        else:
                            # 跳过任务（线程池已满），不再记录日志
                            next_execution_time[date] += self.actual_interval * 0.1 # 增加一个较小的时间偏移
                    # else: # 时间未到，无需操作
                # 计算下一个最早需要执行的任务时间
                next_task_time = min(next_execution_time.values(), default=now + 0.1) # 默认等待0.1秒

                # 计算需要休眠的时间，至少休眠0.01秒，最多0.1秒
                sleep_duration = max(0.01, min(0.1, next_task_time - now))

                # 休眠一段时间，避免CPU空转，并使调度更平稳
                time.sleep(sleep_duration)

        except Exception as e:
            logger.error(f"路由 {self.route_id} 调度器异常: {e}")
            logger.error(traceback.format_exc())

# 全局调度器类
class GlobalScheduler:
    """管理所有航线的调度器"""
    def __init__(self, proxy_manager, token_fetcher, max_workers=None):
        logger.debug("GlobalScheduler START: Initializing...") # Log init start
        self.proxy_manager = proxy_manager
        self.token_fetcher = token_fetcher
        self.schedulers = {}
        self.max_workers = max_workers or min(512, os.cpu_count() * 512)
        logger.debug(f"全局调度器初始化: 最大工作线程数(理论值)={self.max_workers}")
        logger.debug("GlobalScheduler END: Initialization complete.") # Log init end

    def add_route(self, config):
        route_id = f"{config['origin']}-{config['destination']}"
        logger.debug(f"GlobalScheduler Add Route START: {route_id}") # Log add_route start

        origin = config['origin']
        destination = config['destination']

        # --- Concurrency allocation logic (remains the same) ---
        high_priority_cities = ['JFK', 'LAX']
        medium_priority_cities = ['ORD', 'SFO', 'IAD', 'IAH', 'YVR', 'SEA']
        low_priority_cities = ['PEK', 'PVG', 'SHA', 'HKG', 'TAO', 'SZX', 'CAN', 'NRT']
        max_route_workers = 2
        priority_level = "未知/默认"
        if origin in high_priority_cities or destination in high_priority_cities:
            max_route_workers = 40
            priority_level = "高"
        elif origin in medium_priority_cities or destination in medium_priority_cities:
            max_route_workers = 2
            priority_level = "中"
        elif origin in low_priority_cities or destination in low_priority_cities:
            max_route_workers = 1
            priority_level = "低"
        else:
            logger.warning(f"航线 {route_id} 未匹配到优先级，使用默认并发数 {max_route_workers}")
        logger.debug(f"航线 {route_id} ({priority_level}优先级) 计算并发任务数={max_route_workers}")
        # --- End concurrency allocation ---

        scheduler_id = len(self.schedulers)
        # Log before creating RouteScheduler
        logger.debug(f"GlobalScheduler Creating RouteScheduler: {route_id}, scheduler_id={scheduler_id}")
        scheduler = RouteScheduler(
            config,
            self.proxy_manager,
            self.token_fetcher,
            max_workers=max_route_workers,
            scheduler_id=scheduler_id
        )
        # Log after creating RouteScheduler
        logger.debug(f"GlobalScheduler RouteScheduler Created: {route_id}")

        self.schedulers[route_id] = scheduler
        logger.debug(f"已添加航线调度器: {route_id}, 间隔={config['interval_seconds']}秒, 实际并发={max_route_workers}, 调度器ID={scheduler_id}")
        logger.debug(f"GlobalScheduler Add Route END: {route_id}") # Log add_route end
        return scheduler

    def start_all(self):
        """启动所有调度器"""
        for route_id, scheduler in self.schedulers.items():
            scheduler.start()
        logger.success(f"已启动所有航线调度器: {len(self.schedulers)}个")

    def stop_all(self):
        """停止所有调度器"""
        for route_id, scheduler in self.schedulers.items():
            scheduler.stop()
        logger.info("已停止所有航线调度器")

    def get_stats(self):
        """获取调度器统计信息"""
        stats = {}
        for route_id, scheduler in self.schedulers.items():
            stats[route_id] = {
                "requests_per_minute": scheduler.requests_per_minute,
                "active_tasks": len(scheduler.futures),
                "dates": len(scheduler.dates)
            }
        return stats

# Convert the main execution block to async
async def async_main():
    parser = argparse.ArgumentParser(description='航班监控程序')
    parser.parse_args()

    setup_logging()
    logger.debug("--- 检查点: setup_logging() 完成 ---")
    logger.success("程序启动 (异步模式)")

    # 在Docker环境中不需要初始化本地数据库
    # Initialize Proxy Manager (Sync)
    logger.debug("--- 检查点: 即将初始化代理管理器 ---")
    proxy_manager = init_proxy_manager()
    if not proxy_manager:
        logger.warning("代理管理器初始化失败，继续运行")

    # 初始化异步 Redis 客户端
    logger.debug("--- 检查点: 即将调用 initialize_aredis() ---")
    redis_client = await initialize_aredis() # 调用新的初始化函数
    if not redis_client:
        logger.critical("redis.asyncio 客户端初始化失败，程序退出")
        return
    logger.debug("--- 检查点: redis.asyncio 客户端初始化完成 ---")

    # 获取当前事件循环
    try:
        loop = asyncio.get_running_loop()
    except RuntimeError:
        logger.critical("无法获取正在运行的事件循环。")
        if redis_client: await redis_client.aclose() # 使用 aclose 关闭客户端
        return

    # 初始化 AsyncTokenFetcher, 传入 aredis client
    logger.debug("--- 检查点: 即将创建 AsyncTokenFetcher ---")
    try:
        token_fetcher = AsyncTokenFetcher(redis_client, proxy_manager, logger, loop)
        logger.debug("--- 检查点: AsyncTokenFetcher 创建成功 ---")
    except Exception as e:
        logger.critical(f"创建 AsyncTokenFetcher 失败: {e}")
        if redis_client: await redis_client.aclose()
        return

    # ... (启动后台令牌生成器提示保持不变)
    logger.debug("--- 检查点: 确保 reese84/main.py 中的令牌生成器正在独立运行... ---")

    # ... (创建 GlobalScheduler, 加载路由配置, 添加路由, 启动调度器保持不变, token_fetcher 会被传递下去)
    logger.debug("--- 检查点: 即将创建 GlobalScheduler ---")
    max_workers_global_config = os.cpu_count() * 32 # Keep original config value for info
    logger.debug(f"设置全局调度器最大工作线程数(理论配置): {max_workers_global_config}")
    scheduler = GlobalScheduler(proxy_manager, token_fetcher, max_workers=max_workers_global_config)
    logger.debug("--- 检查点: GlobalScheduler 实例创建完成 ---")

    logger.debug("--- 检查点: 即将调用 get_route_configs() ---")
    route_configs = get_route_configs()
    if not route_configs:
        logger.critical("无法加载路由配置，程序退出。")
        if redis_client: await redis_client.aclose()
        return
    logger.debug(f"--- 检查点: 加载了 {len(route_configs)} 条路由配置 ---")

    logger.debug("--- 检查点: 即将进入添加航线循环 ---")
    for i, config in enumerate(route_configs):
        route_id_main = f"{config.get('origin', 'N/A')}-{config.get('destination', 'N/A')}"
        logger.debug(f"--- 检查点: 循环 {i}, 准备添加路由: {route_id_main} ---")
        try:
            scheduler.add_route(config)
            logger.debug(f"--- 检查点: 路由已添加: {route_id_main} ---")
        except Exception as add_route_err:
            logger.critical(f"严重错误 (async_main loop {i}): 添加路由 {route_id_main} 时出错: {add_route_err}", exc_info=True)
            continue
    logger.debug("--- 检查点: 所有航线添加完成 ---")

    logger.debug("--- 检查点: 即将调用 scheduler.start_all() ---")
    scheduler.start_all()
    logger.success("所有航线调度器已启动，开始监控")

    logger.debug("--- 检查点: 即将进入主监控循环 (异步模式) ---")
    monitoring_task = None
    try:
        last_stats_time = time.time()
        stats_interval = 20 # 秒

        # --- Add Redis Cleanup Task ---
        cleanup_interval_seconds = 6 * 3600 # Cleanup every 6 hours
        async def redis_cleanup_loop():
            logger.debug(f"[Redis Cleanup] Scheduler started. Will run every {cleanup_interval_seconds / 3600} hours.")
            # Run once immediately at startup
            try:
                logger.debug("[Redis Cleanup] Triggering initial cleanup task...")
                result = await loop.run_in_executor(None, cleanup_old_redis_data_sync, redis_sync_client)
                if result == -1:
                    logger.error("[Redis Cleanup] Initial cleanup task execution encountered an error.")
                else:
                    logger.debug(f"[Redis Cleanup] Initial cleanup task completed, {result} keys deleted.")
            except Exception as e:
                logger.error(f"[Redis Cleanup] Error in initial cleanup execution: {e}")
                logger.error(traceback.format_exc())

            while True:
                # Wait for the next interval first
                await asyncio.sleep(cleanup_interval_seconds)
                try:
                    # Run the synchronous cleanup function in the default executor
                    logger.debug("[Redis Cleanup] Triggering periodic cleanup task...")
                    # Pass the synchronous redis client instance
                    result = await loop.run_in_executor(None, cleanup_old_redis_data_sync, redis_sync_client)
                    if result == -1:
                        logger.error("[Redis Cleanup] Periodic cleanup task execution encountered an error.")
                    else:
                        logger.debug(f"[Redis Cleanup] Periodic cleanup task completed, {result} keys deleted.")
                except Exception as e:
                    logger.error(f"[Redis Cleanup] Error in cleanup loop scheduler: {e}")
                    logger.error(traceback.format_exc())

        cleanup_task = asyncio.create_task(redis_cleanup_loop())
        # -----------------------------

        async def monitor_loop():
            # nonlocal last_stats_time # 移除这行
            # 声明修改外部作用域变量的意图 - Python 默认允许修改直接外层作用域变量
            nonlocal last_stats_time
            while True:
                await asyncio.sleep(1) # 每秒检查一次
                now = time.time()
                if now - last_stats_time >= stats_interval:
                    try:
                        display_proxy_stats(proxy_manager)
                        stats = scheduler.get_stats()
                        logger.success("调度器统计信息:")
                        logger.success("=" * 80)
                        logger.success(f"{'航线':<15} | {'每分钟请求':<10} | {'活动任务':<10} | {'监控日期数':<10}")
                        logger.success("-" * 80)
                        for route_id, route_stats in stats.items():
                            logger.success(f"{route_id:<15} | {route_stats['requests_per_minute']:<10.1f} | {route_stats['active_tasks']:<10} | {route_stats['dates']:<10}")
                        logger.success("=" * 80)
                        last_stats_time = now # 直接重新绑定外层变量
                    except Exception as stats_ex:
                         logger.error(f"显示统计信息时出错: {stats_ex}")

        monitoring_task = asyncio.create_task(monitor_loop())

        # Combine monitoring and cleanup loops using asyncio.gather
        # Ensure both tasks are awaited so the program doesn't exit prematurely
        await asyncio.gather(
             monitoring_task,
             cleanup_task # Run cleanup concurrently
        )

    # ... (异常处理和取消逻辑保持不变)
    except asyncio.CancelledError: # ...
        logger.info("主监控任务被取消...") # <-- 添加缩进和逻辑
        if monitoring_task: monitoring_task.cancel()
        if cleanup_task: cleanup_task.cancel() # Cancel cleanup task too
    except KeyboardInterrupt: # ...
        logger.info("接收到退出信号，开始关闭...") # <-- 添加缩进和逻辑
        if monitoring_task:
            monitoring_task.cancel()
            try:
                await monitoring_task # 允许取消操作传播
            except asyncio.CancelledError:
                 logger.info("监控任务已成功取消。")
        if cleanup_task:
            cleanup_task.cancel() # Cancel cleanup task too
        try:
             # Wait for tasks to finish cancelling
             await asyncio.gather(monitoring_task, cleanup_task, return_exceptions=True)
        except asyncio.CancelledError:
             logger.info("监控和清理任务已成功取消。")
        except Exception as gather_ex:
             logger.warning(f"Exception during task cancellation gathering: {gather_ex}")

    except Exception as e: # ...
        logger.error(f"主异步循环异常: {e}") # <-- 添加缩进和逻辑
        logger.error(traceback.format_exc())
        if monitoring_task:
            monitoring_task.cancel() # 确保出错时停止监控
        if cleanup_task:
            cleanup_task.cancel() # Cancel cleanup task too
    finally:
        # <-- 添加缩进
        logger.info("开始关闭调度器...")
        scheduler.stop_all() # 确保调度器线程被停止
        logger.info("调度器已停止。")
        # 更新 Redis 关闭逻辑
        if redis_client:
            logger.info("关闭 redis.asyncio 客户端连接...")
            await redis_client.aclose() # 使用 aclose() 关闭客户端
            # 如果 from_url 创建了连接池，可能需要关闭连接池
            # await redis_client.connection_pool.disconnect() # 检查你的 redis-py 版本文档
            logger.info("redis.asyncio 客户端连接已关闭。")
        logger.info("程序退出。")

# Replace the old main execution block
if __name__ == "__main__":
    # ... (启动提示保持不变)
    logger.info("确保 reese84/main.py 中的令牌生成器正在独立运行...")
    try:
        asyncio.run(async_main())
    except Exception as main_err:
        # ... (错误处理保持不变)
        sys.exit(1)

# --- Add cleanup function ---
def cleanup_old_redis_data_sync(redis_client_sync):
    """清理过期的 Redis 数据 (ZSET 和 Hashes)。"""
    deleted_count_zset = 0
    deleted_count_hash = 0

    try:
        # --- 1. 清理 Active ZSET ---
        # 清理 score (时间戳) 早于 HIDE_HOURS + HIDE_MINUTES 之前的记录
        # 保留稍微长一点的时间以防万一，例如 2.5 小时？或者直接按天？
        # 这里改为清理一天前的数据，避免影响 Disappeared 页面
        one_day_ago_dt = datetime.datetime.now() - datetime.timedelta(days=1)
        one_day_ago_ts = one_day_ago_dt.timestamp()
        logger.debug(f"[Redis Cleanup] Starting ZSET cleanup for scores before {one_day_ago_dt.strftime('%Y-%m-%d %H:%M:%S')} ({one_day_ago_ts})...")
        deleted_count_zset = redis_client_sync.zremrangebyscore('flights:active', '-inf', f"({one_day_ago_ts}") # 使用 ( exclude a day ago timestamp
        logger.debug(f"[Redis Cleanup] ZSET cleanup finished. Removed {deleted_count_zset} old entries.")

        # --- 2. 清理区域 Hashes (更慢，但必要) ---
        logger.debug("[Redis Cleanup] Starting regional Hash cleanup...")
        hash_keys_to_scan = ['flights:americas:latest', 'flights:china_japan:latest'] # 添加其他区域如果需要
        # 清理 Hash 中日期字段 (YYYY-MM-DD) 早于今天的记录
        today_str = datetime.datetime.now().strftime('%Y-%m-%d')

        for hash_key in hash_keys_to_scan:
            logger.debug(f"[Redis Cleanup] Scanning Hash: {hash_key} for dates before {today_str}...")
            hash_deleted_for_region = 0
            cursor = '0'
            while True:
                try:
                    cursor, items = redis_client_sync.hscan(hash_key, cursor=cursor, count=100)
                    keys_to_delete_in_batch = []
                    for key, value_str in items.items():
                        try:
                            if not value_str:
                                logger.warning(f"[Redis Cleanup] Empty value for key '{key}' in {hash_key}. Adding to delete list.")
                                keys_to_delete_in_batch.append(key)
                                continue
                            data = json.loads(value_str)
                            flight_date_str = data.get('date')
                            if flight_date_str and flight_date_str < today_str:
                                keys_to_delete_in_batch.append(key)
                        except json.JSONDecodeError:
                             logger.warning(f"[Redis Cleanup] Failed to decode JSON for key '{key}' in {hash_key}. Adding to delete list.")
                             keys_to_delete_in_batch.append(key) # Delete invalid entries too
                        except Exception as e:
                            logger.warning(f"[Redis Cleanup] Error processing key '{key}' in {hash_key}: {e}. Skipping delete for safety.")

                    if keys_to_delete_in_batch:
                        deleted = redis_client_sync.hdel(hash_key, *keys_to_delete_in_batch)
                        if deleted > 0:
                             logger.debug(f"[Redis Cleanup] Deleted {deleted} keys from {hash_key}.")
                             hash_deleted_for_region += deleted
                except redis.exceptions.RedisError as scan_err:
                    logger.error(f"[Redis Cleanup] Error scanning hash {hash_key}: {scan_err}. Breaking scan for this hash.")
                    break # Stop scanning this hash on error

                if cursor == '0': break # Scan complete for this hash

            logger.debug(f"[Redis Cleanup] Hash cleanup for {hash_key} finished. Removed {hash_deleted_for_region} entries.")
            deleted_count_hash += hash_deleted_for_region

        logger.debug(f"[Redis Cleanup] Total items removed: ZSET={deleted_count_zset}, Hashes={deleted_count_hash}")
        return deleted_count_zset + deleted_count_hash

    except redis.exceptions.RedisError as e:
        logger.error(f"[Redis Cleanup] Redis error during cleanup: {e}")
        logger.error(traceback.format_exc())
        return -1 # Indicate error
    except Exception as e:
        logger.error(f"[Redis Cleanup] Unexpected error during cleanup: {e}")
        logger.error(traceback.format_exc())
        return -1 # Indicate error
# --- End cleanup function ---
