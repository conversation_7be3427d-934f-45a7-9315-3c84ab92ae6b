<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="ja-<PERSON>" xml:lang="ja-<PERSON>">

	
	<!-- skey = 2025/06/19 18:28:44 rei22h numHhLrlMq dljdmx+cff  --><head id="j_idt53">
    <base href="about:blank">
    <style>
        /* Hide elements that might try to load external resources, as a fallback. */
        img, svg, video, iframe, link[rel="stylesheet"], script { display: none !important; }
    </style>

		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
			<meta http-equiv="X-UA-Compatible" content="IE=EmulateIE9; IE=EmulateIE10" />
			<meta http-equiv="X-UA-Compatible" content="IE=7" />
			<meta http-equiv="X-UA-Compatible" content="IE=edge" />
		<meta name="description" content="" />
		<meta name="keywords" content="" />
		<meta name="format-detection" content="telephone=no" />
		<script type="text/javascript" src="/international_asw/dynatrace/ruxitagentjs_ICANVfhqrux_10273230920145353.js" data-dtconfig="rid=RID_428750129|rpid=-1941962517|domain=aswbe-i.ana.co.jp|reportUrl=https://bf90419wvr.bf.dynatrace.com/bf|app=78bf0b58acf6ed13|cors=1|owasp=1|featureHash=ICANVfhqrux|xb=www^bs.google-analytics^bs.com^pana^bs.demdex^bs.net^pcollect^bs.tealiumiq^bs.com^pallnipponairways^bs.australia-3^bs.evergage^bs.com^pallnipponairwayscolt^bs.tt^bs.omtrdc^bs.net|rdnt=1|uxrgce=1|cuc=k8vpgh61|mel=100000|expw=1|dpvc=1|md=mdcc1=bAsw.PnrOutput.recordLocator,mdcc2=bAsw.PnrOutput.paymentDetailInfoList[0].fopCode,mdcc3=bAsw.PnrOutput.ticketedFlag,mdcc4=bAsw.PnrOutput.nhPnrFlag,mdcc5=bAsw.PnrOutput.itinerarySummary.departureDateYyyymmdd,mdcc6=bAsw.SearchCriteriaOutput.cffCodeInput,mdcc7=a#main ^rb div.section.sectionNoFlow ^rb table ^rb tbody ^rb tr ^rb td.flightNumber.lastCol ^rb span ^rb span:nth-child(2),mdcc8=cunique|lastModification=1750184416409|tp=500,50,0|agentUri=/international_asw/dynatrace/ruxitagentjs_ICANVfhqrux_10273230920145353.js"></script><link rel="shortcut icon" href="https://www.ana.co.jp/favicon.ico" />
		<title>お客様情報入力 | ANA</title>
		<noscript><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/css/asw_noscript_pc.css?99001fc" />
		</noscript>

		<!--  [ Adobe Target ] -->
		<script type="text/javascript" src="https://www.ana.co.jp/behavior/adobetarget/target_intbe.js"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/jquery-1.8.3.min.js?717d3c0"></script><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/ja/css/asw_initialize.css?99001fc" /><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/css/asw_screen_pc.css?99001fc" /><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/css/asw_common_pc.css?99001fc" /><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/css/asw_forminput_pc.css?99001fc" /><script type="text/javascript">
		var Asw = Asw || {};
		Asw.GlobalWord = Asw.GlobalWord || {};
		
		Asw.GlobalWord.toolTipCloseIconAltText = "閉じる";
		
		</script><script >bazadebezolkohpepadr="1929501395"</script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/akam/13/7301db7c"  defer></script></head><body>
		
		<noscript>当サイトをご利用になるためには、JavaScript対応のブラウザが必要です。設定でJavaScriptを有効にしてください。有効にできない場合はANA電話窓口へお問い合わせください。
		</noscript>
		<div id="preLoadingArea"></div>
	
	<div id="cmnHeader" class="platinum">
		<!--===== cmnHeader =====-->
		<div class="headerWrapper">
			<div class="header">
				<p class="logo"><a href="https://www.ana.co.jp/asw/AMCTopServlet?type=a" tabindex="-1" aria-hidden="true"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/ana_logo_navy.png?717d3c0" alt="ANA Inspiration of JAPAN。リンク先はマイレージクラブトップページです。" height="28" width="274" /></a>
				</p>
					<ul class="headerMenu">
						<li><a href="https://www.ana.co.jp/asw/AMCTopServlet?type=a">ANAマイレージクラブトップページ</a>
						</li>
						<li><a href="https://www.ana.co.jp/other/int/meta/0005.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">お問い合わせ<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
						</li>
							<li class="btnLogoutArea"><a href="https://aswbe-i.ana.co.jp/rei22h/international_asw/rest/logout" role="button" class="btnBase btnLogout">ログアウト</a>
							</li>
					</ul>
			</div>
		</div>
			<div class="amcUserInfoWrapper">
				<div class="amcUserInfo">
					<div class="userNameArea">
						<table>
							<tr>	
									<td class="statusIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_status_05.png?717d3c0" alt="プラチナサービスメンバー" height="46" width="60" />
									</td>
								<td class="userName">佐藤 直樹 <span>様</span>
								</td>
							</tr>					
						</table>
					</div>
						<div class="balance">
								<dl class="upgradePointBalance">
									<dt>アップグレードポイント残高
									</dt>
									<dd>
											<dl>
												<dt>(2025年度)
												</dt>
												<dd>
													<em>20
													</em>ポイント
												</dd>
											</dl>
									</dd>
								</dl>
							<dl class="mileBalance">
								<dt>マイル口座残高
								</dt>
								<dd><em>75,663</em><span class="currencyCode">マイル</span>
								</dd>
							</dl>
						</div>
				</div>
					<p class="nowTime">6月19日18時28分現在
					</p>
			</div>
		<!--===== /cmnHeader =====-->
	</div>
		<div id="sessionKeeperContainer" class="sessionKeepingContainer"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/bg_modalWindow.png?717d3c0" alt="" height="0" width="0" />
			<div id="extendSessionExpiredDialog" class="modalExpire modalSmall modalContainer" role="dialog" aria-labelledby="extendSessionExpiredDialogTitle" aria-hidden="true">
<form id="sessionKeeperContainer:j_idt183" name="sessionKeeperContainer:j_idt183" method="post" action="https://aswbe-i.ana.co.jp/rei22h/international_asw/pages/award/reservation/award_mandatory_passenger_information_input.xhtml?aswcid=1&amp;rand=20250619182844numHhLrlMq" autocomplete="off" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="sessionKeeperContainer:j_idt183" value="sessionKeeperContainer:j_idt183" />
<input type="hidden" name="sessionKeeperContainer:j_idt183_operationTicket" value="dljdmx+cff588dbb42ccb9a20ac128b7391d5af~trxTR4PJ2gO_sKck1fnzcEnlg-h7j7pCoighyjtP!1750325315805.aere-xml-controller-67d4778877-ck48p" /><input type="hidden" name="sessionKeeperContainer:j_idt183_cmnPageTicket" value="4" />
					<h1 id="extendSessionExpiredDialogTitle" class="dialogTitle">まもなくセッションが終了します</h1>
					<div id="extendSessionExpiredDialogMessage" class="dialogMessage" tabindex="-1">このままお手続きを進める場合は、［延長］ボタンを押してください。</div>
					<p class="modalButton"><input id="sessionKeeperContainer:cmnSessionKeepingButton" type="submit" name="sessionKeeperContainer:cmnSessionKeepingButton" value="延長" class="btnBase btnModal btnMainStream" onclick="mojarra.ab(this,event,'action',0,0,{'onevent':function(data) {Asw.doCommonAjaxCallback(data,false, event); Asw.SessionKeeper.doSessionKeep(data);},'onerror':function(data) {Asw.doCommonAjaxErrorCallback(data); }});return false" />
					</p><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:0" value="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" autocomplete="off" />
</form>
			</div>
			<div id="showSessionExpiredDialog" class="modalExpire modalSmall modalContainer" role="dialog" aria-labelledby="showSessionExpiredDialogTitle" aria-hidden="true">
				<h1 id="showSessionExpiredDialogTitle" class="dialogTitle">セッションが終了しました</h1>
				<div id="showSessionExpiredDialogMessage" class="dialogMessage" tabindex="-1">一定時間操作されなかったため、お手続きを進めることができません。お手数ですが、再度トップページからお手続きください。</div>
				<p class="modalButton">
					<input type="submit" value="閉じる" class="btnBase btnModal btnMainStream" onclick="Asw.SessionKeeper.closeTimeoutDialog(); return false;" />
				</p>
			</div>
		</div>
		<!--===== dialogMessages =====--><div id="cmnModalMessages"></div>
		<div id="prebookConfirmDialog" class="modalContainer modalConfirm modalSmall " aria-hidden="true" role="dialog">
<form id="prebookConfirmDialog:j_idt403" name="prebookConfirmDialog:j_idt403" method="post" action="https://aswbe-i.ana.co.jp/rei22h/international_asw/pages/award/reservation/award_mandatory_passenger_information_input.xhtml?aswcid=1&amp;rand=20250619182844numHhLrlMq" autocomplete="off" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="prebookConfirmDialog:j_idt403" value="prebookConfirmDialog:j_idt403" />
<input type="hidden" name="prebookConfirmDialog:j_idt403_operationTicket" value="dljdmx+cff588dbb42ccb9a20ac128b7391d5af~trxTR4PJ2gO_sKck1fnzcEnlg-h7j7pCoighyjtP!1750325315805.aere-xml-controller-67d4778877-ck48p" /><input type="hidden" name="prebookConfirmDialog:j_idt403_cmnPageTicket" value="4" /><div class="dialogMessage" tabindex="0"><p>この後、名・姓の修正や変更ができません。</p><p>「搭乗者選択」欄の氏名が「名」「姓」の順番で表示されていることを再確認ください。</p><p>例）<strong class="highlight">空野 太郎</strong> 様 → MR <strong class="highlight">TARO SORANO</strong></p><p>保安上の理由により、航空券とパスポートのお名前が同じ表記でない場合はご搭乗いただくことができませんので必ず再度ご確認ください。</p></div>
					
					<ul class="modalBtnHorizontal">
							
							<li><input type="submit" name="prebookConfirmDialog:j_idt419" value="戻る" aria-controls="prebookConfirmDialog" class="btnBase btnModal btnCancel" onclick="Asw.Dialog.getInstance('prebookConfirmDialog').close(event, Asw.DialogSequencer.isActiveSequencerNotDisplayedLastDialog());  return false;" />
							</li>
						
						<li class="btnArrowNext"><input type="submit" name="prebookConfirmDialog:j_idt422" value="OK" aria-controls="prebookConfirmDialog" class="btnBase btnModal btnMainStream " onclick="Asw.Dialog.getInstance('prebookConfirmDialog').callOpener('NORMAL', event); return false;" />
						</li>
					</ul><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:1" value="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" autocomplete="off" />
</form>
		</div>
		<!--===== /dialogMessages =====--><div id="cmnContainer">
		<!--=========== .cmnContainer ============--><div id="cmnEmergencyNotice"></div><div id="cmnTitle" class="hgroup ">
		<h1 class="visuallyHidden">お客様情報入力</h1></div>

		<div id="cmnWrapper">
		<!--===== cmnWrapper =====-->
		<ol class="mainFlow" aria-label="完了までの手順、">
			<li><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_flow_01.png?717d3c0" alt="1" height="20" width="28" />フライト検索</li><!--
			--><li><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_flow_02_on.png?717d3c0" alt="2" height="20" width="28" /><span class="visuallyHidden">現在のステップ、</span>お客様情報入力</li><!--
			--><li><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_flow_03.png?717d3c0" alt="3" height="20" width="28" />お支払い情報</li><!--
			--><li class="flowLast"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_flow4_04.png?717d3c0" alt="4" height="20" width="28" />完了</li>
		</ol>

	<div id="main">
	<!--=========== main =============-->
		<ul class="iconExample">
				<li class="requiredIconExample">
					<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="" height="7" width="7" /></span>必須項目です	
				</li>
		</ul>
<form id="j_idt526" name="j_idt526" method="post" action="https://aswbe-i.ana.co.jp/rei22h/international_asw/pages/award/reservation/award_mandatory_passenger_information_input.xhtml?aswcid=1&amp;rand=20250619182844numHhLrlMq" autocomplete="off" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="j_idt526" value="j_idt526" />
<input type="hidden" name="j_idt526_operationTicket" value="dljdmx+cff588dbb42ccb9a20ac128b7391d5af~trxTR4PJ2gO_sKck1fnzcEnlg-h7j7pCoighyjtP!1750325315805.aere-xml-controller-67d4778877-ck48p" /><input type="hidden" name="j_idt526_cmnPageTicket" value="4" />

<div class="section">
	<h2>搭乗者選択<a href="https://www.ana.co.jp/other/int/meta/0086.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="特典のご利用に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a></h2>
	<table class="passengerInfo">
		<thead>
			<tr>
				
				<th class="name">氏名</th>
				
				<th class="dateOfBirthWide" id="dateOfBirthHeader">生年月日</th>
				
				<th class="gender lastCol" id="genderHeader">性別<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span></th>
				
			</tr>
		</thead>
		<tbody>
				
				<tr>
					<th class="parent_01" id="parentHeader01">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:0:passenger" type="checkbox" name="passengers:0:passenger" checked="checked" data-number="1" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:0:passenger">NAOKI SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader01">1963年06月05日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader01">				
							<span class="formWrapper">
							<label for="passengers:0:gender" class="visuallyHidden">NAOKI SATOの性別</label><select id="passengers:0:gender" name="passengers:0:gender" size="1">	<option value="">選択してください</option>
	<option value="M" selected="selected">男性</option>
	<option value="F">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_02" id="parentHeader02">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:1:passenger" type="checkbox" name="passengers:1:passenger" data-number="2" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:1:passenger">REO SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader02">1995年05月18日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader02">				
							<span class="formWrapper">
							<label for="passengers:1:gender" class="visuallyHidden">REO SATOの性別</label><select id="passengers:1:gender" name="passengers:1:gender" size="1">	<option value="">選択してください</option>
	<option value="M" selected="selected">男性</option>
	<option value="F">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_03" id="parentHeader03">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:2:passenger" type="checkbox" name="passengers:2:passenger" data-number="3" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:2:passenger">ERI SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader03">1963年08月14日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader03">				
							<span class="formWrapper">
							<label for="passengers:2:gender" class="visuallyHidden">ERI SATOの性別</label><select id="passengers:2:gender" name="passengers:2:gender" size="1">	<option value="">選択してください</option>
	<option value="M">男性</option>
	<option value="F" selected="selected">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_04" id="parentHeader04">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:3:passenger" type="checkbox" name="passengers:3:passenger" data-number="4" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:3:passenger">TAISHI SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader04">1992年12月03日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader04">				
							<span class="formWrapper">
							<label for="passengers:3:gender" class="visuallyHidden">TAISHI SATOの性別</label><select id="passengers:3:gender" name="passengers:3:gender" size="1">	<option value="">選択してください</option>
	<option value="M" selected="selected">男性</option>
	<option value="F">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_05" id="parentHeader05">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:4:passenger" type="checkbox" name="passengers:4:passenger" data-number="5" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:4:passenger">HIROMI FUJIMOTO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader05">1961年11月23日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader05">				
							<span class="formWrapper">
							<label for="passengers:4:gender" class="visuallyHidden">HIROMI FUJIMOTOの性別</label><select id="passengers:4:gender" name="passengers:4:gender" size="1">	<option value="">選択してください</option>
	<option value="M">男性</option>
	<option value="F" selected="selected">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
		</tbody>
	</table>
</div>
		<div class="section">
			<h2 class="midstream">連絡先情報
			</h2>
			<h3>代表者
			</h3>
	
	
	
	<div class="formInput">
		<fieldset>
			<legend><label>代表者</label></legend>
			<dl id="contactEmailAddressInfo">
				<dt class="hasRequiredIcon">メールアドレス<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span><a href="https://www.ana.co.jp/other/int/meta/0057.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="メールの宛先と内容に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a>
				</dt>
				<dd>
					<ul class="verticalList">
						<li class="nonMarginTop"><label for="representativeEmail" class="visuallyHidden">代表者メールアドレス</label><input id="representativeEmail" type="text" name="representativeEmail" value="<EMAIL>" class="widthLarge" placeholder="例：<EMAIL>" />
						</li>
						<li>
							<span class="supplementTxt" id="confirmMailAddressForRepresentative">確認のため再度ご入力ください。
							</span><label for="confirmrepresentativeEmail" class="visuallyHidden">確認用メールアドレス</label><input id="confirmrepresentativeEmail" type="text" name="confirmrepresentativeEmail" value="<EMAIL>" class="widthLarge jsNotPasteInput" placeholder="例：<EMAIL>" aria-describedby="confirmMailAddressForRepresentative" />
						</li>
					</ul>
				</dd>
					<dd style="display:none;"><input id="updateAlwaysInfo" type="checkbox" name="updateAlwaysInfo" /><label for="updateAlwaysInfo">ログイン中の会員情報へこのメールアドレスを登録</label>
					</dd>
			</dl>
			<dl>
				<dt>電話番号<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span>
				</dt>
				<dd class="js-countryWrap">
					<ul>
						<li><label for="representativeTelType" class="visuallyHidden">電話番号種別</label><select id="representativeTelType" name="representativeTelType" class="basicWidth js-typeSelect" size="1" onchange="changeTelType(this,'representativeTel')">	<option value="M1" selected="selected">携帯</option>
	<option value="B1">会社</option>
	<option value="H1">自宅</option>
</select>
						</li>
						<li><label for="representativeTelCountry" class="visuallyHidden">電話番号の国/地区選択</label><select id="representativeTelCountry" name="representativeTelCountry" class="js-countrySelect" size="1">	<option value="" data-sms-possible="true" data-country-code="---">選択してください</option>
	<option value="JP" selected="selected" data-sms-possible="true">日本</option>
	<option value="-" data-sms-possible="true" data-country-code="---">-----------</option>
	<option value="IS" data-sms-possible="true">アイスランド</option>
	<option value="IE" data-sms-possible="true">アイルランド</option>
	<option value="AZ" data-sms-possible="true">アゼルバイジャン</option>
	<option value="AF" data-sms-possible="true">アフガニスタン</option>
	<option value="US" data-sms-possible="true">アメリカ合衆国/カナダ</option>
	<option value="AE" data-sms-possible="true">アラブ首長国連邦</option>
	<option value="DZ" data-sms-possible="true">アルジェリア</option>
	<option value="AR" data-sms-possible="true">アルゼンチン</option>
	<option value="AW" data-sms-possible="true">アルバ</option>
	<option value="AL" data-sms-possible="true">アルバニア</option>
	<option value="AM" data-sms-possible="true">アルメニア</option>
	<option value="AO" data-sms-possible="true">アンゴラ</option>
	<option value="AG" data-sms-possible="true">アンティグア・バーブーダ</option>
	<option value="AD" data-sms-possible="true">アンドラ</option>
	<option value="YE" data-sms-possible="true">イエメン</option>
	<option value="GB" data-sms-possible="true">イギリス</option>
	<option value="IL" data-sms-possible="true">イスラエル</option>
	<option value="IT" data-sms-possible="true">イタリア</option>
	<option value="IQ" data-sms-possible="true">イラク</option>
	<option value="IR" data-sms-possible="true">イラン</option>
	<option value="IN" data-sms-possible="true">インド</option>
	<option value="ID" data-sms-possible="true">インドネシア</option>
	<option value="UG" data-sms-possible="true">ウガンダ</option>
	<option value="UA" data-sms-possible="true">ウクライナ</option>
	<option value="UZ" data-sms-possible="true">ウズベキスタン</option>
	<option value="UY" data-sms-possible="true">ウルグアイ</option>
	<option value="EC" data-sms-possible="true">エクアドル</option>
	<option value="EG" data-sms-possible="true">エジプト</option>
	<option value="EE" data-sms-possible="true">エストニア</option>
	<option value="SZ" data-sms-possible="true">エスワティニ</option>
	<option value="ET" data-sms-possible="true">エチオピア</option>
	<option value="ER" data-sms-possible="true">エリトリア</option>
	<option value="SV" data-sms-possible="true">エルサルバドル</option>
	<option value="AU" data-sms-possible="true">オーストラリア</option>
	<option value="AT" data-sms-possible="true">オーストリア</option>
	<option value="OM" data-sms-possible="true">オマーン</option>
	<option value="NL" data-sms-possible="true">オランダ</option>
	<option value="GH" data-sms-possible="true">ガーナ</option>
	<option value="CV" data-sms-possible="true">カーボベルデ</option>
	<option value="GY" data-sms-possible="true">ガイアナ</option>
	<option value="KZ" data-sms-possible="true">カザフスタン</option>
	<option value="QA" data-sms-possible="true">カタール</option>
	<option value="CA" data-sms-possible="true">カナダ/アメリカ合衆国</option>
	<option value="GA" data-sms-possible="true">ガボン</option>
	<option value="CM" data-sms-possible="true">カメルーン</option>
	<option value="KR" data-sms-possible="true">韓国</option>
	<option value="GM" data-sms-possible="true">ガンビア</option>
	<option value="KH" data-sms-possible="true">カンボジア</option>
	<option value="KP" data-sms-possible="true">北朝鮮</option>
	<option value="MK" data-sms-possible="true">北マケドニア</option>
	<option value="GN" data-sms-possible="true">ギニア</option>
	<option value="GW" data-sms-possible="true">ギニアビサウ</option>
	<option value="CY" data-sms-possible="true">キプロス</option>
	<option value="CU" data-sms-possible="true">キューバ</option>
	<option value="GR" data-sms-possible="true">ギリシャ</option>
	<option value="KI" data-sms-possible="true">キリバス</option>
	<option value="KG" data-sms-possible="true">キルギス</option>
	<option value="GT" data-sms-possible="true">グアテマラ</option>
	<option value="GU" data-sms-possible="true">グアム</option>
	<option value="KW" data-sms-possible="true">クウェート</option>
	<option value="GD" data-sms-possible="true">グレナダ</option>
	<option value="HR" data-sms-possible="true">クロアチア</option>
	<option value="KY" data-sms-possible="true">ケイマン諸島</option>
	<option value="KE" data-sms-possible="true">ケニア</option>
	<option value="CI" data-sms-possible="true">コートジボワール</option>
	<option value="CR" data-sms-possible="true">コスタリカ</option>
	<option value="KM" data-sms-possible="true">コモロ</option>
	<option value="CO" data-sms-possible="true">コロンビア</option>
	<option value="CG" data-sms-possible="true">コンゴ共和国</option>
	<option value="CD" data-sms-possible="true">コンゴ民主共和国</option>
	<option value="SA" data-sms-possible="true">サウジアラビア</option>
	<option value="ZM" data-sms-possible="true">ザンビア</option>
	<option value="SM" data-sms-possible="true">サンマリノ</option>
	<option value="SL" data-sms-possible="true">シエラレオネ</option>
	<option value="DJ" data-sms-possible="true">ジブチ</option>
	<option value="JM" data-sms-possible="true">ジャマイカ</option>
	<option value="GE" data-sms-possible="true">ジョージア</option>
	<option value="SY" data-sms-possible="true">シリア</option>
	<option value="SG" data-sms-possible="true">シンガポール</option>
	<option value="ZW" data-sms-possible="true">ジンバブエ</option>
	<option value="CH" data-sms-possible="true">スイス</option>
	<option value="SE" data-sms-possible="true">スウェーデン</option>
	<option value="SD" data-sms-possible="true">スーダン</option>
	<option value="ES" data-sms-possible="true">スペイン</option>
	<option value="SR" data-sms-possible="true">スリナム</option>
	<option value="LK" data-sms-possible="true">スリランカ</option>
	<option value="SK" data-sms-possible="true">スロバキア</option>
	<option value="SI" data-sms-possible="true">スロベニア</option>
	<option value="SC" data-sms-possible="true">セイシェル</option>
	<option value="GQ" data-sms-possible="true">赤道ギニア</option>
	<option value="SN" data-sms-possible="true">セネガル</option>
	<option value="RS" data-sms-possible="true">セルビア</option>
	<option value="KN" data-sms-possible="true">セントクリストファー・ネイビス</option>
	<option value="LC" data-sms-possible="true">セントルシア</option>
	<option value="SB" data-sms-possible="true">ソロモン諸島</option>
	<option value="TH" data-sms-possible="true">タイ</option>
	<option value="TW" data-sms-possible="true">台湾</option>
	<option value="TJ" data-sms-possible="true">タジキスタン</option>
	<option value="TZ" data-sms-possible="true">タンザニア</option>
	<option value="CZ" data-sms-possible="true">チェコ</option>
	<option value="TD" data-sms-possible="true">チャド</option>
	<option value="CF" data-sms-possible="true">中央アフリカ共和国</option>
	<option value="CN" data-sms-possible="true">中国</option>
	<option value="TN" data-sms-possible="true">チュニジア</option>
	<option value="CL" data-sms-possible="true">チリ</option>
	<option value="TV" data-sms-possible="true">ツバル</option>
	<option value="DK" data-sms-possible="true">デンマーク</option>
	<option value="DE" data-sms-possible="true">ドイツ</option>
	<option value="TG" data-sms-possible="true">トーゴ</option>
	<option value="DO" data-sms-possible="true">ドミニカ共和国</option>
	<option value="DM" data-sms-possible="true">ドミニカ国</option>
	<option value="TT" data-sms-possible="true">トリニダード・トバゴ</option>
	<option value="TM" data-sms-possible="true">トルクメニスタン</option>
	<option value="TR" data-sms-possible="true">トルコ</option>
	<option value="TO" data-sms-possible="true">トンガ</option>
	<option value="NG" data-sms-possible="true">ナイジェリア</option>
	<option value="NR" data-sms-possible="true">ナウル</option>
	<option value="NA" data-sms-possible="true">ナミビア</option>
	<option value="NI" data-sms-possible="true">ニカラグア</option>
	<option value="NE" data-sms-possible="true">ニジェール</option>
	<option value="NZ" data-sms-possible="true">ニュージーランド</option>
	<option value="NP" data-sms-possible="true">ネパール</option>
	<option value="NO" data-sms-possible="true">ノルウェー</option>
	<option value="BH" data-sms-possible="true">バーレーン</option>
	<option value="HT" data-sms-possible="true">ハイチ</option>
	<option value="PK" data-sms-possible="true">パキスタン</option>
	<option value="VA" data-sms-possible="true">バチカン</option>
	<option value="PA" data-sms-possible="true">パナマ</option>
	<option value="VU" data-sms-possible="true">バヌアツ</option>
	<option value="BS" data-sms-possible="true">バハマ</option>
	<option value="PG" data-sms-possible="true">パプアニューギニア</option>
	<option value="BM" data-sms-possible="true">バミューダ</option>
	<option value="PW" data-sms-possible="true">パラオ</option>
	<option value="PY" data-sms-possible="true">パラグアイ</option>
	<option value="BB" data-sms-possible="true">バルバドス</option>
	<option value="PS" data-sms-possible="true">パレスチナ</option>
	<option value="HU" data-sms-possible="true">ハンガリー</option>
	<option value="BD" data-sms-possible="true">バングラデシュ</option>
	<option value="TL" data-sms-possible="true">東ティモール</option>
	<option value="FJ" data-sms-possible="true">フィジー</option>
	<option value="PH" data-sms-possible="true">フィリピン</option>
	<option value="FI" data-sms-possible="true">フィンランド</option>
	<option value="BT" data-sms-possible="true">ブータン</option>
	<option value="PR" data-sms-possible="true">プエルトリコ</option>
	<option value="BR" data-sms-possible="true">ブラジル</option>
	<option value="FR" data-sms-possible="true">フランス</option>
	<option value="PF" data-sms-possible="true">フランス領ポリネシア</option>
	<option value="BG" data-sms-possible="true">ブルガリア</option>
	<option value="BF" data-sms-possible="true">ブルキナファソ</option>
	<option value="BN" data-sms-possible="true">ブルネイ</option>
	<option value="BI" data-sms-possible="true">ブルンジ</option>
	<option value="VN" data-sms-possible="true">ベトナム</option>
	<option value="BJ" data-sms-possible="true">ベナン</option>
	<option value="VE" data-sms-possible="true">ベネズエラ</option>
	<option value="BY" data-sms-possible="true">ベラルーシ</option>
	<option value="BZ" data-sms-possible="true">ベリーズ</option>
	<option value="PE" data-sms-possible="true">ペルー</option>
	<option value="BE" data-sms-possible="true">ベルギー</option>
	<option value="PL" data-sms-possible="true">ポーランド</option>
	<option value="BA" data-sms-possible="true">ボスニア・ヘルツェゴビナ</option>
	<option value="BW" data-sms-possible="true">ボツワナ</option>
	<option value="BO" data-sms-possible="true">ボリビア</option>
	<option value="PT" data-sms-possible="true">ポルトガル</option>
	<option value="HK" data-sms-possible="true">香港</option>
	<option value="HN" data-sms-possible="true">ホンジュラス</option>
	<option value="MH" data-sms-possible="true">マーシャル諸島</option>
	<option value="MO" data-sms-possible="true">マカオ</option>
	<option value="MG" data-sms-possible="true">マダガスカル</option>
	<option value="MW" data-sms-possible="true">マラウイ</option>
	<option value="ML" data-sms-possible="true">マリ</option>
	<option value="MT" data-sms-possible="true">マルタ</option>
	<option value="MY" data-sms-possible="true">マレーシア</option>
	<option value="FM" data-sms-possible="true">ミクロネシア連邦</option>
	<option value="ZA" data-sms-possible="true">南アフリカ</option>
	<option value="MM" data-sms-possible="true">ミャンマー</option>
	<option value="MX" data-sms-possible="true">メキシコ</option>
	<option value="MU" data-sms-possible="true">モーリシャス</option>
	<option value="MR" data-sms-possible="true">モーリタニア</option>
	<option value="MZ" data-sms-possible="true">モザンビーク</option>
	<option value="MC" data-sms-possible="true">モナコ</option>
	<option value="MV" data-sms-possible="true">モルディヴ</option>
	<option value="MD" data-sms-possible="true">モルドバ</option>
	<option value="MA" data-sms-possible="true">モロッコ</option>
	<option value="MN" data-sms-possible="true">モンゴル</option>
	<option value="ME" data-sms-possible="true">モンテネグロ</option>
	<option value="JO" data-sms-possible="true">ヨルダン</option>
	<option value="LA" data-sms-possible="true">ラオス人民民主共和国</option>
	<option value="LV" data-sms-possible="true">ラトビア</option>
	<option value="LT" data-sms-possible="true">リトアニア</option>
	<option value="LY" data-sms-possible="true">リビア</option>
	<option value="LI" data-sms-possible="true">リヒテンシュタイン</option>
	<option value="LR" data-sms-possible="true">リベリア</option>
	<option value="RO" data-sms-possible="true">ルーマニア</option>
	<option value="LU" data-sms-possible="true">ルクセンブルク</option>
	<option value="RW" data-sms-possible="true">ルワンダ</option>
	<option value="LS" data-sms-possible="true">レソト</option>
	<option value="LB" data-sms-possible="true">レバノン</option>
	<option value="RU" data-sms-possible="true">ロシア</option>
</select>
						</li>
						<li class="numberInputWrap">
							<span id="numberInputInfo1" class="supplementTxt marginTop">先頭に0(ゼロ)がある場合、0(ゼロ)を抜いてください(一部、国・地域除く)。携帯電話番号の場合は、携帯電話を契約された国を選択ください。</span>
							<div class="numberRowWrap">
								<div class="block">
									
									<span class="visuallyHidden">国番号</span>
									<span class="viewInput widthMini js-countryCode"></span>
								</div>
								<div class="block"><label for="representativeTel" class="visuallyHidden">電話番号</label><input id="representativeTel" type="text" name="representativeTel" value="9042504176" class="widthMiddle" placeholder="例：9012345678" maxlength="30" aria-describedby="numberInputInfo1" />
								</div>
							</div>
						</li>
					</ul>
				</dd>
			</dl>
		</fieldset>
	</div>
	<div class="titleWrapper">
		<h3>
			<span class="text">eメール・SMSに関するご案内<a href="https://www.ana.co.jp/other/int/meta/0057.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="eメール・SMSに関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a></span>
		</h3>
	</div>
	<div class="formInput">
	<h4 class="formAreaTitle">主に搭乗・運航に関わるeメールの送付先設定（米国へ通知いたします）<a href="https://www.ana.co.jp/other/int/meta/0629.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="米国入国用eメール・SMS・電話番号に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a>
	</h4>
	<div class="paxSettingArea js-mailFormWrap onePax is-open">
			<div class="settingArea">
					<dl>
						<dt class="breakWord">NAOKI SATO
						</dt>
						<dd>
							<fieldset>
								<legend>NAOKI SATOのeメール送付先および米国到着時に提供するメールアドレス</legend>代表者と同じ
							</fieldset>
						</dd>
					</dl>
			</div>
	</div>
	<h4 class="formAreaTitle hasTopLine">主に搭乗・運航に関わるSMSの送付先および米国で受信できる電話番号の設定（米国へ通知いたします）<a href="https://www.ana.co.jp/other/int/meta/0629.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="米国入国用eメール・SMS・電話番号に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a>
	</h4>
	<div class="paxSettingArea js-smsFormWrap onePax is-open">
			<div class="settingArea">
					<dl>
						<dt class="breakWord">NAOKI SATO<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span>
						</dt>
						<dd>
							<fieldset>
								<legend>NAOKI SATOの1つ目の電話番号</legend>
									<ul class="verticalList js-formItem">
										<li><input type="radio" id="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSameAsRepresentativeSms:radioItem" name="contactsSms:0:recipientSmsEachPersonSelection:radioGroup" value="0" checked="checked" /><label for="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSameAsRepresentativeSms:radioItem">代表者電話番号と同じ</label>
										</li>
										<li><input type="radio" id="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSmsPerPassenger:radioItem" name="contactsSms:0:recipientSmsEachPersonSelection:radioGroup" value="1" /><label for="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSmsPerPassenger:radioItem">電話番号を指定</label>
											<div class="indentArea js-formImputArea">
												<span id="confirmTelNumberForRepresentative:0" class="supplementTxt marginTop">先頭に0(ゼロ)がある場合、0(ゼロ)を抜いてください(一部、国・地域除く)。携帯電話番号の場合は、携帯電話を契約された国を選択ください。</span>
												<ul class="countryNumberInput js-countryWrap">
													<li><label for="contactsSms:0:recipientSmsEachPersonSelection:passengerSmsCountry" class="visuallyHidden">電話番号の国/地区選択</label><select id="contactsSms:0:recipientSmsEachPersonSelection:passengerSmsCountry" name="contactsSms:0:recipientSmsEachPersonSelection:passengerSmsCountry" class="countrySelect js-countrySelect" size="1">	<option value="" data-country-code="---">選択してください</option>
	<option value="JP">日本</option>
	<option value="-" data-country-code="---">-----------</option>
	<option value="IE">アイルランド</option>
	<option value="AZ">アゼルバイジャン</option>
	<option value="AF">アフガニスタン</option>
	<option value="US">アメリカ合衆国/カナダ</option>
	<option value="AE">アラブ首長国連邦</option>
	<option value="DZ">アルジェリア</option>
	<option value="AR">アルゼンチン</option>
	<option value="AW">アルバ</option>
	<option value="AL">アルバニア</option>
	<option value="AM">アルメニア</option>
	<option value="AG">アンティグア・バーブーダ</option>
	<option value="AD">アンドラ</option>
	<option value="YE">イエメン</option>
	<option value="GB">イギリス</option>
	<option value="IL">イスラエル</option>
	<option value="IT">イタリア</option>
	<option value="IQ">イラク</option>
	<option value="IR">イラン</option>
	<option value="IN">インド</option>
	<option value="ID">インドネシア</option>
	<option value="UG">ウガンダ</option>
	<option value="UA">ウクライナ</option>
	<option value="UZ">ウズベキスタン</option>
	<option value="UY">ウルグアイ</option>
	<option value="EC">エクアドル</option>
	<option value="EG">エジプト</option>
	<option value="EE">エストニア</option>
	<option value="ER">エリトリア</option>
	<option value="SV">エルサルバドル</option>
	<option value="AU">オーストラリア</option>
	<option value="AT">オーストリア</option>
	<option value="OM">オマーン</option>
	<option value="NL">オランダ</option>
	<option value="GH">ガーナ</option>
	<option value="GY">ガイアナ</option>
	<option value="KZ">カザフスタン</option>
	<option value="QA">カタール</option>
	<option value="CA">カナダ/アメリカ合衆国</option>
	<option value="GA">ガボン</option>
	<option value="CM">カメルーン</option>
	<option value="KR">韓国</option>
	<option value="GM">ガンビア</option>
	<option value="KH">カンボジア</option>
	<option value="GN">ギニア</option>
	<option value="GW">ギニアビサウ</option>
	<option value="CY">キプロス</option>
	<option value="CU">キューバ</option>
	<option value="GR">ギリシャ</option>
	<option value="KG">キルギス</option>
	<option value="GT">グアテマラ</option>
	<option value="GU">グアム</option>
	<option value="KW">クウェート</option>
	<option value="GD">グレナダ</option>
	<option value="HR">クロアチア</option>
	<option value="KY">ケイマン諸島</option>
	<option value="KE">ケニア</option>
	<option value="CI">コートジボワール</option>
	<option value="CR">コスタリカ</option>
	<option value="KM">コモロ</option>
	<option value="CO">コロンビア</option>
	<option value="CD">コンゴ民主共和国</option>
	<option value="SA">サウジアラビア</option>
	<option value="ZM">ザンビア</option>
	<option value="SL">シエラレオネ</option>
	<option value="JM">ジャマイカ</option>
	<option value="GE">ジョージア</option>
	<option value="SY">シリア</option>
	<option value="SG">シンガポール</option>
	<option value="ZW">ジンバブエ</option>
	<option value="CH">スイス</option>
	<option value="SE">スウェーデン</option>
	<option value="SD">スーダン</option>
	<option value="ES">スペイン</option>
	<option value="SR">スリナム</option>
	<option value="LK">スリランカ</option>
	<option value="SK">スロバキア</option>
	<option value="SI">スロベニア</option>
	<option value="SN">セネガル</option>
	<option value="RS">セルビア</option>
	<option value="KN">セントクリストファー・ネイビス</option>
	<option value="LC">セントルシア</option>
	<option value="TH">タイ</option>
	<option value="TW">台湾</option>
	<option value="TJ">タジキスタン</option>
	<option value="TZ">タンザニア</option>
	<option value="CZ">チェコ</option>
	<option value="TD">チャド</option>
	<option value="CF">中央アフリカ共和国</option>
	<option value="CN">中国</option>
	<option value="TN">チュニジア</option>
	<option value="CL">チリ</option>
	<option value="DK">デンマーク</option>
	<option value="DE">ドイツ</option>
	<option value="TG">トーゴ</option>
	<option value="DO">ドミニカ共和国</option>
	<option value="TT">トリニダード・トバゴ</option>
	<option value="TR">トルコ</option>
	<option value="NG">ナイジェリア</option>
	<option value="NI">ニカラグア</option>
	<option value="NE">ニジェール</option>
	<option value="NZ">ニュージーランド</option>
	<option value="NP">ネパール</option>
	<option value="NO">ノルウェー</option>
	<option value="HT">ハイチ</option>
	<option value="PK">パキスタン</option>
	<option value="PA">パナマ</option>
	<option value="BS">バハマ</option>
	<option value="PG">パプアニューギニア</option>
	<option value="BM">バミューダ</option>
	<option value="PW">パラオ</option>
	<option value="PY">パラグアイ</option>
	<option value="BB">バルバドス</option>
	<option value="PS">パレスチナ</option>
	<option value="HU">ハンガリー</option>
	<option value="BD">バングラデシュ</option>
	<option value="TL">東ティモール</option>
	<option value="FJ">フィジー</option>
	<option value="PH">フィリピン</option>
	<option value="FI">フィンランド</option>
	<option value="BT">ブータン</option>
	<option value="PR">プエルトリコ</option>
	<option value="BR">ブラジル</option>
	<option value="FR">フランス</option>
	<option value="PF">フランス領ポリネシア</option>
	<option value="BG">ブルガリア</option>
	<option value="BF">ブルキナファソ</option>
	<option value="BN">ブルネイ</option>
	<option value="BI">ブルンジ</option>
	<option value="VN">ベトナム</option>
	<option value="BJ">ベナン</option>
	<option value="VE">ベネズエラ</option>
	<option value="BY">ベラルーシ</option>
	<option value="PE">ペルー</option>
	<option value="BE">ベルギー</option>
	<option value="PL">ポーランド</option>
	<option value="BA">ボスニア・ヘルツェゴビナ</option>
	<option value="BW">ボツワナ</option>
	<option value="HK">香港</option>
	<option value="MH">マーシャル諸島</option>
	<option value="MO">マカオ</option>
	<option value="MG">マダガスカル</option>
	<option value="MW">マラウイ</option>
	<option value="ML">マリ</option>
	<option value="MT">マルタ</option>
	<option value="MY">マレーシア</option>
	<option value="ZA">南アフリカ</option>
	<option value="MM">ミャンマー</option>
	<option value="MX">メキシコ</option>
	<option value="MU">モーリシャス</option>
	<option value="MR">モーリタニア</option>
	<option value="MZ">モザンビーク</option>
	<option value="MC">モナコ</option>
	<option value="MV">モルディヴ</option>
	<option value="MA">モロッコ</option>
	<option value="MN">モンゴル</option>
	<option value="ME">モンテネグロ</option>
	<option value="JO">ヨルダン</option>
	<option value="LA">ラオス人民民主共和国</option>
	<option value="LV">ラトビア</option>
	<option value="LT">リトアニア</option>
	<option value="LY">リビア</option>
	<option value="LR">リベリア</option>
	<option value="RO">ルーマニア</option>
	<option value="LU">ルクセンブルク</option>
	<option value="RW">ルワンダ</option>
	<option value="LB">レバノン</option>
	<option value="RU">ロシア</option>
</select>
													</li>
													<li>
														
														<div class="numberRowWrap">
															<div class="block">
																<span class="visuallyHidden">国番号</span>
																<span class="countryCode viewInput js-countryCode"></span>
															</div>
															
															<div class="block"><label for="contactsSms:0:recipientSmsEachPersonSelection:flightStatusNotificationContactPointSmsDescription" class="visuallyHidden">電話番号</label><input id="contactsSms:0:recipientSmsEachPersonSelection:flightStatusNotificationContactPointSmsDescription" type="text" name="contactsSms:0:recipientSmsEachPersonSelection:flightStatusNotificationContactPointSmsDescription" class="telNumber" placeholder="例：9012345678" maxlength="30" aria-describedby="confirmTelNumberForRepresentative:0" />
															</div>
														</div>
													</li>
												</ul>
											</div>
										</li>
									</ul>
							</fieldset>
						</dd>
					</dl>
			</div>
	</div>
	</div>
	
	<div class="formInput articleLine">
		<h3>滞在中の連絡先(任意)
		</h3>
		<dl>
			
			<dt><label for="accommodationOne">現地連絡先1</label>
			</dt>
			<dd><input id="accommodationOne" type="text" name="accommodationOne" class="widthLarge" placeholder="例：09012345678 ABC HOTEL" maxlength="28" title="例：09012345678 ABC HOTEL" />
			</dd>
		</dl>
		<dl>
			
			<dt><label for="accommodationTwo">現地連絡先2</label>
			</dt>
			<dd><input id="accommodationTwo" type="text" name="accommodationTwo" class="widthLarge" placeholder="例：09012345678 ABC HOTEL" maxlength="28" title="例：09012345678 ABC HOTEL" />
			</dd>
		</dl>
	</div>
		</div>
		
		<p class="btnAreaSubmit btnArrowNext "><input id="next" type="submit" name="next" value="次へ" class="btnBase btnMainStream btnVerticalMain btnWidthVariable" onclick="return Asw.Dialog.getInstance('prebookConfirmDialog').toggle(event);return Asw.LoadingWindow.open(&quot;NORMAL&quot;, event)" />
		</p><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:2" value="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" autocomplete="off" />
</form><div id="cmnDynamicMessages" class="infoBoxSection">
				<dl class="infoBox importantInfo jsAccordionSwitch">
					<dt><strong>重要なご案内</strong></dt>
					<dd>
						<ul class="jsAccordionSwitchList" id="informationMessages">
								<li>個人情報の利用目的<a href="https://www.ana.co.jp/other/int/meta/0012.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="ANAプライバシーポリシー(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">ANAプライバシーポリシー<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>を必ずお読みいただき、内容に同意いただいた上で、お進みください。</li>
								<li><a href="https://www.ana.co.jp/other/int/meta/0058.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="Secure Flight Programについて(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">米国Secure Flightプログラム<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>に基づき、パスポート名、生年月日、性別などの情報は事前に米国へ通知いたします。</li>
								<li>入力したeメールアドレス、電話番号は入国に必要な情報として米国に通知される可能性があります。</li>
									<li class="jsHiddenFlg toggleContents">お名前のスペルがパスポート名と一致しているかご確認ください。異なる場合はインターネットではお取り扱いできません。<a href="https://www.ana.co.jp/other/int/meta/intbe0001.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="ANAお問い合わせ先(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">ANA<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>へ名前の変更をお申し出ください。</li>
									<li class="jsHiddenFlg toggleContents">二重国籍のお客様は、ご搭乗時に使用されるパスポートのお名前でご予約ください。<br />パスポートごとにお名前が異なる場合は、お客様ご自身で入国管理事務所・外務省・大使館などで各種条件をご確認のうえ、使用されるパスポートを決めてください。<br />なお、入国可否については国によって異なるため、お客様ご自身で各入国管理事務所などでご確認ください。</li>
									<li class="jsHiddenFlg toggleContents">旅行開始日時点で12歳以上の場合は大人、2歳以上12歳未満の場合は小児、2歳未満の場合は幼児の扱いとなります。なお、小児は大人と同マイル数が必要です。</li>
									<li class="jsHiddenFlg toggleContents">運航に関する情報をお客様へ通知するため、提携航空会社にもお客様のメールアドレスが提供されます。あらかじめご了承ください。</li>
									<li class="jsHiddenFlg toggleContents"><dl><dt>携帯電話へのメール送信をご希望のお客様へ</dt><dd>ドメイン指定受信をご利用の場合は、ana.co.jpとamadeus.comからのメールが受信できるようあらかじめ設定をお願いいたします。</dd></dl></li>
									<li class="jsHiddenFlg toggleContents">SMSはスマートフォンの番号に配信されます。一部の国ではSMSでのご案内をご利用いただけません。詳細は<a href="https://www.ana.co.jp/other/int/meta/0737.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="ショートメッセージ(SMS)でのご案内対象外の国について(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">ショートメッセージ(SMS)でのご案内対象外の国について<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>をご覧ください。</li>
									<li class="jsHiddenFlg toggleContents">ログインされている会員ご本人が搭乗しない場合は、再度フライト検索画面より「ログインされている会員ご本人は搭乗しない」のチェックボックスを選択してお手続きください。</li>
						</ul>
							<a href="#" class="toggleSwitch jsTriggerSwitch" role="button" aria-controls="informationMessages" aria-expanded="false"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/toggle_open.png?7bec38a" alt="開閉ボタン" height="25" width="25" class="jsRollOver" />
							</a>
					</dd>
				</dl></div>
	<!--=========== /main =============-->
	</div>

	<!--=========== Script ============-->
	<script type="text/javascript">
	$(window).on('load', function() {
		// アコーディオンのメソッドと設定
		Asw.AccordionInfo('.jsAccordionSwitch', {
			'triggerSwitch': '.jsAccordionSwitch .jsTriggerSwitch',
			'listClass': '.jsAccordionSwitchList',
			'visibilityHidden': 'toggleContents'
		});
	});
	</script>
	<!--=========== Script ============-->
	<script type="text/javascript">
		//プラットフォーム判別（サマリーエリア追従用として）
		var Asw = Asw || {};
	
	  	Asw.ClientInfo = Asw.ClientInfo  || {};
	  	Asw.ClientInfo.DeviceType = Asw.ClientInfo.DeviceType || {};
	
	  	Asw.ClientInfo.DeviceType.PC = 'PC';
	  	Asw.ClientInfo.DeviceType.SP = 'SP';
	  	Asw.ClientInfo.DeviceType.TC = 'TC';
	
	  	Asw.ClientInfo.deviceType = Asw.ClientInfo.DeviceType.PC;
	
	 	if((navigator.userAgent.indexOf('Android') > 0 && navigator.userAgent.indexOf('Mobile') == -1) || navigator.userAgent.indexOf('iPad') > 0){
	    	Asw.ClientInfo.deviceType = Asw.ClientInfo.DeviceType.TC;
	  	}
	</script>
	<!--=========== /Script ============--><div id="summaryArea">
			<div class="flightSummaryArea">
						<div class="flightSummarySection">
							<h2>区間 1
							</h2>
							<p class="flightDate"><em>6</em>月<em class="hasLeft">24</em>日（火）
							</p>
							<div class="flightSummaryRoute">
									<div class="flightSummaryRouteSection">
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation1_1">
													<span>東京(成田)
													</span>
												</p>
												<p class="time">
													<span>17:00
													</span>
												</p>
											</div>
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation1_1">
													<span>シカゴ(ORD)
													</span>
												</p>
												<p class="time">
													<span>14:55
													</span>
												</p>
											</div>
										<p class="flightNumberClass">
												<span>NH012</span><span><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/flight_ana_summaryarea.png?717d3c0" alt="ANA運航便" height="11" width="41" /></span>
												<br />特典ビジネス
										</p>
									</div>
							</div>
						</div>
						<div class="flightSummarySection">
							<h2 class="midstream">区間 2
							</h2>
							<p class="flightDate"><em>7</em>月<em class="hasLeft">23</em>日（水）
							</p>
							<div class="flightSummaryRoute">
									<div class="flightSummaryRouteSection">
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation2_1">
													<span>ホノルル(オアフ島)
													</span>
												</p>
												<p class="time">
													<span>11:35
													</span>
												</p>
											</div>
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation2_1">
													<span>東京(成田)
													</span>
												</p>
												<p class="time">
													<span>14:50
																	<span>翌日
																	</span>
													</span>
												</p>
											</div>
										<p class="flightNumberClass">
												<span>NH183</span><span><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/flight_ana_summaryarea.png?717d3c0" alt="ANA運航便" height="11" width="41" /></span>
												<br />特典エコノミー
										</p>
									</div>
							</div>
						</div>
					<div class="total">
						<p class="passenger">大人 x1
						</p>
						<div class="totalArea">
					  		<dl class="mealAmountWrap">
									<dt>有料機内食サービス
									</dt>
									<dd><em class="mealAmount">0</em><span class="currencyCode">円</span>
									</dd>
							</dl>
									<div>
										<div class="mileageAndTotal">
												<dl class="requiredMileage">
													<dt>必要マイル
													</dt>
													<dd><em class="requiredMileage">74,000</em><span class="currencyCode">マイル</span>
													</dd>
												</dl>
												<dl>
													<dt>総額
													</dt>
													<dd><em class="awardTotalPayment">68,130</em>円
													</dd>
												</dl>
												<p class="includeFareAndFuelCost">※各種税金、燃油特別付加運賃等を含みます。
												</p>
										</div>
									</div>
						</div>
					</div>
				
			</div></div>
		<!--===== /cmnWrapper =====-->
		</div>
			
		<span id="message" class="visuallyHidden" role="alert" aria-live="assertive"></span>
		<span id="politeMessage" class="visuallyHidden" role="log" aria-live="polite"></span><script type="text/javascript">
		var Asw = Asw || {};
		Asw.BaseOutput = {"sessionID":"numHhLrlMq","language":"ja","siteCatalystLanguageCode":"J","operationDate":"20250619","operationDateTime":"20250619182845","office":{"officeCode":"TYONH08ZZ","country2letterCode":"JP"},"bookingType":"A","pageID":"A04_P01","siteCatalystPageName":"INT_BE_AWARD_J_A04特典予約_P01旅客情報入力（必須情報）","device":{"deviceType":"PC"}};
	</script><div id="promotionArea">
		<!--===== promotionArea =====-->
		<!--===== /promotionArea =====--></div>
		<!--=========== /cmnContainer ============--></div><div id="cmnBaloonArea" aria-live="polite"></div><div id="cmnPopupArea" aria-live="polite"></div>

	

	<div id="cmnFooterWrapper">
		<!--=========== .cmnFooterWrapper ============-->
		<div id="cmnFooter">
			<!--===== cmnFooter =====-->
			<ul>
				<li><a href="https://www.ana.co.jp/other/int/meta/0012.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">プライバシーポリシー<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
					<li><a href="https://www.ana.co.jp/other/int/meta/0670.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">利用者情報の外部送信について<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
					</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/0052.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">サイト利用規約<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/be0238.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">推奨環境<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/0133.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">サイトマップ<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/0732.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">運送約款<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
					<li class="lastChild"><a href="https://www.ana.co.jp/other/int/meta/0556.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">ウェブアクセシビリティについて<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
					</li>
			</ul>
			<p class="blankText" aria-hidden="true"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="" height="10" width="10" />外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。</p>
			<p class="copyright"><span lang="en" xml:lang="en">Copyright&#169;ANA</span>
			</p>
			<p class="starAllianceLogo"><a href="http://www.staralliance.com/ja/" target="_blank"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/star_alliance_logo.png?0fe73b5" alt="A STAR ALLIANCE MEMBER" height="18" width="175" /><span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
			</p>
			<!--===== cmnFooter =====-->
		</div>
		<!--=========== /cmnFooterWrapper ============-->
	</div>

		
		<div id="cmnLoadingForAjax"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/bg_modalWindow.png?717d3c0" alt="" height="0" width="0" />
			<div class="loadingArea">
				<p class="loadingImage"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/loading.gif?717d3c0" alt="リクエストを処理しています...しばらくお待ちください。" height="33" width="33" />
				</p>
			</div>
		</div>
		
		
		<div id="popupAltSentence" style="display: none" aria-hidden="true">別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。</div>
	<div id="cmnSiteCatalyst" aria-hidden="true"><div id="cmnSiteCatalystParamArea"><script type="text/javascript">
				// ▼SiteCatalyst
				// SiteCatalyst code version: H.2.
				// Copyright 1997-2005 Omniture, Inc. More info available at http://www.omniture.com
				SiteCatalystReportSuites   = "INT";
				SiteCatalystCharSet        = "UTF-8";
				SiteCatalystChannel        = "BE_AWARD_J";
				SiteCatalystPageName       = "INT_BE_AWARD_J_A04\u7279\u5178\u4E88\u7D04_P01\u65C5\u5BA2\u60C5\u5831\u5165\u529B\uFF08\u5FC5\u9808\u60C5\u5831\uFF09";
				SiteCatalystERR            = "";
				SiteCatalystEventNum       = "";
				SiteCatalystPurchase       = "FALSE";
				SiteCatalystProductPrice   = "";
				SiteCatalystCurrencyCode   = "";
				SiteCatalystSeatAvailabilityINT   = "";
				SiteCatalystDepartDate     = "";
				SiteCatalystDepart         = "";
				SiteCatalystArrive         = "";
				SiteCatalystFlightNo       = "";
				SiteCatalystClass          = "";
				SiteCatalystFare           = "";
				SiteCatalystPurchaseID     = "";
				
				SiteCatalystLocater        = "";
				SiteCatalystPayment        = "";
				SiteCatalystIncome         = "";
				
				SiteCatalystPax            = "";
				SiteCatalystManipulateDay  = "";
				SiteCatalystCompartmentClass   = "";
				SiteCatalystOptionInfo     = "";
				SiteCatalystOutboundUpsellPrice   = "";
				SiteCatalystInboundUpsellPrice    = "";
				
				SiteCatalystSearchInput    = "";
				SiteCatalystSegment        = "";
				SiteCatalystOfficeCode     = "";
				SiteCatalystPassengerCount = "";
				SiteCatalystPassenger      = "";
				SiteCatalystAncillaryService = "";
				// End SiteCatalyst code version: H.2.
				// ▲SiteCatalyst
			</script></div><script type="text/javascript"><!-- if(navigator.appVersion.indexOf('MSIE')>=0)document.write(unescape('%3C')+'\!-'+'-') // --></script>
		<!--/DO NOT REMOVE/-->
		

		
		<!-- Google Tag Manager -->
		<script type="text/javascript" charset="Shift_JIS" src="https://www.ana.co.jp/common/js/GTMINTBE.js"></script>
		<!-- End Google Tag Manager -->
		<!-- Yahoo Tag Manager -->
		<script type="text/javascript" charset="Shift_JIS" src="https://www.ana.co.jp/common/js/YTMINTBE.js"></script>
		<!-- End Yahoo Tag Manager -->
		

	</div>

	

	<div class="mboxDefault"></div><script type="text/javascript">mboxCreate("ASW_common_A04_P01");</script>
			<script type="text/javascript" src="https://www.ana.co.jp/common/js/tealium/tealium.js"></script>
			<script type="text/javascript" src="//cdn.evgnet.com/beacon/allnipponairways/nonamcprd/scripts/evergage.min.js"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/javax.faces.resource/jsf.js.xhtml?ln=javax.faces"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/jquery.mousewheel.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/jquery.jscrollpane.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/jquery-ui-1.10.4.custom.min.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/jquery.ui.touch-punch.min.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/asw-common.js?beb6a50"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/asw-common-pc.js?fd88d7d"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/asw-format.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/mask.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/loading.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/asw-loadingwindow-pc.js?9e233c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/asw-loadingwindow.js?51db600"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/dialog-pc.js?25d36a4"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/asw-dialog-sequencer.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/asw-event.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/asw-date-select.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/asw-calendar-pc.js?9b86b8a"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/asw-calendar.js?db624c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/heightLine.js?0e7a1ce"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/asw-summaryarea.js?b21c6d7"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/asw-fingerprints.js?99001fc"></script><script type="text/javascript">
			Asw.init("https://aswbe-i.ana.co.jp/rei22h/international_asw", "ja");
			Asw.getCalendarHolidayList = function() {return [20250721,20250811,20250915,20250923,20251013,20251103,20251123,20251124,20260101,20260112,20260211,20260223,20260320,20260429,20260503,20260504,20260505,20260506,20260720,20260811,20260921,20260922,20260923,20261012,20261103,20261123,20270101,20270111,20270211,20270223,20270321,20270322,20270429,20270503,20270504,20270505,20270719,20270811,20270920,20270923,20271011,20271103,20271123,20280101,20280110,20280211,20280223,20280320,20280429,20280503,20280504,20280505,20280717,20280811,20280918,20280922,20281009,20281103,20281123,20290101,20290108,20290211,20290212,20290223,20290320,20290429,20290430,20290503,20290504,20290505,20290716,20290811,20290917,20290923,20290924,20291008,20291103,20291123];}
			Asw.ClientInfo.deviceType = "PC";
			Asw.ClientInfo.mobileDeviceType = "UNKNOWN";
			Asw.ClientInfo.osVersion = "";
			Asw.Format.init("https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/{1}/{2}/{3}?{0}");
			Asw.overwriteTargetAttributeValue("false");
		</script><script type="text/javascript">
		
		var Asw = Asw || {};
		Asw.ContactInput = Asw.ContactInput || {};
		Asw.ContactInput.Const = {
			/** data-属性名「data-type」 @constant @type {String} @default 'data-type' */
			DATA_ATTRIBUTE_NAME_TYPE: 'data-type',
			/** data-属性名「data-sms-possible」 @constant @type {String} @default 'data-sms-possible' */
			DATA_ATTRIBUTE_NAME_SMS_POSSIBLE: 'data-sms-possible',
			/** data-属性名「data-country-code」 @constant @type {String} @default 'data-country-code' */
			DATA_ATTRIBUTE_NAME_COUNTRY_CODE: 'data-country-code',
			/** data-type属性値「sms」 @constant @type {String} @default 'sms' */
			DATA_TYPE_VALUE_SMS: 'sms'
		};

		
		Asw.ContactInput.countryInfoMap = {"IS":{"internationalCallingCode":"+354","smsPossible":false},"IE":{"internationalCallingCode":"+353","smsPossible":true},"AZ":{"internationalCallingCode":"+994","smsPossible":true},"AF":{"internationalCallingCode":"+93","smsPossible":true},"US":{"internationalCallingCode":"+1","smsPossible":true},"AE":{"internationalCallingCode":"+971","smsPossible":true},"DZ":{"internationalCallingCode":"+213","smsPossible":true},"AR":{"internationalCallingCode":"+54","smsPossible":true},"AW":{"internationalCallingCode":"+297","smsPossible":true},"AL":{"internationalCallingCode":"+355","smsPossible":true},"AM":{"internationalCallingCode":"+374","smsPossible":true},"AO":{"internationalCallingCode":"+244","smsPossible":false},"AG":{"internationalCallingCode":"+1","smsPossible":true},"AD":{"internationalCallingCode":"+376","smsPossible":true},"YE":{"internationalCallingCode":"+967","smsPossible":true},"GB":{"internationalCallingCode":"+44","smsPossible":true},"IL":{"internationalCallingCode":"+972","smsPossible":true},"IT":{"internationalCallingCode":"+39","smsPossible":true},"IQ":{"internationalCallingCode":"+964","smsPossible":true},"IR":{"internationalCallingCode":"+98","smsPossible":true},"IN":{"internationalCallingCode":"+91","smsPossible":true},"ID":{"internationalCallingCode":"+62","smsPossible":true},"UG":{"internationalCallingCode":"+256","smsPossible":true},"UA":{"internationalCallingCode":"+380","smsPossible":true},"UZ":{"internationalCallingCode":"+998","smsPossible":true},"UY":{"internationalCallingCode":"+598","smsPossible":true},"EC":{"internationalCallingCode":"+593","smsPossible":true},"EG":{"internationalCallingCode":"+20","smsPossible":true},"EE":{"internationalCallingCode":"+372","smsPossible":true},"SZ":{"internationalCallingCode":"+268","smsPossible":false},"ET":{"internationalCallingCode":"+251","smsPossible":false},"ER":{"internationalCallingCode":"+291","smsPossible":true},"SV":{"internationalCallingCode":"+503","smsPossible":true},"AU":{"internationalCallingCode":"+61","smsPossible":true},"AT":{"internationalCallingCode":"+43","smsPossible":true},"OM":{"internationalCallingCode":"+968","smsPossible":true},"NL":{"internationalCallingCode":"+31","smsPossible":true},"GH":{"internationalCallingCode":"+233","smsPossible":true},"CV":{"internationalCallingCode":"+238","smsPossible":false},"GY":{"internationalCallingCode":"+592","smsPossible":true},"KZ":{"internationalCallingCode":"+7","smsPossible":true},"QA":{"internationalCallingCode":"+974","smsPossible":true},"CA":{"internationalCallingCode":"+1","smsPossible":true},"GA":{"internationalCallingCode":"+241","smsPossible":true},"CM":{"internationalCallingCode":"+237","smsPossible":true},"KR":{"internationalCallingCode":"+82","smsPossible":true},"KH":{"internationalCallingCode":"+855","smsPossible":true},"GM":{"internationalCallingCode":"+220","smsPossible":true},"KP":{"internationalCallingCode":"+850","smsPossible":false},"MK":{"internationalCallingCode":"+389","smsPossible":false},"GN":{"internationalCallingCode":"+224","smsPossible":true},"GW":{"internationalCallingCode":"+245","smsPossible":true},"CY":{"internationalCallingCode":"+357","smsPossible":true},"CU":{"internationalCallingCode":"+53","smsPossible":true},"GR":{"internationalCallingCode":"+30","smsPossible":true},"KI":{"internationalCallingCode":"+686","smsPossible":false},"KG":{"internationalCallingCode":"+996","smsPossible":true},"GT":{"internationalCallingCode":"+502","smsPossible":true},"GU":{"internationalCallingCode":"+1","smsPossible":true},"KW":{"internationalCallingCode":"+965","smsPossible":true},"KY":{"internationalCallingCode":"+1","smsPossible":true},"GD":{"internationalCallingCode":"+1","smsPossible":true},"HR":{"internationalCallingCode":"+385","smsPossible":true},"KE":{"internationalCallingCode":"+254","smsPossible":true},"CI":{"internationalCallingCode":"+225","smsPossible":true},"CR":{"internationalCallingCode":"+506","smsPossible":true},"SA":{"internationalCallingCode":"+966","smsPossible":true},"KM":{"internationalCallingCode":"+269","smsPossible":true},"CO":{"internationalCallingCode":"+57","smsPossible":true},"CG":{"internationalCallingCode":"+242","smsPossible":false},"CD":{"internationalCallingCode":"+243","smsPossible":true},"ZM":{"internationalCallingCode":"+260","smsPossible":true},"SM":{"internationalCallingCode":"+378","smsPossible":false},"SL":{"internationalCallingCode":"+232","smsPossible":true},"DJ":{"internationalCallingCode":"+253","smsPossible":false},"JM":{"internationalCallingCode":"+1","smsPossible":true},"CH":{"internationalCallingCode":"+41","smsPossible":true},"GE":{"internationalCallingCode":"+995","smsPossible":true},"SE":{"internationalCallingCode":"+46","smsPossible":true},"SY":{"internationalCallingCode":"+963","smsPossible":true},"ES":{"internationalCallingCode":"+34","smsPossible":true},"SG":{"internationalCallingCode":"+65","smsPossible":true},"LK":{"internationalCallingCode":"+94","smsPossible":true},"SK":{"internationalCallingCode":"+421","smsPossible":true},"SI":{"internationalCallingCode":"+386","smsPossible":true},"ZW":{"internationalCallingCode":"+263","smsPossible":true},"SD":{"internationalCallingCode":"+249","smsPossible":true},"KN":{"internationalCallingCode":"+1","smsPossible":true},"SR":{"internationalCallingCode":"+597","smsPossible":true},"LC":{"internationalCallingCode":"+1","smsPossible":true},"SC":{"internationalCallingCode":"+248","smsPossible":false},"SN":{"internationalCallingCode":"+221","smsPossible":true},"SB":{"internationalCallingCode":"+677","smsPossible":false},"RS":{"internationalCallingCode":"+381","smsPossible":true},"TH":{"internationalCallingCode":"+66","smsPossible":true},"TW":{"internationalCallingCode":"+886","smsPossible":true},"TJ":{"internationalCallingCode":"+992","smsPossible":true},"TZ":{"internationalCallingCode":"+255","smsPossible":true},"CZ":{"internationalCallingCode":"+420","smsPossible":true},"TD":{"internationalCallingCode":"+235","smsPossible":true},"CF":{"internationalCallingCode":"+236","smsPossible":true},"TN":{"internationalCallingCode":"+216","smsPossible":true},"CL":{"internationalCallingCode":"+56","smsPossible":true},"TV":{"internationalCallingCode":"+688","smsPossible":false},"DK":{"internationalCallingCode":"+45","smsPossible":true},"DE":{"internationalCallingCode":"+49","smsPossible":true},"TG":{"internationalCallingCode":"+228","smsPossible":true},"DO":{"internationalCallingCode":"+1","smsPossible":true},"DM":{"internationalCallingCode":"+1","smsPossible":false},"TT":{"internationalCallingCode":"+1","smsPossible":true},"TM":{"internationalCallingCode":"+993","smsPossible":false},"TR":{"internationalCallingCode":"+90","smsPossible":true},"TO":{"internationalCallingCode":"+676","smsPossible":false},"NG":{"internationalCallingCode":"+234","smsPossible":true},"NR":{"internationalCallingCode":"+674","smsPossible":false},"NA":{"internationalCallingCode":"+264","smsPossible":false},"NI":{"internationalCallingCode":"+505","smsPossible":true},"BH":{"internationalCallingCode":"+973","smsPossible":false},"NE":{"internationalCallingCode":"+227","smsPossible":true},"PK":{"internationalCallingCode":"+92","smsPossible":true},"JP":{"internationalCallingCode":"+81","smsPossible":true},"BS":{"internationalCallingCode":"+1","smsPossible":true},"NZ":{"internationalCallingCode":"+64","smsPossible":true},"BM":{"internationalCallingCode":"+1","smsPossible":true},"NP":{"internationalCallingCode":"+977","smsPossible":true},"BB":{"internationalCallingCode":"+246","smsPossible":true},"NO":{"internationalCallingCode":"+47","smsPossible":true},"HT":{"internationalCallingCode":"+509","smsPossible":true},"BD":{"internationalCallingCode":"+880","smsPossible":true},"VA":{"internationalCallingCode":"+39","smsPossible":false},"PA":{"internationalCallingCode":"+507","smsPossible":true},"VU":{"internationalCallingCode":"+678","smsPossible":false},"PG":{"internationalCallingCode":"+675","smsPossible":true},"PW":{"internationalCallingCode":"+680","smsPossible":true},"PY":{"internationalCallingCode":"+595","smsPossible":true},"PS":{"internationalCallingCode":"+970","smsPossible":true},"BR":{"internationalCallingCode":"+55","smsPossible":true},"HU":{"internationalCallingCode":"+36","smsPossible":true},"TL":{"internationalCallingCode":"+670","smsPossible":true},"BG":{"internationalCallingCode":"+359","smsPossible":true},"BF":{"internationalCallingCode":"+226","smsPossible":true},"BN":{"internationalCallingCode":"+673","smsPossible":true},"FJ":{"internationalCallingCode":"+679","smsPossible":true},"PH":{"internationalCallingCode":"+63","smsPossible":true},"FI":{"internationalCallingCode":"+358","smsPossible":true},"BT":{"internationalCallingCode":"+975","smsPossible":true},"BY":{"internationalCallingCode":"+375","smsPossible":true},"BZ":{"internationalCallingCode":"+501","smsPossible":false},"PR":{"internationalCallingCode":"+1","smsPossible":true},"BE":{"internationalCallingCode":"+32","smsPossible":true},"BA":{"internationalCallingCode":"+387","smsPossible":true},"FR":{"internationalCallingCode":"+33","smsPossible":true},"BO":{"internationalCallingCode":"+591","smsPossible":false},"PF":{"internationalCallingCode":"+689","smsPossible":true},"BI":{"internationalCallingCode":"+257","smsPossible":true},"VN":{"internationalCallingCode":"+84","smsPossible":true},"BJ":{"internationalCallingCode":"+229","smsPossible":true},"VE":{"internationalCallingCode":"+58","smsPossible":true},"PE":{"internationalCallingCode":"+51","smsPossible":true},"PL":{"internationalCallingCode":"+48","smsPossible":true},"BW":{"internationalCallingCode":"+267","smsPossible":true},"PT":{"internationalCallingCode":"+351","smsPossible":false},"HK":{"internationalCallingCode":"+852","smsPossible":true},"HN":{"internationalCallingCode":"+504","smsPossible":false},"MH":{"internationalCallingCode":"+692","smsPossible":true},"MO":{"internationalCallingCode":"+853","smsPossible":true},"MG":{"internationalCallingCode":"+261","smsPossible":true},"MW":{"internationalCallingCode":"+265","smsPossible":true},"ML":{"internationalCallingCode":"+223","smsPossible":true},"MT":{"internationalCallingCode":"+356","smsPossible":true},"MY":{"internationalCallingCode":"+60","smsPossible":true},"FM":{"internationalCallingCode":"+691","smsPossible":false},"MM":{"internationalCallingCode":"+95","smsPossible":true},"MX":{"internationalCallingCode":"+52","smsPossible":true},"MU":{"internationalCallingCode":"+230","smsPossible":true},"MR":{"internationalCallingCode":"+222","smsPossible":true},"MZ":{"internationalCallingCode":"+258","smsPossible":true},"MC":{"internationalCallingCode":"+377","smsPossible":true},"MV":{"internationalCallingCode":"+960","smsPossible":true},"MD":{"internationalCallingCode":"+373","smsPossible":false},"RO":{"internationalCallingCode":"+40","smsPossible":true},"MA":{"internationalCallingCode":"+212","smsPossible":true},"MN":{"internationalCallingCode":"+976","smsPossible":true},"ME":{"internationalCallingCode":"+382","smsPossible":true},"JO":{"internationalCallingCode":"+962","smsPossible":true},"LA":{"internationalCallingCode":"+856","smsPossible":true},"RU":{"internationalCallingCode":"+7","smsPossible":true},"LV":{"internationalCallingCode":"+371","smsPossible":true},"LT":{"internationalCallingCode":"+370","smsPossible":true},"LY":{"internationalCallingCode":"+218","smsPossible":true},"GQ":{"internationalCallingCode":"+240","smsPossible":false},"LI":{"internationalCallingCode":"+423","smsPossible":false},"LR":{"internationalCallingCode":"+231","smsPossible":true},"CN":{"internationalCallingCode":"+86","smsPossible":true},"LU":{"internationalCallingCode":"+352","smsPossible":true},"ZA":{"internationalCallingCode":"+27","smsPossible":true},"RW":{"internationalCallingCode":"+250","smsPossible":true},"LS":{"internationalCallingCode":"+266","smsPossible":false},"LB":{"internationalCallingCode":"+961","smsPossible":true}};

		$(function () {
			contactInfoInit();
		});
		
		function contactInfoInit() {
			
			
			Asw.get('representativeTelType').find('option[value="M1"]').attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_TYPE,
					Asw.ContactInput.Const.DATA_TYPE_VALUE_SMS);
			Asw.get('representativeTelCountry').children('option').each(function(i,option){
				var countryInfo = Asw.ContactInput.countryInfoMap[option.value];
				if (countryInfo === void 0) {
					
					return true;
				}
				$(option).attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_SMS_POSSIBLE, countryInfo.smsPossible);
				$(option).attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_COUNTRY_CODE, countryInfo.internationalCallingCode);
			});
			
			var smsCountrySelectSelector = 'select.js-countrySelect:not(#representativeTelCountry)'
			$(smsCountrySelectSelector).eq(0).children('option').each(function(i,option){
				var countryInfo = Asw.ContactInput.countryInfoMap[option.value];
				if (countryInfo === void 0) {
					
					return true;
				}
				$(smsCountrySelectSelector + ' option:nth-child(' + (i+1) + ')').attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_COUNTRY_CODE,
						countryInfo.internationalCallingCode);
			});
			
			var emergencyCountrySelectSelector = 'select.passengerEmergencyCountry.js-countrySelect:not(#representativeTelCountry)'
			$(emergencyCountrySelectSelector).eq(0).children('option').each(function(i,option){
				var countryInfo = Asw.ContactInput.countryInfoMap[option.value];
				if (countryInfo === void 0) {
					
					return true;
				}
				$(emergencyCountrySelectSelector + ' option:nth-child(' + (i+1) + ')').attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_COUNTRY_CODE,
						countryInfo.internationalCallingCode);
			});
		}
	</script><script type="text/javascript">
		var isInit = true;
		function changeTelType(_this, telNoInputId) {
			var $telNoInput = Asw.get(telNoInputId);
			var $telNoKind = Asw.get("representativeTelType");
			var telNo = Asw.getValueComparedToPlaceholder(telNoInputId);
			var telNoCountry = Asw.get("representativeTelCountry").val();

			if(true && !isInit) {
				if(telInputCheck(telNo, telNoCountry)) {
					if (_this.value == "M1") {
						telNo = exceptDomesticPrefix("09042504176", telNoCountry);
					} else if (_this.value == "B1") {
						telNo = exceptDomesticPrefix("03-5405-2172", telNoCountry);
					} else {
						telNo = exceptDomesticPrefix("090-4250-4176", telNoCountry);
					}
					$telNoInput.val(telNo);
				}
			}
			isInit = false;
		}
		
		function telInputCheck(telNo, telNoCountry) {
	        checkResult = false;
	        
	        var amcHndyTelNo = exceptDomesticPrefix("09042504176", telNoCountry);
	        var amcCmpTelNo = exceptDomesticPrefix("03-5405-2172", telNoCountry);
	        var amcTelNo = exceptDomesticPrefix("090-4250-4176", telNoCountry);
	
	        if(telNo == "" || telNo == amcHndyTelNo || telNo == amcCmpTelNo || telNo == amcTelNo) {
	        	checkResult = true;
	        }
	        return checkResult;
    	}
    	
    	function exceptDomesticPrefix(telNo, telNoCountry) {
    		if(telNoCountry == 'JP') {
    			if(telNo.substr(0, 1) === '0') {
    				return telNo.slice(1);
    			}
    		}
    		return telNo;
    	}
	</script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/asw-customerinfomationform.js?6e91953"></script><script type="text/javascript">
		$(document).ready(function(){
				Asw.enableTextWhenCheckBox('.cascadedSwitch');
				Asw.notPasteInput('.jsNotPasteInput');
			$(function(){
				Asw.alignHeightToParentTdElement( '.passengerInfoInputArea' );
				
					Asw.alignHeightToParentTdElement( '.passengerInfoInputText' );
				
				
				$('select.js-countrySelect').trigger('change');
			});
		});
	</script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/asw-session-keeper.js?6ec0730"></script><script type="text/javascript">
			$(document).ready(function() {
				Asw.SessionKeeper.initAndStart("sessionKeeperContainer", "520", "570", "PC");
			});
			</script><script type="text/javascript">
		Asw.addAjaxCallback(function(data) {
			// アコーディオンのメソッドと設定 
			if($('.jsAccordionSwitch .jsTriggerSwitch').length > 0){
				var events = $._data($('.jsAccordionSwitch .jsTriggerSwitch').get(0), "events");
				if(events === undefined || events.click === undefined) {
					Asw.AccordionInfo('.jsAccordionSwitch', {
						'triggerSwitch': '.jsAccordionSwitch .jsTriggerSwitch',
						'listClass': '.jsAccordionSwitchList',
						'visibilityHidden': 'toggleContents'
					});
				}
			}
		});
	</script><script type="text/javascript">
	Asw.Price = Asw.Price || {};
	// 整数の区切り文字
	Asw.Price.separateString = ',';
	
	// 整数の区切り桁数
	Asw.Price.separateDigitNumber = 3;
	
	// 小数点
	Asw.Price.decimalPointString = '.';
	
	// 小数点以降の有効桁数
	Asw.Price.significantDigit = 0;
	
	// 表示金額の単位
	Asw.Price.currencyUnit = '円';
	</script><script type="text/javascript">
			$(document).ready(function() {
				$('#preLoadingArea').css('visibility', 'hidden');
			});
		</script><noscript><img src="https://aswbe-i.ana.co.jp/akam/13/pixel_7301db7c?a=dD04YjhkYmVhZTFjOTljZTgyNjFmOWU3ZTJiZDJlNThlOTg3ZjY4NTFjJmpzPW9mZg==" style="visibility: hidden; position: absolute; left: -999px; top: -999px;" /></noscript><script type="text/javascript"  src="/RcS9yzCRgrNnM/gY/v0Wf9fQwtpdQ/7NE3X2ruOE2SL9YE/HyNpQmYB/RWt/XOHcnHAU"></script></body>
</html>