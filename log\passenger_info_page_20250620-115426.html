<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="ja-<PERSON>" xml:lang="ja-<PERSON>">

	
	<!-- skey = 2025/06/20 12:54:26 rei21d TwWLeQnhtl dljdmx+6a3  --><head id="j_idt53">
    <base href="about:blank">
    <style>
        /* Hide elements that might try to load external resources, as a fallback. */
        img, svg, video, iframe, link[rel="stylesheet"], script { display: none !important; }
    </style>

		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
			<meta http-equiv="X-UA-Compatible" content="IE=EmulateIE9; IE=EmulateIE10" />
			<meta http-equiv="X-UA-Compatible" content="IE=7" />
			<meta http-equiv="X-UA-Compatible" content="IE=edge" />
		<meta name="description" content="" />
		<meta name="keywords" content="" />
		<meta name="format-detection" content="telephone=no" />
		<script type="text/javascript" src="/international_asw/dynatrace/ruxitagentjs_ICANVfhqrux_10273230920145353.js" data-dtconfig="rid=RID_-*********|rpid=-*********|domain=aswbe-i.ana.co.jp|reportUrl=https://bf90419wvr.bf.dynatrace.com/bf|app=78bf0b58acf6ed13|cors=1|owasp=1|featureHash=ICANVfhqrux|xb=www^bs.google-analytics^bs.com^pana^bs.demdex^bs.net^pcollect^bs.tealiumiq^bs.com^pallnipponairways^bs.australia-3^bs.evergage^bs.com^pallnipponairwayscolt^bs.tt^bs.omtrdc^bs.net|rdnt=1|uxrgce=1|cuc=k8vpgh61|mel=100000|expw=1|dpvc=1|md=mdcc1=bAsw.PnrOutput.recordLocator,mdcc2=bAsw.PnrOutput.paymentDetailInfoList[0].fopCode,mdcc3=bAsw.PnrOutput.ticketedFlag,mdcc4=bAsw.PnrOutput.nhPnrFlag,mdcc5=bAsw.PnrOutput.itinerarySummary.departureDateYyyymmdd,mdcc6=bAsw.SearchCriteriaOutput.cffCodeInput,mdcc7=a#main ^rb div.section.sectionNoFlow ^rb table ^rb tbody ^rb tr ^rb td.flightNumber.lastCol ^rb span ^rb span:nth-child(2),mdcc8=cunique|lastModification=1750184416409|tp=500,50,0|agentUri=/international_asw/dynatrace/ruxitagentjs_ICANVfhqrux_10273230920145353.js"></script><link rel="shortcut icon" href="https://www.ana.co.jp/favicon.ico" />
		<title>お客様情報入力 | ANA</title>
		<noscript><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei21d/international_asw/contents/cmn/css/asw_noscript_pc.css?99001fc" />
		</noscript>

		<!--  [ Adobe Target ] -->
		<script type="text/javascript" src="https://www.ana.co.jp/behavior/adobetarget/target_intbe.js"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21d/international_asw/contents/cmn/script/jquery-1.8.3.min.js?717d3c0"></script><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei21d/international_asw/contents/ja/css/asw_initialize.css?99001fc" /><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei21d/international_asw/contents/cmn/css/asw_screen_pc.css?99001fc" /><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei21d/international_asw/contents/cmn/css/asw_common_pc.css?99001fc" /><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei21d/international_asw/contents/cmn/css/asw_forminput_pc.css?99001fc" /><script type="text/javascript">
		var Asw = Asw || {};
		Asw.GlobalWord = Asw.GlobalWord || {};
		
		Asw.GlobalWord.toolTipCloseIconAltText = "閉じる";
		
		</script><script >bazadebezolkohpepadr="1348689485"</script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/akam/13/50635fd6"  defer></script></head><body>
		
		<noscript>当サイトをご利用になるためには、JavaScript対応のブラウザが必要です。設定でJavaScriptを有効にしてください。有効にできない場合はANA電話窓口へお問い合わせください。
		</noscript>
		<div id="preLoadingArea"></div>
	
	<div id="cmnHeader" class="platinum">
		<!--===== cmnHeader =====-->
		<div class="headerWrapper">
			<div class="header">
				<p class="logo"><a href="https://www.ana.co.jp/asw/AMCTopServlet?type=a" tabindex="-1" aria-hidden="true"><img src="https://aswbe-i.ana.co.jp/rei21d/international_asw/contents/cmn/image/ana_logo_navy.png?717d3c0" alt="ANA Inspiration of JAPAN。リンク先はマイレージクラブトップページです。" height="28" width="274" /></a>
				</p>
					<ul class="headerMenu">
						<li><a href="https://www.ana.co.jp/asw/AMCTopServlet?type=a">ANAマイレージクラブトップページ</a>
						</li>
						<li><a href="https://www.ana.co.jp/other/int/meta/0005.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">お問い合わせ<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21d/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
						</li>
							<li class="btnLogoutArea"><a href="https://aswbe-i.ana.co.jp/rei21d/international_asw/rest/logout" role="button" class="btnBase btnLogout">ログアウト</a>
							</li>
					</ul>
			</div>
		</div>
			<div class="amcUserInfoWrapper">
				<div class="amcUserInfo">
					<div class="userNameArea">
						<table>
							<tr>	
									<td class="statusIcon"><img src="https://aswbe-i.ana.co.jp/rei21d/international_asw/contents/cmn/image/icon_status_05.png?717d3c0" alt="プラチナサービスメンバー" height="46" width="60" />
									</td>
								<td class="userName">佐藤 直樹 <span>様</span>
								</td>
							</tr>					
						</table>
					</div>
						<div class="balance">
								<dl class="upgradePointBalance">
									<dt>アップグレードポイント残高
									</dt>
									<dd>
											<dl>
												<dt>(2025年度)
												</dt>
												<dd>
													<em>20
													</em>ポイント
												</dd>
											</dl>
									</dd>
								</dl>
							<dl class="mileBalance">
								<dt>マイル口座残高
								</dt>
								<dd><em>75,663</em><span class="currencyCode">マイル</span>
								</dd>
							</dl>
						</div>
				</div>
					<p class="nowTime">6月20日12時54分現在
					</p>
			</div>
		<!--===== /cmnHeader =====-->
	</div>
		<div id="sessionKeeperContainer" class="sessionKeepingContainer"><img src="https://aswbe-i.ana.co.jp/rei21d/international_asw/contents/cmn/image/bg_modalWindow.png?717d3c0" alt="" height="0" width="0" />
			<div id="extendSessionExpiredDialog" class="modalExpire modalSmall modalContainer" role="dialog" aria-labelledby="extendSessionExpiredDialogTitle" aria-hidden="true">
<form id="sessionKeeperContainer:j_idt183" name="sessionKeeperContainer:j_idt183" method="post" action="https://aswbe-i.ana.co.jp/rei21d/international_asw/pages/award/reservation/award_mandatory_passenger_information_input.xhtml?aswcid=1&amp;rand=20250620125426TwWLeQnhtl" autocomplete="off" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="sessionKeeperContainer:j_idt183" value="sessionKeeperContainer:j_idt183" />
<input type="hidden" name="sessionKeeperContainer:j_idt183_operationTicket" value="dljdmx+6a365d7f0f6638c20e3ce717bee9817c~AZZYhlBMs9NxsjzRj7dNrr_AWFI6v1BAfRChx_25!1750391658051.aere-xml-controller-67d4778877-tmwgp" /><input type="hidden" name="sessionKeeperContainer:j_idt183_cmnPageTicket" value="4" />
					<h1 id="extendSessionExpiredDialogTitle" class="dialogTitle">まもなくセッションが終了します</h1>
					<div id="extendSessionExpiredDialogMessage" class="dialogMessage" tabindex="-1">このままお手続きを進める場合は、［延長］ボタンを押してください。</div>
					<p class="modalButton"><input id="sessionKeeperContainer:cmnSessionKeepingButton" type="submit" name="sessionKeeperContainer:cmnSessionKeepingButton" value="延長" class="btnBase btnModal btnMainStream" onclick="mojarra.ab(this,event,'action',0,0,{'onevent':function(data) {Asw.doCommonAjaxCallback(data,false, event); Asw.SessionKeeper.doSessionKeep(data);},'onerror':function(data) {Asw.doCommonAjaxErrorCallback(data); }});return false" />
					</p><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:0" value="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" autocomplete="off" />
</form>
			</div>
			<div id="showSessionExpiredDialog" class="modalExpire modalSmall modalContainer" role="dialog" aria-labelledby="showSessionExpiredDialogTitle" aria-hidden="true">
				<h1 id="showSessionExpiredDialogTitle" class="dialogTitle">セッションが終了しました</h1>
				<div id="showSessionExpiredDialogMessage" class="dialogMessage" tabindex="-1">一定時間操作されなかったため、お手続きを進めることができません。お手数ですが、再度トップページからお手続きください。</div>
				<p class="modalButton">
					<input type="submit" value="閉じる" class="btnBase btnModal btnMainStream" onclick="Asw.SessionKeeper.closeTimeoutDialog(); return false;" />
				</p>
			</div>
		</div>
		<!--===== dialogMessages =====--><div id="cmnModalMessages"></div>
		<div id="prebookConfirmDialog" class="modalContainer modalConfirm modalSmall " aria-hidden="true" role="dialog">
<form id="prebookConfirmDialog:j_idt403" name="prebookConfirmDialog:j_idt403" method="post" action="https://aswbe-i.ana.co.jp/rei21d/international_asw/pages/award/reservation/award_mandatory_passenger_information_input.xhtml?aswcid=1&amp;rand=20250620125426TwWLeQnhtl" autocomplete="off" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="prebookConfirmDialog:j_idt403" value="prebookConfirmDialog:j_idt403" />
<input type="hidden" name="prebookConfirmDialog:j_idt403_operationTicket" value="dljdmx+6a365d7f0f6638c20e3ce717bee9817c~AZZYhlBMs9NxsjzRj7dNrr_AWFI6v1BAfRChx_25!1750391658051.aere-xml-controller-67d4778877-tmwgp" /><input type="hidden" name="prebookConfirmDialog:j_idt403_cmnPageTicket" value="4" /><div class="dialogMessage" tabindex="0"><p>この後、名・姓の修正や変更ができません。</p><p>「搭乗者選択」欄の氏名が「名」「姓」の順番で表示されていることを再確認ください。</p><p>例）<strong class="highlight">空野 太郎</strong> 様 → MR <strong class="highlight">TARO SORANO</strong></p><p>保安上の理由により、航空券とパスポートのお名前が同じ表記でない場合はご搭乗いただくことができませんので必ず再度ご確認ください。</p></div>
					
					<ul class="modalBtnHorizontal">
							
							<li><input type="submit" name="prebookConfirmDialog:j_idt419" value="戻る" aria-controls="prebookConfirmDialog" class="btnBase btnModal btnCancel" onclick="Asw.Dialog.getInstance('prebookConfirmDialog').close(event, Asw.DialogSequencer.isActiveSequencerNotDisplayedLastDialog());  return false;" />
							</li>
						
						<li class="btnArrowNext"><input type="submit" name="prebookConfirmDialog:j_idt422" value="OK" aria-controls="prebookConfirmDialog" class="btnBase btnModal btnMainStream " onclick="Asw.Dialog.getInstance('prebookConfirmDialog').callOpener('NORMAL', event); return false;" />
						</li>
					</ul><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:1" value="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" autocomplete="off" />
</form>
		</div>
		<!--===== /dialogMessages =====--><div id="cmnContainer">
		<!--=========== .cmnContainer ============--><div id="cmnEmergencyNotice"></div><div id="cmnTitle" class="hgroup ">
		<h1 class="visuallyHidden">お客様情報入力</h1></div>

		<div id="cmnWrapper">
		<!--===== cmnWrapper =====-->
		<ol class="mainFlow" aria-label="完了までの手順、">
			<li><img src="https://aswbe-i.ana.co.jp/rei21d/international_asw/contents/cmn/image/icon_flow_01.png?717d3c0" alt="1" height="20" width="28" />フライト検索</li><!--
			--><li><img src="https://aswbe-i.ana.co.jp/rei21d/international_asw/contents/cmn/image/icon_flow_02_on.png?717d3c0" alt="2" height="20" width="28" /><span class="visuallyHidden">現在のステップ、</span>お客様情報入力</li><!--
			--><li><img src="https://aswbe-i.ana.co.jp/rei21d/international_asw/contents/cmn/image/icon_flow_03.png?717d3c0" alt="3" height="20" width="28" />お支払い情報</li><!--
			--><li class="flowLast"><img src="https://aswbe-i.ana.co.jp/rei21d/international_asw/contents/cmn/image/icon_flow4_04.png?717d3c0" alt="4" height="20" width="28" />完了</li>
		</ol>

	<div id="main">
	<!--=========== main =============-->
		<ul class="iconExample">
				<li class="requiredIconExample">
					<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei21d/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="" height="7" width="7" /></span>必須項目です	
				</li>
		</ul>
<form id="j_idt1529" name="j_idt1529" method="post" action="https://aswbe-i.ana.co.jp/rei21d/international_asw/pages/award/reservation/award_mandatory_passenger_information_input.xhtml?aswcid=1&amp;rand=20250620125426TwWLeQnhtl" autocomplete="off" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="j_idt1529" value="j_idt1529" />
<input type="hidden" name="j_idt1529_operationTicket" value="dljdmx+6a365d7f0f6638c20e3ce717bee9817c~AZZYhlBMs9NxsjzRj7dNrr_AWFI6v1BAfRChx_25!1750391658051.aere-xml-controller-67d4778877-tmwgp" /><input type="hidden" name="j_idt1529_cmnPageTicket" value="4" />

<div class="section">
	<h2>搭乗者選択<a href="https://www.ana.co.jp/other/int/meta/0086.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="特典のご利用に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei21d/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a></h2>
	<table class="passengerInfo">
		<thead>
			<tr>
				
				<th class="name">氏名</th>
				
				<th class="dateOfBirthWide" id="dateOfBirthHeader">生年月日</th>
				
				<th class="gender lastCol" id="genderHeader">性別<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei21d/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span></th>
				
			</tr>
		</thead>
		<tbody>
				
				<tr>
					<th class="parent_01" id="parentHeader01">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:0:passenger" type="checkbox" name="passengers:0:passenger" checked="checked" data-number="1" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:0:passenger">NAOKI SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader01">1963年06月05日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader01">				
							<span class="formWrapper">
							<label for="passengers:0:gender" class="visuallyHidden">NAOKI SATOの性別</label><select id="passengers:0:gender" name="passengers:0:gender" size="1">	<option value="">選択してください</option>
	<option value="M" selected="selected">男性</option>
	<option value="F">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_02" id="parentHeader02">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:1:passenger" type="checkbox" name="passengers:1:passenger" data-number="2" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:1:passenger">REO SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader02">1995年05月18日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader02">				
							<span class="formWrapper">
							<label for="passengers:1:gender" class="visuallyHidden">REO SATOの性別</label><select id="passengers:1:gender" name="passengers:1:gender" size="1">	<option value="">選択してください</option>
	<option value="M" selected="selected">男性</option>
	<option value="F">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_03" id="parentHeader03">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:2:passenger" type="checkbox" name="passengers:2:passenger" data-number="3" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:2:passenger">ERI SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader03">1963年08月14日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader03">				
							<span class="formWrapper">
							<label for="passengers:2:gender" class="visuallyHidden">ERI SATOの性別</label><select id="passengers:2:gender" name="passengers:2:gender" size="1">	<option value="">選択してください</option>
	<option value="M">男性</option>
	<option value="F" selected="selected">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_04" id="parentHeader04">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:3:passenger" type="checkbox" name="passengers:3:passenger" data-number="4" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:3:passenger">TAISHI SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader04">1992年12月03日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader04">				
							<span class="formWrapper">
							<label for="passengers:3:gender" class="visuallyHidden">TAISHI SATOの性別</label><select id="passengers:3:gender" name="passengers:3:gender" size="1">	<option value="">選択してください</option>
	<option value="M" selected="selected">男性</option>
	<option value="F">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_05" id="parentHeader05">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:4:passenger" type="checkbox" name="passengers:4:passenger" data-number="5" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:4:passenger">HIROMI FUJIMOTO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader05">1961年11月23日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader05">				
							<span class="formWrapper">
							<label for="passengers:4:gender" class="visuallyHidden">HIROMI FUJIMOTOの性別</label><select id="passengers:4:gender" name="passengers:4:gender" size="1">	<option value="">選択してください</option>
	<option value="M">男性</option>
	<option value="F" selected="selected">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
		</tbody>
	</table>
</div>
		<div class="section">
			<h2 class="midstream">連絡先情報
			</h2>
			<h3>代表者
			</h3>
	
	
	
	<div class="formInput">
		<fieldset>
			<legend><label>代表者</label></legend>
			<dl id="contactEmailAddressInfo">
				<dt class="hasRequiredIcon">メールアドレス<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei21d/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span><a href="https://www.ana.co.jp/other/int/meta/0057.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="メールの宛先と内容に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei21d/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a>
				</dt>
				<dd>
					<ul class="verticalList">
						<li class="nonMarginTop"><label for="representativeEmail" class="visuallyHidden">代表者メールアドレス</label><input id="representativeEmail" type="text" name="representativeEmail" value="<EMAIL>" class="widthLarge" placeholder="例：<EMAIL>" />
						</li>
						<li>
							<span class="supplementTxt" id="confirmMailAddressForRepresentative">確認のため再度ご入力ください。
							</span><label for="confirmrepresentativeEmail" class="visuallyHidden">確認用メールアドレス</label><input id="confirmrepresentativeEmail" type="text" name="confirmrepresentativeEmail" value="<EMAIL>" class="widthLarge jsNotPasteInput" placeholder="例：<EMAIL>" aria-describedby="confirmMailAddressForRepresentative" />
						</li>
					</ul>
				</dd>
					<dd style="display:none;"><input id="updateAlwaysInfo" type="checkbox" name="updateAlwaysInfo" /><label for="updateAlwaysInfo">ログイン中の会員情報へこのメールアドレスを登録</label>
					</dd>
			</dl>
			<dl>
				<dt>電話番号<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei21d/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span>
				</dt>
				<dd class="js-countryWrap">
					<ul>
						<li><label for="representativeTelType" class="visuallyHidden">電話番号種別</label><select id="representativeTelType" name="representativeTelType" class="basicWidth js-typeSelect" size="1" onchange="changeTelType(this,'representativeTel')">	<option value="M1" selected="selected">携帯</option>
	<option value="B1">会社</option>
	<option value="H1">自宅</option>
</select>
						</li>
						<li><label for="representativeTelCountry" class="visuallyHidden">電話番号の国/地区選択</label><select id="representativeTelCountry" name="representativeTelCountry" class="js-countrySelect" size="1">	<option value="" data-sms-possible="true" data-country-code="---">選択してください</option>
	<option value="JP" selected="selected" data-sms-possible="true">日本</option>
	<option value="-" data-sms-possible="true" data-country-code="---">-----------</option>
	<option value="IS" data-sms-possible="true">アイスランド</option>
	<option value="IE" data-sms-possible="true">アイルランド</option>
	<option value="AZ" data-sms-possible="true">アゼルバイジャン</option>
	<option value="AF" data-sms-possible="true">アフガニスタン</option>
	<option value="US" data-sms-possible="true">アメリカ合衆国/カナダ</option>
	<option value="AE" data-sms-possible="true">アラブ首長国連邦</option>
	<option value="DZ" data-sms-possible="true">アルジェリア</option>
	<option value="AR" data-sms-possible="true">アルゼンチン</option>
	<option value="AW" data-sms-possible="true">アルバ</option>
	<option value="AL" data-sms-possible="true">アルバニア</option>
	<option value="AM" data-sms-possible="true">アルメニア</option>
	<option value="AO" data-sms-possible="true">アンゴラ</option>
	<option value="AG" data-sms-possible="true">アンティグア・バーブーダ</option>
	<option value="AD" data-sms-possible="true">アンドラ</option>
	<option value="YE" data-sms-possible="true">イエメン</option>
	<option value="GB" data-sms-possible="true">イギリス</option>
	<option value="IL" data-sms-possible="true">イスラエル</option>
	<option value="IT" data-sms-possible="true">イタリア</option>
	<option value="IQ" data-sms-possible="true">イラク</option>
	<option value="IR" data-sms-possible="true">イラン</option>
	<option value="IN" data-sms-possible="true">インド</option>
	<option value="ID" data-sms-possible="true">インドネシア</option>
	<option value="UG" data-sms-possible="true">ウガンダ</option>
	<option value="UA" data-sms-possible="true">ウクライナ</option>
	<option value="UZ" data-sms-possible="true">ウズベキスタン</option>
	<option value="UY" data-sms-possible="true">ウルグアイ</option>
	<option value="EC" data-sms-possible="true">エクアドル</option>
	<option value="EG" data-sms-possible="true">エジプト</option>
	<option value="EE" data-sms-possible="true">エストニア</option>
	<option value="SZ" data-sms-possible="true">エスワティニ</option>
	<option value="ET" data-sms-possible="true">エチオピア</option>
	<option value="ER" data-sms-possible="true">エリトリア</option>
	<option value="SV" data-sms-possible="true">エルサルバドル</option>
	<option value="AU" data-sms-possible="true">オーストラリア</option>
	<option value="AT" data-sms-possible="true">オーストリア</option>
	<option value="OM" data-sms-possible="true">オマーン</option>
	<option value="NL" data-sms-possible="true">オランダ</option>
	<option value="GH" data-sms-possible="true">ガーナ</option>
	<option value="CV" data-sms-possible="true">カーボベルデ</option>
	<option value="GY" data-sms-possible="true">ガイアナ</option>
	<option value="KZ" data-sms-possible="true">カザフスタン</option>
	<option value="QA" data-sms-possible="true">カタール</option>
	<option value="CA" data-sms-possible="true">カナダ/アメリカ合衆国</option>
	<option value="GA" data-sms-possible="true">ガボン</option>
	<option value="CM" data-sms-possible="true">カメルーン</option>
	<option value="KR" data-sms-possible="true">韓国</option>
	<option value="GM" data-sms-possible="true">ガンビア</option>
	<option value="KH" data-sms-possible="true">カンボジア</option>
	<option value="KP" data-sms-possible="true">北朝鮮</option>
	<option value="MK" data-sms-possible="true">北マケドニア</option>
	<option value="GN" data-sms-possible="true">ギニア</option>
	<option value="GW" data-sms-possible="true">ギニアビサウ</option>
	<option value="CY" data-sms-possible="true">キプロス</option>
	<option value="CU" data-sms-possible="true">キューバ</option>
	<option value="GR" data-sms-possible="true">ギリシャ</option>
	<option value="KI" data-sms-possible="true">キリバス</option>
	<option value="KG" data-sms-possible="true">キルギス</option>
	<option value="GT" data-sms-possible="true">グアテマラ</option>
	<option value="GU" data-sms-possible="true">グアム</option>
	<option value="KW" data-sms-possible="true">クウェート</option>
	<option value="GD" data-sms-possible="true">グレナダ</option>
	<option value="HR" data-sms-possible="true">クロアチア</option>
	<option value="KY" data-sms-possible="true">ケイマン諸島</option>
	<option value="KE" data-sms-possible="true">ケニア</option>
	<option value="CI" data-sms-possible="true">コートジボワール</option>
	<option value="CR" data-sms-possible="true">コスタリカ</option>
	<option value="KM" data-sms-possible="true">コモロ</option>
	<option value="CO" data-sms-possible="true">コロンビア</option>
	<option value="CG" data-sms-possible="true">コンゴ共和国</option>
	<option value="CD" data-sms-possible="true">コンゴ民主共和国</option>
	<option value="SA" data-sms-possible="true">サウジアラビア</option>
	<option value="ZM" data-sms-possible="true">ザンビア</option>
	<option value="SM" data-sms-possible="true">サンマリノ</option>
	<option value="SL" data-sms-possible="true">シエラレオネ</option>
	<option value="DJ" data-sms-possible="true">ジブチ</option>
	<option value="JM" data-sms-possible="true">ジャマイカ</option>
	<option value="GE" data-sms-possible="true">ジョージア</option>
	<option value="SY" data-sms-possible="true">シリア</option>
	<option value="SG" data-sms-possible="true">シンガポール</option>
	<option value="ZW" data-sms-possible="true">ジンバブエ</option>
	<option value="CH" data-sms-possible="true">スイス</option>
	<option value="SE" data-sms-possible="true">スウェーデン</option>
	<option value="SD" data-sms-possible="true">スーダン</option>
	<option value="ES" data-sms-possible="true">スペイン</option>
	<option value="SR" data-sms-possible="true">スリナム</option>
	<option value="LK" data-sms-possible="true">スリランカ</option>
	<option value="SK" data-sms-possible="true">スロバキア</option>
	<option value="SI" data-sms-possible="true">スロベニア</option>
	<option value="SC" data-sms-possible="true">セイシェル</option>
	<option value="GQ" data-sms-possible="true">赤道ギニア</option>
	<option value="SN" data-sms-possible="true">セネガル</option>
	<option value="RS" data-sms-possible="true">セルビア</option>
	<option value="KN" data-sms-possible="true">セントクリストファー・ネイビス</option>
	<option value="LC" data-sms-possible="true">セントルシア</option>
	<option value="SB" data-sms-possible="true">ソロモン諸島</option>
	<option value="TH" data-sms-possible="true">タイ</option>
	<option value="TW" data-sms-possible="true">台湾</option>
	<option value="TJ" data-sms-possible="true">タジキスタン</option>
	<option value="TZ" data-sms-possible="true">タンザニア</option>
	<option value="CZ" data-sms-possible="true">チェコ</option>
	<option value="TD" data-sms-possible="true">チャド</option>
	<option value="CF" data-sms-possible="true">中央アフリカ共和国</option>
	<option value="CN" data-sms-possible="true">中国</option>
	<option value="TN" data-sms-possible="true">チュニジア</option>
	<option value="CL" data-sms-possible="true">チリ</option>
	<option value="TV" data-sms-possible="true">ツバル</option>
	<option value="DK" data-sms-possible="true">デンマーク</option>
	<option value="DE" data-sms-possible="true">ドイツ</option>
	<option value="TG" data-sms-possible="true">トーゴ</option>
	<option value="DO" data-sms-possible="true">ドミニカ共和国</option>
	<option value="DM" data-sms-possible="true">ドミニカ国</option>
	<option value="TT" data-sms-possible="true">トリニダード・トバゴ</option>
	<option value="TM" data-sms-possible="true">トルクメニスタン</option>
	<option value="TR" data-sms-possible="true">トルコ</option>
	<option value="TO" data-sms-possible="true">トンガ</option>
	<option value="NG" data-sms-possible="true">ナイジェリア</option>
	<option value="NR" data-sms-possible="true">ナウル</option>
	<option value="NA" data-sms-possible="true">ナミビア</option>
	<option value="NI" data-sms-possible="true">ニカラグア</option>
	<option value="NE" data-sms-possible="true">ニジェール</option>
	<option value="NZ" data-sms-possible="true">ニュージーランド</option>
	<option value="NP" data-sms-possible="true">ネパール</option>
	<option value="NO" data-sms-possible="true">ノルウェー</option>
	<option value="BH" data-sms-possible="true">バーレーン</option>
	<option value="HT" data-sms-possible="true">ハイチ</option>
	<option value="PK" data-sms-possible="true">パキスタン</option>
	<option value="VA" data-sms-possible="true">バチカン</option>
	<option value="PA" data-sms-possible="true">パナマ</option>
	<option value="VU" data-sms-possible="true">バヌアツ</option>
	<option value="BS" data-sms-possible="true">バハマ</option>
	<option value="PG" data-sms-possible="true">パプアニューギニア</option>
	<option value="BM" data-sms-possible="true">バミューダ</option>
	<option value="PW" data-sms-possible="true">パラオ</option>
	<option value="PY" data-sms-possible="true">パラグアイ</option>
	<option value="BB" data-sms-possible="true">バルバドス</option>
	<option value="PS" data-sms-possible="true">パレスチナ</option>
	<option value="HU" data-sms-possible="true">ハンガリー</option>
	<option value="BD" data-sms-possible="true">バングラデシュ</option>
	<option value="TL" data-sms-possible="true">東ティモール</option>
	<option value="FJ" data-sms-possible="true">フィジー</option>
	<option value="PH" data-sms-possible="true">フィリピン</option>
	<option value="FI" data-sms-possible="true">フィンランド</option>
	<option value="BT" data-sms-possible="true">ブータン</option>
	<option value="PR" data-sms-possible="true">プエルトリコ</option>
	<option value="BR" data-sms-possible="true">ブラジル</option>
	<option value="FR" data-sms-possible="true">フランス</option>
	<option value="PF" data-sms-possible="true">フランス領ポリネシア</option>
	<option value="BG" data-sms-possible="true">ブルガリア</option>
	<option value="BF" data-sms-possible="true">ブルキナファソ</option>
	<option value="BN" data-sms-possible="true">ブルネイ</option>
	<option value="BI" data-sms-possible="true">ブルンジ</option>
	<option value="VN" data-sms-possible="true">ベトナム</option>
	<option value="BJ" data-sms-possible="true">ベナン</option>
	<option value="VE" data-sms-possible="true">ベネズエラ</option>
	<option value="BY" data-sms-possible="true">ベラルーシ</option>
	<option value="BZ" data-sms-possible="true">ベリーズ</option>
	<option value="PE" data-sms-possible="true">ペルー</option>
	<option value="BE" data-sms-possible="true">ベルギー</option>
	<option value="PL" data-sms-possible="true">ポーランド</option>
	<option value="BA" data-sms-possible="true">ボスニア・ヘルツェゴビナ</option>
	<option value="BW" data-sms-possible="true">ボツワナ</option>
	<option value="BO" data-sms-possible="true">ボリビア</option>
	<option value="PT" data-sms-possible="true">ポルトガル</option>
	<option value="HK" data-sms-possible="true">香港</option>
	<option value="HN" data-sms-possible="true">ホンジュラス</option>
	<option value="MH" data-sms-possible="true">マーシャル諸島</option>
	<option value="MO" data-sms-possible="true">マカオ</option>
	<option value="MG" data-sms-possible="true">マダガスカル</option>
	<option value="MW" data-sms-possible="true">マラウイ</option>
	<option value="ML" data-sms-possible="true">マリ</option>
	<option value="MT" data-sms-possible="true">マルタ</option>
	<option value="MY" data-sms-possible="true">マレーシア</option>
	<option value="FM" data-sms-possible="true">ミクロネシア連邦</option>
	<option value="ZA" data-sms-possible="true">南アフリカ</option>
	<option value="MM" data-sms-possible="true">ミャンマー</option>
	<option value="MX" data-sms-possible="true">メキシコ</option>
	<option value="MU" data-sms-possible="true">モーリシャス</option>
	<option value="MR" data-sms-possible="true">モーリタニア</option>
	<option value="MZ" data-sms-possible="true">モザンビーク</option>
	<option value="MC" data-sms-possible="true">モナコ</option>
	<option value="MV" data-sms-possible="true">モルディヴ</option>
	<option value="MD" data-sms-possible="true">モルドバ</option>
	<option value="MA" data-sms-possible="true">モロッコ</option>
	<option value="MN" data-sms-possible="true">モンゴル</option>
	<option value="ME" data-sms-possible="true">モンテネグロ</option>
	<option value="JO" data-sms-possible="true">ヨルダン</option>
	<option value="LA" data-sms-possible="true">ラオス人民民主共和国</option>
	<option value="LV" data-sms-possible="true">ラトビア</option>
	<option value="LT" data-sms-possible="true">リトアニア</option>
	<option value="LY" data-sms-possible="true">リビア</option>
	<option value="LI" data-sms-possible="true">リヒテンシュタイン</option>
	<option value="LR" data-sms-possible="true">リベリア</option>
	<option value="RO" data-sms-possible="true">ルーマニア</option>
	<option value="LU" data-sms-possible="true">ルクセンブルク</option>
	<option value="RW" data-sms-possible="true">ルワンダ</option>
	<option value="LS" data-sms-possible="true">レソト</option>
	<option value="LB" data-sms-possible="true">レバノン</option>
	<option value="RU" data-sms-possible="true">ロシア</option>
</select>
						</li>
						<li class="numberInputWrap">
							<span id="numberInputInfo1" class="supplementTxt marginTop">先頭に0(ゼロ)がある場合、0(ゼロ)を抜いてください(一部、国・地域除く)。携帯電話番号の場合は、携帯電話を契約された国を選択ください。</span>
							<div class="numberRowWrap">
								<div class="block">
									
									<span class="visuallyHidden">国番号</span>
									<span class="viewInput widthMini js-countryCode"></span>
								</div>
								<div class="block"><label for="representativeTel" class="visuallyHidden">電話番号</label><input id="representativeTel" type="text" name="representativeTel" value="9042504176" class="widthMiddle" placeholder="例：9012345678" maxlength="30" aria-describedby="numberInputInfo1" />
								</div>
							</div>
						</li>
					</ul>
				</dd>
			</dl>
		</fieldset>
	</div>
	<div class="titleWrapper">
		<h3>
			<span class="text">eメール・SMSに関するご案内<a href="https://www.ana.co.jp/other/int/meta/0057.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="eメール・SMSに関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei21d/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a></span>
		</h3>
	</div>
	<div class="formInput">
	<h4 class="formAreaTitle">主に搭乗・運航に関わるeメールの送付先設定（米国へ通知いたします）<a href="https://www.ana.co.jp/other/int/meta/0629.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="米国入国用eメール・SMS・電話番号に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei21d/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a>
	</h4>
	<div class="paxSettingArea js-mailFormWrap onePax is-open">
			<div class="settingArea">
					<dl>
						<dt class="breakWord">NAOKI SATO
						</dt>
						<dd>
							<fieldset>
								<legend>NAOKI SATOのeメール送付先および米国到着時に提供するメールアドレス</legend>代表者と同じ
							</fieldset>
						</dd>
					</dl>
			</div>
	</div>
	<h4 class="formAreaTitle hasTopLine">主に搭乗・運航に関わるSMSの送付先および米国で受信できる電話番号の設定（米国へ通知いたします）<a href="https://www.ana.co.jp/other/int/meta/0629.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="米国入国用eメール・SMS・電話番号に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei21d/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a>
	</h4>
	<div class="paxSettingArea js-smsFormWrap onePax is-open">
			<div class="settingArea">
					<dl>
						<dt class="breakWord">NAOKI SATO<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei21d/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span>
						</dt>
						<dd>
							<fieldset>
								<legend>NAOKI SATOの1つ目の電話番号</legend>
									<ul class="verticalList js-formItem">
										<li><input type="radio" id="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSameAsRepresentativeSms:radioItem" name="contactsSms:0:recipientSmsEachPersonSelection:radioGroup" value="0" checked="checked" /><label for="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSameAsRepresentativeSms:radioItem">代表者電話番号と同じ</label>
										</li>
										<li><input type="radio" id="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSmsPerPassenger:radioItem" name="contactsSms:0:recipientSmsEachPersonSelection:radioGroup" value="1" /><label for="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSmsPerPassenger:radioItem">電話番号を指定</label>
											<div class="indentArea js-formImputArea">
												<span id="confirmTelNumberForRepresentative:0" class="supplementTxt marginTop">先頭に0(ゼロ)がある場合、0(ゼロ)を抜いてください(一部、国・地域除く)。携帯電話番号の場合は、携帯電話を契約された国を選択ください。</span>
												<ul class="countryNumberInput js-countryWrap">
													<li><label for="contactsSms:0:recipientSmsEachPersonSelection:passengerSmsCountry" class="visuallyHidden">電話番号の国/地区選択</label><select id="contactsSms:0:recipientSmsEachPersonSelection:passengerSmsCountry" name="contactsSms:0:recipientSmsEachPersonSelection:passengerSmsCountry" class="countrySelect js-countrySelect" size="1">	<option value="" data-country-code="---">選択してください</option>
	<option value="JP">日本</option>
	<option value="-" data-country-code="---">-----------</option>
	<option value="IE">アイルランド</option>
	<option value="AZ">アゼルバイジャン</option>
	<option value="AF">アフガニスタン</option>
	<option value="US">アメリカ合衆国/カナダ</option>
	<option value="AE">アラブ首長国連邦</option>
	<option value="DZ">アルジェリア</option>
	<option value="AR">アルゼンチン</option>
	<option value="AW">アルバ</option>
	<option value="AL">アルバニア</option>
	<option value="AM">アルメニア</option>
	<option value="AG">アンティグア・バーブーダ</option>
	<option value="AD">アンドラ</option>
	<option value="YE">イエメン</option>
	<option value="GB">イギリス</option>
	<option value="IL">イスラエル</option>
	<option value="IT">イタリア</option>
	<option value="IQ">イラク</option>
	<option value="IR">イラン</option>
	<option value="IN">インド</option>
	<option value="ID">インドネシア</option>
	<option value="UG">ウガンダ</option>
	<option value="UA">ウクライナ</option>
	<option value="UZ">ウズベキスタン</option>
	<option value="UY">ウルグアイ</option>
	<option value="EC">エクアドル</option>
	<option value="EG">エジプト</option>
	<option value="EE">エストニア</option>
	<option value="ER">エリトリア</option>
	<option value="SV">エルサルバドル</option>
	<option value="AU">オーストラリア</option>
	<option value="AT">オーストリア</option>
	<option value="OM">オマーン</option>
	<option value="NL">オランダ</option>
	<option value="GH">ガーナ</option>
	<option value="GY">ガイアナ</option>
	<option value="KZ">カザフスタン</option>
	<option value="QA">カタール</option>
	<option value="CA">カナダ/アメリカ合衆国</option>
	<option value="GA">ガボン</option>
	<option value="CM">カメルーン</option>
	<option value="KR">韓国</option>
	<option value="GM">ガンビア</option>
	<option value="KH">カンボジア</option>
	<option value="GN">ギニア</option>
	<option value="GW">ギニアビサウ</option>
	<option value="CY">キプロス</option>
	<option value="CU">キューバ</option>
	<option value="GR">ギリシャ</option>
	<option value="KG">キルギス</option>
	<option value="GT">グアテマラ</option>
	<option value="GU">グアム</option>
	<option value="KW">クウェート</option>
	<option value="GD">グレナダ</option>
	<option value="HR">クロアチア</option>
	<option value="KY">ケイマン諸島</option>
	<option value="KE">ケニア</option>
	<option value="CI">コートジボワール</option>
	<option value="CR">コスタリカ</option>
	<option value="KM">コモロ</option>
	<option value="CO">コロンビア</option>
	<option value="CD">コンゴ民主共和国</option>
	<option value="SA">サウジアラビア</option>
	<option value="ZM">ザンビア</option>
	<option value="SL">シエラレオネ</option>
	<option value="JM">ジャマイカ</option>
	<option value="GE">ジョージア</option>
	<option value="SY">シリア</option>
	<option value="SG">シンガポール</option>
	<option value="ZW">ジンバブエ</option>
	<option value="CH">スイス</option>
	<option value="SE">スウェーデン</option>
	<option value="SD">スーダン</option>
	<option value="ES">スペイン</option>
	<option value="SR">スリナム</option>
	<option value="LK">スリランカ</option>
	<option value="SK">スロバキア</option>
	<option value="SI">スロベニア</option>
	<option value="SN">セネガル</option>
	<option value="RS">セルビア</option>
	<option value="KN">セントクリストファー・ネイビス</option>
	<option value="LC">セントルシア</option>
	<option value="TH">タイ</option>
	<option value="TW">台湾</option>
	<option value="TJ">タジキスタン</option>
	<option value="TZ">タンザニア</option>
	<option value="CZ">チェコ</option>
	<option value="TD">チャド</option>
	<option value="CF">中央アフリカ共和国</option>
	<option value="CN">中国</option>
	<option value="TN">チュニジア</option>
	<option value="CL">チリ</option>
	<option value="DK">デンマーク</option>
	<option value="DE">ドイツ</option>
	<option value="TG">トーゴ</option>
	<option value="DO">ドミニカ共和国</option>
	<option value="TT">トリニダード・トバゴ</option>
	<option value="TR">トルコ</option>
	<option value="NG">ナイジェリア</option>
	<option value="NI">ニカラグア</option>
	<option value="NE">ニジェール</option>
	<option value="NZ">ニュージーランド</option>
	<option value="NP">ネパール</option>
	<option value="NO">ノルウェー</option>
	<option value="HT">ハイチ</option>
	<option value="PK">パキスタン</option>
	<option value="PA">パナマ</option>
	<option value="BS">バハマ</option>
	<option value="PG">パプアニューギニア</option>
	<option value="BM">バミューダ</option>
	<option value="PW">パラオ</option>
	<option value="PY">パラグアイ</option>
	<option value="BB">バルバドス</option>
	<option value="PS">パレスチナ</option>
	<option value="HU">ハンガリー</option>
	<option value="BD">バングラデシュ</option>
	<option value="TL">東ティモール</option>
	<option value="FJ">フィジー</option>
	<option value="PH">フィリピン</option>
	<option value="FI">フィンランド</option>
	<option value="BT">ブータン</option>
	<option value="PR">プエルトリコ</option>
	<option value="BR">ブラジル</option>
	<option value="FR">フランス</option>
	<option value="PF">フランス領ポリネシア</option>
	<option value="BG">ブルガリア</option>
	<option value="BF">ブルキナファソ</option>
	<option value="BN">ブルネイ</option>
	<option value="BI">ブルンジ</option>
	<option value="VN">ベトナム</option>
	<option value="BJ">ベナン</option>
	<option value="VE">ベネズエラ</option>
	<option value="BY">ベラルーシ</option>
	<option value="PE">ペルー</option>
	<option value="BE">ベルギー</option>
	<option value="PL">ポーランド</option>
	<option value="BA">ボスニア・ヘルツェゴビナ</option>
	<option value="BW">ボツワナ</option>
	<option value="HK">香港</option>
	<option value="MH">マーシャル諸島</option>
	<option value="MO">マカオ</option>
	<option value="MG">マダガスカル</option>
	<option value="MW">マラウイ</option>
	<option value="ML">マリ</option>
	<option value="MT">マルタ</option>
	<option value="MY">マレーシア</option>
	<option value="ZA">南アフリカ</option>
	<option value="MM">ミャンマー</option>
	<option value="MX">メキシコ</option>
	<option value="MU">モーリシャス</option>
	<option value="MR">モーリタニア</option>
	<option value="MZ">モザンビーク</option>
	<option value="MC">モナコ</option>
	<option value="MV">モルディヴ</option>
	<option value="MA">モロッコ</option>
	<option value="MN">モンゴル</option>
	<option value="ME">モンテネグロ</option>
	<option value="JO">ヨルダン</option>
	<option value="LA">ラオス人民民主共和国</option>
	<option value="LV">ラトビア</option>
	<option value="LT">リトアニア</option>
	<option value="LY">リビア</option>
	<option value="LR">リベリア</option>
	<option value="RO">ルーマニア</option>
	<option value="LU">ルクセンブルク</option>
	<option value="RW">ルワンダ</option>
	<option value="LB">レバノン</option>
	<option value="RU">ロシア</option>
</select>
													</li>
													<li>
														
														<div class="numberRowWrap">
															<div class="block">
																<span class="visuallyHidden">国番号</span>
																<span class="countryCode viewInput js-countryCode"></span>
															</div>
															
															<div class="block"><label for="contactsSms:0:recipientSmsEachPersonSelection:flightStatusNotificationContactPointSmsDescription" class="visuallyHidden">電話番号</label><input id="contactsSms:0:recipientSmsEachPersonSelection:flightStatusNotificationContactPointSmsDescription" type="text" name="contactsSms:0:recipientSmsEachPersonSelection:flightStatusNotificationContactPointSmsDescription" class="telNumber" placeholder="例：9012345678" maxlength="30" aria-describedby="confirmTelNumberForRepresentative:0" />
															</div>
														</div>
													</li>
												</ul>
											</div>
										</li>
									</ul>
							</fieldset>
						</dd>
					</dl>
			</div>
	</div>
	</div>
	
	<div class="formInput articleLine">
		<h3>滞在中の連絡先(任意)
		</h3>
		<dl>
			
			<dt><label for="accommodationOne">現地連絡先1</label>
			</dt>
			<dd><input id="accommodationOne" type="text" name="accommodationOne" class="widthLarge" placeholder="例：09012345678 ABC HOTEL" maxlength="28" title="例：09012345678 ABC HOTEL" />
			</dd>
		</dl>
		<dl>
			
			<dt><label for="accommodationTwo">現地連絡先2</label>
			</dt>
			<dd><input id="accommodationTwo" type="text" name="accommodationTwo" class="widthLarge" placeholder="例：09012345678 ABC HOTEL" maxlength="28" title="例：09012345678 ABC HOTEL" />
			</dd>
		</dl>
	</div>
		</div>
		
		<p class="btnAreaSubmit btnArrowNext "><input id="next" type="submit" name="next" value="次へ" class="btnBase btnMainStream btnVerticalMain btnWidthVariable" onclick="return Asw.Dialog.getInstance('prebookConfirmDialog').toggle(event);return Asw.LoadingWindow.open(&quot;NORMAL&quot;, event)" />
		</p><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:2" value="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" autocomplete="off" />
</form><div id="cmnDynamicMessages" class="infoBoxSection">
				<dl class="infoBox importantInfo jsAccordionSwitch">
					<dt><strong>重要なご案内</strong></dt>
					<dd>
						<ul class="jsAccordionSwitchList" id="informationMessages">
								<li>個人情報の利用目的<a href="https://www.ana.co.jp/other/int/meta/0012.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="ANAプライバシーポリシー(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">ANAプライバシーポリシー<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei21d/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>を必ずお読みいただき、内容に同意いただいた上で、お進みください。</li>
								<li><a href="https://www.ana.co.jp/other/int/meta/0058.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="Secure Flight Programについて(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">米国Secure Flightプログラム<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei21d/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>に基づき、パスポート名、生年月日、性別などの情報は事前に米国へ通知いたします。</li>
								<li>入力したeメールアドレス、電話番号は入国に必要な情報として米国に通知される可能性があります。</li>
									<li class="jsHiddenFlg toggleContents">お名前のスペルがパスポート名と一致しているかご確認ください。異なる場合はインターネットではお取り扱いできません。<a href="https://www.ana.co.jp/other/int/meta/intbe0001.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="ANAお問い合わせ先(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">ANA<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei21d/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>へ名前の変更をお申し出ください。</li>
									<li class="jsHiddenFlg toggleContents">二重国籍のお客様は、ご搭乗時に使用されるパスポートのお名前でご予約ください。<br />パスポートごとにお名前が異なる場合は、お客様ご自身で入国管理事務所・外務省・大使館などで各種条件をご確認のうえ、使用されるパスポートを決めてください。<br />なお、入国可否については国によって異なるため、お客様ご自身で各入国管理事務所などでご確認ください。</li>
									<li class="jsHiddenFlg toggleContents">旅行開始日時点で12歳以上の場合は大人、2歳以上12歳未満の場合は小児、2歳未満の場合は幼児の扱いとなります。なお、小児は大人と同マイル数が必要です。</li>
									<li class="jsHiddenFlg toggleContents">運航に関する情報をお客様へ通知するため、提携航空会社にもお客様のメールアドレスが提供されます。あらかじめご了承ください。</li>
									<li class="jsHiddenFlg toggleContents"><dl><dt>携帯電話へのメール送信をご希望のお客様へ</dt><dd>ドメイン指定受信をご利用の場合は、ana.co.jpとamadeus.comからのメールが受信できるようあらかじめ設定をお願いいたします。</dd></dl></li>
									<li class="jsHiddenFlg toggleContents">SMSはスマートフォンの番号に配信されます。一部の国ではSMSでのご案内をご利用いただけません。詳細は<a href="https://www.ana.co.jp/other/int/meta/0737.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="ショートメッセージ(SMS)でのご案内対象外の国について(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">ショートメッセージ(SMS)でのご案内対象外の国について<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei21d/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>をご覧ください。</li>
									<li class="jsHiddenFlg toggleContents">ログインされている会員ご本人が搭乗しない場合は、再度フライト検索画面より「ログインされている会員ご本人は搭乗しない」のチェックボックスを選択してお手続きください。</li>
						</ul>
							<a href="#" class="toggleSwitch jsTriggerSwitch" role="button" aria-controls="informationMessages" aria-expanded="false"><img src="https://aswbe-i.ana.co.jp/rei21d/international_asw/contents/cmn/image/toggle_open.png?7bec38a" alt="開閉ボタン" height="25" width="25" class="jsRollOver" />
							</a>
					</dd>
				</dl></div>
	<!--=========== /main =============-->
	</div>

	<!--=========== Script ============-->
	<script type="text/javascript">
	$(window).on('load', function() {
		// アコーディオンのメソッドと設定
		Asw.AccordionInfo('.jsAccordionSwitch', {
			'triggerSwitch': '.jsAccordionSwitch .jsTriggerSwitch',
			'listClass': '.jsAccordionSwitchList',
			'visibilityHidden': 'toggleContents'
		});
	});
	</script>
	<!--=========== Script ============-->
	<script type="text/javascript">
		//プラットフォーム判別（サマリーエリア追従用として）
		var Asw = Asw || {};
	
	  	Asw.ClientInfo = Asw.ClientInfo  || {};
	  	Asw.ClientInfo.DeviceType = Asw.ClientInfo.DeviceType || {};
	
	  	Asw.ClientInfo.DeviceType.PC = 'PC';
	  	Asw.ClientInfo.DeviceType.SP = 'SP';
	  	Asw.ClientInfo.DeviceType.TC = 'TC';
	
	  	Asw.ClientInfo.deviceType = Asw.ClientInfo.DeviceType.PC;
	
	 	if((navigator.userAgent.indexOf('Android') > 0 && navigator.userAgent.indexOf('Mobile') == -1) || navigator.userAgent.indexOf('iPad') > 0){
	    	Asw.ClientInfo.deviceType = Asw.ClientInfo.DeviceType.TC;
	  	}
	</script>
	<!--=========== /Script ============--><div id="summaryArea">
			<div class="flightSummaryArea">
						<div class="flightSummarySection">
							<h2>区間 1
							</h2>
							<p class="flightDate"><em>6</em>月<em class="hasLeft">28</em>日（土）
							</p>
							<div class="flightSummaryRoute">
									<div class="flightSummaryRouteSection">
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation1_1">
													<span>東京(成田)
													</span>
												</p>
												<p class="time">
													<span>17:00
													</span>
												</p>
											</div>
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation1_1">
													<span>シカゴ(ORD)
													</span>
												</p>
												<p class="time">
													<span>14:55
													</span>
												</p>
											</div>
										<p class="flightNumberClass">
												<span>NH012</span><span><img src="https://aswbe-i.ana.co.jp/rei21d/international_asw/contents/cmn/image/flight_ana_summaryarea.png?717d3c0" alt="ANA運航便" height="11" width="41" /></span>
												<br />特典ビジネス
										</p>
									</div>
							</div>
						</div>
						<div class="flightSummarySection">
							<h2 class="midstream">区間 2
							</h2>
							<p class="flightDate"><em>7</em>月<em class="hasLeft">23</em>日（水）
							</p>
							<div class="flightSummaryRoute">
									<div class="flightSummaryRouteSection">
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation2_1">
													<span>ホノルル(オアフ島)
													</span>
												</p>
												<p class="time">
													<span>11:35
													</span>
												</p>
											</div>
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation2_1">
													<span>東京(成田)
													</span>
												</p>
												<p class="time">
													<span>14:50
																	<span>翌日
																	</span>
													</span>
												</p>
											</div>
										<p class="flightNumberClass">
												<span>NH183</span><span><img src="https://aswbe-i.ana.co.jp/rei21d/international_asw/contents/cmn/image/flight_ana_summaryarea.png?717d3c0" alt="ANA運航便" height="11" width="41" /></span>
												<br />特典エコノミー
										</p>
									</div>
							</div>
						</div>
					<div class="total">
						<p class="passenger">大人 x1
						</p>
						<div class="totalArea">
					  		<dl class="mealAmountWrap">
									<dt>有料機内食サービス
									</dt>
									<dd><em class="mealAmount">0</em><span class="currencyCode">円</span>
									</dd>
							</dl>
									<div>
										<div class="mileageAndTotal">
												<dl class="requiredMileage">
													<dt>必要マイル
													</dt>
													<dd><em class="requiredMileage">74,000</em><span class="currencyCode">マイル</span>
													</dd>
												</dl>
												<dl>
													<dt>総額
													</dt>
													<dd><em class="awardTotalPayment">68,130</em>円
													</dd>
												</dl>
												<p class="includeFareAndFuelCost">※各種税金、燃油特別付加運賃等を含みます。
												</p>
										</div>
									</div>
						</div>
					</div>
				
			</div></div>
		<!--===== /cmnWrapper =====-->
		</div>
			
		<span id="message" class="visuallyHidden" role="alert" aria-live="assertive"></span>
		<span id="politeMessage" class="visuallyHidden" role="log" aria-live="polite"></span><script type="text/javascript">
		var Asw = Asw || {};
		Asw.BaseOutput = {"sessionID":"TwWLeQnhtl","language":"ja","siteCatalystLanguageCode":"J","operationDate":"20250620","operationDateTime":"20250620125426","office":{"officeCode":"TYONH08ZZ","country2letterCode":"JP"},"bookingType":"A","pageID":"A04_P01","siteCatalystPageName":"INT_BE_AWARD_J_A04特典予約_P01旅客情報入力（必須情報）","device":{"deviceType":"PC"}};
	</script><div id="promotionArea">
		<!--===== promotionArea =====-->
		<!--===== /promotionArea =====--></div>
		<!--=========== /cmnContainer ============--></div><div id="cmnBaloonArea" aria-live="polite"></div><div id="cmnPopupArea" aria-live="polite"></div>

	

	<div id="cmnFooterWrapper">
		<!--=========== .cmnFooterWrapper ============-->
		<div id="cmnFooter">
			<!--===== cmnFooter =====-->
			<ul>
				<li><a href="https://www.ana.co.jp/other/int/meta/0012.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">プライバシーポリシー<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21d/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
					<li><a href="https://www.ana.co.jp/other/int/meta/0670.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">利用者情報の外部送信について<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21d/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
					</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/0052.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">サイト利用規約<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21d/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/be0238.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">推奨環境<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21d/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/0133.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">サイトマップ<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21d/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/0732.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">運送約款<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21d/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
					<li class="lastChild"><a href="https://www.ana.co.jp/other/int/meta/0556.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">ウェブアクセシビリティについて<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21d/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
					</li>
			</ul>
			<p class="blankText" aria-hidden="true"><img src="https://aswbe-i.ana.co.jp/rei21d/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="" height="10" width="10" />外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。</p>
			<p class="copyright"><span lang="en" xml:lang="en">Copyright&#169;ANA</span>
			</p>
			<p class="starAllianceLogo"><a href="http://www.staralliance.com/ja/" target="_blank"><img src="https://aswbe-i.ana.co.jp/rei21d/international_asw/contents/cmn/image/star_alliance_logo.png?0fe73b5" alt="A STAR ALLIANCE MEMBER" height="18" width="175" /><span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21d/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
			</p>
			<!--===== cmnFooter =====-->
		</div>
		<!--=========== /cmnFooterWrapper ============-->
	</div>

		
		<div id="cmnLoadingForAjax"><img src="https://aswbe-i.ana.co.jp/rei21d/international_asw/contents/cmn/image/bg_modalWindow.png?717d3c0" alt="" height="0" width="0" />
			<div class="loadingArea">
				<p class="loadingImage"><img src="https://aswbe-i.ana.co.jp/rei21d/international_asw/contents/cmn/image/loading.gif?717d3c0" alt="リクエストを処理しています...しばらくお待ちください。" height="33" width="33" />
				</p>
			</div>
		</div>
		
		
		<div id="popupAltSentence" style="display: none" aria-hidden="true">別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。</div>
	<div id="cmnSiteCatalyst" aria-hidden="true"><div id="cmnSiteCatalystParamArea"><script type="text/javascript">
				// ▼SiteCatalyst
				// SiteCatalyst code version: H.2.
				// Copyright 1997-2005 Omniture, Inc. More info available at http://www.omniture.com
				SiteCatalystReportSuites   = "INT";
				SiteCatalystCharSet        = "UTF-8";
				SiteCatalystChannel        = "BE_AWARD_J";
				SiteCatalystPageName       = "INT_BE_AWARD_J_A04\u7279\u5178\u4E88\u7D04_P01\u65C5\u5BA2\u60C5\u5831\u5165\u529B\uFF08\u5FC5\u9808\u60C5\u5831\uFF09";
				SiteCatalystERR            = "";
				SiteCatalystEventNum       = "";
				SiteCatalystPurchase       = "FALSE";
				SiteCatalystProductPrice   = "";
				SiteCatalystCurrencyCode   = "";
				SiteCatalystSeatAvailabilityINT   = "";
				SiteCatalystDepartDate     = "";
				SiteCatalystDepart         = "";
				SiteCatalystArrive         = "";
				SiteCatalystFlightNo       = "";
				SiteCatalystClass          = "";
				SiteCatalystFare           = "";
				SiteCatalystPurchaseID     = "";
				
				SiteCatalystLocater        = "";
				SiteCatalystPayment        = "";
				SiteCatalystIncome         = "";
				
				SiteCatalystPax            = "";
				SiteCatalystManipulateDay  = "";
				SiteCatalystCompartmentClass   = "";
				SiteCatalystOptionInfo     = "";
				SiteCatalystOutboundUpsellPrice   = "";
				SiteCatalystInboundUpsellPrice    = "";
				
				SiteCatalystSearchInput    = "";
				SiteCatalystSegment        = "";
				SiteCatalystOfficeCode     = "";
				SiteCatalystPassengerCount = "";
				SiteCatalystPassenger      = "";
				SiteCatalystAncillaryService = "";
				// End SiteCatalyst code version: H.2.
				// ▲SiteCatalyst
			</script></div><script type="text/javascript"><!-- if(navigator.appVersion.indexOf('MSIE')>=0)document.write(unescape('%3C')+'\!-'+'-') // --></script>
		<!--/DO NOT REMOVE/-->
		

		
		<!-- Google Tag Manager -->
		<script type="text/javascript" charset="Shift_JIS" src="https://www.ana.co.jp/common/js/GTMINTBE.js"></script>
		<!-- End Google Tag Manager -->
		<!-- Yahoo Tag Manager -->
		<script type="text/javascript" charset="Shift_JIS" src="https://www.ana.co.jp/common/js/YTMINTBE.js"></script>
		<!-- End Yahoo Tag Manager -->
		

	</div>

	

	<div class="mboxDefault"></div><script type="text/javascript">mboxCreate("ASW_common_A04_P01");</script>
			<script type="text/javascript" src="https://www.ana.co.jp/common/js/tealium/tealium.js"></script>
			<script type="text/javascript" src="//cdn.evgnet.com/beacon/allnipponairways/nonamcprd/scripts/evergage.min.js"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21d/international_asw/javax.faces.resource/jsf.js.xhtml?ln=javax.faces"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21d/international_asw/contents/cmn/script/jquery.mousewheel.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21d/international_asw/contents/cmn/script/jquery.jscrollpane.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21d/international_asw/contents/cmn/script/jquery-ui-1.10.4.custom.min.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21d/international_asw/contents/cmn/script/jquery.ui.touch-punch.min.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21d/international_asw/contents/cmn/script/asw-common.js?beb6a50"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21d/international_asw/contents/cmn/script/asw-common-pc.js?fd88d7d"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21d/international_asw/contents/cmn/script/asw-format.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21d/international_asw/contents/cmn/script/mask.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21d/international_asw/contents/cmn/script/loading.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21d/international_asw/contents/cmn/script/asw-loadingwindow-pc.js?9e233c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21d/international_asw/contents/cmn/script/asw-loadingwindow.js?51db600"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21d/international_asw/contents/cmn/script/dialog-pc.js?25d36a4"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21d/international_asw/contents/cmn/script/asw-dialog-sequencer.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21d/international_asw/contents/cmn/script/asw-event.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21d/international_asw/contents/cmn/script/asw-date-select.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21d/international_asw/contents/cmn/script/asw-calendar-pc.js?9b86b8a"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21d/international_asw/contents/cmn/script/asw-calendar.js?db624c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21d/international_asw/contents/cmn/script/heightLine.js?0e7a1ce"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21d/international_asw/contents/cmn/script/asw-summaryarea.js?b21c6d7"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21d/international_asw/contents/cmn/script/asw-fingerprints.js?99001fc"></script><script type="text/javascript">
			Asw.init("https://aswbe-i.ana.co.jp/rei21d/international_asw", "ja");
			Asw.getCalendarHolidayList = function() {return [20250721,20250811,20250915,20250923,20251013,20251103,20251123,20251124,20260101,20260112,20260211,20260223,20260320,20260429,20260503,20260504,20260505,20260506,20260720,20260811,20260921,20260922,20260923,20261012,20261103,20261123,20270101,20270111,20270211,20270223,20270321,20270322,20270429,20270503,20270504,20270505,20270719,20270811,20270920,20270923,20271011,20271103,20271123,20280101,20280110,20280211,20280223,20280320,20280429,20280503,20280504,20280505,20280717,20280811,20280918,20280922,20281009,20281103,20281123,20290101,20290108,20290211,20290212,20290223,20290320,20290429,20290430,20290503,20290504,20290505,20290716,20290811,20290917,20290923,20290924,20291008,20291103,20291123];}
			Asw.ClientInfo.deviceType = "PC";
			Asw.ClientInfo.mobileDeviceType = "UNKNOWN";
			Asw.ClientInfo.osVersion = "";
			Asw.Format.init("https://aswbe-i.ana.co.jp/rei21d/international_asw/contents/{1}/{2}/{3}?{0}");
			Asw.overwriteTargetAttributeValue("false");
		</script><script type="text/javascript">
		
		var Asw = Asw || {};
		Asw.ContactInput = Asw.ContactInput || {};
		Asw.ContactInput.Const = {
			/** data-属性名「data-type」 @constant @type {String} @default 'data-type' */
			DATA_ATTRIBUTE_NAME_TYPE: 'data-type',
			/** data-属性名「data-sms-possible」 @constant @type {String} @default 'data-sms-possible' */
			DATA_ATTRIBUTE_NAME_SMS_POSSIBLE: 'data-sms-possible',
			/** data-属性名「data-country-code」 @constant @type {String} @default 'data-country-code' */
			DATA_ATTRIBUTE_NAME_COUNTRY_CODE: 'data-country-code',
			/** data-type属性値「sms」 @constant @type {String} @default 'sms' */
			DATA_TYPE_VALUE_SMS: 'sms'
		};

		
		Asw.ContactInput.countryInfoMap = {"IS":{"internationalCallingCode":"+354","smsPossible":false},"IE":{"internationalCallingCode":"+353","smsPossible":true},"AZ":{"internationalCallingCode":"+994","smsPossible":true},"AF":{"internationalCallingCode":"+93","smsPossible":true},"US":{"internationalCallingCode":"+1","smsPossible":true},"AE":{"internationalCallingCode":"+971","smsPossible":true},"DZ":{"internationalCallingCode":"+213","smsPossible":true},"AR":{"internationalCallingCode":"+54","smsPossible":true},"AW":{"internationalCallingCode":"+297","smsPossible":true},"AL":{"internationalCallingCode":"+355","smsPossible":true},"AM":{"internationalCallingCode":"+374","smsPossible":true},"AO":{"internationalCallingCode":"+244","smsPossible":false},"AG":{"internationalCallingCode":"+1","smsPossible":true},"AD":{"internationalCallingCode":"+376","smsPossible":true},"YE":{"internationalCallingCode":"+967","smsPossible":true},"GB":{"internationalCallingCode":"+44","smsPossible":true},"IL":{"internationalCallingCode":"+972","smsPossible":true},"IT":{"internationalCallingCode":"+39","smsPossible":true},"IQ":{"internationalCallingCode":"+964","smsPossible":true},"IR":{"internationalCallingCode":"+98","smsPossible":true},"IN":{"internationalCallingCode":"+91","smsPossible":true},"ID":{"internationalCallingCode":"+62","smsPossible":true},"UG":{"internationalCallingCode":"+256","smsPossible":true},"UA":{"internationalCallingCode":"+380","smsPossible":true},"UZ":{"internationalCallingCode":"+998","smsPossible":true},"UY":{"internationalCallingCode":"+598","smsPossible":true},"EC":{"internationalCallingCode":"+593","smsPossible":true},"EG":{"internationalCallingCode":"+20","smsPossible":true},"EE":{"internationalCallingCode":"+372","smsPossible":true},"SZ":{"internationalCallingCode":"+268","smsPossible":false},"ET":{"internationalCallingCode":"+251","smsPossible":false},"ER":{"internationalCallingCode":"+291","smsPossible":true},"SV":{"internationalCallingCode":"+503","smsPossible":true},"AU":{"internationalCallingCode":"+61","smsPossible":true},"AT":{"internationalCallingCode":"+43","smsPossible":true},"OM":{"internationalCallingCode":"+968","smsPossible":true},"NL":{"internationalCallingCode":"+31","smsPossible":true},"GH":{"internationalCallingCode":"+233","smsPossible":true},"CV":{"internationalCallingCode":"+238","smsPossible":false},"GY":{"internationalCallingCode":"+592","smsPossible":true},"KZ":{"internationalCallingCode":"+7","smsPossible":true},"QA":{"internationalCallingCode":"+974","smsPossible":true},"CA":{"internationalCallingCode":"+1","smsPossible":true},"GA":{"internationalCallingCode":"+241","smsPossible":true},"CM":{"internationalCallingCode":"+237","smsPossible":true},"KR":{"internationalCallingCode":"+82","smsPossible":true},"KH":{"internationalCallingCode":"+855","smsPossible":true},"GM":{"internationalCallingCode":"+220","smsPossible":true},"KP":{"internationalCallingCode":"+850","smsPossible":false},"MK":{"internationalCallingCode":"+389","smsPossible":false},"GN":{"internationalCallingCode":"+224","smsPossible":true},"GW":{"internationalCallingCode":"+245","smsPossible":true},"CY":{"internationalCallingCode":"+357","smsPossible":true},"CU":{"internationalCallingCode":"+53","smsPossible":true},"GR":{"internationalCallingCode":"+30","smsPossible":true},"KI":{"internationalCallingCode":"+686","smsPossible":false},"KG":{"internationalCallingCode":"+996","smsPossible":true},"GT":{"internationalCallingCode":"+502","smsPossible":true},"GU":{"internationalCallingCode":"+1","smsPossible":true},"KW":{"internationalCallingCode":"+965","smsPossible":true},"KY":{"internationalCallingCode":"+1","smsPossible":true},"GD":{"internationalCallingCode":"+1","smsPossible":true},"HR":{"internationalCallingCode":"+385","smsPossible":true},"KE":{"internationalCallingCode":"+254","smsPossible":true},"CI":{"internationalCallingCode":"+225","smsPossible":true},"CR":{"internationalCallingCode":"+506","smsPossible":true},"SA":{"internationalCallingCode":"+966","smsPossible":true},"KM":{"internationalCallingCode":"+269","smsPossible":true},"CO":{"internationalCallingCode":"+57","smsPossible":true},"CG":{"internationalCallingCode":"+242","smsPossible":false},"CD":{"internationalCallingCode":"+243","smsPossible":true},"ZM":{"internationalCallingCode":"+260","smsPossible":true},"SM":{"internationalCallingCode":"+378","smsPossible":false},"SL":{"internationalCallingCode":"+232","smsPossible":true},"DJ":{"internationalCallingCode":"+253","smsPossible":false},"JM":{"internationalCallingCode":"+1","smsPossible":true},"CH":{"internationalCallingCode":"+41","smsPossible":true},"GE":{"internationalCallingCode":"+995","smsPossible":true},"SE":{"internationalCallingCode":"+46","smsPossible":true},"SY":{"internationalCallingCode":"+963","smsPossible":true},"ES":{"internationalCallingCode":"+34","smsPossible":true},"SG":{"internationalCallingCode":"+65","smsPossible":true},"LK":{"internationalCallingCode":"+94","smsPossible":true},"SK":{"internationalCallingCode":"+421","smsPossible":true},"SI":{"internationalCallingCode":"+386","smsPossible":true},"ZW":{"internationalCallingCode":"+263","smsPossible":true},"SD":{"internationalCallingCode":"+249","smsPossible":true},"KN":{"internationalCallingCode":"+1","smsPossible":true},"SR":{"internationalCallingCode":"+597","smsPossible":true},"LC":{"internationalCallingCode":"+1","smsPossible":true},"SC":{"internationalCallingCode":"+248","smsPossible":false},"SN":{"internationalCallingCode":"+221","smsPossible":true},"SB":{"internationalCallingCode":"+677","smsPossible":false},"RS":{"internationalCallingCode":"+381","smsPossible":true},"TH":{"internationalCallingCode":"+66","smsPossible":true},"TW":{"internationalCallingCode":"+886","smsPossible":true},"TJ":{"internationalCallingCode":"+992","smsPossible":true},"TZ":{"internationalCallingCode":"+255","smsPossible":true},"CZ":{"internationalCallingCode":"+420","smsPossible":true},"TD":{"internationalCallingCode":"+235","smsPossible":true},"CF":{"internationalCallingCode":"+236","smsPossible":true},"TN":{"internationalCallingCode":"+216","smsPossible":true},"CL":{"internationalCallingCode":"+56","smsPossible":true},"TV":{"internationalCallingCode":"+688","smsPossible":false},"DK":{"internationalCallingCode":"+45","smsPossible":true},"DE":{"internationalCallingCode":"+49","smsPossible":true},"TG":{"internationalCallingCode":"+228","smsPossible":true},"DO":{"internationalCallingCode":"+1","smsPossible":true},"DM":{"internationalCallingCode":"+1","smsPossible":false},"TT":{"internationalCallingCode":"+1","smsPossible":true},"TM":{"internationalCallingCode":"+993","smsPossible":false},"TR":{"internationalCallingCode":"+90","smsPossible":true},"TO":{"internationalCallingCode":"+676","smsPossible":false},"NG":{"internationalCallingCode":"+234","smsPossible":true},"NR":{"internationalCallingCode":"+674","smsPossible":false},"NA":{"internationalCallingCode":"+264","smsPossible":false},"NI":{"internationalCallingCode":"+505","smsPossible":true},"BH":{"internationalCallingCode":"+973","smsPossible":false},"NE":{"internationalCallingCode":"+227","smsPossible":true},"PK":{"internationalCallingCode":"+92","smsPossible":true},"JP":{"internationalCallingCode":"+81","smsPossible":true},"BS":{"internationalCallingCode":"+1","smsPossible":true},"NZ":{"internationalCallingCode":"+64","smsPossible":true},"BM":{"internationalCallingCode":"+1","smsPossible":true},"NP":{"internationalCallingCode":"+977","smsPossible":true},"BB":{"internationalCallingCode":"+246","smsPossible":true},"NO":{"internationalCallingCode":"+47","smsPossible":true},"HT":{"internationalCallingCode":"+509","smsPossible":true},"BD":{"internationalCallingCode":"+880","smsPossible":true},"VA":{"internationalCallingCode":"+39","smsPossible":false},"PA":{"internationalCallingCode":"+507","smsPossible":true},"VU":{"internationalCallingCode":"+678","smsPossible":false},"PG":{"internationalCallingCode":"+675","smsPossible":true},"PW":{"internationalCallingCode":"+680","smsPossible":true},"PY":{"internationalCallingCode":"+595","smsPossible":true},"PS":{"internationalCallingCode":"+970","smsPossible":true},"BR":{"internationalCallingCode":"+55","smsPossible":true},"HU":{"internationalCallingCode":"+36","smsPossible":true},"TL":{"internationalCallingCode":"+670","smsPossible":true},"BG":{"internationalCallingCode":"+359","smsPossible":true},"BF":{"internationalCallingCode":"+226","smsPossible":true},"BN":{"internationalCallingCode":"+673","smsPossible":true},"FJ":{"internationalCallingCode":"+679","smsPossible":true},"PH":{"internationalCallingCode":"+63","smsPossible":true},"FI":{"internationalCallingCode":"+358","smsPossible":true},"BT":{"internationalCallingCode":"+975","smsPossible":true},"BY":{"internationalCallingCode":"+375","smsPossible":true},"BZ":{"internationalCallingCode":"+501","smsPossible":false},"PR":{"internationalCallingCode":"+1","smsPossible":true},"BE":{"internationalCallingCode":"+32","smsPossible":true},"BA":{"internationalCallingCode":"+387","smsPossible":true},"FR":{"internationalCallingCode":"+33","smsPossible":true},"BO":{"internationalCallingCode":"+591","smsPossible":false},"PF":{"internationalCallingCode":"+689","smsPossible":true},"BI":{"internationalCallingCode":"+257","smsPossible":true},"VN":{"internationalCallingCode":"+84","smsPossible":true},"BJ":{"internationalCallingCode":"+229","smsPossible":true},"VE":{"internationalCallingCode":"+58","smsPossible":true},"PE":{"internationalCallingCode":"+51","smsPossible":true},"PL":{"internationalCallingCode":"+48","smsPossible":true},"BW":{"internationalCallingCode":"+267","smsPossible":true},"PT":{"internationalCallingCode":"+351","smsPossible":false},"HK":{"internationalCallingCode":"+852","smsPossible":true},"HN":{"internationalCallingCode":"+504","smsPossible":false},"MH":{"internationalCallingCode":"+692","smsPossible":true},"MO":{"internationalCallingCode":"+853","smsPossible":true},"MG":{"internationalCallingCode":"+261","smsPossible":true},"MW":{"internationalCallingCode":"+265","smsPossible":true},"ML":{"internationalCallingCode":"+223","smsPossible":true},"MT":{"internationalCallingCode":"+356","smsPossible":true},"MY":{"internationalCallingCode":"+60","smsPossible":true},"FM":{"internationalCallingCode":"+691","smsPossible":false},"MM":{"internationalCallingCode":"+95","smsPossible":true},"MX":{"internationalCallingCode":"+52","smsPossible":true},"MU":{"internationalCallingCode":"+230","smsPossible":true},"MR":{"internationalCallingCode":"+222","smsPossible":true},"MZ":{"internationalCallingCode":"+258","smsPossible":true},"MC":{"internationalCallingCode":"+377","smsPossible":true},"MV":{"internationalCallingCode":"+960","smsPossible":true},"MD":{"internationalCallingCode":"+373","smsPossible":false},"RO":{"internationalCallingCode":"+40","smsPossible":true},"MA":{"internationalCallingCode":"+212","smsPossible":true},"MN":{"internationalCallingCode":"+976","smsPossible":true},"ME":{"internationalCallingCode":"+382","smsPossible":true},"JO":{"internationalCallingCode":"+962","smsPossible":true},"LA":{"internationalCallingCode":"+856","smsPossible":true},"RU":{"internationalCallingCode":"+7","smsPossible":true},"LV":{"internationalCallingCode":"+371","smsPossible":true},"LT":{"internationalCallingCode":"+370","smsPossible":true},"LY":{"internationalCallingCode":"+218","smsPossible":true},"GQ":{"internationalCallingCode":"+240","smsPossible":false},"LI":{"internationalCallingCode":"+423","smsPossible":false},"LR":{"internationalCallingCode":"+231","smsPossible":true},"CN":{"internationalCallingCode":"+86","smsPossible":true},"LU":{"internationalCallingCode":"+352","smsPossible":true},"ZA":{"internationalCallingCode":"+27","smsPossible":true},"RW":{"internationalCallingCode":"+250","smsPossible":true},"LS":{"internationalCallingCode":"+266","smsPossible":false},"LB":{"internationalCallingCode":"+961","smsPossible":true}};

		$(function () {
			contactInfoInit();
		});
		
		function contactInfoInit() {
			
			
			Asw.get('representativeTelType').find('option[value="M1"]').attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_TYPE,
					Asw.ContactInput.Const.DATA_TYPE_VALUE_SMS);
			Asw.get('representativeTelCountry').children('option').each(function(i,option){
				var countryInfo = Asw.ContactInput.countryInfoMap[option.value];
				if (countryInfo === void 0) {
					
					return true;
				}
				$(option).attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_SMS_POSSIBLE, countryInfo.smsPossible);
				$(option).attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_COUNTRY_CODE, countryInfo.internationalCallingCode);
			});
			
			var smsCountrySelectSelector = 'select.js-countrySelect:not(#representativeTelCountry)'
			$(smsCountrySelectSelector).eq(0).children('option').each(function(i,option){
				var countryInfo = Asw.ContactInput.countryInfoMap[option.value];
				if (countryInfo === void 0) {
					
					return true;
				}
				$(smsCountrySelectSelector + ' option:nth-child(' + (i+1) + ')').attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_COUNTRY_CODE,
						countryInfo.internationalCallingCode);
			});
			
			var emergencyCountrySelectSelector = 'select.passengerEmergencyCountry.js-countrySelect:not(#representativeTelCountry)'
			$(emergencyCountrySelectSelector).eq(0).children('option').each(function(i,option){
				var countryInfo = Asw.ContactInput.countryInfoMap[option.value];
				if (countryInfo === void 0) {
					
					return true;
				}
				$(emergencyCountrySelectSelector + ' option:nth-child(' + (i+1) + ')').attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_COUNTRY_CODE,
						countryInfo.internationalCallingCode);
			});
		}
	</script><script type="text/javascript">
		var isInit = true;
		function changeTelType(_this, telNoInputId) {
			var $telNoInput = Asw.get(telNoInputId);
			var $telNoKind = Asw.get("representativeTelType");
			var telNo = Asw.getValueComparedToPlaceholder(telNoInputId);
			var telNoCountry = Asw.get("representativeTelCountry").val();

			if(true && !isInit) {
				if(telInputCheck(telNo, telNoCountry)) {
					if (_this.value == "M1") {
						telNo = exceptDomesticPrefix("09042504176", telNoCountry);
					} else if (_this.value == "B1") {
						telNo = exceptDomesticPrefix("03-5405-2172", telNoCountry);
					} else {
						telNo = exceptDomesticPrefix("090-4250-4176", telNoCountry);
					}
					$telNoInput.val(telNo);
				}
			}
			isInit = false;
		}
		
		function telInputCheck(telNo, telNoCountry) {
	        checkResult = false;
	        
	        var amcHndyTelNo = exceptDomesticPrefix("09042504176", telNoCountry);
	        var amcCmpTelNo = exceptDomesticPrefix("03-5405-2172", telNoCountry);
	        var amcTelNo = exceptDomesticPrefix("090-4250-4176", telNoCountry);
	
	        if(telNo == "" || telNo == amcHndyTelNo || telNo == amcCmpTelNo || telNo == amcTelNo) {
	        	checkResult = true;
	        }
	        return checkResult;
    	}
    	
    	function exceptDomesticPrefix(telNo, telNoCountry) {
    		if(telNoCountry == 'JP') {
    			if(telNo.substr(0, 1) === '0') {
    				return telNo.slice(1);
    			}
    		}
    		return telNo;
    	}
	</script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21d/international_asw/contents/cmn/script/asw-customerinfomationform.js?6e91953"></script><script type="text/javascript">
		$(document).ready(function(){
				Asw.enableTextWhenCheckBox('.cascadedSwitch');
				Asw.notPasteInput('.jsNotPasteInput');
			$(function(){
				Asw.alignHeightToParentTdElement( '.passengerInfoInputArea' );
				
					Asw.alignHeightToParentTdElement( '.passengerInfoInputText' );
				
				
				$('select.js-countrySelect').trigger('change');
			});
		});
	</script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21d/international_asw/contents/cmn/script/asw-session-keeper.js?6ec0730"></script><script type="text/javascript">
			$(document).ready(function() {
				Asw.SessionKeeper.initAndStart("sessionKeeperContainer", "520", "570", "PC");
			});
			</script><script type="text/javascript">
		Asw.addAjaxCallback(function(data) {
			// アコーディオンのメソッドと設定 
			if($('.jsAccordionSwitch .jsTriggerSwitch').length > 0){
				var events = $._data($('.jsAccordionSwitch .jsTriggerSwitch').get(0), "events");
				if(events === undefined || events.click === undefined) {
					Asw.AccordionInfo('.jsAccordionSwitch', {
						'triggerSwitch': '.jsAccordionSwitch .jsTriggerSwitch',
						'listClass': '.jsAccordionSwitchList',
						'visibilityHidden': 'toggleContents'
					});
				}
			}
		});
	</script><script type="text/javascript">
	Asw.Price = Asw.Price || {};
	// 整数の区切り文字
	Asw.Price.separateString = ',';
	
	// 整数の区切り桁数
	Asw.Price.separateDigitNumber = 3;
	
	// 小数点
	Asw.Price.decimalPointString = '.';
	
	// 小数点以降の有効桁数
	Asw.Price.significantDigit = 0;
	
	// 表示金額の単位
	Asw.Price.currencyUnit = '円';
	</script><script type="text/javascript">
			$(document).ready(function() {
				$('#preLoadingArea').css('visibility', 'hidden');
			});
		</script><noscript><img src="https://aswbe-i.ana.co.jp/akam/13/pixel_50635fd6?a=dD1iODM3Yzg4NjNlNmQzYWQ2MWYxOTk0MDFhZDJkZTlhZDc3MWFjNWNiJmpzPW9mZg==" style="visibility: hidden; position: absolute; left: -999px; top: -999px;" /></noscript><script type="text/javascript"  src="/Q1XS4o/Cn_ls/KfDO7/xA/t9OfhmEwSiGQaG/NG4dCQE/bw/QJBChQIQEB"></script></body>
</html>