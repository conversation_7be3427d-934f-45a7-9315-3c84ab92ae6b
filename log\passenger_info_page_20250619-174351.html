<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="ja-<PERSON>" xml:lang="ja-<PERSON>">

	
	<!-- skey = 2025/06/19 18:43:51 rei22h iZyHknED4g dljdmx+56b  --><head id="j_idt53">
    <base href="about:blank">
    <style>
        /* Hide elements that might try to load external resources, as a fallback. */
        img, svg, video, iframe, link[rel="stylesheet"], script { display: none !important; }
    </style>

		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
			<meta http-equiv="X-UA-Compatible" content="IE=EmulateIE9; IE=EmulateIE10" />
			<meta http-equiv="X-UA-Compatible" content="IE=7" />
			<meta http-equiv="X-UA-Compatible" content="IE=edge" />
		<meta name="description" content="" />
		<meta name="keywords" content="" />
		<meta name="format-detection" content="telephone=no" />
		<script type="text/javascript" src="/international_asw/dynatrace/ruxitagentjs_ICANVfhqrux_10273230920145353.js" data-dtconfig="rid=RID_-*********|rpid=**********|domain=aswbe-i.ana.co.jp|reportUrl=https://bf90419wvr.bf.dynatrace.com/bf|app=78bf0b58acf6ed13|cors=1|owasp=1|featureHash=ICANVfhqrux|xb=www^bs.google-analytics^bs.com^pana^bs.demdex^bs.net^pcollect^bs.tealiumiq^bs.com^pallnipponairways^bs.australia-3^bs.evergage^bs.com^pallnipponairwayscolt^bs.tt^bs.omtrdc^bs.net|rdnt=1|uxrgce=1|cuc=k8vpgh61|mel=100000|expw=1|dpvc=1|md=mdcc1=bAsw.PnrOutput.recordLocator,mdcc2=bAsw.PnrOutput.paymentDetailInfoList[0].fopCode,mdcc3=bAsw.PnrOutput.ticketedFlag,mdcc4=bAsw.PnrOutput.nhPnrFlag,mdcc5=bAsw.PnrOutput.itinerarySummary.departureDateYyyymmdd,mdcc6=bAsw.SearchCriteriaOutput.cffCodeInput,mdcc7=a#main ^rb div.section.sectionNoFlow ^rb table ^rb tbody ^rb tr ^rb td.flightNumber.lastCol ^rb span ^rb span:nth-child(2),mdcc8=cunique|lastModification=1750184416409|tp=500,50,0|agentUri=/international_asw/dynatrace/ruxitagentjs_ICANVfhqrux_10273230920145353.js"></script><link rel="shortcut icon" href="https://www.ana.co.jp/favicon.ico" />
		<title>お客様情報入力 | ANA</title>
		<noscript><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/css/asw_noscript_pc.css?99001fc" />
		</noscript>

		<!--  [ Adobe Target ] -->
		<script type="text/javascript" src="https://www.ana.co.jp/behavior/adobetarget/target_intbe.js"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/jquery-1.8.3.min.js?717d3c0"></script><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/ja/css/asw_initialize.css?99001fc" /><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/css/asw_screen_pc.css?99001fc" /><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/css/asw_common_pc.css?99001fc" /><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/css/asw_forminput_pc.css?99001fc" /><script type="text/javascript">
		var Asw = Asw || {};
		Asw.GlobalWord = Asw.GlobalWord || {};
		
		Asw.GlobalWord.toolTipCloseIconAltText = "閉じる";
		
		</script><script >bazadebezolkohpepadr="1132109034"</script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/akam/13/437a9c79"  defer></script></head><body>
		
		<noscript>当サイトをご利用になるためには、JavaScript対応のブラウザが必要です。設定でJavaScriptを有効にしてください。有効にできない場合はANA電話窓口へお問い合わせください。
		</noscript>
		<div id="preLoadingArea"></div>
	
	<div id="cmnHeader" class="platinum">
		<!--===== cmnHeader =====-->
		<div class="headerWrapper">
			<div class="header">
				<p class="logo"><a href="https://www.ana.co.jp/asw/AMCTopServlet?type=a" tabindex="-1" aria-hidden="true"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/ana_logo_navy.png?717d3c0" alt="ANA Inspiration of JAPAN。リンク先はマイレージクラブトップページです。" height="28" width="274" /></a>
				</p>
					<ul class="headerMenu">
						<li><a href="https://www.ana.co.jp/asw/AMCTopServlet?type=a">ANAマイレージクラブトップページ</a>
						</li>
						<li><a href="https://www.ana.co.jp/other/int/meta/0005.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">お問い合わせ<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
						</li>
							<li class="btnLogoutArea"><a href="https://aswbe-i.ana.co.jp/rei22h/international_asw/rest/logout" role="button" class="btnBase btnLogout">ログアウト</a>
							</li>
					</ul>
			</div>
		</div>
			<div class="amcUserInfoWrapper">
				<div class="amcUserInfo">
					<div class="userNameArea">
						<table>
							<tr>	
									<td class="statusIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_status_05.png?717d3c0" alt="プラチナサービスメンバー" height="46" width="60" />
									</td>
								<td class="userName">佐藤 直樹 <span>様</span>
								</td>
							</tr>					
						</table>
					</div>
						<div class="balance">
								<dl class="upgradePointBalance">
									<dt>アップグレードポイント残高
									</dt>
									<dd>
											<dl>
												<dt>(2025年度)
												</dt>
												<dd>
													<em>20
													</em>ポイント
												</dd>
											</dl>
									</dd>
								</dl>
							<dl class="mileBalance">
								<dt>マイル口座残高
								</dt>
								<dd><em>75,663</em><span class="currencyCode">マイル</span>
								</dd>
							</dl>
						</div>
				</div>
					<p class="nowTime">6月19日18時43分現在
					</p>
			</div>
		<!--===== /cmnHeader =====-->
	</div>
		<div id="sessionKeeperContainer" class="sessionKeepingContainer"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/bg_modalWindow.png?717d3c0" alt="" height="0" width="0" />
			<div id="extendSessionExpiredDialog" class="modalExpire modalSmall modalContainer" role="dialog" aria-labelledby="extendSessionExpiredDialogTitle" aria-hidden="true">
<form id="sessionKeeperContainer:j_idt183" name="sessionKeeperContainer:j_idt183" method="post" action="https://aswbe-i.ana.co.jp/rei22h/international_asw/pages/award/reservation/award_mandatory_passenger_information_input.xhtml?aswcid=1&amp;rand=20250619184351iZyHknED4g" autocomplete="off" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="sessionKeeperContainer:j_idt183" value="sessionKeeperContainer:j_idt183" />
<input type="hidden" name="sessionKeeperContainer:j_idt183_operationTicket" value="dljdmx+56bdfe9f9ba636b530a9c9bdb5cacf86~0lHCy2QZWgDgBBtLCW8oTyUCnIpg2bI4zzOxThsM!1750326218385.aere-xml-controller-67d4778877-jgdjz" /><input type="hidden" name="sessionKeeperContainer:j_idt183_cmnPageTicket" value="4" />
					<h1 id="extendSessionExpiredDialogTitle" class="dialogTitle">まもなくセッションが終了します</h1>
					<div id="extendSessionExpiredDialogMessage" class="dialogMessage" tabindex="-1">このままお手続きを進める場合は、［延長］ボタンを押してください。</div>
					<p class="modalButton"><input id="sessionKeeperContainer:cmnSessionKeepingButton" type="submit" name="sessionKeeperContainer:cmnSessionKeepingButton" value="延長" class="btnBase btnModal btnMainStream" onclick="mojarra.ab(this,event,'action',0,0,{'onevent':function(data) {Asw.doCommonAjaxCallback(data,false, event); Asw.SessionKeeper.doSessionKeep(data);},'onerror':function(data) {Asw.doCommonAjaxErrorCallback(data); }});return false" />
					</p><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:0" value="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" autocomplete="off" />
</form>
			</div>
			<div id="showSessionExpiredDialog" class="modalExpire modalSmall modalContainer" role="dialog" aria-labelledby="showSessionExpiredDialogTitle" aria-hidden="true">
				<h1 id="showSessionExpiredDialogTitle" class="dialogTitle">セッションが終了しました</h1>
				<div id="showSessionExpiredDialogMessage" class="dialogMessage" tabindex="-1">一定時間操作されなかったため、お手続きを進めることができません。お手数ですが、再度トップページからお手続きください。</div>
				<p class="modalButton">
					<input type="submit" value="閉じる" class="btnBase btnModal btnMainStream" onclick="Asw.SessionKeeper.closeTimeoutDialog(); return false;" />
				</p>
			</div>
		</div>
		<!--===== dialogMessages =====--><div id="cmnModalMessages"></div>
		<div id="prebookConfirmDialog" class="modalContainer modalConfirm modalSmall " aria-hidden="true" role="dialog">
<form id="prebookConfirmDialog:j_idt403" name="prebookConfirmDialog:j_idt403" method="post" action="https://aswbe-i.ana.co.jp/rei22h/international_asw/pages/award/reservation/award_mandatory_passenger_information_input.xhtml?aswcid=1&amp;rand=20250619184351iZyHknED4g" autocomplete="off" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="prebookConfirmDialog:j_idt403" value="prebookConfirmDialog:j_idt403" />
<input type="hidden" name="prebookConfirmDialog:j_idt403_operationTicket" value="dljdmx+56bdfe9f9ba636b530a9c9bdb5cacf86~0lHCy2QZWgDgBBtLCW8oTyUCnIpg2bI4zzOxThsM!1750326218385.aere-xml-controller-67d4778877-jgdjz" /><input type="hidden" name="prebookConfirmDialog:j_idt403_cmnPageTicket" value="4" /><div class="dialogMessage" tabindex="0"><p>この後、名・姓の修正や変更ができません。</p><p>「搭乗者選択」欄の氏名が「名」「姓」の順番で表示されていることを再確認ください。</p><p>例）<strong class="highlight">空野 太郎</strong> 様 → MR <strong class="highlight">TARO SORANO</strong></p><p>保安上の理由により、航空券とパスポートのお名前が同じ表記でない場合はご搭乗いただくことができませんので必ず再度ご確認ください。</p></div>
					
					<ul class="modalBtnHorizontal">
							
							<li><input type="submit" name="prebookConfirmDialog:j_idt419" value="戻る" aria-controls="prebookConfirmDialog" class="btnBase btnModal btnCancel" onclick="Asw.Dialog.getInstance('prebookConfirmDialog').close(event, Asw.DialogSequencer.isActiveSequencerNotDisplayedLastDialog());  return false;" />
							</li>
						
						<li class="btnArrowNext"><input type="submit" name="prebookConfirmDialog:j_idt422" value="OK" aria-controls="prebookConfirmDialog" class="btnBase btnModal btnMainStream " onclick="Asw.Dialog.getInstance('prebookConfirmDialog').callOpener('NORMAL', event); return false;" />
						</li>
					</ul><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:1" value="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" autocomplete="off" />
</form>
		</div>
		<!--===== /dialogMessages =====--><div id="cmnContainer">
		<!--=========== .cmnContainer ============--><div id="cmnEmergencyNotice"></div><div id="cmnTitle" class="hgroup ">
		<h1 class="visuallyHidden">お客様情報入力</h1></div>

		<div id="cmnWrapper">
		<!--===== cmnWrapper =====-->
		<ol class="mainFlow" aria-label="完了までの手順、">
			<li><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_flow_01.png?717d3c0" alt="1" height="20" width="28" />フライト検索</li><!--
			--><li><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_flow_02_on.png?717d3c0" alt="2" height="20" width="28" /><span class="visuallyHidden">現在のステップ、</span>お客様情報入力</li><!--
			--><li><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_flow_03.png?717d3c0" alt="3" height="20" width="28" />お支払い情報</li><!--
			--><li class="flowLast"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_flow4_04.png?717d3c0" alt="4" height="20" width="28" />完了</li>
		</ol>

	<div id="main">
	<!--=========== main =============-->
		<ul class="iconExample">
				<li class="requiredIconExample">
					<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="" height="7" width="7" /></span>必須項目です	
				</li>
		</ul>
<form id="j_idt526" name="j_idt526" method="post" action="https://aswbe-i.ana.co.jp/rei22h/international_asw/pages/award/reservation/award_mandatory_passenger_information_input.xhtml?aswcid=1&amp;rand=20250619184351iZyHknED4g" autocomplete="off" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="j_idt526" value="j_idt526" />
<input type="hidden" name="j_idt526_operationTicket" value="dljdmx+56bdfe9f9ba636b530a9c9bdb5cacf86~0lHCy2QZWgDgBBtLCW8oTyUCnIpg2bI4zzOxThsM!1750326218385.aere-xml-controller-67d4778877-jgdjz" /><input type="hidden" name="j_idt526_cmnPageTicket" value="4" />

<div class="section">
	<h2>搭乗者選択<a href="https://www.ana.co.jp/other/int/meta/0086.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="特典のご利用に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a></h2>
	<table class="passengerInfo">
		<thead>
			<tr>
				
				<th class="name">氏名</th>
				
				<th class="dateOfBirthWide" id="dateOfBirthHeader">生年月日</th>
				
				<th class="gender lastCol" id="genderHeader">性別<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span></th>
				
			</tr>
		</thead>
		<tbody>
				
				<tr>
					<th class="parent_01" id="parentHeader01">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:0:passenger" type="checkbox" name="passengers:0:passenger" checked="checked" data-number="1" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:0:passenger">NAOKI SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader01">1963年06月05日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader01">				
							<span class="formWrapper">
							<label for="passengers:0:gender" class="visuallyHidden">NAOKI SATOの性別</label><select id="passengers:0:gender" name="passengers:0:gender" size="1">	<option value="">選択してください</option>
	<option value="M" selected="selected">男性</option>
	<option value="F">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_02" id="parentHeader02">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:1:passenger" type="checkbox" name="passengers:1:passenger" data-number="2" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:1:passenger">REO SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader02">1995年05月18日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader02">				
							<span class="formWrapper">
							<label for="passengers:1:gender" class="visuallyHidden">REO SATOの性別</label><select id="passengers:1:gender" name="passengers:1:gender" size="1">	<option value="">選択してください</option>
	<option value="M" selected="selected">男性</option>
	<option value="F">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_03" id="parentHeader03">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:2:passenger" type="checkbox" name="passengers:2:passenger" data-number="3" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:2:passenger">ERI SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader03">1963年08月14日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader03">				
							<span class="formWrapper">
							<label for="passengers:2:gender" class="visuallyHidden">ERI SATOの性別</label><select id="passengers:2:gender" name="passengers:2:gender" size="1">	<option value="">選択してください</option>
	<option value="M">男性</option>
	<option value="F" selected="selected">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_04" id="parentHeader04">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:3:passenger" type="checkbox" name="passengers:3:passenger" data-number="4" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:3:passenger">TAISHI SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader04">1992年12月03日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader04">				
							<span class="formWrapper">
							<label for="passengers:3:gender" class="visuallyHidden">TAISHI SATOの性別</label><select id="passengers:3:gender" name="passengers:3:gender" size="1">	<option value="">選択してください</option>
	<option value="M" selected="selected">男性</option>
	<option value="F">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_05" id="parentHeader05">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:4:passenger" type="checkbox" name="passengers:4:passenger" data-number="5" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:4:passenger">HIROMI FUJIMOTO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader05">1961年11月23日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader05">				
							<span class="formWrapper">
							<label for="passengers:4:gender" class="visuallyHidden">HIROMI FUJIMOTOの性別</label><select id="passengers:4:gender" name="passengers:4:gender" size="1">	<option value="">選択してください</option>
	<option value="M">男性</option>
	<option value="F" selected="selected">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
		</tbody>
	</table>
</div>
		<div class="section">
			<h2 class="midstream">連絡先情報
			</h2>
			<h3>代表者
			</h3>
	
	
	
	<div class="formInput">
		<fieldset>
			<legend><label>代表者</label></legend>
			<dl id="contactEmailAddressInfo">
				<dt class="hasRequiredIcon">メールアドレス<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span><a href="https://www.ana.co.jp/other/int/meta/0057.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="メールの宛先と内容に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a>
				</dt>
				<dd>
					<ul class="verticalList">
						<li class="nonMarginTop"><label for="representativeEmail" class="visuallyHidden">代表者メールアドレス</label><input id="representativeEmail" type="text" name="representativeEmail" value="<EMAIL>" class="widthLarge" placeholder="例：<EMAIL>" />
						</li>
						<li>
							<span class="supplementTxt" id="confirmMailAddressForRepresentative">確認のため再度ご入力ください。
							</span><label for="confirmrepresentativeEmail" class="visuallyHidden">確認用メールアドレス</label><input id="confirmrepresentativeEmail" type="text" name="confirmrepresentativeEmail" value="<EMAIL>" class="widthLarge jsNotPasteInput" placeholder="例：<EMAIL>" aria-describedby="confirmMailAddressForRepresentative" />
						</li>
					</ul>
				</dd>
					<dd style="display:none;"><input id="updateAlwaysInfo" type="checkbox" name="updateAlwaysInfo" /><label for="updateAlwaysInfo">ログイン中の会員情報へこのメールアドレスを登録</label>
					</dd>
			</dl>
			<dl>
				<dt>電話番号<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span>
				</dt>
				<dd class="js-countryWrap">
					<ul>
						<li><label for="representativeTelType" class="visuallyHidden">電話番号種別</label><select id="representativeTelType" name="representativeTelType" class="basicWidth js-typeSelect" size="1" onchange="changeTelType(this,'representativeTel')">	<option value="M1" selected="selected">携帯</option>
	<option value="B1">会社</option>
	<option value="H1">自宅</option>
</select>
						</li>
						<li><label for="representativeTelCountry" class="visuallyHidden">電話番号の国/地区選択</label><select id="representativeTelCountry" name="representativeTelCountry" class="js-countrySelect" size="1">	<option value="" data-sms-possible="true" data-country-code="---">選択してください</option>
	<option value="JP" selected="selected" data-sms-possible="true">日本</option>
	<option value="-" data-sms-possible="true" data-country-code="---">-----------</option>
	<option value="IS" data-sms-possible="true">アイスランド</option>
	<option value="IE" data-sms-possible="true">アイルランド</option>
	<option value="AZ" data-sms-possible="true">アゼルバイジャン</option>
	<option value="AF" data-sms-possible="true">アフガニスタン</option>
	<option value="US" data-sms-possible="true">アメリカ合衆国/カナダ</option>
	<option value="AE" data-sms-possible="true">アラブ首長国連邦</option>
	<option value="DZ" data-sms-possible="true">アルジェリア</option>
	<option value="AR" data-sms-possible="true">アルゼンチン</option>
	<option value="AW" data-sms-possible="true">アルバ</option>
	<option value="AL" data-sms-possible="true">アルバニア</option>
	<option value="AM" data-sms-possible="true">アルメニア</option>
	<option value="AO" data-sms-possible="true">アンゴラ</option>
	<option value="AG" data-sms-possible="true">アンティグア・バーブーダ</option>
	<option value="AD" data-sms-possible="true">アンドラ</option>
	<option value="YE" data-sms-possible="true">イエメン</option>
	<option value="GB" data-sms-possible="true">イギリス</option>
	<option value="IL" data-sms-possible="true">イスラエル</option>
	<option value="IT" data-sms-possible="true">イタリア</option>
	<option value="IQ" data-sms-possible="true">イラク</option>
	<option value="IR" data-sms-possible="true">イラン</option>
	<option value="IN" data-sms-possible="true">インド</option>
	<option value="ID" data-sms-possible="true">インドネシア</option>
	<option value="UG" data-sms-possible="true">ウガンダ</option>
	<option value="UA" data-sms-possible="true">ウクライナ</option>
	<option value="UZ" data-sms-possible="true">ウズベキスタン</option>
	<option value="UY" data-sms-possible="true">ウルグアイ</option>
	<option value="EC" data-sms-possible="true">エクアドル</option>
	<option value="EG" data-sms-possible="true">エジプト</option>
	<option value="EE" data-sms-possible="true">エストニア</option>
	<option value="SZ" data-sms-possible="true">エスワティニ</option>
	<option value="ET" data-sms-possible="true">エチオピア</option>
	<option value="ER" data-sms-possible="true">エリトリア</option>
	<option value="SV" data-sms-possible="true">エルサルバドル</option>
	<option value="AU" data-sms-possible="true">オーストラリア</option>
	<option value="AT" data-sms-possible="true">オーストリア</option>
	<option value="OM" data-sms-possible="true">オマーン</option>
	<option value="NL" data-sms-possible="true">オランダ</option>
	<option value="GH" data-sms-possible="true">ガーナ</option>
	<option value="CV" data-sms-possible="true">カーボベルデ</option>
	<option value="GY" data-sms-possible="true">ガイアナ</option>
	<option value="KZ" data-sms-possible="true">カザフスタン</option>
	<option value="QA" data-sms-possible="true">カタール</option>
	<option value="CA" data-sms-possible="true">カナダ/アメリカ合衆国</option>
	<option value="GA" data-sms-possible="true">ガボン</option>
	<option value="CM" data-sms-possible="true">カメルーン</option>
	<option value="KR" data-sms-possible="true">韓国</option>
	<option value="GM" data-sms-possible="true">ガンビア</option>
	<option value="KH" data-sms-possible="true">カンボジア</option>
	<option value="KP" data-sms-possible="true">北朝鮮</option>
	<option value="MK" data-sms-possible="true">北マケドニア</option>
	<option value="GN" data-sms-possible="true">ギニア</option>
	<option value="GW" data-sms-possible="true">ギニアビサウ</option>
	<option value="CY" data-sms-possible="true">キプロス</option>
	<option value="CU" data-sms-possible="true">キューバ</option>
	<option value="GR" data-sms-possible="true">ギリシャ</option>
	<option value="KI" data-sms-possible="true">キリバス</option>
	<option value="KG" data-sms-possible="true">キルギス</option>
	<option value="GT" data-sms-possible="true">グアテマラ</option>
	<option value="GU" data-sms-possible="true">グアム</option>
	<option value="KW" data-sms-possible="true">クウェート</option>
	<option value="GD" data-sms-possible="true">グレナダ</option>
	<option value="HR" data-sms-possible="true">クロアチア</option>
	<option value="KY" data-sms-possible="true">ケイマン諸島</option>
	<option value="KE" data-sms-possible="true">ケニア</option>
	<option value="CI" data-sms-possible="true">コートジボワール</option>
	<option value="CR" data-sms-possible="true">コスタリカ</option>
	<option value="KM" data-sms-possible="true">コモロ</option>
	<option value="CO" data-sms-possible="true">コロンビア</option>
	<option value="CG" data-sms-possible="true">コンゴ共和国</option>
	<option value="CD" data-sms-possible="true">コンゴ民主共和国</option>
	<option value="SA" data-sms-possible="true">サウジアラビア</option>
	<option value="ZM" data-sms-possible="true">ザンビア</option>
	<option value="SM" data-sms-possible="true">サンマリノ</option>
	<option value="SL" data-sms-possible="true">シエラレオネ</option>
	<option value="DJ" data-sms-possible="true">ジブチ</option>
	<option value="JM" data-sms-possible="true">ジャマイカ</option>
	<option value="GE" data-sms-possible="true">ジョージア</option>
	<option value="SY" data-sms-possible="true">シリア</option>
	<option value="SG" data-sms-possible="true">シンガポール</option>
	<option value="ZW" data-sms-possible="true">ジンバブエ</option>
	<option value="CH" data-sms-possible="true">スイス</option>
	<option value="SE" data-sms-possible="true">スウェーデン</option>
	<option value="SD" data-sms-possible="true">スーダン</option>
	<option value="ES" data-sms-possible="true">スペイン</option>
	<option value="SR" data-sms-possible="true">スリナム</option>
	<option value="LK" data-sms-possible="true">スリランカ</option>
	<option value="SK" data-sms-possible="true">スロバキア</option>
	<option value="SI" data-sms-possible="true">スロベニア</option>
	<option value="SC" data-sms-possible="true">セイシェル</option>
	<option value="GQ" data-sms-possible="true">赤道ギニア</option>
	<option value="SN" data-sms-possible="true">セネガル</option>
	<option value="RS" data-sms-possible="true">セルビア</option>
	<option value="KN" data-sms-possible="true">セントクリストファー・ネイビス</option>
	<option value="LC" data-sms-possible="true">セントルシア</option>
	<option value="SB" data-sms-possible="true">ソロモン諸島</option>
	<option value="TH" data-sms-possible="true">タイ</option>
	<option value="TW" data-sms-possible="true">台湾</option>
	<option value="TJ" data-sms-possible="true">タジキスタン</option>
	<option value="TZ" data-sms-possible="true">タンザニア</option>
	<option value="CZ" data-sms-possible="true">チェコ</option>
	<option value="TD" data-sms-possible="true">チャド</option>
	<option value="CF" data-sms-possible="true">中央アフリカ共和国</option>
	<option value="CN" data-sms-possible="true">中国</option>
	<option value="TN" data-sms-possible="true">チュニジア</option>
	<option value="CL" data-sms-possible="true">チリ</option>
	<option value="TV" data-sms-possible="true">ツバル</option>
	<option value="DK" data-sms-possible="true">デンマーク</option>
	<option value="DE" data-sms-possible="true">ドイツ</option>
	<option value="TG" data-sms-possible="true">トーゴ</option>
	<option value="DO" data-sms-possible="true">ドミニカ共和国</option>
	<option value="DM" data-sms-possible="true">ドミニカ国</option>
	<option value="TT" data-sms-possible="true">トリニダード・トバゴ</option>
	<option value="TM" data-sms-possible="true">トルクメニスタン</option>
	<option value="TR" data-sms-possible="true">トルコ</option>
	<option value="TO" data-sms-possible="true">トンガ</option>
	<option value="NG" data-sms-possible="true">ナイジェリア</option>
	<option value="NR" data-sms-possible="true">ナウル</option>
	<option value="NA" data-sms-possible="true">ナミビア</option>
	<option value="NI" data-sms-possible="true">ニカラグア</option>
	<option value="NE" data-sms-possible="true">ニジェール</option>
	<option value="NZ" data-sms-possible="true">ニュージーランド</option>
	<option value="NP" data-sms-possible="true">ネパール</option>
	<option value="NO" data-sms-possible="true">ノルウェー</option>
	<option value="BH" data-sms-possible="true">バーレーン</option>
	<option value="HT" data-sms-possible="true">ハイチ</option>
	<option value="PK" data-sms-possible="true">パキスタン</option>
	<option value="VA" data-sms-possible="true">バチカン</option>
	<option value="PA" data-sms-possible="true">パナマ</option>
	<option value="VU" data-sms-possible="true">バヌアツ</option>
	<option value="BS" data-sms-possible="true">バハマ</option>
	<option value="PG" data-sms-possible="true">パプアニューギニア</option>
	<option value="BM" data-sms-possible="true">バミューダ</option>
	<option value="PW" data-sms-possible="true">パラオ</option>
	<option value="PY" data-sms-possible="true">パラグアイ</option>
	<option value="BB" data-sms-possible="true">バルバドス</option>
	<option value="PS" data-sms-possible="true">パレスチナ</option>
	<option value="HU" data-sms-possible="true">ハンガリー</option>
	<option value="BD" data-sms-possible="true">バングラデシュ</option>
	<option value="TL" data-sms-possible="true">東ティモール</option>
	<option value="FJ" data-sms-possible="true">フィジー</option>
	<option value="PH" data-sms-possible="true">フィリピン</option>
	<option value="FI" data-sms-possible="true">フィンランド</option>
	<option value="BT" data-sms-possible="true">ブータン</option>
	<option value="PR" data-sms-possible="true">プエルトリコ</option>
	<option value="BR" data-sms-possible="true">ブラジル</option>
	<option value="FR" data-sms-possible="true">フランス</option>
	<option value="PF" data-sms-possible="true">フランス領ポリネシア</option>
	<option value="BG" data-sms-possible="true">ブルガリア</option>
	<option value="BF" data-sms-possible="true">ブルキナファソ</option>
	<option value="BN" data-sms-possible="true">ブルネイ</option>
	<option value="BI" data-sms-possible="true">ブルンジ</option>
	<option value="VN" data-sms-possible="true">ベトナム</option>
	<option value="BJ" data-sms-possible="true">ベナン</option>
	<option value="VE" data-sms-possible="true">ベネズエラ</option>
	<option value="BY" data-sms-possible="true">ベラルーシ</option>
	<option value="BZ" data-sms-possible="true">ベリーズ</option>
	<option value="PE" data-sms-possible="true">ペルー</option>
	<option value="BE" data-sms-possible="true">ベルギー</option>
	<option value="PL" data-sms-possible="true">ポーランド</option>
	<option value="BA" data-sms-possible="true">ボスニア・ヘルツェゴビナ</option>
	<option value="BW" data-sms-possible="true">ボツワナ</option>
	<option value="BO" data-sms-possible="true">ボリビア</option>
	<option value="PT" data-sms-possible="true">ポルトガル</option>
	<option value="HK" data-sms-possible="true">香港</option>
	<option value="HN" data-sms-possible="true">ホンジュラス</option>
	<option value="MH" data-sms-possible="true">マーシャル諸島</option>
	<option value="MO" data-sms-possible="true">マカオ</option>
	<option value="MG" data-sms-possible="true">マダガスカル</option>
	<option value="MW" data-sms-possible="true">マラウイ</option>
	<option value="ML" data-sms-possible="true">マリ</option>
	<option value="MT" data-sms-possible="true">マルタ</option>
	<option value="MY" data-sms-possible="true">マレーシア</option>
	<option value="FM" data-sms-possible="true">ミクロネシア連邦</option>
	<option value="ZA" data-sms-possible="true">南アフリカ</option>
	<option value="MM" data-sms-possible="true">ミャンマー</option>
	<option value="MX" data-sms-possible="true">メキシコ</option>
	<option value="MU" data-sms-possible="true">モーリシャス</option>
	<option value="MR" data-sms-possible="true">モーリタニア</option>
	<option value="MZ" data-sms-possible="true">モザンビーク</option>
	<option value="MC" data-sms-possible="true">モナコ</option>
	<option value="MV" data-sms-possible="true">モルディヴ</option>
	<option value="MD" data-sms-possible="true">モルドバ</option>
	<option value="MA" data-sms-possible="true">モロッコ</option>
	<option value="MN" data-sms-possible="true">モンゴル</option>
	<option value="ME" data-sms-possible="true">モンテネグロ</option>
	<option value="JO" data-sms-possible="true">ヨルダン</option>
	<option value="LA" data-sms-possible="true">ラオス人民民主共和国</option>
	<option value="LV" data-sms-possible="true">ラトビア</option>
	<option value="LT" data-sms-possible="true">リトアニア</option>
	<option value="LY" data-sms-possible="true">リビア</option>
	<option value="LI" data-sms-possible="true">リヒテンシュタイン</option>
	<option value="LR" data-sms-possible="true">リベリア</option>
	<option value="RO" data-sms-possible="true">ルーマニア</option>
	<option value="LU" data-sms-possible="true">ルクセンブルク</option>
	<option value="RW" data-sms-possible="true">ルワンダ</option>
	<option value="LS" data-sms-possible="true">レソト</option>
	<option value="LB" data-sms-possible="true">レバノン</option>
	<option value="RU" data-sms-possible="true">ロシア</option>
</select>
						</li>
						<li class="numberInputWrap">
							<span id="numberInputInfo1" class="supplementTxt marginTop">先頭に0(ゼロ)がある場合、0(ゼロ)を抜いてください(一部、国・地域除く)。携帯電話番号の場合は、携帯電話を契約された国を選択ください。</span>
							<div class="numberRowWrap">
								<div class="block">
									
									<span class="visuallyHidden">国番号</span>
									<span class="viewInput widthMini js-countryCode"></span>
								</div>
								<div class="block"><label for="representativeTel" class="visuallyHidden">電話番号</label><input id="representativeTel" type="text" name="representativeTel" value="9042504176" class="widthMiddle" placeholder="例：9012345678" maxlength="30" aria-describedby="numberInputInfo1" />
								</div>
							</div>
						</li>
					</ul>
				</dd>
			</dl>
		</fieldset>
	</div>
	<div class="titleWrapper">
		<h3>
			<span class="text">eメール・SMSに関するご案内<a href="https://www.ana.co.jp/other/int/meta/0057.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="eメール・SMSに関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a></span>
		</h3>
	</div>
	<div class="formInput">
	<h4 class="formAreaTitle">主に搭乗・運航に関わるeメールの送付先設定（米国へ通知いたします）<a href="https://www.ana.co.jp/other/int/meta/0629.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="米国入国用eメール・SMS・電話番号に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a>
	</h4>
	<div class="paxSettingArea js-mailFormWrap onePax is-open">
			<div class="settingArea">
					<dl>
						<dt class="breakWord">NAOKI SATO
						</dt>
						<dd>
							<fieldset>
								<legend>NAOKI SATOのeメール送付先および米国到着時に提供するメールアドレス</legend>代表者と同じ
							</fieldset>
						</dd>
					</dl>
			</div>
	</div>
	<h4 class="formAreaTitle hasTopLine">主に搭乗・運航に関わるSMSの送付先および米国で受信できる電話番号の設定（米国へ通知いたします）<a href="https://www.ana.co.jp/other/int/meta/0629.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="米国入国用eメール・SMS・電話番号に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a>
	</h4>
	<div class="paxSettingArea js-smsFormWrap onePax is-open">
			<div class="settingArea">
					<dl>
						<dt class="breakWord">NAOKI SATO<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span>
						</dt>
						<dd>
							<fieldset>
								<legend>NAOKI SATOの1つ目の電話番号</legend>
									<ul class="verticalList js-formItem">
										<li><input type="radio" id="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSameAsRepresentativeSms:radioItem" name="contactsSms:0:recipientSmsEachPersonSelection:radioGroup" value="0" checked="checked" /><label for="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSameAsRepresentativeSms:radioItem">代表者電話番号と同じ</label>
										</li>
										<li><input type="radio" id="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSmsPerPassenger:radioItem" name="contactsSms:0:recipientSmsEachPersonSelection:radioGroup" value="1" /><label for="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSmsPerPassenger:radioItem">電話番号を指定</label>
											<div class="indentArea js-formImputArea">
												<span id="confirmTelNumberForRepresentative:0" class="supplementTxt marginTop">先頭に0(ゼロ)がある場合、0(ゼロ)を抜いてください(一部、国・地域除く)。携帯電話番号の場合は、携帯電話を契約された国を選択ください。</span>
												<ul class="countryNumberInput js-countryWrap">
													<li><label for="contactsSms:0:recipientSmsEachPersonSelection:passengerSmsCountry" class="visuallyHidden">電話番号の国/地区選択</label><select id="contactsSms:0:recipientSmsEachPersonSelection:passengerSmsCountry" name="contactsSms:0:recipientSmsEachPersonSelection:passengerSmsCountry" class="countrySelect js-countrySelect" size="1">	<option value="" data-country-code="---">選択してください</option>
	<option value="JP">日本</option>
	<option value="-" data-country-code="---">-----------</option>
	<option value="IE">アイルランド</option>
	<option value="AZ">アゼルバイジャン</option>
	<option value="AF">アフガニスタン</option>
	<option value="US">アメリカ合衆国/カナダ</option>
	<option value="AE">アラブ首長国連邦</option>
	<option value="DZ">アルジェリア</option>
	<option value="AR">アルゼンチン</option>
	<option value="AW">アルバ</option>
	<option value="AL">アルバニア</option>
	<option value="AM">アルメニア</option>
	<option value="AG">アンティグア・バーブーダ</option>
	<option value="AD">アンドラ</option>
	<option value="YE">イエメン</option>
	<option value="GB">イギリス</option>
	<option value="IL">イスラエル</option>
	<option value="IT">イタリア</option>
	<option value="IQ">イラク</option>
	<option value="IR">イラン</option>
	<option value="IN">インド</option>
	<option value="ID">インドネシア</option>
	<option value="UG">ウガンダ</option>
	<option value="UA">ウクライナ</option>
	<option value="UZ">ウズベキスタン</option>
	<option value="UY">ウルグアイ</option>
	<option value="EC">エクアドル</option>
	<option value="EG">エジプト</option>
	<option value="EE">エストニア</option>
	<option value="ER">エリトリア</option>
	<option value="SV">エルサルバドル</option>
	<option value="AU">オーストラリア</option>
	<option value="AT">オーストリア</option>
	<option value="OM">オマーン</option>
	<option value="NL">オランダ</option>
	<option value="GH">ガーナ</option>
	<option value="GY">ガイアナ</option>
	<option value="KZ">カザフスタン</option>
	<option value="QA">カタール</option>
	<option value="CA">カナダ/アメリカ合衆国</option>
	<option value="GA">ガボン</option>
	<option value="CM">カメルーン</option>
	<option value="KR">韓国</option>
	<option value="GM">ガンビア</option>
	<option value="KH">カンボジア</option>
	<option value="GN">ギニア</option>
	<option value="GW">ギニアビサウ</option>
	<option value="CY">キプロス</option>
	<option value="CU">キューバ</option>
	<option value="GR">ギリシャ</option>
	<option value="KG">キルギス</option>
	<option value="GT">グアテマラ</option>
	<option value="GU">グアム</option>
	<option value="KW">クウェート</option>
	<option value="GD">グレナダ</option>
	<option value="HR">クロアチア</option>
	<option value="KY">ケイマン諸島</option>
	<option value="KE">ケニア</option>
	<option value="CI">コートジボワール</option>
	<option value="CR">コスタリカ</option>
	<option value="KM">コモロ</option>
	<option value="CO">コロンビア</option>
	<option value="CD">コンゴ民主共和国</option>
	<option value="SA">サウジアラビア</option>
	<option value="ZM">ザンビア</option>
	<option value="SL">シエラレオネ</option>
	<option value="JM">ジャマイカ</option>
	<option value="GE">ジョージア</option>
	<option value="SY">シリア</option>
	<option value="SG">シンガポール</option>
	<option value="ZW">ジンバブエ</option>
	<option value="CH">スイス</option>
	<option value="SE">スウェーデン</option>
	<option value="SD">スーダン</option>
	<option value="ES">スペイン</option>
	<option value="SR">スリナム</option>
	<option value="LK">スリランカ</option>
	<option value="SK">スロバキア</option>
	<option value="SI">スロベニア</option>
	<option value="SN">セネガル</option>
	<option value="RS">セルビア</option>
	<option value="KN">セントクリストファー・ネイビス</option>
	<option value="LC">セントルシア</option>
	<option value="TH">タイ</option>
	<option value="TW">台湾</option>
	<option value="TJ">タジキスタン</option>
	<option value="TZ">タンザニア</option>
	<option value="CZ">チェコ</option>
	<option value="TD">チャド</option>
	<option value="CF">中央アフリカ共和国</option>
	<option value="CN">中国</option>
	<option value="TN">チュニジア</option>
	<option value="CL">チリ</option>
	<option value="DK">デンマーク</option>
	<option value="DE">ドイツ</option>
	<option value="TG">トーゴ</option>
	<option value="DO">ドミニカ共和国</option>
	<option value="TT">トリニダード・トバゴ</option>
	<option value="TR">トルコ</option>
	<option value="NG">ナイジェリア</option>
	<option value="NI">ニカラグア</option>
	<option value="NE">ニジェール</option>
	<option value="NZ">ニュージーランド</option>
	<option value="NP">ネパール</option>
	<option value="NO">ノルウェー</option>
	<option value="HT">ハイチ</option>
	<option value="PK">パキスタン</option>
	<option value="PA">パナマ</option>
	<option value="BS">バハマ</option>
	<option value="PG">パプアニューギニア</option>
	<option value="BM">バミューダ</option>
	<option value="PW">パラオ</option>
	<option value="PY">パラグアイ</option>
	<option value="BB">バルバドス</option>
	<option value="PS">パレスチナ</option>
	<option value="HU">ハンガリー</option>
	<option value="BD">バングラデシュ</option>
	<option value="TL">東ティモール</option>
	<option value="FJ">フィジー</option>
	<option value="PH">フィリピン</option>
	<option value="FI">フィンランド</option>
	<option value="BT">ブータン</option>
	<option value="PR">プエルトリコ</option>
	<option value="BR">ブラジル</option>
	<option value="FR">フランス</option>
	<option value="PF">フランス領ポリネシア</option>
	<option value="BG">ブルガリア</option>
	<option value="BF">ブルキナファソ</option>
	<option value="BN">ブルネイ</option>
	<option value="BI">ブルンジ</option>
	<option value="VN">ベトナム</option>
	<option value="BJ">ベナン</option>
	<option value="VE">ベネズエラ</option>
	<option value="BY">ベラルーシ</option>
	<option value="PE">ペルー</option>
	<option value="BE">ベルギー</option>
	<option value="PL">ポーランド</option>
	<option value="BA">ボスニア・ヘルツェゴビナ</option>
	<option value="BW">ボツワナ</option>
	<option value="HK">香港</option>
	<option value="MH">マーシャル諸島</option>
	<option value="MO">マカオ</option>
	<option value="MG">マダガスカル</option>
	<option value="MW">マラウイ</option>
	<option value="ML">マリ</option>
	<option value="MT">マルタ</option>
	<option value="MY">マレーシア</option>
	<option value="ZA">南アフリカ</option>
	<option value="MM">ミャンマー</option>
	<option value="MX">メキシコ</option>
	<option value="MU">モーリシャス</option>
	<option value="MR">モーリタニア</option>
	<option value="MZ">モザンビーク</option>
	<option value="MC">モナコ</option>
	<option value="MV">モルディヴ</option>
	<option value="MA">モロッコ</option>
	<option value="MN">モンゴル</option>
	<option value="ME">モンテネグロ</option>
	<option value="JO">ヨルダン</option>
	<option value="LA">ラオス人民民主共和国</option>
	<option value="LV">ラトビア</option>
	<option value="LT">リトアニア</option>
	<option value="LY">リビア</option>
	<option value="LR">リベリア</option>
	<option value="RO">ルーマニア</option>
	<option value="LU">ルクセンブルク</option>
	<option value="RW">ルワンダ</option>
	<option value="LB">レバノン</option>
	<option value="RU">ロシア</option>
</select>
													</li>
													<li>
														
														<div class="numberRowWrap">
															<div class="block">
																<span class="visuallyHidden">国番号</span>
																<span class="countryCode viewInput js-countryCode"></span>
															</div>
															
															<div class="block"><label for="contactsSms:0:recipientSmsEachPersonSelection:flightStatusNotificationContactPointSmsDescription" class="visuallyHidden">電話番号</label><input id="contactsSms:0:recipientSmsEachPersonSelection:flightStatusNotificationContactPointSmsDescription" type="text" name="contactsSms:0:recipientSmsEachPersonSelection:flightStatusNotificationContactPointSmsDescription" class="telNumber" placeholder="例：9012345678" maxlength="30" aria-describedby="confirmTelNumberForRepresentative:0" />
															</div>
														</div>
													</li>
												</ul>
											</div>
										</li>
									</ul>
							</fieldset>
						</dd>
					</dl>
			</div>
	</div>
	</div>
	
	<div class="formInput articleLine">
		<h3>滞在中の連絡先(任意)
		</h3>
		<dl>
			
			<dt><label for="accommodationOne">現地連絡先1</label>
			</dt>
			<dd><input id="accommodationOne" type="text" name="accommodationOne" class="widthLarge" placeholder="例：09012345678 ABC HOTEL" maxlength="28" title="例：09012345678 ABC HOTEL" />
			</dd>
		</dl>
		<dl>
			
			<dt><label for="accommodationTwo">現地連絡先2</label>
			</dt>
			<dd><input id="accommodationTwo" type="text" name="accommodationTwo" class="widthLarge" placeholder="例：09012345678 ABC HOTEL" maxlength="28" title="例：09012345678 ABC HOTEL" />
			</dd>
		</dl>
	</div>
		</div>
		
		<p class="btnAreaSubmit btnArrowNext "><input id="next" type="submit" name="next" value="次へ" class="btnBase btnMainStream btnVerticalMain btnWidthVariable" onclick="return Asw.Dialog.getInstance('prebookConfirmDialog').toggle(event);return Asw.LoadingWindow.open(&quot;NORMAL&quot;, event)" />
		</p><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:2" value="H/OiD2iWGAne4gFJtq5KJJBabgWvPEHPvV98P3D/RKz6Sn3fKJN6j9hgnlpb+sFqH/XbELweFKT6yFzGeLZCzY4tv4qyePZm3o/dh13KsvfhENj8ozp7MV4FRswwNYudPQCMnFAeStKGG62aPAvjgKuSIkRu8BUs+wSVj0kH7zAlFJGZRpBgMWSFpRNil7HsZFqlI2hIyGtVb/+7/ZqDkP7IxhwxMWEn1snKkmKt19ab6DT2+i2X3b2lqNkXtJ8O/VMGj9K65nuEeoTZqpVCnQmh3Dpop1hCrEfG0JYavOY4Q/Duv/Jq+J0Bh+GZButun16G+hXi6z5TID6AM+Wvpl+ZgB0mVqbpu21u3Z89sXpkEns7Xnvxme0C77MF8OO/D5ZLpRLKoV+4Zn537Wyj+1b8N/iwPfQx+Ry1YDL253WxCgF19bQ2hI+sPb1tx+3jQKecrotkrHm4ccaEehH4/JpvsNujgbehPPKQG3ek42EGb/50bgCvUG1cdIOoMHgcC1hvb5897C32lOFAIqeGbmIyO/N9r8tNe0i1FxXj3FUef5v7qCW+1pfxcViTUXubPXg6XxCXXK04oU39iZftd1tlJsHe3FW5QHxX7xWJBKVjpiDQRiKaKnd1gt0Gpj8v92qxFvBEGqs+zRafX8icShatOXVklXhaXaimWRntzHBCeXyqENHzmK/KJ/eMYDsYV5KnAH4C44nwPwPBdgGwgY6jSvHNfbLeuvCQ/jij80z0DUqQGEWgDLhXqOe7TvtCiiG7nvE+8VIfbXn2unNaTXgT/51YVE/XIJu2M9sodzFyPXUk8VUHfGwONrNGenDnnHlHWHTgYS2iUDsOssjHw7NvYeXllLGYvB56tWiOUFjItoHSc6RS/KDVAPEvfRMJoN6JOC6cO25Yg2oK74OZxOKS3RJGjtUdcONAOS95ekQLTao0Gi6Q004C+YhubiXYoTktdufvOvbDBIh4Al0CcpJW5EQMxtQ2ioSuN1GTeY6TsaBouvtNzbspey7Icznje1vKHu3a+evIHHnPXLIb5CZJSJV5UlqiE3rLa9x7xevhip6z+nhK0553w6SA/rm3kRB7AlZAq6XKOW6hto4yu03grOhycWRx0v8FeyMiVkL2DOmSyZ8vbb20h+j9IxeFAX7Dj+aEjsUfqjkpuQF05Jt57TpmmOFQDn475pIFJt0WX5WrKIKRd8dF91ql5XGSPuNvF3lFy7asTECmoxjpLAlaAdDLia33Kpxn97penKJKLADlK/w7z82sRznZT1t31O3be4zYhLglMSqzUbzfsl2r3fev0mP1zE8Zn4Lm4qmmehiAgCjaMGX84w8qCy4ADroEst8RMbgIQlP3Rk4WUct66380gEWhFHT1lT1rCBxfzJljGVUplvRLFVWslWxfR7Blbfv+0ZoNPEI7CfFEdZYlr3lnyHiNmTgcs4oCfBKu7SIffL4thNgxf+mBXHq+yc+WwE+vX+4wZy+x/f9OBjlZOE+iCoZUkJyQ14kev6aAkgD7riOXN2R/9Zmh+sVP26MnCuV4dLMl5+kMU7rwmLEdQ64kEMCJYYXmEHH1hAt5g08APLVgj4YkT41FUQUv/0xLqa0A3SQbcAroax5LLm6iaw0Vg2FxnARDlemaWeU/TvYFbaxAJll2nBXmva34gR4Tp0yevqoSQitqd7GSBvxg9Z5hrtUifuQgJXoBx/laP8XPVSHdZxFwvHs7GRpU/RY3ZO0dMXnlSbs89eaLO1ds8YPPYGiD2sqZ2OOvO2sY4Zw25t020xfKlhveOUUVEqkqFj3u2P1J/j1vOlTPGtbdRpOWfaiBGP8oBBPwbiR2jk/9gTbM+g5c4O2z5dWeQYIVut3OQOksrOvoIH2yCdiQIEMmFzdNA9ktkXbP9Vlqt5PbGr5ZBT00wPiQNYXNC1b5W4XbKyTSYyLBXpg5rikVujK00OjR75LtPq5hDdvtP0JN2Z4dt6YBOBORtzNBTkiup7afqCFhEJMkcVo9EJk3bJpBkrpkQkw45zwwkSHPmT2D6+eTwYaWbwly+37ekeVuf+dETwqrHEtcZeLA10FG9nPAF/1ntnPIhn57yN0o1CRs/zLvzQKftDfktyColLJOaKFW32d2+MfocQ6wlN9vjqE44d0EaZeic9t9iT02C05rRkIhnbqV7sWCxW/8uZH1W3U/050UbQgF2O9CAAko0Url17TdD6ynxph0D0sDO6M1YNhXCs39tYyjKC7W1T8+257eziMwvbEEUSVpxK70YalteSzAc/zErSgDht4JxWHqE8/Kh/LlMtNWfpqf12tWMl5FmMpcDsGvKMb2qvSISPXOJsh27YQcf4ryknkNpnx3ZDQmT0aA3esPFK7Ssu/yAIqwPwPsEJ/41C58/pVd4iFOmBu1PA5T/2AruvWa4RZEB40baS9e0w/miI5L/Xbl19XPTYodKPsmzgwwqqitGpdURlvMX6k9cnPu87KicH58pwr5kR5nwOCmh5IMJqrJWY5SKjwBgoYOOcRFXSrnkR10mqFXhBU3T7KiskD6Gl7fdoUXkr4PbmVLHAyLmEvmgQ7uIvv0BEEWcs9dgGB5WRA3Ay6ZvdK0Yal9IZYeWoQtwgQXT9NBh3HqG89h0VDy+XdZgWSRKhkamVhP8dt/SQsiGr/6zj2C0HvLK2RklQRMrjqFAlmuRfxV4bn5NHj+XDVPLsoVEHibX5EHs5pcfyrIm2roH79L90Rks8lVq/h2SkuKq7MXMdeTxG1NCsO4QNnFrQd3FP6ZSfhUxFMUsUQa6nL/cE9LPpq8oArSDjPmcq5BVIybnd53yfkNOneXs4lXDlbBKY5F5Kg1btwMIGTZXLvtM4W3HS1vs4UTy77yl4qSx5TaAElXOvsIdIuBGSp2bBIUMHQi8IBfG6mxhSki/SCzg63ZPOL3TNBwwmXD46FQuutSgiILJ/sv+1SU8uBBV3j8eQAagRJvqgHljFAGa4QONF8+t7b3RqYdu94iGjGz+GUUEuDydmxwLxJnmKZHAs1OCOTBsiU8nfM9I1QEyWaM5UofXfc4Bs/YmZse5M4l9zZ4wQ6MBXV3SyP/N3FSr7Gn4a+421FfCL+ILBOX8fz52j8Om2fGzUBdMh57OYZHihpK3uWwi+BmvNmVJQNIjoHVEEgUG7nsatGF02B3QypwncXplHZGSVRqNL/+rjWkFMIBwbGWmfm2j87ny1EtxbrSUqfemXnKA6kG4kqoZqbVCP70MYOy0AJvzTSsuzlkjGnwCzn7HTHp+mg2c9bjNuLat51mD/yrXHn+C8r9LfkIghLYPTF72tKAoGBcR9gH2qihy1VRhmGfdIOEB/r2HVch6XfRATiRPyQPOz0DF4UPbHwUEdmd5ZLr9wSdXt+Blzs03+9xcgaapBI+kyxgERJQIf9bSwG3adFOzI4N8Ygoe/al+yVgDE2RpnOeRah+mlbFSAfFwrmkgUpFaP16hqzxvg7aJUhr5egoKPaMQ4vdaJxJ3N1TPvdpM19uVDFALTVJBrB+WMx0pR/k0/d+Hjb6BpYSkghyq1799Th3HfqaL028XufK6PA0+NecKN61YFCO5rTfDtlhng7Tp1uNrmn27tEf1rLd7KfXnQqWe1zxrm+J2LDvPOaSw9T2G5H4xsbcVII5i2gRMvAE+vlJ/CzyBuYcqHeTRo/x7T5oIX89ktCs+Wa3zSHhix97MHZdeLe/c1sVctbKlNVTWS9Pn8opqR7Qe3TBDKfK0tUF3MtFH9/WqGdNLqIqwdQmRs/0znuGhSlkWklLR25DSfgOxvP3Z7D70n4k04HtFOcgbZc238sy/sB7hltHChToWbJb2ZY0dA4/EoRJTnJ+APnkFHNIxYBaiCNDSbEvP0t4VC0uQxt/uT8BlVHBI3hnUNDMTeN/gyi24N4cwKEUNklM8mVwfGa4CQqJJOIJu6gxeVfAYDFm5lp2BdmCnJYp7Ep9us1QNdkLwzgmsCkBCyRABY9iJgAyN16BokIqNlCRMH0qYFN+fM+3SOo8n1sgqsGspQmdKg6TUAhmY+rHKiFnPmc6nto5nRojGrLoBOnX9Z0HkBhHXVVXtUIshzEheXZtDurhXugBG3RWTmgdvNaKFwse7DtfMWheQDX25AvdWv+oqcyhrBu2hN4zg9xxz8PdtYePrQiUljvoOHu8wL7297oWbpCb+foGK+t+uzoayDtNLZVvyEdSOn1I0fnPN3peOgBjhvqJYLgwfifRCusDxYIMF2+ZTMGecMAyZzF5ZR+X9KRJsu1ay5G+HmOAVkWmRBBZGFzIJLdjbErVKTKjxut2kr6YMJnba8hMxSqgw8MEIi0P/XejI0vpEmf7BGBukjnT264iTygiVIXEAr4XFvs/iawzh+hrIKPOgyDAiFYdYluzXorPU2iXtxJXrb/e2w6EiMUDf9D6S5+/Y/3UErCTANwCZeYAq3uSQQhVLV1ackpzAmgRO6Sf19klr+kY9UT0sv94hnIi6AdaoXbZAtPI9DYrHo8nkWowk7Xi5xNluD5t9BrIWocWf5sch4uOLR2C8GrFn0yIZy1GlvoaEvTqJpHc/b6+A2tIbu7o7AJ3BGDu6+NM4/10udc2zb16NZouz0+YRk9IQoVuzJkzGF5md79cyJXlyCn4bBufKck5PLkP+MLHA3ftuTwIKgHkrBYRGfB/8aAsJ3Rd/ToPO4yPZbdfNGSemUzAcyvOswLTFGgnJKvVIV+pLeT28syJ8CR6ZfBcagOsfJuZ0iECyzEajDG1fbAKifoR/yLAxiYbntn6Z4NGgvGeebj5ocIQ6b3nPdniKsgoSJhrYyd5S9nAvCwLATt76qG611vf0zK7Duo97GCZWjcrLzy6ZGFDbI6zR5ZjWL1GYRL5aQGLWBFbVZlyKmDzLamqr/sMACsoRRVBLBt1INcawCe8gsws/OJgLDYNj2r74BiVLdNkNwepzyIG05DI/iOH7L8kQdW3x2BY7SPNP/LWzTDjoicgLonBk0jXrHrkoDKopQO5+8ixb8PFi/ZmV4XhihvVXrWCpWfhbywgQyuJ8P8ZHO+cU+1h30EWd7+iz9O77hWWE7+OHoyGdH9ygJaWk5P1adjVPGPZ/a+tWPz78nqCbzd1+L7KwH44lCdN8TJygsDcfuEM8qBj6ZiYJl25cxqldsKjlFH39vJG/kYnU/GuLREOkOOGHzP8w6+/vzUyErSUlNyV+dFmebxGUMip1eN8DseGADv8+sPyX/xYvj3A+gxU6zqJ99Wnq0AAIQdM5xQGTrDxPpHEhhHlq8RZiM0gHLeTE1QEkXm7FgI4FRu0yH+zWZegowP5yixqGBN+w/UQQMW/28vnGNG2BRvqy+qgP+TPidgj49h4XlrFz5L2tUJNJdWCcfN5MQfB3WLUDPb7athonMbozEtAvnrPWGH5BGNoDADgW3jg7399IESIW352F9/DtBpXBM39/nGkCP3DuWLwKJYMJaf8O3MCJ97Kb4NiQhSuW901ag8/NFJWj1nBj13xxtVAr9TxqX4AdgFchErNO/LDMlWcVzcPD9+nnX33JRAt4kQj9PS0RnGyKIYktFaibiwPYt5FHA4o7hnyKrRW20F10u4GSsF6RUNkZSh8qZCeL5QO+nO3Y5VCTxBjkwKQKBiYbhhFmqkGHcPAzNZH8GLCO0VPDMyyB2+XFe4D9gvTKOlKeuDHGo5y/5KYpmgSagFk69EH126sawxFbuNnJa0uveBCpT/rk8a4+2IctZJRlR3k3KGALxIqXfeqOqy9vbOWANexy2S0bcUXZWChp3+IXJK337/JN9/NZhbmyf4ltfbRoHjfwwYronFDFxbrz2b9rtA7jk6ghOqw9pQkbxdxPWeIBNr4voIUHGM7CJhjjr1qGVYHQk+RKEYrqmXTwxf04K4dH08IikSu0C9IOnW2ONsoNiWEaHrVf9ihnKh4azICbU7kAE3n8G21pKuhP23YJEycB/xMAcP3Qieu6ob7j9JtdZZAwjidMjnee1Hh7gHCqWLzRogdsd3jDI3UtwqfsLSp19YpW4jZlWm3YH+YyGEAg4lM/rDJwgEhxpqK059f5ftLsQxor2Bt86pWYd7fO1/+olQoeOHx6Y0PBBuCaipyBKdFIVrv8JB13FOqqQnc2HRGqNLn5cm9BgNdlOoUWIjnrLAWnJQz1EZ3ezXiIzn3Y+ECnykTCmzhb9RRbsdMI7faGJE15lRhlaymcFJvux/5aDelWgRF26Kdm+quxEZA2E784NXeZHm5xK8wXwILOnHu6prAyZ2x1zPQZji1mBh9nqI9QoRQjUkqoPDn04pFQcwTjgqLaghLhfx9GTMECI3rc4NEGnd8/R6EOW45AOX6+y4YtecG8SwsySa5fnfGxuXByd8ziaKvEu7jmKf1Pn370lHHQndr8hR1G5JW2wfh/zZDr1yi6kRHH0PgURPC9SbW9FweZQ/7VayB+pOOekgw8y0dvHA1kNWvLTryRvo+dEcv0mMlNOjBn3Nuk1ucNCO8zaRxiPB19kz0a9VKbpXc9CdtAZIVyN5C/4aCKboCltZDu5uvTRJJ1gwhCSlIzEPdso0ODnwzEk31cGQIjDWU+p2JVat7O+YUzsZJrwBY1ht7jCNmS2kqWpqGWdb1cJnXnNHyFDyOumF/mAB3kESguKuwcYcTvm0UVcGwpRL8Xt216/qgt4Vp4W9AEuqIjEHcyOcSex5obPMCn9SnaA8mw+5ciqAcs4Nblfj24MEkB5/VXe/CJKcEGFN/W6hXrBlZaFvAU7mYZUQOb1TWVn+Iyk/ph9LwqD85XQqa8qTiYxAeeux5p+cdZ4E5q6oSnNFJTUfNxShtn5OBSUxlXVhNBMdQROkLBqItdUXzxkw7DKy8Rz4XbjmAka4EnzCO6Y7QwYT9zZnpImORmEiC3O/tO+M9K+VFnJxXOcKYV1R0cxgU1lpqp9jgKfAGsBJCMaUPnRlYSAZE3Wwmx2CpMWN21yKJlzUIzyws6dzyNS23uHsc6KKj0RXN5IrywgkXrIwvNppUhqI+gNiyWm5okN3F7vO+69FXKOimnhqZrrH9s1nv5WKXmXbuJd3hSiziTXplD/s9elHdBnLMNe+Qad8ITdu4wWtt4nhVANH+vt4QhgBnZGhOmttu5GYEi6Plc1SuA8BU70oqNkMW7vULlu/ZbMWTOzfhVdCSx8hemjnAy4dX49AmBZo26tE7FHiEK07TLWBuzw3m1fyGFVAiCHUUC/MN/4c1U8LIH12a85OdwhF/6Iz66j4RSy3dkgnknQIaM7pr4Neu0J631ImiyyIXcpWAnz/hoyyIocXIH/YtEOZquvM5MiZ6PdI+vCg2yiQOlTaWpjK6LgOqhjHraX2iIUI0dN2gKpNEKplbCyFRMgmXc7sFofNrLQQ8wHpuDoq6QudOcxlS/QAhNNyZnX5RhbI+HK7qDRUID/6rSMlzCZ21ZrCejE8W1qpmWnOeW3GLHukEaSTd+qN5usWzbvnhJV7219+y1mbMwvyIiuEMQrh8FupdfbT4WbWxkOp2nCEo+svrmIkJqsO+NsavMt/72wEiwdyQEGRDYiC465Ie13M9urZJKclhPEur0OQ2v32urXxL0v5xHwWzh/MRoc2RD0DXW0utnySU+/YU2xubPxmAYQ1AQ+c3aLxKc15UfciaHHNjpukGrjD5iALRy4byinuF5MIEiql5sldt370VwRCcM4O1AJvbBARITvlQIjbx7iTMFkUW9235zHUGkzbY6vLRIEu+93Fdo0xwRsqQWXrma9YY6t/QQc485WDeBVFWpP5HPCvnVsreZBxp8WzvAXipyhdO/rTMHcQ+aSCrApaCX+yM66M8++EwPUq+3Nsxzj+xqasNRLvoAyw3se9O+J2dUu568tzulf/Swv08E7yfrFtopZD0pJeaHLlWqWW0l+0boiec5M23GkoJTvIZYg1nzRtsWUdXh1QSUy4EPwg9pXrr44d1edisTQUaohIc2bKhAvfc63tqWslo2rYqNbie42MRB/QiHjg5+pqfZHs4jAPig3RSzmaugtnu5LBk9DriYjtGmy1t1AzHkKJR/DyXvQ5nWv2/0uEwMn/EEByZd8VPtpiXziwufSwOpGs/NnnaSdX5erFLPC2xFGLb7s9UofmB8YXSs39mkGtTC+hDO5fxgpeg4NWXpd+sw1mo8xRMqZW3w/Vg6TiBp/UCQLXesq9h7vs8yaDI5p13+vzrDpBV2kzcLkaQ5odscV3DQ3GjAtkB4SzmHrOEVFhy6RmCJH7bKThYV63abuhPgUJ4HQsgfASlvY/Zp91HM0vZ6LcwIcwe6MrzlXklIFOXiOoVFk7bwMmE89zqFxisu6OtXjUkBcb0wZavT93/BiJstKVnaVwgm4AV6TidIpI=" autocomplete="off" />
</form><div id="cmnDynamicMessages" class="infoBoxSection">
				<dl class="infoBox importantInfo jsAccordionSwitch">
					<dt><strong>重要なご案内</strong></dt>
					<dd>
						<ul class="jsAccordionSwitchList" id="informationMessages">
								<li>個人情報の利用目的<a href="https://www.ana.co.jp/other/int/meta/0012.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="ANAプライバシーポリシー(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">ANAプライバシーポリシー<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>を必ずお読みいただき、内容に同意いただいた上で、お進みください。</li>
								<li><a href="https://www.ana.co.jp/other/int/meta/0058.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="Secure Flight Programについて(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">米国Secure Flightプログラム<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>に基づき、パスポート名、生年月日、性別などの情報は事前に米国へ通知いたします。</li>
								<li>入力したeメールアドレス、電話番号は入国に必要な情報として米国に通知される可能性があります。</li>
									<li class="jsHiddenFlg toggleContents">お名前のスペルがパスポート名と一致しているかご確認ください。異なる場合はインターネットではお取り扱いできません。<a href="https://www.ana.co.jp/other/int/meta/intbe0001.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="ANAお問い合わせ先(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">ANA<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>へ名前の変更をお申し出ください。</li>
									<li class="jsHiddenFlg toggleContents">二重国籍のお客様は、ご搭乗時に使用されるパスポートのお名前でご予約ください。<br />パスポートごとにお名前が異なる場合は、お客様ご自身で入国管理事務所・外務省・大使館などで各種条件をご確認のうえ、使用されるパスポートを決めてください。<br />なお、入国可否については国によって異なるため、お客様ご自身で各入国管理事務所などでご確認ください。</li>
									<li class="jsHiddenFlg toggleContents">旅行開始日時点で12歳以上の場合は大人、2歳以上12歳未満の場合は小児、2歳未満の場合は幼児の扱いとなります。なお、小児は大人と同マイル数が必要です。</li>
									<li class="jsHiddenFlg toggleContents">運航に関する情報をお客様へ通知するため、提携航空会社にもお客様のメールアドレスが提供されます。あらかじめご了承ください。</li>
									<li class="jsHiddenFlg toggleContents"><dl><dt>携帯電話へのメール送信をご希望のお客様へ</dt><dd>ドメイン指定受信をご利用の場合は、ana.co.jpとamadeus.comからのメールが受信できるようあらかじめ設定をお願いいたします。</dd></dl></li>
									<li class="jsHiddenFlg toggleContents">SMSはスマートフォンの番号に配信されます。一部の国ではSMSでのご案内をご利用いただけません。詳細は<a href="https://www.ana.co.jp/other/int/meta/0737.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="ショートメッセージ(SMS)でのご案内対象外の国について(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">ショートメッセージ(SMS)でのご案内対象外の国について<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>をご覧ください。</li>
									<li class="jsHiddenFlg toggleContents">ログインされている会員ご本人が搭乗しない場合は、再度フライト検索画面より「ログインされている会員ご本人は搭乗しない」のチェックボックスを選択してお手続きください。</li>
						</ul>
							<a href="#" class="toggleSwitch jsTriggerSwitch" role="button" aria-controls="informationMessages" aria-expanded="false"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/toggle_open.png?7bec38a" alt="開閉ボタン" height="25" width="25" class="jsRollOver" />
							</a>
					</dd>
				</dl></div>
	<!--=========== /main =============-->
	</div>

	<!--=========== Script ============-->
	<script type="text/javascript">
	$(window).on('load', function() {
		// アコーディオンのメソッドと設定
		Asw.AccordionInfo('.jsAccordionSwitch', {
			'triggerSwitch': '.jsAccordionSwitch .jsTriggerSwitch',
			'listClass': '.jsAccordionSwitchList',
			'visibilityHidden': 'toggleContents'
		});
	});
	</script>
	<!--=========== Script ============-->
	<script type="text/javascript">
		//プラットフォーム判別（サマリーエリア追従用として）
		var Asw = Asw || {};
	
	  	Asw.ClientInfo = Asw.ClientInfo  || {};
	  	Asw.ClientInfo.DeviceType = Asw.ClientInfo.DeviceType || {};
	
	  	Asw.ClientInfo.DeviceType.PC = 'PC';
	  	Asw.ClientInfo.DeviceType.SP = 'SP';
	  	Asw.ClientInfo.DeviceType.TC = 'TC';
	
	  	Asw.ClientInfo.deviceType = Asw.ClientInfo.DeviceType.PC;
	
	 	if((navigator.userAgent.indexOf('Android') > 0 && navigator.userAgent.indexOf('Mobile') == -1) || navigator.userAgent.indexOf('iPad') > 0){
	    	Asw.ClientInfo.deviceType = Asw.ClientInfo.DeviceType.TC;
	  	}
	</script>
	<!--=========== /Script ============--><div id="summaryArea">
			<div class="flightSummaryArea">
						<div class="flightSummarySection">
							<h2>区間 1
							</h2>
							<p class="flightDate"><em>6</em>月<em class="hasLeft">24</em>日（火）
							</p>
							<div class="flightSummaryRoute">
									<div class="flightSummaryRouteSection">
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation1_1">
													<span>東京(成田)
													</span>
												</p>
												<p class="time">
													<span>17:00
													</span>
												</p>
											</div>
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation1_1">
													<span>シカゴ(ORD)
													</span>
												</p>
												<p class="time">
													<span>14:55
													</span>
												</p>
											</div>
										<p class="flightNumberClass">
												<span>NH012</span><span><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/flight_ana_summaryarea.png?717d3c0" alt="ANA運航便" height="11" width="41" /></span>
												<br />特典ビジネス
										</p>
									</div>
							</div>
						</div>
						<div class="flightSummarySection">
							<h2 class="midstream">区間 2
							</h2>
							<p class="flightDate"><em>7</em>月<em class="hasLeft">23</em>日（水）
							</p>
							<div class="flightSummaryRoute">
									<div class="flightSummaryRouteSection">
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation2_1">
													<span>ホノルル(オアフ島)
													</span>
												</p>
												<p class="time">
													<span>11:35
													</span>
												</p>
											</div>
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation2_1">
													<span>東京(成田)
													</span>
												</p>
												<p class="time">
													<span>14:50
																	<span>翌日
																	</span>
													</span>
												</p>
											</div>
										<p class="flightNumberClass">
												<span>NH183</span><span><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/flight_ana_summaryarea.png?717d3c0" alt="ANA運航便" height="11" width="41" /></span>
												<br />特典エコノミー
										</p>
									</div>
							</div>
						</div>
					<div class="total">
						<p class="passenger">大人 x1
						</p>
						<div class="totalArea">
					  		<dl class="mealAmountWrap">
									<dt>有料機内食サービス
									</dt>
									<dd><em class="mealAmount">0</em><span class="currencyCode">円</span>
									</dd>
							</dl>
									<div>
										<div class="mileageAndTotal">
												<dl class="requiredMileage">
													<dt>必要マイル
													</dt>
													<dd><em class="requiredMileage">74,000</em><span class="currencyCode">マイル</span>
													</dd>
												</dl>
												<dl>
													<dt>総額
													</dt>
													<dd><em class="awardTotalPayment">68,130</em>円
													</dd>
												</dl>
												<p class="includeFareAndFuelCost">※各種税金、燃油特別付加運賃等を含みます。
												</p>
										</div>
									</div>
						</div>
					</div>
				
			</div></div>
		<!--===== /cmnWrapper =====-->
		</div>
			
		<span id="message" class="visuallyHidden" role="alert" aria-live="assertive"></span>
		<span id="politeMessage" class="visuallyHidden" role="log" aria-live="polite"></span><script type="text/javascript">
		var Asw = Asw || {};
		Asw.BaseOutput = {"sessionID":"iZyHknED4g","language":"ja","siteCatalystLanguageCode":"J","operationDate":"20250619","operationDateTime":"20250619184351","office":{"officeCode":"TYONH08ZZ","country2letterCode":"JP"},"bookingType":"A","pageID":"A04_P01","siteCatalystPageName":"INT_BE_AWARD_J_A04特典予約_P01旅客情報入力（必須情報）","device":{"deviceType":"PC"}};
	</script><div id="promotionArea">
		<!--===== promotionArea =====-->
		<!--===== /promotionArea =====--></div>
		<!--=========== /cmnContainer ============--></div><div id="cmnBaloonArea" aria-live="polite"></div><div id="cmnPopupArea" aria-live="polite"></div>

	

	<div id="cmnFooterWrapper">
		<!--=========== .cmnFooterWrapper ============-->
		<div id="cmnFooter">
			<!--===== cmnFooter =====-->
			<ul>
				<li><a href="https://www.ana.co.jp/other/int/meta/0012.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">プライバシーポリシー<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
					<li><a href="https://www.ana.co.jp/other/int/meta/0670.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">利用者情報の外部送信について<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
					</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/0052.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">サイト利用規約<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/be0238.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">推奨環境<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/0133.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">サイトマップ<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/0732.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">運送約款<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
					<li class="lastChild"><a href="https://www.ana.co.jp/other/int/meta/0556.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">ウェブアクセシビリティについて<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
					</li>
			</ul>
			<p class="blankText" aria-hidden="true"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="" height="10" width="10" />外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。</p>
			<p class="copyright"><span lang="en" xml:lang="en">Copyright&#169;ANA</span>
			</p>
			<p class="starAllianceLogo"><a href="http://www.staralliance.com/ja/" target="_blank"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/star_alliance_logo.png?0fe73b5" alt="A STAR ALLIANCE MEMBER" height="18" width="175" /><span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
			</p>
			<!--===== cmnFooter =====-->
		</div>
		<!--=========== /cmnFooterWrapper ============-->
	</div>

		
		<div id="cmnLoadingForAjax"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/bg_modalWindow.png?717d3c0" alt="" height="0" width="0" />
			<div class="loadingArea">
				<p class="loadingImage"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/loading.gif?717d3c0" alt="リクエストを処理しています...しばらくお待ちください。" height="33" width="33" />
				</p>
			</div>
		</div>
		
		
		<div id="popupAltSentence" style="display: none" aria-hidden="true">別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。</div>
	<div id="cmnSiteCatalyst" aria-hidden="true"><div id="cmnSiteCatalystParamArea"><script type="text/javascript">
				// ▼SiteCatalyst
				// SiteCatalyst code version: H.2.
				// Copyright 1997-2005 Omniture, Inc. More info available at http://www.omniture.com
				SiteCatalystReportSuites   = "INT";
				SiteCatalystCharSet        = "UTF-8";
				SiteCatalystChannel        = "BE_AWARD_J";
				SiteCatalystPageName       = "INT_BE_AWARD_J_A04\u7279\u5178\u4E88\u7D04_P01\u65C5\u5BA2\u60C5\u5831\u5165\u529B\uFF08\u5FC5\u9808\u60C5\u5831\uFF09";
				SiteCatalystERR            = "";
				SiteCatalystEventNum       = "";
				SiteCatalystPurchase       = "FALSE";
				SiteCatalystProductPrice   = "";
				SiteCatalystCurrencyCode   = "";
				SiteCatalystSeatAvailabilityINT   = "";
				SiteCatalystDepartDate     = "";
				SiteCatalystDepart         = "";
				SiteCatalystArrive         = "";
				SiteCatalystFlightNo       = "";
				SiteCatalystClass          = "";
				SiteCatalystFare           = "";
				SiteCatalystPurchaseID     = "";
				
				SiteCatalystLocater        = "";
				SiteCatalystPayment        = "";
				SiteCatalystIncome         = "";
				
				SiteCatalystPax            = "";
				SiteCatalystManipulateDay  = "";
				SiteCatalystCompartmentClass   = "";
				SiteCatalystOptionInfo     = "";
				SiteCatalystOutboundUpsellPrice   = "";
				SiteCatalystInboundUpsellPrice    = "";
				
				SiteCatalystSearchInput    = "";
				SiteCatalystSegment        = "";
				SiteCatalystOfficeCode     = "";
				SiteCatalystPassengerCount = "";
				SiteCatalystPassenger      = "";
				SiteCatalystAncillaryService = "";
				// End SiteCatalyst code version: H.2.
				// ▲SiteCatalyst
			</script></div><script type="text/javascript"><!-- if(navigator.appVersion.indexOf('MSIE')>=0)document.write(unescape('%3C')+'\!-'+'-') // --></script>
		<!--/DO NOT REMOVE/-->
		

		
		<!-- Google Tag Manager -->
		<script type="text/javascript" charset="Shift_JIS" src="https://www.ana.co.jp/common/js/GTMINTBE.js"></script>
		<!-- End Google Tag Manager -->
		<!-- Yahoo Tag Manager -->
		<script type="text/javascript" charset="Shift_JIS" src="https://www.ana.co.jp/common/js/YTMINTBE.js"></script>
		<!-- End Yahoo Tag Manager -->
		

	</div>

	

	<div class="mboxDefault"></div><script type="text/javascript">mboxCreate("ASW_common_A04_P01");</script>
			<script type="text/javascript" src="https://www.ana.co.jp/common/js/tealium/tealium.js"></script>
			<script type="text/javascript" src="//cdn.evgnet.com/beacon/allnipponairways/nonamcprd/scripts/evergage.min.js"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/javax.faces.resource/jsf.js.xhtml?ln=javax.faces"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/jquery.mousewheel.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/jquery.jscrollpane.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/jquery-ui-1.10.4.custom.min.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/jquery.ui.touch-punch.min.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/asw-common.js?beb6a50"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/asw-common-pc.js?fd88d7d"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/asw-format.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/mask.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/loading.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/asw-loadingwindow-pc.js?9e233c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/asw-loadingwindow.js?51db600"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/dialog-pc.js?25d36a4"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/asw-dialog-sequencer.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/asw-event.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/asw-date-select.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/asw-calendar-pc.js?9b86b8a"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/asw-calendar.js?db624c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/heightLine.js?0e7a1ce"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/asw-summaryarea.js?b21c6d7"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/asw-fingerprints.js?99001fc"></script><script type="text/javascript">
			Asw.init("https://aswbe-i.ana.co.jp/rei22h/international_asw", "ja");
			Asw.getCalendarHolidayList = function() {return [20250721,20250811,20250915,20250923,20251013,20251103,20251123,20251124,20260101,20260112,20260211,20260223,20260320,20260429,20260503,20260504,20260505,20260506,20260720,20260811,20260921,20260922,20260923,20261012,20261103,20261123,20270101,20270111,20270211,20270223,20270321,20270322,20270429,20270503,20270504,20270505,20270719,20270811,20270920,20270923,20271011,20271103,20271123,20280101,20280110,20280211,20280223,20280320,20280429,20280503,20280504,20280505,20280717,20280811,20280918,20280922,20281009,20281103,20281123,20290101,20290108,20290211,20290212,20290223,20290320,20290429,20290430,20290503,20290504,20290505,20290716,20290811,20290917,20290923,20290924,20291008,20291103,20291123];}
			Asw.ClientInfo.deviceType = "PC";
			Asw.ClientInfo.mobileDeviceType = "UNKNOWN";
			Asw.ClientInfo.osVersion = "";
			Asw.Format.init("https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/{1}/{2}/{3}?{0}");
			Asw.overwriteTargetAttributeValue("false");
		</script><script type="text/javascript">
		
		var Asw = Asw || {};
		Asw.ContactInput = Asw.ContactInput || {};
		Asw.ContactInput.Const = {
			/** data-属性名「data-type」 @constant @type {String} @default 'data-type' */
			DATA_ATTRIBUTE_NAME_TYPE: 'data-type',
			/** data-属性名「data-sms-possible」 @constant @type {String} @default 'data-sms-possible' */
			DATA_ATTRIBUTE_NAME_SMS_POSSIBLE: 'data-sms-possible',
			/** data-属性名「data-country-code」 @constant @type {String} @default 'data-country-code' */
			DATA_ATTRIBUTE_NAME_COUNTRY_CODE: 'data-country-code',
			/** data-type属性値「sms」 @constant @type {String} @default 'sms' */
			DATA_TYPE_VALUE_SMS: 'sms'
		};

		
		Asw.ContactInput.countryInfoMap = {"IS":{"internationalCallingCode":"+354","smsPossible":false},"IE":{"internationalCallingCode":"+353","smsPossible":true},"AZ":{"internationalCallingCode":"+994","smsPossible":true},"AF":{"internationalCallingCode":"+93","smsPossible":true},"US":{"internationalCallingCode":"+1","smsPossible":true},"AE":{"internationalCallingCode":"+971","smsPossible":true},"DZ":{"internationalCallingCode":"+213","smsPossible":true},"AR":{"internationalCallingCode":"+54","smsPossible":true},"AW":{"internationalCallingCode":"+297","smsPossible":true},"AL":{"internationalCallingCode":"+355","smsPossible":true},"AM":{"internationalCallingCode":"+374","smsPossible":true},"AO":{"internationalCallingCode":"+244","smsPossible":false},"AG":{"internationalCallingCode":"+1","smsPossible":true},"AD":{"internationalCallingCode":"+376","smsPossible":true},"YE":{"internationalCallingCode":"+967","smsPossible":true},"GB":{"internationalCallingCode":"+44","smsPossible":true},"IL":{"internationalCallingCode":"+972","smsPossible":true},"IT":{"internationalCallingCode":"+39","smsPossible":true},"IQ":{"internationalCallingCode":"+964","smsPossible":true},"IR":{"internationalCallingCode":"+98","smsPossible":true},"IN":{"internationalCallingCode":"+91","smsPossible":true},"ID":{"internationalCallingCode":"+62","smsPossible":true},"UG":{"internationalCallingCode":"+256","smsPossible":true},"UA":{"internationalCallingCode":"+380","smsPossible":true},"UZ":{"internationalCallingCode":"+998","smsPossible":true},"UY":{"internationalCallingCode":"+598","smsPossible":true},"EC":{"internationalCallingCode":"+593","smsPossible":true},"EG":{"internationalCallingCode":"+20","smsPossible":true},"EE":{"internationalCallingCode":"+372","smsPossible":true},"SZ":{"internationalCallingCode":"+268","smsPossible":false},"ET":{"internationalCallingCode":"+251","smsPossible":false},"ER":{"internationalCallingCode":"+291","smsPossible":true},"SV":{"internationalCallingCode":"+503","smsPossible":true},"AU":{"internationalCallingCode":"+61","smsPossible":true},"AT":{"internationalCallingCode":"+43","smsPossible":true},"OM":{"internationalCallingCode":"+968","smsPossible":true},"NL":{"internationalCallingCode":"+31","smsPossible":true},"GH":{"internationalCallingCode":"+233","smsPossible":true},"CV":{"internationalCallingCode":"+238","smsPossible":false},"GY":{"internationalCallingCode":"+592","smsPossible":true},"KZ":{"internationalCallingCode":"+7","smsPossible":true},"QA":{"internationalCallingCode":"+974","smsPossible":true},"CA":{"internationalCallingCode":"+1","smsPossible":true},"GA":{"internationalCallingCode":"+241","smsPossible":true},"CM":{"internationalCallingCode":"+237","smsPossible":true},"KR":{"internationalCallingCode":"+82","smsPossible":true},"KH":{"internationalCallingCode":"+855","smsPossible":true},"GM":{"internationalCallingCode":"+220","smsPossible":true},"KP":{"internationalCallingCode":"+850","smsPossible":false},"MK":{"internationalCallingCode":"+389","smsPossible":false},"GN":{"internationalCallingCode":"+224","smsPossible":true},"GW":{"internationalCallingCode":"+245","smsPossible":true},"CY":{"internationalCallingCode":"+357","smsPossible":true},"CU":{"internationalCallingCode":"+53","smsPossible":true},"GR":{"internationalCallingCode":"+30","smsPossible":true},"KI":{"internationalCallingCode":"+686","smsPossible":false},"KG":{"internationalCallingCode":"+996","smsPossible":true},"GT":{"internationalCallingCode":"+502","smsPossible":true},"GU":{"internationalCallingCode":"+1","smsPossible":true},"KW":{"internationalCallingCode":"+965","smsPossible":true},"KY":{"internationalCallingCode":"+1","smsPossible":true},"GD":{"internationalCallingCode":"+1","smsPossible":true},"HR":{"internationalCallingCode":"+385","smsPossible":true},"KE":{"internationalCallingCode":"+254","smsPossible":true},"CI":{"internationalCallingCode":"+225","smsPossible":true},"CR":{"internationalCallingCode":"+506","smsPossible":true},"SA":{"internationalCallingCode":"+966","smsPossible":true},"KM":{"internationalCallingCode":"+269","smsPossible":true},"CO":{"internationalCallingCode":"+57","smsPossible":true},"CG":{"internationalCallingCode":"+242","smsPossible":false},"CD":{"internationalCallingCode":"+243","smsPossible":true},"ZM":{"internationalCallingCode":"+260","smsPossible":true},"SM":{"internationalCallingCode":"+378","smsPossible":false},"SL":{"internationalCallingCode":"+232","smsPossible":true},"DJ":{"internationalCallingCode":"+253","smsPossible":false},"JM":{"internationalCallingCode":"+1","smsPossible":true},"CH":{"internationalCallingCode":"+41","smsPossible":true},"GE":{"internationalCallingCode":"+995","smsPossible":true},"SE":{"internationalCallingCode":"+46","smsPossible":true},"SY":{"internationalCallingCode":"+963","smsPossible":true},"ES":{"internationalCallingCode":"+34","smsPossible":true},"SG":{"internationalCallingCode":"+65","smsPossible":true},"LK":{"internationalCallingCode":"+94","smsPossible":true},"SK":{"internationalCallingCode":"+421","smsPossible":true},"SI":{"internationalCallingCode":"+386","smsPossible":true},"ZW":{"internationalCallingCode":"+263","smsPossible":true},"SD":{"internationalCallingCode":"+249","smsPossible":true},"KN":{"internationalCallingCode":"+1","smsPossible":true},"SR":{"internationalCallingCode":"+597","smsPossible":true},"LC":{"internationalCallingCode":"+1","smsPossible":true},"SC":{"internationalCallingCode":"+248","smsPossible":false},"SN":{"internationalCallingCode":"+221","smsPossible":true},"SB":{"internationalCallingCode":"+677","smsPossible":false},"RS":{"internationalCallingCode":"+381","smsPossible":true},"TH":{"internationalCallingCode":"+66","smsPossible":true},"TW":{"internationalCallingCode":"+886","smsPossible":true},"TJ":{"internationalCallingCode":"+992","smsPossible":true},"TZ":{"internationalCallingCode":"+255","smsPossible":true},"CZ":{"internationalCallingCode":"+420","smsPossible":true},"TD":{"internationalCallingCode":"+235","smsPossible":true},"CF":{"internationalCallingCode":"+236","smsPossible":true},"TN":{"internationalCallingCode":"+216","smsPossible":true},"CL":{"internationalCallingCode":"+56","smsPossible":true},"TV":{"internationalCallingCode":"+688","smsPossible":false},"DK":{"internationalCallingCode":"+45","smsPossible":true},"DE":{"internationalCallingCode":"+49","smsPossible":true},"TG":{"internationalCallingCode":"+228","smsPossible":true},"DO":{"internationalCallingCode":"+1","smsPossible":true},"DM":{"internationalCallingCode":"+1","smsPossible":false},"TT":{"internationalCallingCode":"+1","smsPossible":true},"TM":{"internationalCallingCode":"+993","smsPossible":false},"TR":{"internationalCallingCode":"+90","smsPossible":true},"TO":{"internationalCallingCode":"+676","smsPossible":false},"NG":{"internationalCallingCode":"+234","smsPossible":true},"NR":{"internationalCallingCode":"+674","smsPossible":false},"NA":{"internationalCallingCode":"+264","smsPossible":false},"NI":{"internationalCallingCode":"+505","smsPossible":true},"BH":{"internationalCallingCode":"+973","smsPossible":false},"NE":{"internationalCallingCode":"+227","smsPossible":true},"PK":{"internationalCallingCode":"+92","smsPossible":true},"JP":{"internationalCallingCode":"+81","smsPossible":true},"BS":{"internationalCallingCode":"+1","smsPossible":true},"NZ":{"internationalCallingCode":"+64","smsPossible":true},"BM":{"internationalCallingCode":"+1","smsPossible":true},"NP":{"internationalCallingCode":"+977","smsPossible":true},"BB":{"internationalCallingCode":"+246","smsPossible":true},"NO":{"internationalCallingCode":"+47","smsPossible":true},"HT":{"internationalCallingCode":"+509","smsPossible":true},"BD":{"internationalCallingCode":"+880","smsPossible":true},"VA":{"internationalCallingCode":"+39","smsPossible":false},"PA":{"internationalCallingCode":"+507","smsPossible":true},"VU":{"internationalCallingCode":"+678","smsPossible":false},"PG":{"internationalCallingCode":"+675","smsPossible":true},"PW":{"internationalCallingCode":"+680","smsPossible":true},"PY":{"internationalCallingCode":"+595","smsPossible":true},"PS":{"internationalCallingCode":"+970","smsPossible":true},"BR":{"internationalCallingCode":"+55","smsPossible":true},"HU":{"internationalCallingCode":"+36","smsPossible":true},"TL":{"internationalCallingCode":"+670","smsPossible":true},"BG":{"internationalCallingCode":"+359","smsPossible":true},"BF":{"internationalCallingCode":"+226","smsPossible":true},"BN":{"internationalCallingCode":"+673","smsPossible":true},"FJ":{"internationalCallingCode":"+679","smsPossible":true},"PH":{"internationalCallingCode":"+63","smsPossible":true},"FI":{"internationalCallingCode":"+358","smsPossible":true},"BT":{"internationalCallingCode":"+975","smsPossible":true},"BY":{"internationalCallingCode":"+375","smsPossible":true},"BZ":{"internationalCallingCode":"+501","smsPossible":false},"PR":{"internationalCallingCode":"+1","smsPossible":true},"BE":{"internationalCallingCode":"+32","smsPossible":true},"BA":{"internationalCallingCode":"+387","smsPossible":true},"FR":{"internationalCallingCode":"+33","smsPossible":true},"BO":{"internationalCallingCode":"+591","smsPossible":false},"PF":{"internationalCallingCode":"+689","smsPossible":true},"BI":{"internationalCallingCode":"+257","smsPossible":true},"VN":{"internationalCallingCode":"+84","smsPossible":true},"BJ":{"internationalCallingCode":"+229","smsPossible":true},"VE":{"internationalCallingCode":"+58","smsPossible":true},"PE":{"internationalCallingCode":"+51","smsPossible":true},"PL":{"internationalCallingCode":"+48","smsPossible":true},"BW":{"internationalCallingCode":"+267","smsPossible":true},"PT":{"internationalCallingCode":"+351","smsPossible":false},"HK":{"internationalCallingCode":"+852","smsPossible":true},"HN":{"internationalCallingCode":"+504","smsPossible":false},"MH":{"internationalCallingCode":"+692","smsPossible":true},"MO":{"internationalCallingCode":"+853","smsPossible":true},"MG":{"internationalCallingCode":"+261","smsPossible":true},"MW":{"internationalCallingCode":"+265","smsPossible":true},"ML":{"internationalCallingCode":"+223","smsPossible":true},"MT":{"internationalCallingCode":"+356","smsPossible":true},"MY":{"internationalCallingCode":"+60","smsPossible":true},"FM":{"internationalCallingCode":"+691","smsPossible":false},"MM":{"internationalCallingCode":"+95","smsPossible":true},"MX":{"internationalCallingCode":"+52","smsPossible":true},"MU":{"internationalCallingCode":"+230","smsPossible":true},"MR":{"internationalCallingCode":"+222","smsPossible":true},"MZ":{"internationalCallingCode":"+258","smsPossible":true},"MC":{"internationalCallingCode":"+377","smsPossible":true},"MV":{"internationalCallingCode":"+960","smsPossible":true},"MD":{"internationalCallingCode":"+373","smsPossible":false},"RO":{"internationalCallingCode":"+40","smsPossible":true},"MA":{"internationalCallingCode":"+212","smsPossible":true},"MN":{"internationalCallingCode":"+976","smsPossible":true},"ME":{"internationalCallingCode":"+382","smsPossible":true},"JO":{"internationalCallingCode":"+962","smsPossible":true},"LA":{"internationalCallingCode":"+856","smsPossible":true},"RU":{"internationalCallingCode":"+7","smsPossible":true},"LV":{"internationalCallingCode":"+371","smsPossible":true},"LT":{"internationalCallingCode":"+370","smsPossible":true},"LY":{"internationalCallingCode":"+218","smsPossible":true},"GQ":{"internationalCallingCode":"+240","smsPossible":false},"LI":{"internationalCallingCode":"+423","smsPossible":false},"LR":{"internationalCallingCode":"+231","smsPossible":true},"CN":{"internationalCallingCode":"+86","smsPossible":true},"LU":{"internationalCallingCode":"+352","smsPossible":true},"ZA":{"internationalCallingCode":"+27","smsPossible":true},"RW":{"internationalCallingCode":"+250","smsPossible":true},"LS":{"internationalCallingCode":"+266","smsPossible":false},"LB":{"internationalCallingCode":"+961","smsPossible":true}};

		$(function () {
			contactInfoInit();
		});
		
		function contactInfoInit() {
			
			
			Asw.get('representativeTelType').find('option[value="M1"]').attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_TYPE,
					Asw.ContactInput.Const.DATA_TYPE_VALUE_SMS);
			Asw.get('representativeTelCountry').children('option').each(function(i,option){
				var countryInfo = Asw.ContactInput.countryInfoMap[option.value];
				if (countryInfo === void 0) {
					
					return true;
				}
				$(option).attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_SMS_POSSIBLE, countryInfo.smsPossible);
				$(option).attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_COUNTRY_CODE, countryInfo.internationalCallingCode);
			});
			
			var smsCountrySelectSelector = 'select.js-countrySelect:not(#representativeTelCountry)'
			$(smsCountrySelectSelector).eq(0).children('option').each(function(i,option){
				var countryInfo = Asw.ContactInput.countryInfoMap[option.value];
				if (countryInfo === void 0) {
					
					return true;
				}
				$(smsCountrySelectSelector + ' option:nth-child(' + (i+1) + ')').attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_COUNTRY_CODE,
						countryInfo.internationalCallingCode);
			});
			
			var emergencyCountrySelectSelector = 'select.passengerEmergencyCountry.js-countrySelect:not(#representativeTelCountry)'
			$(emergencyCountrySelectSelector).eq(0).children('option').each(function(i,option){
				var countryInfo = Asw.ContactInput.countryInfoMap[option.value];
				if (countryInfo === void 0) {
					
					return true;
				}
				$(emergencyCountrySelectSelector + ' option:nth-child(' + (i+1) + ')').attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_COUNTRY_CODE,
						countryInfo.internationalCallingCode);
			});
		}
	</script><script type="text/javascript">
		var isInit = true;
		function changeTelType(_this, telNoInputId) {
			var $telNoInput = Asw.get(telNoInputId);
			var $telNoKind = Asw.get("representativeTelType");
			var telNo = Asw.getValueComparedToPlaceholder(telNoInputId);
			var telNoCountry = Asw.get("representativeTelCountry").val();

			if(true && !isInit) {
				if(telInputCheck(telNo, telNoCountry)) {
					if (_this.value == "M1") {
						telNo = exceptDomesticPrefix("09042504176", telNoCountry);
					} else if (_this.value == "B1") {
						telNo = exceptDomesticPrefix("03-5405-2172", telNoCountry);
					} else {
						telNo = exceptDomesticPrefix("090-4250-4176", telNoCountry);
					}
					$telNoInput.val(telNo);
				}
			}
			isInit = false;
		}
		
		function telInputCheck(telNo, telNoCountry) {
	        checkResult = false;
	        
	        var amcHndyTelNo = exceptDomesticPrefix("09042504176", telNoCountry);
	        var amcCmpTelNo = exceptDomesticPrefix("03-5405-2172", telNoCountry);
	        var amcTelNo = exceptDomesticPrefix("090-4250-4176", telNoCountry);
	
	        if(telNo == "" || telNo == amcHndyTelNo || telNo == amcCmpTelNo || telNo == amcTelNo) {
	        	checkResult = true;
	        }
	        return checkResult;
    	}
    	
    	function exceptDomesticPrefix(telNo, telNoCountry) {
    		if(telNoCountry == 'JP') {
    			if(telNo.substr(0, 1) === '0') {
    				return telNo.slice(1);
    			}
    		}
    		return telNo;
    	}
	</script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/asw-customerinfomationform.js?6e91953"></script><script type="text/javascript">
		$(document).ready(function(){
				Asw.enableTextWhenCheckBox('.cascadedSwitch');
				Asw.notPasteInput('.jsNotPasteInput');
			$(function(){
				Asw.alignHeightToParentTdElement( '.passengerInfoInputArea' );
				
					Asw.alignHeightToParentTdElement( '.passengerInfoInputText' );
				
				
				$('select.js-countrySelect').trigger('change');
			});
		});
	</script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/asw-session-keeper.js?6ec0730"></script><script type="text/javascript">
			$(document).ready(function() {
				Asw.SessionKeeper.initAndStart("sessionKeeperContainer", "520", "570", "PC");
			});
			</script><script type="text/javascript">
		Asw.addAjaxCallback(function(data) {
			// アコーディオンのメソッドと設定 
			if($('.jsAccordionSwitch .jsTriggerSwitch').length > 0){
				var events = $._data($('.jsAccordionSwitch .jsTriggerSwitch').get(0), "events");
				if(events === undefined || events.click === undefined) {
					Asw.AccordionInfo('.jsAccordionSwitch', {
						'triggerSwitch': '.jsAccordionSwitch .jsTriggerSwitch',
						'listClass': '.jsAccordionSwitchList',
						'visibilityHidden': 'toggleContents'
					});
				}
			}
		});
	</script><script type="text/javascript">
	Asw.Price = Asw.Price || {};
	// 整数の区切り文字
	Asw.Price.separateString = ',';
	
	// 整数の区切り桁数
	Asw.Price.separateDigitNumber = 3;
	
	// 小数点
	Asw.Price.decimalPointString = '.';
	
	// 小数点以降の有効桁数
	Asw.Price.significantDigit = 0;
	
	// 表示金額の単位
	Asw.Price.currencyUnit = '円';
	</script><script type="text/javascript">
			$(document).ready(function() {
				$('#preLoadingArea').css('visibility', 'hidden');
			});
		</script><noscript><img src="https://aswbe-i.ana.co.jp/akam/13/pixel_437a9c79?a=dD1lNjBjM2M0ZTY3NjQ2YTMxOWU5NDU1YjNiOTg0NWMyMmY4YmFkYmI0JmpzPW9mZg==" style="visibility: hidden; position: absolute; left: -999px; top: -999px;" /></noscript><script type="text/javascript"  src="/RcS9yzCRgrNnM/gY/v0Wf9fQwtpdQ/7NE3X2ruOE2SL9YE/HyNpQmYB/RWt/XOHcnHAU"></script></body>
</html>