"""
代理池管理系统

这个模块实现了一个高级代理池管理系统，用于:
1. 管理多个代理
2. 跟踪每个代理的成功率
3. 实施智能代理选择策略
4. 控制请求频率
5. 处理代理故障和重试逻辑

使用方法:
1. 创建ProxyManager实例
2. 添加代理到池中
3. 使用select_proxy()获取代理
4. 使用update_proxy_result()更新代理成功/失败状态
"""

import time
import random
import json
import os
import threading
import requests
from loguru import logger
from datetime import datetime, timedelta
import hashlib
import uuid
import platform
import ssl
import sys
from typing import Dict, List, Tuple, Optional, Any, Union

# 配置日志
def setup_logging():
    """配置日志系统"""
    try:
        # 配置loguru日志输出到控制台
        logger.remove()  # 移除默认的处理器
        logger.add(
            sys.stderr,
            colorize=True, 
            level="INFO"
        )
        logger.info("日志系统初始化完成 (仅控制台输出)")
        
    except Exception as e:
        print(f"日志配置过程中发生错误: {e}")

class Proxy:
    """代理对象，存储代理信息和统计数据"""
    
    def __init__(self, proxy_str: str, proxy_type: str = "datacenter", country: str = "unknown", 
                 city: str = "unknown", isp: str = "unknown", asn: str = "unknown"):
        """
        初始化代理对象
        
        Args:
            proxy_str: 代理字符串，格式为 "host:port:username:password"
            proxy_type: 代理类型，可以是 "datacenter", "residential", "mobile"
            country: 代理所在国家
            city: 代理所在城市
            isp: 代理所属ISP
            asn: 代理所属ASN
        """
        self.proxy_str = proxy_str
        self.proxy_type = proxy_type
        self.country = country
        self.city = city
        self.isp = isp
        self.asn = asn
        
        # 解析代理字符串
        self.parse_proxy_string()
        
        # 统计数据
        self.total_requests = 0
        self.successful_requests = 0
        self.failed_requests = 0
        self.success_rate = 1.0  # 初始成功率为1.0
        self.consecutive_failures = 0
        self.last_used_time = 0
        self.last_success_time = 0
        self.average_response_time = 0
        self.total_response_time = 0
        
        # 冷却时间（秒）
        self.cooldown_time = 0
        
        # 是否被禁用
        self.disabled = False
        
        # 代理健康状态
        self.health_score = 100  # 0-100，100表示最健康
        
        # 创建时间
        self.created_at = time.time()
        
        logger.debug(f"创建代理: {self.host}:{self.port}, 类型: {self.proxy_type}")
    
    def parse_proxy_string(self):
        """解析代理字符串"""
        try:
            # 检查是否是SOCKS5代理
            is_socks5 = self.proxy_str.startswith("socks5://")
            # 检查是否是HTTP代理
            is_http = self.proxy_str.startswith("http://")
            
            # 处理SOCKS5代理
            if is_socks5:
                # 移除 "socks5://" 前缀
                proxy_without_prefix = self.proxy_str[9:]
                
                # 检查是否包含认证信息
                if ':' in proxy_without_prefix and proxy_without_prefix.count(':') >= 2:
                    # 分离主机和端口
                    host_port_parts = proxy_without_prefix.split(':', 1)
                    self.host = host_port_parts[0]
                    
                    # 分离端口和认证信息
                    remaining_parts = host_port_parts[1].split(':', 1)
                    self.port = remaining_parts[0]
                    
                    # 分离用户名和密码
                    auth_parts = remaining_parts[1].split(':', 1)
                    self.username = auth_parts[0]
                    self.password = auth_parts[1] if len(auth_parts) > 1 else ""
                    
                    # 使用socks5h前缀，让DNS解析也通过代理进行
                    self.proxies = {
                        'http': f'socks5h://{self.username}:{self.password}@{self.host}:{self.port}',
                        'https': f'socks5h://{self.username}:{self.password}@{self.host}:{self.port}'
                    }
                    
                    logger.debug(f"SOCKS5代理配置(带认证) - 主机: {self.host}, 端口: {self.port}, 用户: {self.username}, 密码: {self.password[:3]}***")
                else:
                    # 无认证SOCKS5代理: socks5://host:port
                    parts = proxy_without_prefix.split(':')
                    self.host = parts[0]
                    self.port = parts[1] if len(parts) > 1 else "1080"  # 默认SOCKS5端口
                    self.username = ""
                    self.password = ""
                    
                    # 使用socks5h前缀，让DNS解析也通过代理进行
                    self.proxies = {
                        'http': f'socks5h://{self.host}:{self.port}',
                        'https': f'socks5h://{self.host}:{self.port}'
                    }
                    
                    logger.debug(f"SOCKS5代理配置(无认证) - 主机: {self.host}, 端口: {self.port}")
            
            # 处理HTTP代理
            elif is_http:
                # 移除 "http://" 前缀
                proxy_without_prefix = self.proxy_str[7:]
                
                # 检查是否包含认证信息 (@)
                if '@' in proxy_without_prefix:
                    # 格式: *********************:port
                    auth, host_port = proxy_without_prefix.split('@', 1)
                    self.username, self.password = auth.split(':', 1)
                    
                    if ':' in host_port:
                        self.host, self.port = host_port.split(':', 1)
                    else:
                        self.host = host_port
                        self.port = "80"  # 默认HTTP端口
                    
                    self.proxies = {
                        'http': f'http://{self.username}:{self.password}@{self.host}:{self.port}',
                        'https': f'http://{self.username}:{self.password}@{self.host}:{self.port}'
                    }
                    
                    logger.debug(f"HTTP代理配置(带认证) - 主机: {self.host}, 端口: {self.port}, 用户: {self.username}, 密码: {self.password[:3]}***")
                else:
                    # 无认证HTTP代理: http://host:port
                    parts = proxy_without_prefix.split(':')
                    self.host = parts[0]
                    self.port = parts[1] if len(parts) > 1 else "80"  # 默认HTTP端口
                    self.username = ""
                    self.password = ""
                    
                    self.proxies = {
                        'http': f'http://{self.host}:{self.port}',
                        'https': f'http://{self.host}:{self.port}'
                    }
                    
                    logger.debug(f"HTTP代理配置(无认证) - 主机: {self.host}, 端口: {self.port}")
            
            # 处理传统格式
            elif '@' in self.proxy_str:
                # 格式: host:port@user:pass
                host_port, auth = self.proxy_str.split('@', 1)
                if ':' in host_port:
                    self.host, self.port = host_port.split(':', 1)
                else:
                    self.host = host_port
                    self.port = "80"  # 默认端口
                self.username, self.password = auth.split(':', 1)
                
                self.proxies = {
                    'http': f'http://{self.username}:{self.password}@{self.host}:{self.port}',
                    'https': f'http://{self.username}:{self.password}@{self.host}:{self.port}'
                }
                
                logger.debug(f"传统格式代理配置 - 主机: {self.host}, 端口: {self.port}, 用户: {self.username}, 密码: {self.password[:3]}***")
            else:
                # 格式: host:port:user:pass
                parts = self.proxy_str.split(':')
                
                # 检查是否包含认证信息
                if len(parts) >= 3:
                    self.host = parts[0]
                    self.port = parts[1]
                    self.username = parts[2]
                    self.password = ':'.join(parts[3:]) if len(parts) > 3 else ""  # 合并可能包含冒号的密码部分
                    
                    self.proxies = {
                        'http': f'http://{self.username}:{self.password}@{self.host}:{self.port}',
                        'https': f'http://{self.username}:{self.password}@{self.host}:{self.port}'
                    }
                    
                    logger.debug(f"传统格式代理配置 - 主机: {self.host}, 端口: {self.port}, 用户: {self.username}, 密码: {self.password[:3]}***")
                else:
                    # 无认证代理: host:port
                    self.host = parts[0]
                    self.port = parts[1] if len(parts) > 1 else "80"  # 默认端口
                    self.username = ""
                    self.password = ""
                    
                    self.proxies = {
                        'http': f'http://{self.host}:{self.port}',
                        'https': f'http://{self.host}:{self.port}'
                    }
                    
                    logger.debug(f"无认证代理配置 - 主机: {self.host}, 端口: {self.port}")
            
        except Exception as e:
            logger.error(f"解析代理字符串失败: {self.proxy_str}, 错误: {e}")
            self.host = "unknown"
            self.port = "unknown"
            self.username = "unknown"
            self.password = "unknown"
            self.proxies = None
    
    def update_success(self, response_time: float):
        """
        更新代理成功请求统计
        
        Args:
            response_time: 请求响应时间（秒）
        """
        self.total_requests += 1
        self.successful_requests += 1
        self.consecutive_failures = 0
        self.last_used_time = time.time()
        self.last_success_time = time.time()
        
        # 更新平均响应时间
        self.total_response_time += response_time
        self.average_response_time = self.total_response_time / self.successful_requests
        
        # 更新成功率
        self.success_rate = self.successful_requests / self.total_requests
        
        # 更新健康分数
        self._update_health_score()
        
        logger.debug(f"代理成功: {self.host}:{self.port}, 成功率: {self.success_rate:.2f}, 响应时间: {response_time:.2f}秒")
    
    def update_failure(self, error_type: str = "unknown"):
        """
        更新代理失败请求统计
        
        Args:
            error_type: 错误类型
        """
        self.total_requests += 1
        self.failed_requests += 1
        self.consecutive_failures += 1
        self.last_used_time = time.time()
        
        # 更新成功率
        self.success_rate = self.successful_requests / self.total_requests if self.total_requests > 0 else 0
        
        # 根据连续失败次数设置冷却时间
        self._set_cooldown_time()
        
        # 更新健康分数
        self._update_health_score()
        
        logger.debug(f"代理失败: {self.host}:{self.port}, 成功率: {self.success_rate:.2f}, 连续失败: {self.consecutive_failures}, 错误类型: {error_type}")
    
    def _set_cooldown_time(self):
        """根据连续失败次数设置冷却时间"""
        if self.consecutive_failures == 1:
            self.cooldown_time = 5  # 5秒
        elif self.consecutive_failures == 2:
            self.cooldown_time = 30  # 30秒
        elif self.consecutive_failures == 3:
            self.cooldown_time = 60  # 1分钟
        elif self.consecutive_failures == 4:
            self.cooldown_time = 300  # 5分钟
        elif self.consecutive_failures >= 5:
            self.cooldown_time = 1800  # 30分钟
            
            # 修改: 更保守的成功率禁用条件：请求超过200次且成功率低于50%
            if self.total_requests > 200 and self.success_rate < 0.5:
                self.disabled = True  # 禁用代理
                logger.warning(f"代理已禁用(请求过多且成功率极低): {self.host}:{self.port}, 总请求: {self.total_requests}, 成功率: {self.success_rate:.2f}")
                # 添加专门的禁用代理日志
                logger.error(f"禁用代理统计: {self.host}:{self.port}, 总请求: {self.total_requests}, 成功请求: {self.successful_requests}, 失败请求: {self.failed_requests}, 成功率: {self.success_rate:.2f}")
        
        # 修改: 更保守的连续失败禁用条件：连续20次失败且总请求数超过50次
        if self.consecutive_failures >= 20 and self.total_requests > 50:
            self.disabled = True  # 禁用代理
            logger.warning(f"代理已禁用(连续失败过多): {self.host}:{self.port}, 连续失败: {self.consecutive_failures}, 总请求: {self.total_requests}")
            # 添加专门的禁用代理日志
            logger.error(f"禁用代理统计(连续失败): {self.host}:{self.port}, 连续失败次数: {self.consecutive_failures}, 总请求: {self.total_requests}")
    
    def _update_health_score(self):
        """更新代理健康分数"""
        # 基于成功率的分数 (0-50分)
        success_score = min(50, int(self.success_rate * 50))
        
        # 基于连续失败次数的分数 (0-30分)
        failure_score = max(0, 30 - self.consecutive_failures * 10)
        
        # 基于响应时间的分数 (0-20分)
        if self.average_response_time > 0:
            if self.average_response_time < 1:
                response_time_score = 20
            elif self.average_response_time < 2:
                response_time_score = 15
            elif self.average_response_time < 5:
                response_time_score = 10
            elif self.average_response_time < 10:
                response_time_score = 5
            else:
                response_time_score = 0
        else:
            response_time_score = 10  # 默认分数
        
        # 计算总分
        self.health_score = success_score + failure_score + response_time_score
        
        logger.debug(f"代理健康分数更新: {self.host}:{self.port}, 分数: {self.health_score}")
    
    def is_available(self) -> bool:
        """检查代理是否可用"""
        if self.disabled:
            return False
        
        # 检查冷却时间
        if self.cooldown_time > 0:
            elapsed = time.time() - self.last_used_time
            if elapsed < self.cooldown_time:
                return False
            else:
                # 冷却时间已过，重置冷却时间
                self.cooldown_time = 0
        
        return True
    
    def to_dict(self) -> Dict[str, Any]:
        """将代理对象转换为字典"""
        return {
            "proxy_str": self.proxy_str,
            "proxy_type": self.proxy_type,
            "host": self.host,
            "port": self.port,
            "username": self.username,
            "password": self.password[:3] + "***" if self.password else "",  # 隐藏完整密码
            "country": self.country,
            "city": self.city,
            "isp": self.isp,
            "asn": self.asn,
            "total_requests": self.total_requests,
            "successful_requests": self.successful_requests,
            "failed_requests": self.failed_requests,
            "success_rate": self.success_rate,
            "consecutive_failures": self.consecutive_failures,
            "last_used_time": datetime.fromtimestamp(self.last_used_time).strftime('%Y-%m-%d %H:%M:%S') if self.last_used_time > 0 else "never",
            "last_success_time": datetime.fromtimestamp(self.last_success_time).strftime('%Y-%m-%d %H:%M:%S') if self.last_success_time > 0 else "never",
            "average_response_time": self.average_response_time,
            "cooldown_time": self.cooldown_time,
            "disabled": self.disabled,
            "health_score": self.health_score,
            "created_at": datetime.fromtimestamp(self.created_at).strftime('%Y-%m-%d %H:%M:%S')
        }
    
    def __str__(self) -> str:
        """返回代理的字符串表示"""
        return f"Proxy({self.host}:{self.port}, type={self.proxy_type}, success_rate={self.success_rate:.2f}, health={self.health_score})"

class ProxyManager:
    """代理池管理器"""
    
    def __init__(self, config_file: str = None):
        """
        初始化代理池管理器
        
        Args:
            config_file: 配置文件路径，如果提供，将从文件加载配置
        """
        # 代理池
        self.proxies: List[Proxy] = []
        
        # 代理选择策略
        self.selection_strategy = "weighted_random"  # 可选: "random", "round_robin", "weighted_random", "best_performance"
        
        # 请求频率控制
        self.min_request_interval = 1.0  # 最小请求间隔（秒）
        self.last_request_time = 0
        
        # 直接请求（不使用代理）的配置
        self.direct_request_enabled = True
        self.direct_request_ratio = 0.1  # 10%的请求使用直接连接
        self.direct_request_count = 0
        self.total_request_count = 0
        
        # 代理轮换配置
        self.max_requests_per_proxy = 50  # 每个代理最多使用次数
        
        # 代理健康检查配置
        self.health_check_interval = 3600  # 健康检查间隔（秒）
        self.last_health_check_time = 0
        
        # 代理统计数据
        self.stats = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "direct_requests": 0,
            "proxy_requests": 0
        }
        
        # 锁，用于线程安全
        self.lock = threading.RLock()
        
        # 加载配置
        if config_file:
            self.load_config(config_file)
        
        # 在初始化时重置所有代理的禁用状态和统计数据
        logger.info("程序启动，重置所有代理的禁用状态和统计数据")
        self.reset_all_proxies()
        
        logger.info(f"代理池管理器初始化完成，选择策略: {self.selection_strategy}")
    
    def reset_all_proxies(self):
        """重置所有代理的禁用状态和统计数据"""
        with self.lock:
            reset_count = 0
            for proxy in self.proxies:
                if proxy.disabled or proxy.total_requests > 0:
                    # 重置代理状态
                    proxy.disabled = False
                    proxy.cooldown_time = 0
                    proxy.consecutive_failures = 0
                    proxy.success_rate = 1.0
                    proxy.total_requests = 0
                    proxy.successful_requests = 0
                    proxy.failed_requests = 0
                    proxy.average_response_time = 0
                    proxy.total_response_time = 0
                    proxy.health_score = 100
                    reset_count += 1
            
            if reset_count > 0:
                logger.info(f"程序启动时重置了 {reset_count} 个代理的状态和统计数据")
    
    def load_config(self, config_file: str):
        """
        从配置文件加载配置
        
        Args:
            config_file: 配置文件路径
        """
        try:
            if not os.path.exists(config_file):
                logger.warning(f"配置文件不存在: {config_file}")
                return
            
            with open(config_file, 'r') as f:
                config = json.load(f)
            
            # 加载代理选择策略
            if "selection_strategy" in config:
                self.selection_strategy = config["selection_strategy"]
            
            # 加载请求频率控制
            if "min_request_interval" in config:
                self.min_request_interval = config["min_request_interval"]
            
            # 加载直接请求配置
            if "direct_request_enabled" in config:
                self.direct_request_enabled = config["direct_request_enabled"]
            if "direct_request_ratio" in config:
                self.direct_request_ratio = config["direct_request_ratio"]
            
            # 加载代理轮换配置
            if "max_requests_per_proxy" in config:
                self.max_requests_per_proxy = config["max_requests_per_proxy"]
            
            # 加载代理健康检查配置
            if "health_check_interval" in config:
                self.health_check_interval = config["health_check_interval"]
            
            # 加载代理列表 (期望根级别是 JSON 字符串数组)
            if isinstance(config, list):
                loaded_count = 0
                invalid_count = 0
                for proxy_item in config: #直接迭代根列表
                    if isinstance(proxy_item, str):
                        # 直接使用字符串创建 Proxy 对象
                        self.add_proxy(proxy_item)
                        loaded_count += 1
                    elif isinstance(proxy_item, dict) and 'proxy_str' in proxy_item:
                        # 兼容旧格式 (包含 proxy_str 键的字典)
                        proxy = Proxy(
                            proxy_str=proxy_item["proxy_str"],
                            proxy_type=proxy_item.get("proxy_type", "datacenter"),
                            country=proxy_item.get("country", "unknown"),
                            city=proxy_item.get("city", "unknown"),
                            isp=proxy_item.get("isp", "unknown"),
                            asn=proxy_item.get("asn", "unknown")
                        )
                        self.add_proxy(proxy)
                        loaded_count += 1
                        logger.debug(f"加载旧格式代理: {proxy_item['proxy_str']}")
                    else:
                        logger.warning(f"配置文件中发现无效的代理条目，已跳过: {proxy_item}")
                        invalid_count += 1
                logger.info(f"从配置文件加载了 {loaded_count} 个代理 (跳过 {invalid_count} 个无效条目)")
            elif isinstance(config, dict) and "proxies" in config:
                 # 处理旧的字典格式或包含其他配置的格式
                 if isinstance(config["proxies"], list):
                     loaded_count = 0
                     invalid_count = 0
                     for proxy_item in config["proxies"]:
                         if isinstance(proxy_item, str):
                             self.add_proxy(proxy_item)
                             loaded_count += 1
                         elif isinstance(proxy_item, dict) and 'proxy_str' in proxy_item:
                             # ... (加载旧格式字典的代码)
                             proxy = Proxy(
                                 proxy_str=proxy_item["proxy_str"],
                                 proxy_type=proxy_item.get("proxy_type", "datacenter"),
                                 country=proxy_item.get("country", "unknown"),
                                 city=proxy_item.get("city", "unknown"),
                                 isp=proxy_item.get("isp", "unknown"),
                                 asn=proxy_item.get("asn", "unknown")
                             )
                             self.add_proxy(proxy)
                             loaded_count += 1
                             logger.debug(f"加载旧格式代理: {proxy_item['proxy_str']}")
                         else:
                             logger.warning(f"配置文件中发现无效的代理条目，已跳过: {proxy_item}")
                             invalid_count += 1
                     logger.info(f"从配置文件(字典格式)加载了 {loaded_count} 个代理 (跳过 {invalid_count} 个无效条目)")
                 else:
                     logger.warning("配置文件中的 'proxies' 字段不是列表，无法加载代理")
            else:
                 logger.info("配置文件格式未知或未包含代理列表")

            logger.info(f"从配置文件加载配置成功: {config_file}")
        
        except Exception as e:
            logger.error(f"加载配置文件失败: {config_file}, 错误: {e}")
    
    def save_config(self, config_file: str):
        """
        保存配置到文件
        
        Args:
            config_file: 配置文件路径
        """
        try:
            config = {
                "selection_strategy": self.selection_strategy,
                "min_request_interval": self.min_request_interval,
                "direct_request_enabled": self.direct_request_enabled,
                "direct_request_ratio": self.direct_request_ratio,
                "max_requests_per_proxy": self.max_requests_per_proxy,
                "health_check_interval": self.health_check_interval,
                # 保存为字符串列表 (放在 "proxies" 键下)
                "proxies": [proxy.proxy_str for proxy in self.proxies],
                "stats": self.stats
            }
            
            # 确保目录存在
            os.makedirs(os.path.dirname(os.path.abspath(config_file)), exist_ok=True)
            
            with open(config_file, 'w') as f:
                 # 将包含 "proxies" 键的整个 config 字典写入文件
                 json.dump(config, f, indent=4)
            
            logger.info(f"保存配置到文件成功: {config_file}")
        
        except Exception as e:
            logger.error(f"保存配置到文件失败: {config_file}, 错误: {e}")
    
    def add_proxy(self, proxy: Union[Proxy, str], proxy_type: str = "datacenter", 
                 country: str = "unknown", city: str = "unknown", 
                 isp: str = "unknown", asn: str = "unknown") -> Proxy:
        """
        添加代理到池中
        
        Args:
            proxy: 代理对象或代理字符串
            proxy_type: 代理类型，可以是 "datacenter", "residential", "mobile"
            country: 代理所在国家
            city: 代理所在城市
            isp: 代理所属ISP
            asn: 代理所属ASN
        
        Returns:
            添加的代理对象
        """
        with self.lock:
            if isinstance(proxy, str):
                proxy = Proxy(proxy, proxy_type, country, city, isp, asn)
            
            # 检查代理是否已存在
            for existing_proxy in self.proxies:
                if existing_proxy.proxy_str == proxy.proxy_str:
                    logger.warning(f"代理已存在: {proxy.proxy_str}")
                    return existing_proxy
            
            self.proxies.append(proxy)
            logger.info(f"添加代理: {proxy}")
            return proxy
    
    def remove_proxy(self, proxy: Union[Proxy, str]) -> bool:
        """
        从池中移除代理
        
        Args:
            proxy: 代理对象或代理字符串
        
        Returns:
            是否成功移除
        """
        with self.lock:
            if isinstance(proxy, str):
                for i, p in enumerate(self.proxies):
                    if p.proxy_str == proxy:
                        self.proxies.pop(i)
                        logger.info(f"移除代理: {proxy}")
                        return True
                logger.warning(f"代理不存在: {proxy}")
                return False
            else:
                if proxy in self.proxies:
                    self.proxies.remove(proxy)
                    logger.info(f"移除代理: {proxy}")
                    return True
                logger.warning(f"代理不存在: {proxy}")
                return False
    
    def get_proxy_count(self) -> int:
        """获取代理数量"""
        return len(self.proxies)
    
    def get_available_proxy_count(self) -> int:
        """获取可用代理数量"""
        return sum(1 for proxy in self.proxies if proxy.is_available())
    
    def check_and_restore_proxies(self):
        """
        检查可用代理比例，如果低于40%则恢复所有代理并重置统计
        
        Returns:
            bool: 是否执行了恢复操作
        """
        with self.lock:
            total_proxies = len(self.proxies)
            if total_proxies == 0:
                return False
                
            available_proxies = [p for p in self.proxies if p.is_available()]
            available_ratio = len(available_proxies) / total_proxies
            
            if available_ratio < 0.4:  # 可用代理低于40%
                logger.warning(f"可用代理比例过低 ({available_ratio:.2f}), 恢复所有代理并重置统计")
                
                # 恢复所有代理
                for proxy in self.proxies:
                    proxy.disabled = False
                    proxy.cooldown_time = 0
                    proxy.consecutive_failures = 0
                    proxy.success_rate = 1.0  # 重置成功率
                    proxy.total_requests = 0
                    proxy.successful_requests = 0
                    proxy.failed_requests = 0
                    proxy.average_response_time = 0
                    proxy.total_response_time = 0
                    proxy.health_score = 100
                
                # 记录恢复操作
                logger.error(f"代理池恢复: 总代理数 {total_proxies}, 恢复前可用代理数 {len(available_proxies)}, 可用比例 {available_ratio:.2f}")
                return True
            
            return False
    
    def select_proxy(self) -> Tuple[Optional[Proxy], bool]:
        """
        选择一个代理
        
        Returns:
            (代理对象, 是否使用直接连接)
        """
        with self.lock:
            self.total_request_count += 1
            
            # 检查代理池状态，必要时恢复代理
            self.check_and_restore_proxies()
            
            # 检查是否应该使用直接连接
            use_direct = self._should_use_direct_connection()
            if use_direct:
                self.direct_request_count += 1
                self.stats["direct_requests"] += 1
                logger.debug(f"使用直接连接，直接请求比例: {self.direct_request_count / self.total_request_count:.2f}")
                return None, True
            
            # 获取可用代理列表
            available_proxies = [p for p in self.proxies if p.is_available()]
            
            if not available_proxies:
                logger.warning("没有可用的代理")
                return None, True  # 如果没有可用代理，使用直接连接
            
            # 根据选择策略选择代理
            if self.selection_strategy == "random":
                proxy = random.choice(available_proxies)
            elif self.selection_strategy == "round_robin":
                proxy = available_proxies[self.total_request_count % len(available_proxies)]
            elif self.selection_strategy == "weighted_random":
                # 使用健康分数作为权重
                weights = [p.health_score for p in available_proxies]
                proxy = random.choices(available_proxies, weights=weights, k=1)[0]
            elif self.selection_strategy == "best_performance":
                # 选择健康分数最高的代理
                proxy = max(available_proxies, key=lambda p: p.health_score)
            else:
                # 默认使用加权随机
                weights = [p.health_score for p in available_proxies]
                proxy = random.choices(available_proxies, weights=weights, k=1)[0]
            
            # 更新统计数据
            self.stats["proxy_requests"] += 1
            
            logger.debug(f"选择代理: {proxy}")
            return proxy, False
    
    def _should_use_direct_connection(self) -> bool:
        """
        判断是否应该使用直接连接
        
        Returns:
            是否使用直接连接
        """
        if not self.direct_request_enabled:
            return False
        
        # 如果没有可用代理，使用直接连接
        available_proxies = [p for p in self.proxies if p.is_available()]
        if not available_proxies:
            return True
        
        # 根据比例决定是否使用直接连接
        return random.random() < self.direct_request_ratio
    
    def update_proxy_result(self, proxy: Optional[Proxy], success: bool, 
                           response_time: float = 0, error_type: str = "unknown"):
        """
        更新代理请求结果
        
        Args:
            proxy: 代理对象
            success: 请求是否成功
            response_time: 请求响应时间（秒）
            error_type: 错误类型
        """
        with self.lock:
            # 更新全局统计数据
            self.stats["total_requests"] += 1
            if success:
                self.stats["successful_requests"] += 1
            else:
                self.stats["failed_requests"] += 1
            
            # 如果没有使用代理，直接返回
            if proxy is None:
                return
            
            # 更新代理统计数据
            if success:
                proxy.update_success(response_time)
            else:
                proxy.update_failure(error_type)
            
            # 移除重复的禁用逻辑 - 禁用逻辑已在Proxy类的update_failure方法中处理
            # 这里不再重复检查，避免多管闲事的禁用策略
    
    def wait_for_request_interval(self):
        """等待请求间隔 - 优化版本，不再等待"""
        with self.lock:
            # 只记录时间，不进行等待
            self.last_request_time = time.time()
            # 记录请求，但不阻塞
            logger.debug("请求间隔检查 - 不等待")
    
    def check_proxy_health(self, test_url: str = "https://httpbin.org/ip"):
        """
        检查所有代理的健康状态
        
        Args:
            test_url: 测试URL
        """
        with self.lock:
            now = time.time()
            
            # 检查是否需要进行健康检查
            if now - self.last_health_check_time < self.health_check_interval:
                return
            
            self.last_health_check_time = now
            
            logger.info("开始代理健康检查")
            
            for proxy in self.proxies:
                if proxy.disabled:
                    continue
                
                try:
                    # 创建会话
                    session = requests.Session()
                    
                    # 设置代理
                    session.proxies = proxy.proxies
                    
                    # 设置超时
                    timeout = 10
                    
                    # 发送请求
                    start_time = time.time()
                    response = session.get(test_url, timeout=timeout)
                    end_time = time.time()
                    
                    # 计算响应时间
                    response_time = end_time - start_time
                    
                    # 检查响应状态
                    if response.status_code == 200:
                        proxy.update_success(response_time)
                        logger.debug(f"代理健康检查成功: {proxy}, 响应时间: {response_time:.2f}秒")
                    else:
                        proxy.update_failure("status_code_" + str(response.status_code))
                        logger.warning(f"代理健康检查失败: {proxy}, 状态码: {response.status_code}")
                
                except requests.exceptions.RequestException as e:
                    proxy.update_failure(str(type(e).__name__))
                    logger.warning(f"代理健康检查异常: {proxy}, 错误: {e}")
            
            logger.info("代理健康检查完成")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取代理池统计数据"""
        with self.lock:
            stats = self.stats.copy()
            
            # 添加代理统计数据
            stats["proxy_count"] = self.get_proxy_count()
            stats["available_proxy_count"] = self.get_available_proxy_count()
            stats["disabled_proxy_count"] = sum(1 for proxy in self.proxies if proxy.disabled)
            
            # 添加成功率
            if stats["total_requests"] > 0:
                stats["success_rate"] = stats["successful_requests"] / stats["total_requests"]
            else:
                stats["success_rate"] = 0
            
            # 添加直接请求比例
            if stats["total_requests"] > 0:
                stats["direct_request_ratio_actual"] = stats["direct_requests"] / stats["total_requests"]
            else:
                stats["direct_request_ratio_actual"] = 0
            
            return stats
    
    def get_proxy_stats(self) -> List[Dict[str, Any]]:
        """获取所有代理的统计数据"""
        with self.lock:
            return [proxy.to_dict() for proxy in self.proxies]
    
    def reset_stats(self):
        """重置统计数据"""
        with self.lock:
            self.stats = {
                "total_requests": 0,
                "successful_requests": 0,
                "failed_requests": 0,
                "direct_requests": 0,
                "proxy_requests": 0
            }
            
            # 重置代理统计数据
            for proxy in self.proxies:
                proxy.total_requests = 0
                proxy.successful_requests = 0
                proxy.failed_requests = 0
                proxy.success_rate = 1.0
                proxy.consecutive_failures = 0
                proxy.average_response_time = 0
                proxy.total_response_time = 0
                proxy.health_score = 100
                proxy.disabled = False
                proxy.cooldown_time = 0
            
            # 重置请求计数
            self.direct_request_count = 0
            self.total_request_count = 0
            
            logger.info("统计数据已重置")
