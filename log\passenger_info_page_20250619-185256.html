<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="ja-<PERSON>" xml:lang="ja-<PERSON>">

	
	<!-- skey = 2025/06/19 19:52:57 rei21c homH0aqtCl dljdmx+4ec  --><head id="j_idt53">
    <base href="about:blank">
    <style>
        /* Hide elements that might try to load external resources, as a fallback. */
        img, svg, video, iframe, link[rel="stylesheet"], script { display: none !important; }
    </style>

		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
			<meta http-equiv="X-UA-Compatible" content="IE=EmulateIE9; IE=EmulateIE10" />
			<meta http-equiv="X-UA-Compatible" content="IE=7" />
			<meta http-equiv="X-UA-Compatible" content="IE=edge" />
		<meta name="description" content="" />
		<meta name="keywords" content="" />
		<meta name="format-detection" content="telephone=no" />
		<script type="text/javascript" src="/international_asw/dynatrace/ruxitagentjs_ICANVfhqrux_10273230920145353.js" data-dtconfig="rid=RID_-1121036275|rpid=-1136736947|domain=aswbe-i.ana.co.jp|reportUrl=https://bf90419wvr.bf.dynatrace.com/bf|app=78bf0b58acf6ed13|cors=1|owasp=1|featureHash=ICANVfhqrux|xb=www^bs.google-analytics^bs.com^pana^bs.demdex^bs.net^pcollect^bs.tealiumiq^bs.com^pallnipponairways^bs.australia-3^bs.evergage^bs.com^pallnipponairwayscolt^bs.tt^bs.omtrdc^bs.net|rdnt=1|uxrgce=1|cuc=k8vpgh61|mel=100000|expw=1|dpvc=1|md=mdcc1=bAsw.PnrOutput.recordLocator,mdcc2=bAsw.PnrOutput.paymentDetailInfoList[0].fopCode,mdcc3=bAsw.PnrOutput.ticketedFlag,mdcc4=bAsw.PnrOutput.nhPnrFlag,mdcc5=bAsw.PnrOutput.itinerarySummary.departureDateYyyymmdd,mdcc6=bAsw.SearchCriteriaOutput.cffCodeInput,mdcc7=a#main ^rb div.section.sectionNoFlow ^rb table ^rb tbody ^rb tr ^rb td.flightNumber.lastCol ^rb span ^rb span:nth-child(2),mdcc8=cunique|lastModification=1750184416409|tp=500,50,0|agentUri=/international_asw/dynatrace/ruxitagentjs_ICANVfhqrux_10273230920145353.js"></script><link rel="shortcut icon" href="https://www.ana.co.jp/favicon.ico" />
		<title>お客様情報入力 | ANA</title>
		<noscript><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/css/asw_noscript_pc.css?99001fc" />
		</noscript>

		<!--  [ Adobe Target ] -->
		<script type="text/javascript" src="https://www.ana.co.jp/behavior/adobetarget/target_intbe.js"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/jquery-1.8.3.min.js?717d3c0"></script><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/ja/css/asw_initialize.css?99001fc" /><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/css/asw_screen_pc.css?99001fc" /><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/css/asw_common_pc.css?99001fc" /><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/css/asw_forminput_pc.css?99001fc" /><script type="text/javascript">
		var Asw = Asw || {};
		Asw.GlobalWord = Asw.GlobalWord || {};
		
		Asw.GlobalWord.toolTipCloseIconAltText = "閉じる";
		
		</script><script >bazadebezolkohpepadr="1586635698"</script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/akam/13/5e9222ad"  defer></script></head><body>
		
		<noscript>当サイトをご利用になるためには、JavaScript対応のブラウザが必要です。設定でJavaScriptを有効にしてください。有効にできない場合はANA電話窓口へお問い合わせください。
		</noscript>
		<div id="preLoadingArea"></div>
	
	<div id="cmnHeader" class="platinum">
		<!--===== cmnHeader =====-->
		<div class="headerWrapper">
			<div class="header">
				<p class="logo"><a href="https://www.ana.co.jp/asw/AMCTopServlet?type=a" tabindex="-1" aria-hidden="true"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/ana_logo_navy.png?717d3c0" alt="ANA Inspiration of JAPAN。リンク先はマイレージクラブトップページです。" height="28" width="274" /></a>
				</p>
					<ul class="headerMenu">
						<li><a href="https://www.ana.co.jp/asw/AMCTopServlet?type=a">ANAマイレージクラブトップページ</a>
						</li>
						<li><a href="https://www.ana.co.jp/other/int/meta/0005.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">お問い合わせ<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
						</li>
							<li class="btnLogoutArea"><a href="https://aswbe-i.ana.co.jp/rei21c/international_asw/rest/logout" role="button" class="btnBase btnLogout">ログアウト</a>
							</li>
					</ul>
			</div>
		</div>
			<div class="amcUserInfoWrapper">
				<div class="amcUserInfo">
					<div class="userNameArea">
						<table>
							<tr>	
									<td class="statusIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_status_05.png?717d3c0" alt="プラチナサービスメンバー" height="46" width="60" />
									</td>
								<td class="userName">佐藤 直樹 <span>様</span>
								</td>
							</tr>					
						</table>
					</div>
						<div class="balance">
								<dl class="upgradePointBalance">
									<dt>アップグレードポイント残高
									</dt>
									<dd>
											<dl>
												<dt>(2025年度)
												</dt>
												<dd>
													<em>20
													</em>ポイント
												</dd>
											</dl>
									</dd>
								</dl>
							<dl class="mileBalance">
								<dt>マイル口座残高
								</dt>
								<dd><em>75,663</em><span class="currencyCode">マイル</span>
								</dd>
							</dl>
						</div>
				</div>
					<p class="nowTime">6月19日19時52分現在
					</p>
			</div>
		<!--===== /cmnHeader =====-->
	</div>
		<div id="sessionKeeperContainer" class="sessionKeepingContainer"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/bg_modalWindow.png?717d3c0" alt="" height="0" width="0" />
			<div id="extendSessionExpiredDialog" class="modalExpire modalSmall modalContainer" role="dialog" aria-labelledby="extendSessionExpiredDialogTitle" aria-hidden="true">
<form id="sessionKeeperContainer:j_idt183" name="sessionKeeperContainer:j_idt183" method="post" action="https://aswbe-i.ana.co.jp/rei21c/international_asw/pages/award/reservation/award_mandatory_passenger_information_input.xhtml?aswcid=1&amp;rand=20250619195257homH0aqtCl" autocomplete="off" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="sessionKeeperContainer:j_idt183" value="sessionKeeperContainer:j_idt183" />
<input type="hidden" name="sessionKeeperContainer:j_idt183_operationTicket" value="dljdmx+4ec9adf4c32009b2dbafb27600e6c51f~wN_-cZLTD0xvAp-PtocHJpgUjpLf4-PsC6E8jChf!1750330359811.aere-xml-controller-67d4778877-84sff" /><input type="hidden" name="sessionKeeperContainer:j_idt183_cmnPageTicket" value="4" />
					<h1 id="extendSessionExpiredDialogTitle" class="dialogTitle">まもなくセッションが終了します</h1>
					<div id="extendSessionExpiredDialogMessage" class="dialogMessage" tabindex="-1">このままお手続きを進める場合は、［延長］ボタンを押してください。</div>
					<p class="modalButton"><input id="sessionKeeperContainer:cmnSessionKeepingButton" type="submit" name="sessionKeeperContainer:cmnSessionKeepingButton" value="延長" class="btnBase btnModal btnMainStream" onclick="mojarra.ab(this,event,'action',0,0,{'onevent':function(data) {Asw.doCommonAjaxCallback(data,false, event); Asw.SessionKeeper.doSessionKeep(data);},'onerror':function(data) {Asw.doCommonAjaxErrorCallback(data); }});return false" />
					</p><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:0" value="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" autocomplete="off" />
</form>
			</div>
			<div id="showSessionExpiredDialog" class="modalExpire modalSmall modalContainer" role="dialog" aria-labelledby="showSessionExpiredDialogTitle" aria-hidden="true">
				<h1 id="showSessionExpiredDialogTitle" class="dialogTitle">セッションが終了しました</h1>
				<div id="showSessionExpiredDialogMessage" class="dialogMessage" tabindex="-1">一定時間操作されなかったため、お手続きを進めることができません。お手数ですが、再度トップページからお手続きください。</div>
				<p class="modalButton">
					<input type="submit" value="閉じる" class="btnBase btnModal btnMainStream" onclick="Asw.SessionKeeper.closeTimeoutDialog(); return false;" />
				</p>
			</div>
		</div>
		<!--===== dialogMessages =====--><div id="cmnModalMessages"></div>
		<div id="prebookConfirmDialog" class="modalContainer modalConfirm modalSmall " aria-hidden="true" role="dialog">
<form id="prebookConfirmDialog:j_idt403" name="prebookConfirmDialog:j_idt403" method="post" action="https://aswbe-i.ana.co.jp/rei21c/international_asw/pages/award/reservation/award_mandatory_passenger_information_input.xhtml?aswcid=1&amp;rand=20250619195257homH0aqtCl" autocomplete="off" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="prebookConfirmDialog:j_idt403" value="prebookConfirmDialog:j_idt403" />
<input type="hidden" name="prebookConfirmDialog:j_idt403_operationTicket" value="dljdmx+4ec9adf4c32009b2dbafb27600e6c51f~wN_-cZLTD0xvAp-PtocHJpgUjpLf4-PsC6E8jChf!1750330359811.aere-xml-controller-67d4778877-84sff" /><input type="hidden" name="prebookConfirmDialog:j_idt403_cmnPageTicket" value="4" /><div class="dialogMessage" tabindex="0"><p>この後、名・姓の修正や変更ができません。</p><p>「搭乗者選択」欄の氏名が「名」「姓」の順番で表示されていることを再確認ください。</p><p>例）<strong class="highlight">空野 太郎</strong> 様 → MR <strong class="highlight">TARO SORANO</strong></p><p>保安上の理由により、航空券とパスポートのお名前が同じ表記でない場合はご搭乗いただくことができませんので必ず再度ご確認ください。</p></div>
					
					<ul class="modalBtnHorizontal">
							
							<li><input type="submit" name="prebookConfirmDialog:j_idt419" value="戻る" aria-controls="prebookConfirmDialog" class="btnBase btnModal btnCancel" onclick="Asw.Dialog.getInstance('prebookConfirmDialog').close(event, Asw.DialogSequencer.isActiveSequencerNotDisplayedLastDialog());  return false;" />
							</li>
						
						<li class="btnArrowNext"><input type="submit" name="prebookConfirmDialog:j_idt422" value="OK" aria-controls="prebookConfirmDialog" class="btnBase btnModal btnMainStream " onclick="Asw.Dialog.getInstance('prebookConfirmDialog').callOpener('NORMAL', event); return false;" />
						</li>
					</ul><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:1" value="54udTpt4E0xx95DlY8j0dfFhkwD1bytGPVcBRv5pMxjdg4jodV6a8DrfZJlSvXe2jFM2Igekadgdx9EtGEHcR2uD/1xKtIRapPdZnhxPK5pzri83ADGJD16CU0mQCptUYlMIBqvJyqZ4lrCjBi3BpksVc2m200Xj1M7NTnkQ3gqWjQ8vinp8uOUlrhtskzqIfLBNXu+FSyMSVzqlubNcIh9rSXdEEPgfcU1Q3GMN/E/y9ZMEWtneD90SAI9CrgUG1K57PsvP1CdRLQoYbiAnbJUMAIH16zpDgWORYJ9DvbYnIDO8Be3eRWUcy61w9JnRGvK7uy9GOXpMhvu4LaGxs6iEKoCf3vbXdDmCxCZuADLPyhWGDA4rui+71YNlx+hA5DnBhawjXJQ4p3g1lzI9k0dQtqcru1CruZbOgo3p2ZuYZzbeAKxt90FUGB857IQGsFaF71E0hiPRzGG8V26552MZ1LAoTL6EpmeQMoJlJHZ4FJmSAjxryjf3fwrkcUfetR/DLsvNQ2GxGE8C48mX8Cxwq0U0WyqPV+77qVzwSCitgqqrYB8QyRi/eXUP3F4IaUKDm3AubXidNbF4Y4baeSWQXx9ED98k9sz3m8fmA1Hhp+V0QdDjM97qeA+n3drubu+VwiX9VEts2vrSPBUduaFZFR70t9JkccymxNkkMbse8cGcDT6NZ626rrjlDnmLZJpbpob9BOBkJCrNVVA6YvaSGmmPckgXApgGH+dtKHX2f/zELV8zN9ev1RVcyiLnUwfMUbplW0m3QvHXjUFgpS+IS7fJFvhCj7QdzD3WICiNfDXAWRo4qfoMgavFgVUxGKLIWDvJMi471y5V8I1gnZ34kRaIQ2Xl8P6ro2C2g18w5o15OJIL1r5W+NWf5YW4SfQpgNDHJp8LCFy1R3Re3GIaGIBuN/m4jEUxBB0qz2q5GQu8kM4YdpBDvzLEuuR7aiN4Tr7W9VXvemLDWnNmJjxMN6GJRsy1zey1bf8jIyVnkgmizXSSCzacCjR3UQVhskO+YBzhIfTCmOfRv87a112grn72LJ2T5IB9a8/eJHYhT18CmGwjmeQ6tWbgKOJU0TNCfEkGAl4FGgbtrzm3MMYc2Dx8F3pVPzrWXu0P0drzqhwufT0uE0jqwLWSzHwPv0r5NDy6TsiqnQnnpWlS0/9h1ife6PJwOAgCLZFRmiZj1aaqqTNaOoBCWp62I5lrwj1NFOCiMdTeH9Dufw4ro9ABxacFc2Q489bADqOSkfbZVE4BtJ9Lr1N1e8zu8ayPi1mK4qb8+q79S5K+4k6uKfZ794XyS8tWhW0OmZ5iNqrhyJHtSV0hr90P9KxI+r2kEd+H5KKh68WAalHpm9mxDz1GpSYvaxWR8pD6C2VxT7HEeuHtsCnUjFPYPQ8sa3UN4Vjjs8GmYLTXpHp7i3L1qe9Gnu3eSceNtN4Ywah8MV44sd4y0LoOIEuWL+VAJV1aWLZ4+D6Gye95WSxU5s4QQMypy2Jghecy6toPAhrrzfEaMxGHoHyLv9TLVZFqZKwcMkdlhleZNYbK8tt90FygBViXELJFYyal5jpUTOMPAZwvKQg4Lcbs+Ww2mhL2xqvwzhqNQn3TEnLFVWR2fQL56doonL+UKLVzrHUpNjias1AaviHWjTeJl/yq8DeuDBqxckUaNVP3x3YaECfE+xWZFZBKVM1fCqGV3H+1aJd6RzyQUsdcxW8f3V9LrYRxeAxw3y5hclmvscTgwqERqGFhdmRRPgv+xoBcuX6c/+x4SYYnau5qcY9celok+Up4V4T59a0fJfEgxnmXVSat/RQNmaq10awUUCFXJt5Qgr2EU9C1RdJ0XMAcCbCYzZ39HQLAyGvhOWwMl+bhEG9nCtRmvRGH3JBCrDXeAnhFGtS8EJ2KE4jRN596hlR+WqgJES0AmPM2GX3logFGgLqqMHosjQaDHTgyPmHiqQAn9fcdt4xJBdRiYMhK5ma9gsTyR6IDKfjkyGvf9bOeN7OAYTHmTacouOVj3IZ19OaB8Bc8TiWatA75J73+teIFh8KqD9z/7c3Qd717TXCkFPbwHQafbDHugfptUSHq2cYh7ro0uSLd1zv7oYhHeqW6ioMsxI50nnJ0sJLA7DbFbTdgR8H8jiqh1ipM7C7eefZdxdlB1cq4SzOKZm3HeaOkCjaPDKfgMLuJxfRpTMJONMk9K/0jYE4umr7n6NXh5hs3cHtjrXlFFYxMAzzhAoMOZ3f7CP2a8S9XYZGL4GU6ncr++b8wT6e33CgmZJVaXXd+Kz4Pr9ry1qoeIsr+j1+pZtqIaarGPW2H6s6pRnC7SdrzjXtSxFj2KzPP3PPMNSZ3/veWGpSI0i7OsA2WVyCa0/Uy2Qo7vXViwG2YlGGd5EaHquAWFT3lGIOgEblI6UyG/96qvw8WYmNTrksvEkxPOhz50tl+YwA4Yy5zwQRWYLNLfwdN1upSqVekjxB6Cvi5zumZ3TbosrZR7aDT0lrem6GcEuIWqLyUZIFGBSk33snBNqNkyTpHNUGaRWQ6QRA/MFC+VevPlxGOmVarXgGxBrqCwxeeh7rHLdyjRk894UR7qgPJ+L2mwEkjZ2qCJSsfF8iVspp4eB77Olg2W2IU9cR17Fhu0KJ+t1EvL96GXwzUAeAW2JzbMUp5NuDeSESfZRcXnLAWc5elmqsxi1+T6HQi34gtIRtsMuQs205QAnCrIb5W4fewp7u+YWCJqcDKevScoUfYd5ZU22tWhDbh9LzjnuSVpAYlVLBgUr83iHPS6UysaWRBG9d99rRemtP/XapHLCzqn+Acf1NV7c5qkhjOANlZW8P7nEUrFvv8lhvpVTsWxbmM841Gx/dvxbrB41vVhYCv7K/7YI5E0Oi1zhgsqT3mHyXSjpHC4SMafWi53FbxO5W7z0MYCCZ59J5Eo5EbuOYYbpyWj9/Zt+sIOJsCeQt2ZWOTedq+tOq6GT46c+BlYWJf8FJYcJd7cCb1fRoPNhnTEzRDFeYU4Wwd38PzMs7awJpoNLINPZu4NsVD+mLcpru2gPK91sEnPiuEGpkcUdZZ1oq2bq/YUj+/ltjemDHkk4X+gyFyekgnhoeBzCfJ20Rv4+uf8AZZW7Oegukr/66fsU2xGXrfYrjQ8lou3DI0sbGylEBxzs4LMU6mQJRhjrthe+pr05gPps53YeORtYm1akTpUqrgO/5QSmVPA5M9CBk4/5lc3uONBwoEGhu/dibANLuOYj9oxfpY0sTxsPY7ILXx9nTknvFX86XtjMDual5aTtdRoDR4JODMjf6Vp8opZATCLFAOJYXorpQPBExz37uGGzx/Fh5vqde9YhJ8vejGHW6Tanit2SvdhiKiTO4yxyZaPs04gB2zAB1yGTXt/NGsu6V6jV22cEp5eIf4OmQq05Y0OXjfymuZzr4ex90xHdqAqlSOkrz//EuVTkr3tcUqyt1sCQtNU6us6MzBJNKHSaU+CdrSBMBxXx7Zk0mU+OEpx/r7hCKel2aM8kxtz77FcSvW5WTaomx6hkWB7OquHsH1Gts2Sp4DCU6y2M47qu+IGx2ZJl15BlBSEaDmJ7L/PNMXMop7kz7MenkM5FxGAbtNUO0Wfa3AWJkPht5BrABdU/OB0khReoo6xGzULpc16gMQJBLL+zV4Ly8pnIF7wAqcYyaPaBMjL5ScYKk0CteiNGmIi7CTFIiVMSH0k38jLfaN8o0uVrKEfSFh7BKlAEMy5/QKLng/PmsKLsYpBSB93KYxbxHYAtwQavIjxdrjV9TC5nIzRAYiXgWL0bq9JwH9lj+3XKVTzlxxu3lukpsg0EmqaFKZRwHPeRKjWzYtV/jaN71a3u9D4MwbZyK5mokv/K5pW88Eb3AA9r4YWUk6ZXQ6OW9oFBmCsFkmv8oAz3r2qypP1TwCDMlxKJOEv5tJxryVM0RI70edtltgOFIwIXCmohTxflARH/V2Ki6Sfn3jwOglWyeri3vP5T0OiYyhzK7QbCe2rrdPOztejlnKObzmzcQRsjGK3IEH9whaTC2l/HDENoJyEfzsNK+Z8gEp0DGpIdkIOhBDAo9yBz88TJddrdkmoAyRHouG/kfoR+xr5oYt2F6RDf3pV2KImp2N+xKSqHyL1S8fEAP4N74kqoPgqWXDXtGLpFOfW/t6tLqwJN1Nvxy1T4yLwK6doaiYbXiYZjwF5F1ZfDZ7bKxA3atcmD5hdu3UqwOyx0I6e7FfQS9Fb1XvcSo6HMT+HoDpZZl3/Gi1Na3BdlmlJvqXLjWCOcj9KBYKlYfoxRrkPBGGmZnNTzu8dWTiy7wN29yE0nN7UXLHLqDECmiRwxgRbXiNo7nvwaOj24nfAJIy3MgnSuq+Mqhff2iIMf6KpCJNZW0oGlKHb+AE2XJhQZ5QFgkjaDpAsgzUamjs+8PxnHFH6zBXEZsCid/892YHxbFKIgUnMnIU9jmPRSh8r7XqFDL/KDwaA9TcuiLMDbJ2J0r3dwW5xfb3xhK4Te+KgphkQxXz71BQt9D6cnGxUb6IMXPleGtyKRhMTJ2yRLm66ZLuTrz5Z1/c7ZyZu8I9c1bQwoS6Sew91rRUvosG8MTXiFboKg0kY59aDPHXMgZ0N18wiknPHmiebzMWQAwQVdbH+EyOA/brCmVHd3YSz3YP6E4Ef5uhK7F3G8N6mF7o/lNOtSRqR9yma1Ic52VW5/dvtQ0pRmQJGV+FR5/GQnh6/kwMxKMwUk89p+Q9L2Utiixo2yxoSw7wMbACVD5vLBfciJ9EsKSiyIswc8sljW68MiPn+9KWiBSnsdL7NLPwuFjeM1Cd5vwx4Hw8usx86P7ORHD4EE2HU97vWe+268u6D8hb85fkYfejYqAEnOhhJBgmjizIts7+a9mwj9UhpJCzSsofW236ci3Dbr8IAw0DAi4iGnAkZ37AAl76sZexk+uxGBxIbwtJBpWPxuApHkai4+D+MmO9QmbHXpo9sOGfMtW+eNq6oaDZoGhGa3jnJ9ogePP5LefdLa8pfmPEXe1ZX96FcKiJcKTmUXKSUDMbBWpU3ZhSSNC1a5Ponl/4Z0bAoSG5FF0pRIZNIqUZ/S/arop7Bc5jq77AgXqrMdc0aoYv02W5MDXLk+fyDvMOJz2AdHNIwrdTqp58rCYM2r8N45sBCi3rqMQU16T19VByx10hIYFwEFjO3cQfQFW1KCmuGkupQfJirLJNqJMeHXtqBrLY50dYTpjcEog0ie5ziZAiJKEwWaQgIX55x9YgtWd3PZKuYyMZ27zlCLoMSgIp/40pXa/nczGaAXpSJA7JyqPQ9RZhKxd+j+KYRMRk9RfIPcE2ezom7mgh0Z/x00jktj0YrlRk02t0m0J8+6/lo0pJ8NAVTdfsIL9xSjh+zbn/6pRybwyJevZzyOmgxQoq63SjeqOZjHrJvkATDXQ5eOzRbxk7Asz9EPo3WhAIwGcPoMiq/IfWIzSw03sN93J/yEYgNd9itT0eMkyU4stVaFWZ0vzR2SPtsN9C3OUXHGiUUEbG149ZCOXOQDj9AUPWQKd2V/FVs45U2tqv6ZeTkcCPmbUChDiba9lDytG4ItlXgxat7j4RHfFpT7sJJleuGEcsgziPfA2MoFks/hD1roKtLtr39uWhE7NvtrTMEkjOx0vWleWth1iDYcNZviN8HS0MKVoCLt1AyTX2/w9j5EGceKeFh6bf28UqKFpya8I1/qoKAHlBroaMtdFZzQJidMJI1BbtEMWstxqOS6ifzGVeebVj6cE1liQADk9Tgq2JDCuS3tjUYc3gdBj7aKtbzwA3NkFAgezYb+oLLOaPUz6VDaPqtvOSORvstWB1ZUPpW2FCvYTTLrTSe8gXRZ7oYcustDGH+PpOwJI6vH0WSIEMAuccWYOl6BJpJWzgYVgegkMEH1ynFS+DS3fY560ERCsUitUMyBk5KlZkLBuyVUAEIjL5A1Jc5+cKJIJJZjO2G5IT0oNRjVQ9JV7ZMWe9MrfXs4ogBP2zlmEikmbz/o0ouVIS035VG0Q9X2BkeFFHAId32MKTRMu/WmGhGvo7A1Vk17rXsGbEot3qa953N1mwExpRTVGvzWjDRhYnTEvw/SDmFTUbwH0VIbpQMgj5J8JYOmHUArafMMKgKsGI5H9+RUw1b8XE1Z8U9JwBjx6TijbKqEmJi185XDVBtrxRC03T3Q7tYLJYrZEW4Wd1UryD3Rhldi4XHxOhDf4A4XJtYokxwakEfvx49AA0iH+cq4zLjYtqYouHT75v5AFcV15xx6VfbWR7++wn0OUjmxllH5n9E6gCPjh1noMTFrtkKqI0BpT9GRJ+B8iloXAzY24G/IT2cEiznj8p98nqzDVFDwgvXmg4Jd6g5B/MiMA1BF7zMrNj8L1Q9Nky6QZcRVnJAoS701KLbpi68hHd1fSQwc5fx5sz7luJWZYStJRe/s4K1P9fwTs5ZlBzEJoivdz7U2nXpRWoYsbpf9zcIBpz/mFQZYeqWAemAvxNU1oSo7I3B5qchOgVohWf1GYEZO268XXQBlvkBH5rgtZjlKnYaElJ/Zf/ZmVH6U2DSID40LIvayUwTAiEMw6d9oh/1Hib0ldDJ1ep+hkKl0TnhVlzFvJJ4MvZbkKN2EbAa6NI/MAUlWUfIlZGBJNDet/7vmkyVsfWdXPJxR4qc1II2OB8FwRnU2J+WF12scjY1mhYwNC/zMnJj4kRwt99oI5BvJTfIrnWmYisuysAieQfSKjuAlh27cXmDGcCkdDL2Iz6dcXMTMx9Zq5AV0+fWrr/LtBLmvIOTNBvAUX8Ym3bh2gBVelS3AfJY2vcw/xfAPsrDNuS6DdTJMD5lp7oqwXxN7Hs/dte2E5Gpu35JZjtWhkIldxp5lbvHROmP+gDw4g8mZ9VepQ+jK+fU246nUjDS1GwUmmQ06QrE86boeUkPqP220luFaZfNqmU4mf/sHnVBeLPopIVwEHsTBxZO2pBcex8O9XIOSixSAXtFZMBydMUR8TBOc10DfGbEGMjzpGN6dUa+RnW3EZiAWSREjbRnvNCfaK+gkqYSB1za3V3OrDIxEJzSM2aYKMZZ9LQ2Lt1UDMI8/Qpcln3ST+z2kgEzWHEQHQS92vma6TqjoKaCLdHBqC+yTEuWbrPa4lbYZVq3sNkm6cqHgZ2MQCrDZCUtTtP7SUCCDhDHcBrYq1rDs9drTcI7mnkrtzgVklkSGSuKnaColn0kKIqag+ZQdUhMHrPJMoH2wawABw8f72uaLa9wYRZy1Ej59n770DZ+FhiqPDfsJmwCZNn/qzoWbEP6eo8PwGIzA5wNTwaxP9+KhN8I0RnJbW33fd3+wW7LpJdbbG02RQFMZh9O0AfKkJW56DSwgKPECfpH5ePjwbqd8qGYuNy8w7DAXkiKyUuWJ9KYSjur1qVbIPCvv1V+XZ9ewLByCh1sZVH8hHgwZ4dMpkmy6uyR01L3ntehGp41Y6Ur1y7zKc26x339GbWrGqGzzBTRgQnvwCHb8tTY4XlwCgyZixjXDBKk4fEp17zWq3WB9iyp/elPTXXyf+zAuKaiBbhCfqzKnV1T9NOsBtKNpPEhidrt914nEHoZwAJBZfIaePHeLGm/HjGkIeGKH/OrHtzoU83qpGl/Qr1CgfyrI2/85vWquqyKSsl8rCJ7EI6Pm1W6G1cPzCDsWpDLzIEfaPlpmYSD9zNgedUWGBflBV8nrjd6LIuVrLKeEo5LVeNOeaIzzdgTsw9c1XeHrw3pIIuRO3ejGL66RjUVetJsIuF67j/VDpSI4H8HWszq/Z6qGdoUeeV1PkobKt/o5INTiwJhrCbcqt2Q6tmd/TTICpnJz7sJHV1eJudUXXhgr0TwEgdq9SQTTdtjzVQYLdQ3GzbKCl4UTdwga1yecYODG9qdBGZpxyZFcnPHP+vZ2y89PhhwKXBRhNZB5nV/LYrPAE0xUlHpfX1VjF/6K0zFYp5ETQ12Tui2PRJVfiyORgf78YrhzejZtV8AuDeiVW5Lpp8oVMykQV3V0tPZOz4WSth/qI4CvDdb9jy/NUV7Z2eAjhBVOkRqaGgtObmZ58te5neVirb3w4QqB4I9rgoK7a1ulWlmR4k9m34wRQxnMakEPmZxXt+AnL6ya8Pdg3UTHnmCbWnZJ6Y103/3vkBFyIjhHSLk8d/xZ6RECkjrzTlhbjVBO+vn8nhNtaHJV/1khNV+SsLnkJOIB3WfxUphR4m7tEvAwtwII5adEJ1HiFOrE5h5Y7a3rkkCPkogDIuz0fSPldUyYOPTHO1wAJfKviNydiVZck/xBW9lyOyoVEmXSv6lQpLWDVbPGesOeGkOFnks6XpGXVHScPhjuzxBPcN9kmPH9KLatsWVxupxnzl0hWm4LhogBgpKrkc3LjAcKpvQzuBE2tYsMcsF7pdTZ/bi1lciWAdX2M/" autocomplete="off" />
</form>
		</div>
		<!--===== /dialogMessages =====--><div id="cmnContainer">
		<!--=========== .cmnContainer ============--><div id="cmnEmergencyNotice"></div><div id="cmnTitle" class="hgroup ">
		<h1 class="visuallyHidden">お客様情報入力</h1></div>

		<div id="cmnWrapper">
		<!--===== cmnWrapper =====-->
		<ol class="mainFlow" aria-label="完了までの手順、">
			<li><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_flow_01.png?717d3c0" alt="1" height="20" width="28" />フライト検索</li><!--
			--><li><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_flow_02_on.png?717d3c0" alt="2" height="20" width="28" /><span class="visuallyHidden">現在のステップ、</span>お客様情報入力</li><!--
			--><li><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_flow_03.png?717d3c0" alt="3" height="20" width="28" />お支払い情報</li><!--
			--><li class="flowLast"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_flow4_04.png?717d3c0" alt="4" height="20" width="28" />完了</li>
		</ol>

	<div id="main">
	<!--=========== main =============-->
		<ul class="iconExample">
				<li class="requiredIconExample">
					<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="" height="7" width="7" /></span>必須項目です	
				</li>
		</ul>
<form id="j_idt526" name="j_idt526" method="post" action="https://aswbe-i.ana.co.jp/rei21c/international_asw/pages/award/reservation/award_mandatory_passenger_information_input.xhtml?aswcid=1&amp;rand=20250619195257homH0aqtCl" autocomplete="off" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="j_idt526" value="j_idt526" />
<input type="hidden" name="j_idt526_operationTicket" value="dljdmx+4ec9adf4c32009b2dbafb27600e6c51f~wN_-cZLTD0xvAp-PtocHJpgUjpLf4-PsC6E8jChf!1750330359811.aere-xml-controller-67d4778877-84sff" /><input type="hidden" name="j_idt526_cmnPageTicket" value="4" />

<div class="section">
	<h2>搭乗者選択<a href="https://www.ana.co.jp/other/int/meta/0086.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="特典のご利用に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a></h2>
	<table class="passengerInfo">
		<thead>
			<tr>
				
				<th class="name">氏名</th>
				
				<th class="dateOfBirthWide" id="dateOfBirthHeader">生年月日</th>
				
				<th class="gender lastCol" id="genderHeader">性別<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span></th>
				
			</tr>
		</thead>
		<tbody>
				
				<tr>
					<th class="parent_01" id="parentHeader01">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:0:passenger" type="checkbox" name="passengers:0:passenger" checked="checked" data-number="1" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:0:passenger">NAOKI SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader01">1963年06月05日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader01">				
							<span class="formWrapper">
							<label for="passengers:0:gender" class="visuallyHidden">NAOKI SATOの性別</label><select id="passengers:0:gender" name="passengers:0:gender" size="1">	<option value="">選択してください</option>
	<option value="M" selected="selected">男性</option>
	<option value="F">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_02" id="parentHeader02">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:1:passenger" type="checkbox" name="passengers:1:passenger" data-number="2" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:1:passenger">REO SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader02">1995年05月18日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader02">				
							<span class="formWrapper">
							<label for="passengers:1:gender" class="visuallyHidden">REO SATOの性別</label><select id="passengers:1:gender" name="passengers:1:gender" size="1">	<option value="">選択してください</option>
	<option value="M" selected="selected">男性</option>
	<option value="F">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_03" id="parentHeader03">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:2:passenger" type="checkbox" name="passengers:2:passenger" data-number="3" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:2:passenger">ERI SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader03">1963年08月14日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader03">				
							<span class="formWrapper">
							<label for="passengers:2:gender" class="visuallyHidden">ERI SATOの性別</label><select id="passengers:2:gender" name="passengers:2:gender" size="1">	<option value="">選択してください</option>
	<option value="M">男性</option>
	<option value="F" selected="selected">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_04" id="parentHeader04">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:3:passenger" type="checkbox" name="passengers:3:passenger" data-number="4" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:3:passenger">TAISHI SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader04">1992年12月03日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader04">				
							<span class="formWrapper">
							<label for="passengers:3:gender" class="visuallyHidden">TAISHI SATOの性別</label><select id="passengers:3:gender" name="passengers:3:gender" size="1">	<option value="">選択してください</option>
	<option value="M" selected="selected">男性</option>
	<option value="F">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_05" id="parentHeader05">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:4:passenger" type="checkbox" name="passengers:4:passenger" data-number="5" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:4:passenger">HIROMI FUJIMOTO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader05">1961年11月23日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader05">				
							<span class="formWrapper">
							<label for="passengers:4:gender" class="visuallyHidden">HIROMI FUJIMOTOの性別</label><select id="passengers:4:gender" name="passengers:4:gender" size="1">	<option value="">選択してください</option>
	<option value="M">男性</option>
	<option value="F" selected="selected">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
		</tbody>
	</table>
</div>
		<div class="section">
			<h2 class="midstream">連絡先情報
			</h2>
			<h3>代表者
			</h3>
	
	
	
	<div class="formInput">
		<fieldset>
			<legend><label>代表者</label></legend>
			<dl id="contactEmailAddressInfo">
				<dt class="hasRequiredIcon">メールアドレス<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span><a href="https://www.ana.co.jp/other/int/meta/0057.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="メールの宛先と内容に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a>
				</dt>
				<dd>
					<ul class="verticalList">
						<li class="nonMarginTop"><label for="representativeEmail" class="visuallyHidden">代表者メールアドレス</label><input id="representativeEmail" type="text" name="representativeEmail" value="<EMAIL>" class="widthLarge" placeholder="例：<EMAIL>" />
						</li>
						<li>
							<span class="supplementTxt" id="confirmMailAddressForRepresentative">確認のため再度ご入力ください。
							</span><label for="confirmrepresentativeEmail" class="visuallyHidden">確認用メールアドレス</label><input id="confirmrepresentativeEmail" type="text" name="confirmrepresentativeEmail" value="<EMAIL>" class="widthLarge jsNotPasteInput" placeholder="例：<EMAIL>" aria-describedby="confirmMailAddressForRepresentative" />
						</li>
					</ul>
				</dd>
					<dd style="display:none;"><input id="updateAlwaysInfo" type="checkbox" name="updateAlwaysInfo" /><label for="updateAlwaysInfo">ログイン中の会員情報へこのメールアドレスを登録</label>
					</dd>
			</dl>
			<dl>
				<dt>電話番号<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span>
				</dt>
				<dd class="js-countryWrap">
					<ul>
						<li><label for="representativeTelType" class="visuallyHidden">電話番号種別</label><select id="representativeTelType" name="representativeTelType" class="basicWidth js-typeSelect" size="1" onchange="changeTelType(this,'representativeTel')">	<option value="M1" selected="selected">携帯</option>
	<option value="B1">会社</option>
	<option value="H1">自宅</option>
</select>
						</li>
						<li><label for="representativeTelCountry" class="visuallyHidden">電話番号の国/地区選択</label><select id="representativeTelCountry" name="representativeTelCountry" class="js-countrySelect" size="1">	<option value="" data-sms-possible="true" data-country-code="---">選択してください</option>
	<option value="JP" selected="selected" data-sms-possible="true">日本</option>
	<option value="-" data-sms-possible="true" data-country-code="---">-----------</option>
	<option value="IS" data-sms-possible="true">アイスランド</option>
	<option value="IE" data-sms-possible="true">アイルランド</option>
	<option value="AZ" data-sms-possible="true">アゼルバイジャン</option>
	<option value="AF" data-sms-possible="true">アフガニスタン</option>
	<option value="US" data-sms-possible="true">アメリカ合衆国/カナダ</option>
	<option value="AE" data-sms-possible="true">アラブ首長国連邦</option>
	<option value="DZ" data-sms-possible="true">アルジェリア</option>
	<option value="AR" data-sms-possible="true">アルゼンチン</option>
	<option value="AW" data-sms-possible="true">アルバ</option>
	<option value="AL" data-sms-possible="true">アルバニア</option>
	<option value="AM" data-sms-possible="true">アルメニア</option>
	<option value="AO" data-sms-possible="true">アンゴラ</option>
	<option value="AG" data-sms-possible="true">アンティグア・バーブーダ</option>
	<option value="AD" data-sms-possible="true">アンドラ</option>
	<option value="YE" data-sms-possible="true">イエメン</option>
	<option value="GB" data-sms-possible="true">イギリス</option>
	<option value="IL" data-sms-possible="true">イスラエル</option>
	<option value="IT" data-sms-possible="true">イタリア</option>
	<option value="IQ" data-sms-possible="true">イラク</option>
	<option value="IR" data-sms-possible="true">イラン</option>
	<option value="IN" data-sms-possible="true">インド</option>
	<option value="ID" data-sms-possible="true">インドネシア</option>
	<option value="UG" data-sms-possible="true">ウガンダ</option>
	<option value="UA" data-sms-possible="true">ウクライナ</option>
	<option value="UZ" data-sms-possible="true">ウズベキスタン</option>
	<option value="UY" data-sms-possible="true">ウルグアイ</option>
	<option value="EC" data-sms-possible="true">エクアドル</option>
	<option value="EG" data-sms-possible="true">エジプト</option>
	<option value="EE" data-sms-possible="true">エストニア</option>
	<option value="SZ" data-sms-possible="true">エスワティニ</option>
	<option value="ET" data-sms-possible="true">エチオピア</option>
	<option value="ER" data-sms-possible="true">エリトリア</option>
	<option value="SV" data-sms-possible="true">エルサルバドル</option>
	<option value="AU" data-sms-possible="true">オーストラリア</option>
	<option value="AT" data-sms-possible="true">オーストリア</option>
	<option value="OM" data-sms-possible="true">オマーン</option>
	<option value="NL" data-sms-possible="true">オランダ</option>
	<option value="GH" data-sms-possible="true">ガーナ</option>
	<option value="CV" data-sms-possible="true">カーボベルデ</option>
	<option value="GY" data-sms-possible="true">ガイアナ</option>
	<option value="KZ" data-sms-possible="true">カザフスタン</option>
	<option value="QA" data-sms-possible="true">カタール</option>
	<option value="CA" data-sms-possible="true">カナダ/アメリカ合衆国</option>
	<option value="GA" data-sms-possible="true">ガボン</option>
	<option value="CM" data-sms-possible="true">カメルーン</option>
	<option value="KR" data-sms-possible="true">韓国</option>
	<option value="GM" data-sms-possible="true">ガンビア</option>
	<option value="KH" data-sms-possible="true">カンボジア</option>
	<option value="KP" data-sms-possible="true">北朝鮮</option>
	<option value="MK" data-sms-possible="true">北マケドニア</option>
	<option value="GN" data-sms-possible="true">ギニア</option>
	<option value="GW" data-sms-possible="true">ギニアビサウ</option>
	<option value="CY" data-sms-possible="true">キプロス</option>
	<option value="CU" data-sms-possible="true">キューバ</option>
	<option value="GR" data-sms-possible="true">ギリシャ</option>
	<option value="KI" data-sms-possible="true">キリバス</option>
	<option value="KG" data-sms-possible="true">キルギス</option>
	<option value="GT" data-sms-possible="true">グアテマラ</option>
	<option value="GU" data-sms-possible="true">グアム</option>
	<option value="KW" data-sms-possible="true">クウェート</option>
	<option value="GD" data-sms-possible="true">グレナダ</option>
	<option value="HR" data-sms-possible="true">クロアチア</option>
	<option value="KY" data-sms-possible="true">ケイマン諸島</option>
	<option value="KE" data-sms-possible="true">ケニア</option>
	<option value="CI" data-sms-possible="true">コートジボワール</option>
	<option value="CR" data-sms-possible="true">コスタリカ</option>
	<option value="KM" data-sms-possible="true">コモロ</option>
	<option value="CO" data-sms-possible="true">コロンビア</option>
	<option value="CG" data-sms-possible="true">コンゴ共和国</option>
	<option value="CD" data-sms-possible="true">コンゴ民主共和国</option>
	<option value="SA" data-sms-possible="true">サウジアラビア</option>
	<option value="ZM" data-sms-possible="true">ザンビア</option>
	<option value="SM" data-sms-possible="true">サンマリノ</option>
	<option value="SL" data-sms-possible="true">シエラレオネ</option>
	<option value="DJ" data-sms-possible="true">ジブチ</option>
	<option value="JM" data-sms-possible="true">ジャマイカ</option>
	<option value="GE" data-sms-possible="true">ジョージア</option>
	<option value="SY" data-sms-possible="true">シリア</option>
	<option value="SG" data-sms-possible="true">シンガポール</option>
	<option value="ZW" data-sms-possible="true">ジンバブエ</option>
	<option value="CH" data-sms-possible="true">スイス</option>
	<option value="SE" data-sms-possible="true">スウェーデン</option>
	<option value="SD" data-sms-possible="true">スーダン</option>
	<option value="ES" data-sms-possible="true">スペイン</option>
	<option value="SR" data-sms-possible="true">スリナム</option>
	<option value="LK" data-sms-possible="true">スリランカ</option>
	<option value="SK" data-sms-possible="true">スロバキア</option>
	<option value="SI" data-sms-possible="true">スロベニア</option>
	<option value="SC" data-sms-possible="true">セイシェル</option>
	<option value="GQ" data-sms-possible="true">赤道ギニア</option>
	<option value="SN" data-sms-possible="true">セネガル</option>
	<option value="RS" data-sms-possible="true">セルビア</option>
	<option value="KN" data-sms-possible="true">セントクリストファー・ネイビス</option>
	<option value="LC" data-sms-possible="true">セントルシア</option>
	<option value="SB" data-sms-possible="true">ソロモン諸島</option>
	<option value="TH" data-sms-possible="true">タイ</option>
	<option value="TW" data-sms-possible="true">台湾</option>
	<option value="TJ" data-sms-possible="true">タジキスタン</option>
	<option value="TZ" data-sms-possible="true">タンザニア</option>
	<option value="CZ" data-sms-possible="true">チェコ</option>
	<option value="TD" data-sms-possible="true">チャド</option>
	<option value="CF" data-sms-possible="true">中央アフリカ共和国</option>
	<option value="CN" data-sms-possible="true">中国</option>
	<option value="TN" data-sms-possible="true">チュニジア</option>
	<option value="CL" data-sms-possible="true">チリ</option>
	<option value="TV" data-sms-possible="true">ツバル</option>
	<option value="DK" data-sms-possible="true">デンマーク</option>
	<option value="DE" data-sms-possible="true">ドイツ</option>
	<option value="TG" data-sms-possible="true">トーゴ</option>
	<option value="DO" data-sms-possible="true">ドミニカ共和国</option>
	<option value="DM" data-sms-possible="true">ドミニカ国</option>
	<option value="TT" data-sms-possible="true">トリニダード・トバゴ</option>
	<option value="TM" data-sms-possible="true">トルクメニスタン</option>
	<option value="TR" data-sms-possible="true">トルコ</option>
	<option value="TO" data-sms-possible="true">トンガ</option>
	<option value="NG" data-sms-possible="true">ナイジェリア</option>
	<option value="NR" data-sms-possible="true">ナウル</option>
	<option value="NA" data-sms-possible="true">ナミビア</option>
	<option value="NI" data-sms-possible="true">ニカラグア</option>
	<option value="NE" data-sms-possible="true">ニジェール</option>
	<option value="NZ" data-sms-possible="true">ニュージーランド</option>
	<option value="NP" data-sms-possible="true">ネパール</option>
	<option value="NO" data-sms-possible="true">ノルウェー</option>
	<option value="BH" data-sms-possible="true">バーレーン</option>
	<option value="HT" data-sms-possible="true">ハイチ</option>
	<option value="PK" data-sms-possible="true">パキスタン</option>
	<option value="VA" data-sms-possible="true">バチカン</option>
	<option value="PA" data-sms-possible="true">パナマ</option>
	<option value="VU" data-sms-possible="true">バヌアツ</option>
	<option value="BS" data-sms-possible="true">バハマ</option>
	<option value="PG" data-sms-possible="true">パプアニューギニア</option>
	<option value="BM" data-sms-possible="true">バミューダ</option>
	<option value="PW" data-sms-possible="true">パラオ</option>
	<option value="PY" data-sms-possible="true">パラグアイ</option>
	<option value="BB" data-sms-possible="true">バルバドス</option>
	<option value="PS" data-sms-possible="true">パレスチナ</option>
	<option value="HU" data-sms-possible="true">ハンガリー</option>
	<option value="BD" data-sms-possible="true">バングラデシュ</option>
	<option value="TL" data-sms-possible="true">東ティモール</option>
	<option value="FJ" data-sms-possible="true">フィジー</option>
	<option value="PH" data-sms-possible="true">フィリピン</option>
	<option value="FI" data-sms-possible="true">フィンランド</option>
	<option value="BT" data-sms-possible="true">ブータン</option>
	<option value="PR" data-sms-possible="true">プエルトリコ</option>
	<option value="BR" data-sms-possible="true">ブラジル</option>
	<option value="FR" data-sms-possible="true">フランス</option>
	<option value="PF" data-sms-possible="true">フランス領ポリネシア</option>
	<option value="BG" data-sms-possible="true">ブルガリア</option>
	<option value="BF" data-sms-possible="true">ブルキナファソ</option>
	<option value="BN" data-sms-possible="true">ブルネイ</option>
	<option value="BI" data-sms-possible="true">ブルンジ</option>
	<option value="VN" data-sms-possible="true">ベトナム</option>
	<option value="BJ" data-sms-possible="true">ベナン</option>
	<option value="VE" data-sms-possible="true">ベネズエラ</option>
	<option value="BY" data-sms-possible="true">ベラルーシ</option>
	<option value="BZ" data-sms-possible="true">ベリーズ</option>
	<option value="PE" data-sms-possible="true">ペルー</option>
	<option value="BE" data-sms-possible="true">ベルギー</option>
	<option value="PL" data-sms-possible="true">ポーランド</option>
	<option value="BA" data-sms-possible="true">ボスニア・ヘルツェゴビナ</option>
	<option value="BW" data-sms-possible="true">ボツワナ</option>
	<option value="BO" data-sms-possible="true">ボリビア</option>
	<option value="PT" data-sms-possible="true">ポルトガル</option>
	<option value="HK" data-sms-possible="true">香港</option>
	<option value="HN" data-sms-possible="true">ホンジュラス</option>
	<option value="MH" data-sms-possible="true">マーシャル諸島</option>
	<option value="MO" data-sms-possible="true">マカオ</option>
	<option value="MG" data-sms-possible="true">マダガスカル</option>
	<option value="MW" data-sms-possible="true">マラウイ</option>
	<option value="ML" data-sms-possible="true">マリ</option>
	<option value="MT" data-sms-possible="true">マルタ</option>
	<option value="MY" data-sms-possible="true">マレーシア</option>
	<option value="FM" data-sms-possible="true">ミクロネシア連邦</option>
	<option value="ZA" data-sms-possible="true">南アフリカ</option>
	<option value="MM" data-sms-possible="true">ミャンマー</option>
	<option value="MX" data-sms-possible="true">メキシコ</option>
	<option value="MU" data-sms-possible="true">モーリシャス</option>
	<option value="MR" data-sms-possible="true">モーリタニア</option>
	<option value="MZ" data-sms-possible="true">モザンビーク</option>
	<option value="MC" data-sms-possible="true">モナコ</option>
	<option value="MV" data-sms-possible="true">モルディヴ</option>
	<option value="MD" data-sms-possible="true">モルドバ</option>
	<option value="MA" data-sms-possible="true">モロッコ</option>
	<option value="MN" data-sms-possible="true">モンゴル</option>
	<option value="ME" data-sms-possible="true">モンテネグロ</option>
	<option value="JO" data-sms-possible="true">ヨルダン</option>
	<option value="LA" data-sms-possible="true">ラオス人民民主共和国</option>
	<option value="LV" data-sms-possible="true">ラトビア</option>
	<option value="LT" data-sms-possible="true">リトアニア</option>
	<option value="LY" data-sms-possible="true">リビア</option>
	<option value="LI" data-sms-possible="true">リヒテンシュタイン</option>
	<option value="LR" data-sms-possible="true">リベリア</option>
	<option value="RO" data-sms-possible="true">ルーマニア</option>
	<option value="LU" data-sms-possible="true">ルクセンブルク</option>
	<option value="RW" data-sms-possible="true">ルワンダ</option>
	<option value="LS" data-sms-possible="true">レソト</option>
	<option value="LB" data-sms-possible="true">レバノン</option>
	<option value="RU" data-sms-possible="true">ロシア</option>
</select>
						</li>
						<li class="numberInputWrap">
							<span id="numberInputInfo1" class="supplementTxt marginTop">先頭に0(ゼロ)がある場合、0(ゼロ)を抜いてください(一部、国・地域除く)。携帯電話番号の場合は、携帯電話を契約された国を選択ください。</span>
							<div class="numberRowWrap">
								<div class="block">
									
									<span class="visuallyHidden">国番号</span>
									<span class="viewInput widthMini js-countryCode"></span>
								</div>
								<div class="block"><label for="representativeTel" class="visuallyHidden">電話番号</label><input id="representativeTel" type="text" name="representativeTel" value="9042504176" class="widthMiddle" placeholder="例：9012345678" maxlength="30" aria-describedby="numberInputInfo1" />
								</div>
							</div>
						</li>
					</ul>
				</dd>
			</dl>
		</fieldset>
	</div>
	<div class="titleWrapper">
		<h3>
			<span class="text">eメール・SMSに関するご案内<a href="https://www.ana.co.jp/other/int/meta/0057.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="eメール・SMSに関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a></span>
		</h3>
	</div>
	<div class="formInput">
	<h4 class="formAreaTitle">主に搭乗・運航に関わるeメールの送付先設定（米国へ通知いたします）<a href="https://www.ana.co.jp/other/int/meta/0629.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="米国入国用eメール・SMS・電話番号に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a>
	</h4>
	<div class="paxSettingArea js-mailFormWrap onePax is-open">
			<div class="settingArea">
					<dl>
						<dt class="breakWord">NAOKI SATO
						</dt>
						<dd>
							<fieldset>
								<legend>NAOKI SATOのeメール送付先および米国到着時に提供するメールアドレス</legend>代表者と同じ
							</fieldset>
						</dd>
					</dl>
			</div>
	</div>
	<h4 class="formAreaTitle hasTopLine">主に搭乗・運航に関わるSMSの送付先および米国で受信できる電話番号の設定（米国へ通知いたします）<a href="https://www.ana.co.jp/other/int/meta/0629.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="米国入国用eメール・SMS・電話番号に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a>
	</h4>
	<div class="paxSettingArea js-smsFormWrap onePax is-open">
			<div class="settingArea">
					<dl>
						<dt class="breakWord">NAOKI SATO<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span>
						</dt>
						<dd>
							<fieldset>
								<legend>NAOKI SATOの1つ目の電話番号</legend>
									<ul class="verticalList js-formItem">
										<li><input type="radio" id="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSameAsRepresentativeSms:radioItem" name="contactsSms:0:recipientSmsEachPersonSelection:radioGroup" value="0" checked="checked" /><label for="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSameAsRepresentativeSms:radioItem">代表者電話番号と同じ</label>
										</li>
										<li><input type="radio" id="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSmsPerPassenger:radioItem" name="contactsSms:0:recipientSmsEachPersonSelection:radioGroup" value="1" /><label for="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSmsPerPassenger:radioItem">電話番号を指定</label>
											<div class="indentArea js-formImputArea">
												<span id="confirmTelNumberForRepresentative:0" class="supplementTxt marginTop">先頭に0(ゼロ)がある場合、0(ゼロ)を抜いてください(一部、国・地域除く)。携帯電話番号の場合は、携帯電話を契約された国を選択ください。</span>
												<ul class="countryNumberInput js-countryWrap">
													<li><label for="contactsSms:0:recipientSmsEachPersonSelection:passengerSmsCountry" class="visuallyHidden">電話番号の国/地区選択</label><select id="contactsSms:0:recipientSmsEachPersonSelection:passengerSmsCountry" name="contactsSms:0:recipientSmsEachPersonSelection:passengerSmsCountry" class="countrySelect js-countrySelect" size="1">	<option value="" data-country-code="---">選択してください</option>
	<option value="JP">日本</option>
	<option value="-" data-country-code="---">-----------</option>
	<option value="IE">アイルランド</option>
	<option value="AZ">アゼルバイジャン</option>
	<option value="AF">アフガニスタン</option>
	<option value="US">アメリカ合衆国/カナダ</option>
	<option value="AE">アラブ首長国連邦</option>
	<option value="DZ">アルジェリア</option>
	<option value="AR">アルゼンチン</option>
	<option value="AW">アルバ</option>
	<option value="AL">アルバニア</option>
	<option value="AM">アルメニア</option>
	<option value="AG">アンティグア・バーブーダ</option>
	<option value="AD">アンドラ</option>
	<option value="YE">イエメン</option>
	<option value="GB">イギリス</option>
	<option value="IL">イスラエル</option>
	<option value="IT">イタリア</option>
	<option value="IQ">イラク</option>
	<option value="IR">イラン</option>
	<option value="IN">インド</option>
	<option value="ID">インドネシア</option>
	<option value="UG">ウガンダ</option>
	<option value="UA">ウクライナ</option>
	<option value="UZ">ウズベキスタン</option>
	<option value="UY">ウルグアイ</option>
	<option value="EC">エクアドル</option>
	<option value="EG">エジプト</option>
	<option value="EE">エストニア</option>
	<option value="ER">エリトリア</option>
	<option value="SV">エルサルバドル</option>
	<option value="AU">オーストラリア</option>
	<option value="AT">オーストリア</option>
	<option value="OM">オマーン</option>
	<option value="NL">オランダ</option>
	<option value="GH">ガーナ</option>
	<option value="GY">ガイアナ</option>
	<option value="KZ">カザフスタン</option>
	<option value="QA">カタール</option>
	<option value="CA">カナダ/アメリカ合衆国</option>
	<option value="GA">ガボン</option>
	<option value="CM">カメルーン</option>
	<option value="KR">韓国</option>
	<option value="GM">ガンビア</option>
	<option value="KH">カンボジア</option>
	<option value="GN">ギニア</option>
	<option value="GW">ギニアビサウ</option>
	<option value="CY">キプロス</option>
	<option value="CU">キューバ</option>
	<option value="GR">ギリシャ</option>
	<option value="KG">キルギス</option>
	<option value="GT">グアテマラ</option>
	<option value="GU">グアム</option>
	<option value="KW">クウェート</option>
	<option value="GD">グレナダ</option>
	<option value="HR">クロアチア</option>
	<option value="KY">ケイマン諸島</option>
	<option value="KE">ケニア</option>
	<option value="CI">コートジボワール</option>
	<option value="CR">コスタリカ</option>
	<option value="KM">コモロ</option>
	<option value="CO">コロンビア</option>
	<option value="CD">コンゴ民主共和国</option>
	<option value="SA">サウジアラビア</option>
	<option value="ZM">ザンビア</option>
	<option value="SL">シエラレオネ</option>
	<option value="JM">ジャマイカ</option>
	<option value="GE">ジョージア</option>
	<option value="SY">シリア</option>
	<option value="SG">シンガポール</option>
	<option value="ZW">ジンバブエ</option>
	<option value="CH">スイス</option>
	<option value="SE">スウェーデン</option>
	<option value="SD">スーダン</option>
	<option value="ES">スペイン</option>
	<option value="SR">スリナム</option>
	<option value="LK">スリランカ</option>
	<option value="SK">スロバキア</option>
	<option value="SI">スロベニア</option>
	<option value="SN">セネガル</option>
	<option value="RS">セルビア</option>
	<option value="KN">セントクリストファー・ネイビス</option>
	<option value="LC">セントルシア</option>
	<option value="TH">タイ</option>
	<option value="TW">台湾</option>
	<option value="TJ">タジキスタン</option>
	<option value="TZ">タンザニア</option>
	<option value="CZ">チェコ</option>
	<option value="TD">チャド</option>
	<option value="CF">中央アフリカ共和国</option>
	<option value="CN">中国</option>
	<option value="TN">チュニジア</option>
	<option value="CL">チリ</option>
	<option value="DK">デンマーク</option>
	<option value="DE">ドイツ</option>
	<option value="TG">トーゴ</option>
	<option value="DO">ドミニカ共和国</option>
	<option value="TT">トリニダード・トバゴ</option>
	<option value="TR">トルコ</option>
	<option value="NG">ナイジェリア</option>
	<option value="NI">ニカラグア</option>
	<option value="NE">ニジェール</option>
	<option value="NZ">ニュージーランド</option>
	<option value="NP">ネパール</option>
	<option value="NO">ノルウェー</option>
	<option value="HT">ハイチ</option>
	<option value="PK">パキスタン</option>
	<option value="PA">パナマ</option>
	<option value="BS">バハマ</option>
	<option value="PG">パプアニューギニア</option>
	<option value="BM">バミューダ</option>
	<option value="PW">パラオ</option>
	<option value="PY">パラグアイ</option>
	<option value="BB">バルバドス</option>
	<option value="PS">パレスチナ</option>
	<option value="HU">ハンガリー</option>
	<option value="BD">バングラデシュ</option>
	<option value="TL">東ティモール</option>
	<option value="FJ">フィジー</option>
	<option value="PH">フィリピン</option>
	<option value="FI">フィンランド</option>
	<option value="BT">ブータン</option>
	<option value="PR">プエルトリコ</option>
	<option value="BR">ブラジル</option>
	<option value="FR">フランス</option>
	<option value="PF">フランス領ポリネシア</option>
	<option value="BG">ブルガリア</option>
	<option value="BF">ブルキナファソ</option>
	<option value="BN">ブルネイ</option>
	<option value="BI">ブルンジ</option>
	<option value="VN">ベトナム</option>
	<option value="BJ">ベナン</option>
	<option value="VE">ベネズエラ</option>
	<option value="BY">ベラルーシ</option>
	<option value="PE">ペルー</option>
	<option value="BE">ベルギー</option>
	<option value="PL">ポーランド</option>
	<option value="BA">ボスニア・ヘルツェゴビナ</option>
	<option value="BW">ボツワナ</option>
	<option value="HK">香港</option>
	<option value="MH">マーシャル諸島</option>
	<option value="MO">マカオ</option>
	<option value="MG">マダガスカル</option>
	<option value="MW">マラウイ</option>
	<option value="ML">マリ</option>
	<option value="MT">マルタ</option>
	<option value="MY">マレーシア</option>
	<option value="ZA">南アフリカ</option>
	<option value="MM">ミャンマー</option>
	<option value="MX">メキシコ</option>
	<option value="MU">モーリシャス</option>
	<option value="MR">モーリタニア</option>
	<option value="MZ">モザンビーク</option>
	<option value="MC">モナコ</option>
	<option value="MV">モルディヴ</option>
	<option value="MA">モロッコ</option>
	<option value="MN">モンゴル</option>
	<option value="ME">モンテネグロ</option>
	<option value="JO">ヨルダン</option>
	<option value="LA">ラオス人民民主共和国</option>
	<option value="LV">ラトビア</option>
	<option value="LT">リトアニア</option>
	<option value="LY">リビア</option>
	<option value="LR">リベリア</option>
	<option value="RO">ルーマニア</option>
	<option value="LU">ルクセンブルク</option>
	<option value="RW">ルワンダ</option>
	<option value="LB">レバノン</option>
	<option value="RU">ロシア</option>
</select>
													</li>
													<li>
														
														<div class="numberRowWrap">
															<div class="block">
																<span class="visuallyHidden">国番号</span>
																<span class="countryCode viewInput js-countryCode"></span>
															</div>
															
															<div class="block"><label for="contactsSms:0:recipientSmsEachPersonSelection:flightStatusNotificationContactPointSmsDescription" class="visuallyHidden">電話番号</label><input id="contactsSms:0:recipientSmsEachPersonSelection:flightStatusNotificationContactPointSmsDescription" type="text" name="contactsSms:0:recipientSmsEachPersonSelection:flightStatusNotificationContactPointSmsDescription" class="telNumber" placeholder="例：9012345678" maxlength="30" aria-describedby="confirmTelNumberForRepresentative:0" />
															</div>
														</div>
													</li>
												</ul>
											</div>
										</li>
									</ul>
							</fieldset>
						</dd>
					</dl>
			</div>
	</div>
	</div>
	
	<div class="formInput articleLine">
		<h3>滞在中の連絡先(任意)
		</h3>
		<dl>
			
			<dt><label for="accommodationOne">現地連絡先1</label>
			</dt>
			<dd><input id="accommodationOne" type="text" name="accommodationOne" class="widthLarge" placeholder="例：09012345678 ABC HOTEL" maxlength="28" title="例：09012345678 ABC HOTEL" />
			</dd>
		</dl>
		<dl>
			
			<dt><label for="accommodationTwo">現地連絡先2</label>
			</dt>
			<dd><input id="accommodationTwo" type="text" name="accommodationTwo" class="widthLarge" placeholder="例：09012345678 ABC HOTEL" maxlength="28" title="例：09012345678 ABC HOTEL" />
			</dd>
		</dl>
	</div>
		</div>
		
		<p class="btnAreaSubmit btnArrowNext "><input id="next" type="submit" name="next" value="次へ" class="btnBase btnMainStream btnVerticalMain btnWidthVariable" onclick="return Asw.Dialog.getInstance('prebookConfirmDialog').toggle(event);return Asw.LoadingWindow.open(&quot;NORMAL&quot;, event)" />
		</p><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:2" value="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" autocomplete="off" />
</form><div id="cmnDynamicMessages" class="infoBoxSection">
				<dl class="infoBox importantInfo jsAccordionSwitch">
					<dt><strong>重要なご案内</strong></dt>
					<dd>
						<ul class="jsAccordionSwitchList" id="informationMessages">
								<li>個人情報の利用目的<a href="https://www.ana.co.jp/other/int/meta/0012.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="ANAプライバシーポリシー(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">ANAプライバシーポリシー<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>を必ずお読みいただき、内容に同意いただいた上で、お進みください。</li>
								<li><a href="https://www.ana.co.jp/other/int/meta/0058.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="Secure Flight Programについて(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">米国Secure Flightプログラム<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>に基づき、パスポート名、生年月日、性別などの情報は事前に米国へ通知いたします。</li>
								<li>入力したeメールアドレス、電話番号は入国に必要な情報として米国に通知される可能性があります。</li>
									<li class="jsHiddenFlg toggleContents">お名前のスペルがパスポート名と一致しているかご確認ください。異なる場合はインターネットではお取り扱いできません。<a href="https://www.ana.co.jp/other/int/meta/intbe0001.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="ANAお問い合わせ先(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">ANA<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>へ名前の変更をお申し出ください。</li>
									<li class="jsHiddenFlg toggleContents">二重国籍のお客様は、ご搭乗時に使用されるパスポートのお名前でご予約ください。<br />パスポートごとにお名前が異なる場合は、お客様ご自身で入国管理事務所・外務省・大使館などで各種条件をご確認のうえ、使用されるパスポートを決めてください。<br />なお、入国可否については国によって異なるため、お客様ご自身で各入国管理事務所などでご確認ください。</li>
									<li class="jsHiddenFlg toggleContents">旅行開始日時点で12歳以上の場合は大人、2歳以上12歳未満の場合は小児、2歳未満の場合は幼児の扱いとなります。なお、小児は大人と同マイル数が必要です。</li>
									<li class="jsHiddenFlg toggleContents">運航に関する情報をお客様へ通知するため、提携航空会社にもお客様のメールアドレスが提供されます。あらかじめご了承ください。</li>
									<li class="jsHiddenFlg toggleContents"><dl><dt>携帯電話へのメール送信をご希望のお客様へ</dt><dd>ドメイン指定受信をご利用の場合は、ana.co.jpとamadeus.comからのメールが受信できるようあらかじめ設定をお願いいたします。</dd></dl></li>
									<li class="jsHiddenFlg toggleContents">SMSはスマートフォンの番号に配信されます。一部の国ではSMSでのご案内をご利用いただけません。詳細は<a href="https://www.ana.co.jp/other/int/meta/0737.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="ショートメッセージ(SMS)でのご案内対象外の国について(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">ショートメッセージ(SMS)でのご案内対象外の国について<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>をご覧ください。</li>
									<li class="jsHiddenFlg toggleContents">ログインされている会員ご本人が搭乗しない場合は、再度フライト検索画面より「ログインされている会員ご本人は搭乗しない」のチェックボックスを選択してお手続きください。</li>
						</ul>
							<a href="#" class="toggleSwitch jsTriggerSwitch" role="button" aria-controls="informationMessages" aria-expanded="false"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/toggle_open.png?7bec38a" alt="開閉ボタン" height="25" width="25" class="jsRollOver" />
							</a>
					</dd>
				</dl></div>
	<!--=========== /main =============-->
	</div>

	<!--=========== Script ============-->
	<script type="text/javascript">
	$(window).on('load', function() {
		// アコーディオンのメソッドと設定
		Asw.AccordionInfo('.jsAccordionSwitch', {
			'triggerSwitch': '.jsAccordionSwitch .jsTriggerSwitch',
			'listClass': '.jsAccordionSwitchList',
			'visibilityHidden': 'toggleContents'
		});
	});
	</script>
	<!--=========== Script ============-->
	<script type="text/javascript">
		//プラットフォーム判別（サマリーエリア追従用として）
		var Asw = Asw || {};
	
	  	Asw.ClientInfo = Asw.ClientInfo  || {};
	  	Asw.ClientInfo.DeviceType = Asw.ClientInfo.DeviceType || {};
	
	  	Asw.ClientInfo.DeviceType.PC = 'PC';
	  	Asw.ClientInfo.DeviceType.SP = 'SP';
	  	Asw.ClientInfo.DeviceType.TC = 'TC';
	
	  	Asw.ClientInfo.deviceType = Asw.ClientInfo.DeviceType.PC;
	
	 	if((navigator.userAgent.indexOf('Android') > 0 && navigator.userAgent.indexOf('Mobile') == -1) || navigator.userAgent.indexOf('iPad') > 0){
	    	Asw.ClientInfo.deviceType = Asw.ClientInfo.DeviceType.TC;
	  	}
	</script>
	<!--=========== /Script ============--><div id="summaryArea">
			<div class="flightSummaryArea">
						<div class="flightSummarySection">
							<h2>区間 1
							</h2>
							<p class="flightDate"><em>6</em>月<em class="hasLeft">24</em>日（火）
							</p>
							<div class="flightSummaryRoute">
									<div class="flightSummaryRouteSection">
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation1_1">
													<span>東京(成田)
													</span>
												</p>
												<p class="time">
													<span>17:00
													</span>
												</p>
											</div>
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation1_1">
													<span>シカゴ(ORD)
													</span>
												</p>
												<p class="time">
													<span>14:55
													</span>
												</p>
											</div>
										<p class="flightNumberClass">
												<span>NH012</span><span><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/flight_ana_summaryarea.png?717d3c0" alt="ANA運航便" height="11" width="41" /></span>
												<br />特典ビジネス
										</p>
									</div>
							</div>
						</div>
						<div class="flightSummarySection">
							<h2 class="midstream">区間 2
							</h2>
							<p class="flightDate"><em>7</em>月<em class="hasLeft">23</em>日（水）
							</p>
							<div class="flightSummaryRoute">
									<div class="flightSummaryRouteSection">
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation2_1">
													<span>ホノルル(オアフ島)
													</span>
												</p>
												<p class="time">
													<span>11:35
													</span>
												</p>
											</div>
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation2_1">
													<span>東京(成田)
													</span>
												</p>
												<p class="time">
													<span>14:50
																	<span>翌日
																	</span>
													</span>
												</p>
											</div>
										<p class="flightNumberClass">
												<span>NH183</span><span><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/flight_ana_summaryarea.png?717d3c0" alt="ANA運航便" height="11" width="41" /></span>
												<br />特典エコノミー
										</p>
									</div>
							</div>
						</div>
					<div class="total">
						<p class="passenger">大人 x1
						</p>
						<div class="totalArea">
					  		<dl class="mealAmountWrap">
									<dt>有料機内食サービス
									</dt>
									<dd><em class="mealAmount">0</em><span class="currencyCode">円</span>
									</dd>
							</dl>
									<div>
										<div class="mileageAndTotal">
												<dl class="requiredMileage">
													<dt>必要マイル
													</dt>
													<dd><em class="requiredMileage">74,000</em><span class="currencyCode">マイル</span>
													</dd>
												</dl>
												<dl>
													<dt>総額
													</dt>
													<dd><em class="awardTotalPayment">68,130</em>円
													</dd>
												</dl>
												<p class="includeFareAndFuelCost">※各種税金、燃油特別付加運賃等を含みます。
												</p>
										</div>
									</div>
						</div>
					</div>
				
			</div></div>
		<!--===== /cmnWrapper =====-->
		</div>
			
		<span id="message" class="visuallyHidden" role="alert" aria-live="assertive"></span>
		<span id="politeMessage" class="visuallyHidden" role="log" aria-live="polite"></span><script type="text/javascript">
		var Asw = Asw || {};
		Asw.BaseOutput = {"sessionID":"homH0aqtCl","language":"ja","siteCatalystLanguageCode":"J","operationDate":"20250619","operationDateTime":"20250619195257","office":{"officeCode":"TYONH08ZZ","country2letterCode":"JP"},"bookingType":"A","pageID":"A04_P01","siteCatalystPageName":"INT_BE_AWARD_J_A04特典予約_P01旅客情報入力（必須情報）","device":{"deviceType":"PC"}};
	</script><div id="promotionArea">
		<!--===== promotionArea =====-->
		<!--===== /promotionArea =====--></div>
		<!--=========== /cmnContainer ============--></div><div id="cmnBaloonArea" aria-live="polite"></div><div id="cmnPopupArea" aria-live="polite"></div>

	

	<div id="cmnFooterWrapper">
		<!--=========== .cmnFooterWrapper ============-->
		<div id="cmnFooter">
			<!--===== cmnFooter =====-->
			<ul>
				<li><a href="https://www.ana.co.jp/other/int/meta/0012.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">プライバシーポリシー<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
					<li><a href="https://www.ana.co.jp/other/int/meta/0670.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">利用者情報の外部送信について<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
					</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/0052.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">サイト利用規約<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/be0238.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">推奨環境<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/0133.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">サイトマップ<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/0732.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">運送約款<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
					<li class="lastChild"><a href="https://www.ana.co.jp/other/int/meta/0556.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">ウェブアクセシビリティについて<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
					</li>
			</ul>
			<p class="blankText" aria-hidden="true"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="" height="10" width="10" />外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。</p>
			<p class="copyright"><span lang="en" xml:lang="en">Copyright&#169;ANA</span>
			</p>
			<p class="starAllianceLogo"><a href="http://www.staralliance.com/ja/" target="_blank"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/star_alliance_logo.png?0fe73b5" alt="A STAR ALLIANCE MEMBER" height="18" width="175" /><span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
			</p>
			<!--===== cmnFooter =====-->
		</div>
		<!--=========== /cmnFooterWrapper ============-->
	</div>

		
		<div id="cmnLoadingForAjax"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/bg_modalWindow.png?717d3c0" alt="" height="0" width="0" />
			<div class="loadingArea">
				<p class="loadingImage"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/loading.gif?717d3c0" alt="リクエストを処理しています...しばらくお待ちください。" height="33" width="33" />
				</p>
			</div>
		</div>
		
		
		<div id="popupAltSentence" style="display: none" aria-hidden="true">別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。</div>
	<div id="cmnSiteCatalyst" aria-hidden="true"><div id="cmnSiteCatalystParamArea"><script type="text/javascript">
				// ▼SiteCatalyst
				// SiteCatalyst code version: H.2.
				// Copyright 1997-2005 Omniture, Inc. More info available at http://www.omniture.com
				SiteCatalystReportSuites   = "INT";
				SiteCatalystCharSet        = "UTF-8";
				SiteCatalystChannel        = "BE_AWARD_J";
				SiteCatalystPageName       = "INT_BE_AWARD_J_A04\u7279\u5178\u4E88\u7D04_P01\u65C5\u5BA2\u60C5\u5831\u5165\u529B\uFF08\u5FC5\u9808\u60C5\u5831\uFF09";
				SiteCatalystERR            = "";
				SiteCatalystEventNum       = "";
				SiteCatalystPurchase       = "FALSE";
				SiteCatalystProductPrice   = "";
				SiteCatalystCurrencyCode   = "";
				SiteCatalystSeatAvailabilityINT   = "";
				SiteCatalystDepartDate     = "";
				SiteCatalystDepart         = "";
				SiteCatalystArrive         = "";
				SiteCatalystFlightNo       = "";
				SiteCatalystClass          = "";
				SiteCatalystFare           = "";
				SiteCatalystPurchaseID     = "";
				
				SiteCatalystLocater        = "";
				SiteCatalystPayment        = "";
				SiteCatalystIncome         = "";
				
				SiteCatalystPax            = "";
				SiteCatalystManipulateDay  = "";
				SiteCatalystCompartmentClass   = "";
				SiteCatalystOptionInfo     = "";
				SiteCatalystOutboundUpsellPrice   = "";
				SiteCatalystInboundUpsellPrice    = "";
				
				SiteCatalystSearchInput    = "";
				SiteCatalystSegment        = "";
				SiteCatalystOfficeCode     = "";
				SiteCatalystPassengerCount = "";
				SiteCatalystPassenger      = "";
				SiteCatalystAncillaryService = "";
				// End SiteCatalyst code version: H.2.
				// ▲SiteCatalyst
			</script></div><script type="text/javascript"><!-- if(navigator.appVersion.indexOf('MSIE')>=0)document.write(unescape('%3C')+'\!-'+'-') // --></script>
		<!--/DO NOT REMOVE/-->
		

		
		<!-- Google Tag Manager -->
		<script type="text/javascript" charset="Shift_JIS" src="https://www.ana.co.jp/common/js/GTMINTBE.js"></script>
		<!-- End Google Tag Manager -->
		<!-- Yahoo Tag Manager -->
		<script type="text/javascript" charset="Shift_JIS" src="https://www.ana.co.jp/common/js/YTMINTBE.js"></script>
		<!-- End Yahoo Tag Manager -->
		

	</div>

	

	<div class="mboxDefault"></div><script type="text/javascript">mboxCreate("ASW_common_A04_P01");</script>
			<script type="text/javascript" src="https://www.ana.co.jp/common/js/tealium/tealium.js"></script>
			<script type="text/javascript" src="//cdn.evgnet.com/beacon/allnipponairways/nonamcprd/scripts/evergage.min.js"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/javax.faces.resource/jsf.js.xhtml?ln=javax.faces"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/jquery.mousewheel.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/jquery.jscrollpane.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/jquery-ui-1.10.4.custom.min.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/jquery.ui.touch-punch.min.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-common.js?beb6a50"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-common-pc.js?fd88d7d"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-format.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/mask.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/loading.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-loadingwindow-pc.js?9e233c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-loadingwindow.js?51db600"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/dialog-pc.js?25d36a4"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-dialog-sequencer.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-event.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-date-select.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-calendar-pc.js?9b86b8a"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-calendar.js?db624c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/heightLine.js?0e7a1ce"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-summaryarea.js?b21c6d7"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-fingerprints.js?99001fc"></script><script type="text/javascript">
			Asw.init("https://aswbe-i.ana.co.jp/rei21c/international_asw", "ja");
			Asw.getCalendarHolidayList = function() {return [20250721,20250811,20250915,20250923,20251013,20251103,20251123,20251124,20260101,20260112,20260211,20260223,20260320,20260429,20260503,20260504,20260505,20260506,20260720,20260811,20260921,20260922,20260923,20261012,20261103,20261123,20270101,20270111,20270211,20270223,20270321,20270322,20270429,20270503,20270504,20270505,20270719,20270811,20270920,20270923,20271011,20271103,20271123,20280101,20280110,20280211,20280223,20280320,20280429,20280503,20280504,20280505,20280717,20280811,20280918,20280922,20281009,20281103,20281123,20290101,20290108,20290211,20290212,20290223,20290320,20290429,20290430,20290503,20290504,20290505,20290716,20290811,20290917,20290923,20290924,20291008,20291103,20291123];}
			Asw.ClientInfo.deviceType = "PC";
			Asw.ClientInfo.mobileDeviceType = "UNKNOWN";
			Asw.ClientInfo.osVersion = "";
			Asw.Format.init("https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/{1}/{2}/{3}?{0}");
			Asw.overwriteTargetAttributeValue("false");
		</script><script type="text/javascript">
		
		var Asw = Asw || {};
		Asw.ContactInput = Asw.ContactInput || {};
		Asw.ContactInput.Const = {
			/** data-属性名「data-type」 @constant @type {String} @default 'data-type' */
			DATA_ATTRIBUTE_NAME_TYPE: 'data-type',
			/** data-属性名「data-sms-possible」 @constant @type {String} @default 'data-sms-possible' */
			DATA_ATTRIBUTE_NAME_SMS_POSSIBLE: 'data-sms-possible',
			/** data-属性名「data-country-code」 @constant @type {String} @default 'data-country-code' */
			DATA_ATTRIBUTE_NAME_COUNTRY_CODE: 'data-country-code',
			/** data-type属性値「sms」 @constant @type {String} @default 'sms' */
			DATA_TYPE_VALUE_SMS: 'sms'
		};

		
		Asw.ContactInput.countryInfoMap = {"IS":{"internationalCallingCode":"+354","smsPossible":false},"IE":{"internationalCallingCode":"+353","smsPossible":true},"AZ":{"internationalCallingCode":"+994","smsPossible":true},"AF":{"internationalCallingCode":"+93","smsPossible":true},"US":{"internationalCallingCode":"+1","smsPossible":true},"AE":{"internationalCallingCode":"+971","smsPossible":true},"DZ":{"internationalCallingCode":"+213","smsPossible":true},"AR":{"internationalCallingCode":"+54","smsPossible":true},"AW":{"internationalCallingCode":"+297","smsPossible":true},"AL":{"internationalCallingCode":"+355","smsPossible":true},"AM":{"internationalCallingCode":"+374","smsPossible":true},"AO":{"internationalCallingCode":"+244","smsPossible":false},"AG":{"internationalCallingCode":"+1","smsPossible":true},"AD":{"internationalCallingCode":"+376","smsPossible":true},"YE":{"internationalCallingCode":"+967","smsPossible":true},"GB":{"internationalCallingCode":"+44","smsPossible":true},"IL":{"internationalCallingCode":"+972","smsPossible":true},"IT":{"internationalCallingCode":"+39","smsPossible":true},"IQ":{"internationalCallingCode":"+964","smsPossible":true},"IR":{"internationalCallingCode":"+98","smsPossible":true},"IN":{"internationalCallingCode":"+91","smsPossible":true},"ID":{"internationalCallingCode":"+62","smsPossible":true},"UG":{"internationalCallingCode":"+256","smsPossible":true},"UA":{"internationalCallingCode":"+380","smsPossible":true},"UZ":{"internationalCallingCode":"+998","smsPossible":true},"UY":{"internationalCallingCode":"+598","smsPossible":true},"EC":{"internationalCallingCode":"+593","smsPossible":true},"EG":{"internationalCallingCode":"+20","smsPossible":true},"EE":{"internationalCallingCode":"+372","smsPossible":true},"SZ":{"internationalCallingCode":"+268","smsPossible":false},"ET":{"internationalCallingCode":"+251","smsPossible":false},"ER":{"internationalCallingCode":"+291","smsPossible":true},"SV":{"internationalCallingCode":"+503","smsPossible":true},"AU":{"internationalCallingCode":"+61","smsPossible":true},"AT":{"internationalCallingCode":"+43","smsPossible":true},"OM":{"internationalCallingCode":"+968","smsPossible":true},"NL":{"internationalCallingCode":"+31","smsPossible":true},"GH":{"internationalCallingCode":"+233","smsPossible":true},"CV":{"internationalCallingCode":"+238","smsPossible":false},"GY":{"internationalCallingCode":"+592","smsPossible":true},"KZ":{"internationalCallingCode":"+7","smsPossible":true},"QA":{"internationalCallingCode":"+974","smsPossible":true},"CA":{"internationalCallingCode":"+1","smsPossible":true},"GA":{"internationalCallingCode":"+241","smsPossible":true},"CM":{"internationalCallingCode":"+237","smsPossible":true},"KR":{"internationalCallingCode":"+82","smsPossible":true},"KH":{"internationalCallingCode":"+855","smsPossible":true},"GM":{"internationalCallingCode":"+220","smsPossible":true},"KP":{"internationalCallingCode":"+850","smsPossible":false},"MK":{"internationalCallingCode":"+389","smsPossible":false},"GN":{"internationalCallingCode":"+224","smsPossible":true},"GW":{"internationalCallingCode":"+245","smsPossible":true},"CY":{"internationalCallingCode":"+357","smsPossible":true},"CU":{"internationalCallingCode":"+53","smsPossible":true},"GR":{"internationalCallingCode":"+30","smsPossible":true},"KI":{"internationalCallingCode":"+686","smsPossible":false},"KG":{"internationalCallingCode":"+996","smsPossible":true},"GT":{"internationalCallingCode":"+502","smsPossible":true},"GU":{"internationalCallingCode":"+1","smsPossible":true},"KW":{"internationalCallingCode":"+965","smsPossible":true},"KY":{"internationalCallingCode":"+1","smsPossible":true},"GD":{"internationalCallingCode":"+1","smsPossible":true},"HR":{"internationalCallingCode":"+385","smsPossible":true},"KE":{"internationalCallingCode":"+254","smsPossible":true},"CI":{"internationalCallingCode":"+225","smsPossible":true},"CR":{"internationalCallingCode":"+506","smsPossible":true},"SA":{"internationalCallingCode":"+966","smsPossible":true},"KM":{"internationalCallingCode":"+269","smsPossible":true},"CO":{"internationalCallingCode":"+57","smsPossible":true},"CG":{"internationalCallingCode":"+242","smsPossible":false},"CD":{"internationalCallingCode":"+243","smsPossible":true},"ZM":{"internationalCallingCode":"+260","smsPossible":true},"SM":{"internationalCallingCode":"+378","smsPossible":false},"SL":{"internationalCallingCode":"+232","smsPossible":true},"DJ":{"internationalCallingCode":"+253","smsPossible":false},"JM":{"internationalCallingCode":"+1","smsPossible":true},"CH":{"internationalCallingCode":"+41","smsPossible":true},"GE":{"internationalCallingCode":"+995","smsPossible":true},"SE":{"internationalCallingCode":"+46","smsPossible":true},"SY":{"internationalCallingCode":"+963","smsPossible":true},"ES":{"internationalCallingCode":"+34","smsPossible":true},"SG":{"internationalCallingCode":"+65","smsPossible":true},"LK":{"internationalCallingCode":"+94","smsPossible":true},"SK":{"internationalCallingCode":"+421","smsPossible":true},"SI":{"internationalCallingCode":"+386","smsPossible":true},"ZW":{"internationalCallingCode":"+263","smsPossible":true},"SD":{"internationalCallingCode":"+249","smsPossible":true},"KN":{"internationalCallingCode":"+1","smsPossible":true},"SR":{"internationalCallingCode":"+597","smsPossible":true},"LC":{"internationalCallingCode":"+1","smsPossible":true},"SC":{"internationalCallingCode":"+248","smsPossible":false},"SN":{"internationalCallingCode":"+221","smsPossible":true},"SB":{"internationalCallingCode":"+677","smsPossible":false},"RS":{"internationalCallingCode":"+381","smsPossible":true},"TH":{"internationalCallingCode":"+66","smsPossible":true},"TW":{"internationalCallingCode":"+886","smsPossible":true},"TJ":{"internationalCallingCode":"+992","smsPossible":true},"TZ":{"internationalCallingCode":"+255","smsPossible":true},"CZ":{"internationalCallingCode":"+420","smsPossible":true},"TD":{"internationalCallingCode":"+235","smsPossible":true},"CF":{"internationalCallingCode":"+236","smsPossible":true},"TN":{"internationalCallingCode":"+216","smsPossible":true},"CL":{"internationalCallingCode":"+56","smsPossible":true},"TV":{"internationalCallingCode":"+688","smsPossible":false},"DK":{"internationalCallingCode":"+45","smsPossible":true},"DE":{"internationalCallingCode":"+49","smsPossible":true},"TG":{"internationalCallingCode":"+228","smsPossible":true},"DO":{"internationalCallingCode":"+1","smsPossible":true},"DM":{"internationalCallingCode":"+1","smsPossible":false},"TT":{"internationalCallingCode":"+1","smsPossible":true},"TM":{"internationalCallingCode":"+993","smsPossible":false},"TR":{"internationalCallingCode":"+90","smsPossible":true},"TO":{"internationalCallingCode":"+676","smsPossible":false},"NG":{"internationalCallingCode":"+234","smsPossible":true},"NR":{"internationalCallingCode":"+674","smsPossible":false},"NA":{"internationalCallingCode":"+264","smsPossible":false},"NI":{"internationalCallingCode":"+505","smsPossible":true},"BH":{"internationalCallingCode":"+973","smsPossible":false},"NE":{"internationalCallingCode":"+227","smsPossible":true},"PK":{"internationalCallingCode":"+92","smsPossible":true},"JP":{"internationalCallingCode":"+81","smsPossible":true},"BS":{"internationalCallingCode":"+1","smsPossible":true},"NZ":{"internationalCallingCode":"+64","smsPossible":true},"BM":{"internationalCallingCode":"+1","smsPossible":true},"NP":{"internationalCallingCode":"+977","smsPossible":true},"BB":{"internationalCallingCode":"+246","smsPossible":true},"NO":{"internationalCallingCode":"+47","smsPossible":true},"HT":{"internationalCallingCode":"+509","smsPossible":true},"BD":{"internationalCallingCode":"+880","smsPossible":true},"VA":{"internationalCallingCode":"+39","smsPossible":false},"PA":{"internationalCallingCode":"+507","smsPossible":true},"VU":{"internationalCallingCode":"+678","smsPossible":false},"PG":{"internationalCallingCode":"+675","smsPossible":true},"PW":{"internationalCallingCode":"+680","smsPossible":true},"PY":{"internationalCallingCode":"+595","smsPossible":true},"PS":{"internationalCallingCode":"+970","smsPossible":true},"BR":{"internationalCallingCode":"+55","smsPossible":true},"HU":{"internationalCallingCode":"+36","smsPossible":true},"TL":{"internationalCallingCode":"+670","smsPossible":true},"BG":{"internationalCallingCode":"+359","smsPossible":true},"BF":{"internationalCallingCode":"+226","smsPossible":true},"BN":{"internationalCallingCode":"+673","smsPossible":true},"FJ":{"internationalCallingCode":"+679","smsPossible":true},"PH":{"internationalCallingCode":"+63","smsPossible":true},"FI":{"internationalCallingCode":"+358","smsPossible":true},"BT":{"internationalCallingCode":"+975","smsPossible":true},"BY":{"internationalCallingCode":"+375","smsPossible":true},"BZ":{"internationalCallingCode":"+501","smsPossible":false},"PR":{"internationalCallingCode":"+1","smsPossible":true},"BE":{"internationalCallingCode":"+32","smsPossible":true},"BA":{"internationalCallingCode":"+387","smsPossible":true},"FR":{"internationalCallingCode":"+33","smsPossible":true},"BO":{"internationalCallingCode":"+591","smsPossible":false},"PF":{"internationalCallingCode":"+689","smsPossible":true},"BI":{"internationalCallingCode":"+257","smsPossible":true},"VN":{"internationalCallingCode":"+84","smsPossible":true},"BJ":{"internationalCallingCode":"+229","smsPossible":true},"VE":{"internationalCallingCode":"+58","smsPossible":true},"PE":{"internationalCallingCode":"+51","smsPossible":true},"PL":{"internationalCallingCode":"+48","smsPossible":true},"BW":{"internationalCallingCode":"+267","smsPossible":true},"PT":{"internationalCallingCode":"+351","smsPossible":false},"HK":{"internationalCallingCode":"+852","smsPossible":true},"HN":{"internationalCallingCode":"+504","smsPossible":false},"MH":{"internationalCallingCode":"+692","smsPossible":true},"MO":{"internationalCallingCode":"+853","smsPossible":true},"MG":{"internationalCallingCode":"+261","smsPossible":true},"MW":{"internationalCallingCode":"+265","smsPossible":true},"ML":{"internationalCallingCode":"+223","smsPossible":true},"MT":{"internationalCallingCode":"+356","smsPossible":true},"MY":{"internationalCallingCode":"+60","smsPossible":true},"FM":{"internationalCallingCode":"+691","smsPossible":false},"MM":{"internationalCallingCode":"+95","smsPossible":true},"MX":{"internationalCallingCode":"+52","smsPossible":true},"MU":{"internationalCallingCode":"+230","smsPossible":true},"MR":{"internationalCallingCode":"+222","smsPossible":true},"MZ":{"internationalCallingCode":"+258","smsPossible":true},"MC":{"internationalCallingCode":"+377","smsPossible":true},"MV":{"internationalCallingCode":"+960","smsPossible":true},"MD":{"internationalCallingCode":"+373","smsPossible":false},"RO":{"internationalCallingCode":"+40","smsPossible":true},"MA":{"internationalCallingCode":"+212","smsPossible":true},"MN":{"internationalCallingCode":"+976","smsPossible":true},"ME":{"internationalCallingCode":"+382","smsPossible":true},"JO":{"internationalCallingCode":"+962","smsPossible":true},"LA":{"internationalCallingCode":"+856","smsPossible":true},"RU":{"internationalCallingCode":"+7","smsPossible":true},"LV":{"internationalCallingCode":"+371","smsPossible":true},"LT":{"internationalCallingCode":"+370","smsPossible":true},"LY":{"internationalCallingCode":"+218","smsPossible":true},"GQ":{"internationalCallingCode":"+240","smsPossible":false},"LI":{"internationalCallingCode":"+423","smsPossible":false},"LR":{"internationalCallingCode":"+231","smsPossible":true},"CN":{"internationalCallingCode":"+86","smsPossible":true},"LU":{"internationalCallingCode":"+352","smsPossible":true},"ZA":{"internationalCallingCode":"+27","smsPossible":true},"RW":{"internationalCallingCode":"+250","smsPossible":true},"LS":{"internationalCallingCode":"+266","smsPossible":false},"LB":{"internationalCallingCode":"+961","smsPossible":true}};

		$(function () {
			contactInfoInit();
		});
		
		function contactInfoInit() {
			
			
			Asw.get('representativeTelType').find('option[value="M1"]').attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_TYPE,
					Asw.ContactInput.Const.DATA_TYPE_VALUE_SMS);
			Asw.get('representativeTelCountry').children('option').each(function(i,option){
				var countryInfo = Asw.ContactInput.countryInfoMap[option.value];
				if (countryInfo === void 0) {
					
					return true;
				}
				$(option).attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_SMS_POSSIBLE, countryInfo.smsPossible);
				$(option).attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_COUNTRY_CODE, countryInfo.internationalCallingCode);
			});
			
			var smsCountrySelectSelector = 'select.js-countrySelect:not(#representativeTelCountry)'
			$(smsCountrySelectSelector).eq(0).children('option').each(function(i,option){
				var countryInfo = Asw.ContactInput.countryInfoMap[option.value];
				if (countryInfo === void 0) {
					
					return true;
				}
				$(smsCountrySelectSelector + ' option:nth-child(' + (i+1) + ')').attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_COUNTRY_CODE,
						countryInfo.internationalCallingCode);
			});
			
			var emergencyCountrySelectSelector = 'select.passengerEmergencyCountry.js-countrySelect:not(#representativeTelCountry)'
			$(emergencyCountrySelectSelector).eq(0).children('option').each(function(i,option){
				var countryInfo = Asw.ContactInput.countryInfoMap[option.value];
				if (countryInfo === void 0) {
					
					return true;
				}
				$(emergencyCountrySelectSelector + ' option:nth-child(' + (i+1) + ')').attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_COUNTRY_CODE,
						countryInfo.internationalCallingCode);
			});
		}
	</script><script type="text/javascript">
		var isInit = true;
		function changeTelType(_this, telNoInputId) {
			var $telNoInput = Asw.get(telNoInputId);
			var $telNoKind = Asw.get("representativeTelType");
			var telNo = Asw.getValueComparedToPlaceholder(telNoInputId);
			var telNoCountry = Asw.get("representativeTelCountry").val();

			if(true && !isInit) {
				if(telInputCheck(telNo, telNoCountry)) {
					if (_this.value == "M1") {
						telNo = exceptDomesticPrefix("09042504176", telNoCountry);
					} else if (_this.value == "B1") {
						telNo = exceptDomesticPrefix("03-5405-2172", telNoCountry);
					} else {
						telNo = exceptDomesticPrefix("090-4250-4176", telNoCountry);
					}
					$telNoInput.val(telNo);
				}
			}
			isInit = false;
		}
		
		function telInputCheck(telNo, telNoCountry) {
	        checkResult = false;
	        
	        var amcHndyTelNo = exceptDomesticPrefix("09042504176", telNoCountry);
	        var amcCmpTelNo = exceptDomesticPrefix("03-5405-2172", telNoCountry);
	        var amcTelNo = exceptDomesticPrefix("090-4250-4176", telNoCountry);
	
	        if(telNo == "" || telNo == amcHndyTelNo || telNo == amcCmpTelNo || telNo == amcTelNo) {
	        	checkResult = true;
	        }
	        return checkResult;
    	}
    	
    	function exceptDomesticPrefix(telNo, telNoCountry) {
    		if(telNoCountry == 'JP') {
    			if(telNo.substr(0, 1) === '0') {
    				return telNo.slice(1);
    			}
    		}
    		return telNo;
    	}
	</script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-customerinfomationform.js?6e91953"></script><script type="text/javascript">
		$(document).ready(function(){
				Asw.enableTextWhenCheckBox('.cascadedSwitch');
				Asw.notPasteInput('.jsNotPasteInput');
			$(function(){
				Asw.alignHeightToParentTdElement( '.passengerInfoInputArea' );
				
					Asw.alignHeightToParentTdElement( '.passengerInfoInputText' );
				
				
				$('select.js-countrySelect').trigger('change');
			});
		});
	</script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-session-keeper.js?6ec0730"></script><script type="text/javascript">
			$(document).ready(function() {
				Asw.SessionKeeper.initAndStart("sessionKeeperContainer", "520", "570", "PC");
			});
			</script><script type="text/javascript">
		Asw.addAjaxCallback(function(data) {
			// アコーディオンのメソッドと設定 
			if($('.jsAccordionSwitch .jsTriggerSwitch').length > 0){
				var events = $._data($('.jsAccordionSwitch .jsTriggerSwitch').get(0), "events");
				if(events === undefined || events.click === undefined) {
					Asw.AccordionInfo('.jsAccordionSwitch', {
						'triggerSwitch': '.jsAccordionSwitch .jsTriggerSwitch',
						'listClass': '.jsAccordionSwitchList',
						'visibilityHidden': 'toggleContents'
					});
				}
			}
		});
	</script><script type="text/javascript">
	Asw.Price = Asw.Price || {};
	// 整数の区切り文字
	Asw.Price.separateString = ',';
	
	// 整数の区切り桁数
	Asw.Price.separateDigitNumber = 3;
	
	// 小数点
	Asw.Price.decimalPointString = '.';
	
	// 小数点以降の有効桁数
	Asw.Price.significantDigit = 0;
	
	// 表示金額の単位
	Asw.Price.currencyUnit = '円';
	</script><script type="text/javascript">
			$(document).ready(function() {
				$('#preLoadingArea').css('visibility', 'hidden');
			});
		</script><noscript><img src="https://aswbe-i.ana.co.jp/akam/13/pixel_5e9222ad?a=dD1jOTY2NjkzYjAzZGE1NjIyNzA1YmUwM2EzNzliY2IwZTg2YWQ1MTIzJmpzPW9mZg==" style="visibility: hidden; position: absolute; left: -999px; top: -999px;" /></noscript><script type="text/javascript"  src="/RcS9yzCRgrNnM/gY/v0Wf9fQwtpdQ/7NE3X2ruOE2SL9YE/HyNpQmYB/RWt/XOHcnHAU"></script></body>
</html>