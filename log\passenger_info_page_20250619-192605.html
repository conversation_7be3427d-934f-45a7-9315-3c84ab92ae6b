<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="ja-<PERSON>" xml:lang="ja-<PERSON>">

	
	<!-- skey = 2025/06/19 20:26:05 rei22a SvWH8B2G65 dljdmx+860  --><head id="j_idt53">
    <base href="about:blank">
    <style>
        /* Hide elements that might try to load external resources, as a fallback. */
        img, svg, video, iframe, link[rel="stylesheet"], script { display: none !important; }
    </style>

		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
			<meta http-equiv="X-UA-Compatible" content="IE=EmulateIE9; IE=EmulateIE10" />
			<meta http-equiv="X-UA-Compatible" content="IE=7" />
			<meta http-equiv="X-UA-Compatible" content="IE=edge" />
		<meta name="description" content="" />
		<meta name="keywords" content="" />
		<meta name="format-detection" content="telephone=no" />
		<script type="text/javascript" src="/international_asw/dynatrace/ruxitagentjs_ICANVfhqrux_10273230920145353.js" data-dtconfig="rid=RID_-1716681351|rpid=1803778597|domain=aswbe-i.ana.co.jp|reportUrl=https://bf90419wvr.bf.dynatrace.com/bf|app=78bf0b58acf6ed13|cors=1|owasp=1|featureHash=ICANVfhqrux|xb=www^bs.google-analytics^bs.com^pana^bs.demdex^bs.net^pcollect^bs.tealiumiq^bs.com^pallnipponairways^bs.australia-3^bs.evergage^bs.com^pallnipponairwayscolt^bs.tt^bs.omtrdc^bs.net|rdnt=1|uxrgce=1|cuc=k8vpgh61|mel=100000|expw=1|dpvc=1|md=mdcc1=bAsw.PnrOutput.recordLocator,mdcc2=bAsw.PnrOutput.paymentDetailInfoList[0].fopCode,mdcc3=bAsw.PnrOutput.ticketedFlag,mdcc4=bAsw.PnrOutput.nhPnrFlag,mdcc5=bAsw.PnrOutput.itinerarySummary.departureDateYyyymmdd,mdcc6=bAsw.SearchCriteriaOutput.cffCodeInput,mdcc7=a#main ^rb div.section.sectionNoFlow ^rb table ^rb tbody ^rb tr ^rb td.flightNumber.lastCol ^rb span ^rb span:nth-child(2),mdcc8=cunique|lastModification=1750184416409|tp=500,50,0|agentUri=/international_asw/dynatrace/ruxitagentjs_ICANVfhqrux_10273230920145353.js"></script><link rel="shortcut icon" href="https://www.ana.co.jp/favicon.ico" />
		<title>お客様情報入力 | ANA</title>
		<noscript><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/css/asw_noscript_pc.css?99001fc" />
		</noscript>

		<!--  [ Adobe Target ] -->
		<script type="text/javascript" src="https://www.ana.co.jp/behavior/adobetarget/target_intbe.js"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/script/jquery-1.8.3.min.js?717d3c0"></script><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/ja/css/asw_initialize.css?99001fc" /><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/css/asw_screen_pc.css?99001fc" /><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/css/asw_common_pc.css?99001fc" /><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/css/asw_forminput_pc.css?99001fc" /><script type="text/javascript">
		var Asw = Asw || {};
		Asw.GlobalWord = Asw.GlobalWord || {};
		
		Asw.GlobalWord.toolTipCloseIconAltText = "閉じる";
		
		</script><script >bazadebezolkohpepadr="2045341933"</script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/akam/13/79e970a6"  defer></script></head><body>
		
		<noscript>当サイトをご利用になるためには、JavaScript対応のブラウザが必要です。設定でJavaScriptを有効にしてください。有効にできない場合はANA電話窓口へお問い合わせください。
		</noscript>
		<div id="preLoadingArea"></div>
	
	<div id="cmnHeader" class="platinum">
		<!--===== cmnHeader =====-->
		<div class="headerWrapper">
			<div class="header">
				<p class="logo"><a href="https://www.ana.co.jp/asw/AMCTopServlet?type=a" tabindex="-1" aria-hidden="true"><img src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/image/ana_logo_navy.png?717d3c0" alt="ANA Inspiration of JAPAN。リンク先はマイレージクラブトップページです。" height="28" width="274" /></a>
				</p>
					<ul class="headerMenu">
						<li><a href="https://www.ana.co.jp/asw/AMCTopServlet?type=a">ANAマイレージクラブトップページ</a>
						</li>
						<li><a href="https://www.ana.co.jp/other/int/meta/0005.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">お問い合わせ<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
						</li>
							<li class="btnLogoutArea"><a href="https://aswbe-i.ana.co.jp/rei22a/international_asw/rest/logout" role="button" class="btnBase btnLogout">ログアウト</a>
							</li>
					</ul>
			</div>
		</div>
			<div class="amcUserInfoWrapper">
				<div class="amcUserInfo">
					<div class="userNameArea">
						<table>
							<tr>	
									<td class="statusIcon"><img src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/image/icon_status_05.png?717d3c0" alt="プラチナサービスメンバー" height="46" width="60" />
									</td>
								<td class="userName">佐藤 直樹 <span>様</span>
								</td>
							</tr>					
						</table>
					</div>
						<div class="balance">
								<dl class="upgradePointBalance">
									<dt>アップグレードポイント残高
									</dt>
									<dd>
											<dl>
												<dt>(2025年度)
												</dt>
												<dd>
													<em>20
													</em>ポイント
												</dd>
											</dl>
									</dd>
								</dl>
							<dl class="mileBalance">
								<dt>マイル口座残高
								</dt>
								<dd><em>75,663</em><span class="currencyCode">マイル</span>
								</dd>
							</dl>
						</div>
				</div>
					<p class="nowTime">6月19日20時26分現在
					</p>
			</div>
		<!--===== /cmnHeader =====-->
	</div>
		<div id="sessionKeeperContainer" class="sessionKeepingContainer"><img src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/image/bg_modalWindow.png?717d3c0" alt="" height="0" width="0" />
			<div id="extendSessionExpiredDialog" class="modalExpire modalSmall modalContainer" role="dialog" aria-labelledby="extendSessionExpiredDialogTitle" aria-hidden="true">
<form id="sessionKeeperContainer:j_idt183" name="sessionKeeperContainer:j_idt183" method="post" action="https://aswbe-i.ana.co.jp/rei22a/international_asw/pages/award/reservation/award_mandatory_passenger_information_input.xhtml?aswcid=1&amp;rand=20250619202605SvWH8B2G65" autocomplete="off" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="sessionKeeperContainer:j_idt183" value="sessionKeeperContainer:j_idt183" />
<input type="hidden" name="sessionKeeperContainer:j_idt183_operationTicket" value="dljdmx+860d2541309d864e7bd94d62fdbedd80~0Rj1MBp0CZEBT8ag1Jg3LQWZOZMmAK98Oum7BJd6!1750332354977.aere-xml-controller-67d4778877-r8c8p" /><input type="hidden" name="sessionKeeperContainer:j_idt183_cmnPageTicket" value="4" />
					<h1 id="extendSessionExpiredDialogTitle" class="dialogTitle">まもなくセッションが終了します</h1>
					<div id="extendSessionExpiredDialogMessage" class="dialogMessage" tabindex="-1">このままお手続きを進める場合は、［延長］ボタンを押してください。</div>
					<p class="modalButton"><input id="sessionKeeperContainer:cmnSessionKeepingButton" type="submit" name="sessionKeeperContainer:cmnSessionKeepingButton" value="延長" class="btnBase btnModal btnMainStream" onclick="mojarra.ab(this,event,'action',0,0,{'onevent':function(data) {Asw.doCommonAjaxCallback(data,false, event); Asw.SessionKeeper.doSessionKeep(data);},'onerror':function(data) {Asw.doCommonAjaxErrorCallback(data); }});return false" />
					</p><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:0" value="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" autocomplete="off" />
</form>
			</div>
			<div id="showSessionExpiredDialog" class="modalExpire modalSmall modalContainer" role="dialog" aria-labelledby="showSessionExpiredDialogTitle" aria-hidden="true">
				<h1 id="showSessionExpiredDialogTitle" class="dialogTitle">セッションが終了しました</h1>
				<div id="showSessionExpiredDialogMessage" class="dialogMessage" tabindex="-1">一定時間操作されなかったため、お手続きを進めることができません。お手数ですが、再度トップページからお手続きください。</div>
				<p class="modalButton">
					<input type="submit" value="閉じる" class="btnBase btnModal btnMainStream" onclick="Asw.SessionKeeper.closeTimeoutDialog(); return false;" />
				</p>
			</div>
		</div>
		<!--===== dialogMessages =====--><div id="cmnModalMessages"></div>
		<div id="prebookConfirmDialog" class="modalContainer modalConfirm modalSmall " aria-hidden="true" role="dialog">
<form id="prebookConfirmDialog:j_idt403" name="prebookConfirmDialog:j_idt403" method="post" action="https://aswbe-i.ana.co.jp/rei22a/international_asw/pages/award/reservation/award_mandatory_passenger_information_input.xhtml?aswcid=1&amp;rand=20250619202605SvWH8B2G65" autocomplete="off" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="prebookConfirmDialog:j_idt403" value="prebookConfirmDialog:j_idt403" />
<input type="hidden" name="prebookConfirmDialog:j_idt403_operationTicket" value="dljdmx+860d2541309d864e7bd94d62fdbedd80~0Rj1MBp0CZEBT8ag1Jg3LQWZOZMmAK98Oum7BJd6!1750332354977.aere-xml-controller-67d4778877-r8c8p" /><input type="hidden" name="prebookConfirmDialog:j_idt403_cmnPageTicket" value="4" /><div class="dialogMessage" tabindex="0"><p>この後、名・姓の修正や変更ができません。</p><p>「搭乗者選択」欄の氏名が「名」「姓」の順番で表示されていることを再確認ください。</p><p>例）<strong class="highlight">空野 太郎</strong> 様 → MR <strong class="highlight">TARO SORANO</strong></p><p>保安上の理由により、航空券とパスポートのお名前が同じ表記でない場合はご搭乗いただくことができませんので必ず再度ご確認ください。</p></div>
					
					<ul class="modalBtnHorizontal">
							
							<li><input type="submit" name="prebookConfirmDialog:j_idt419" value="戻る" aria-controls="prebookConfirmDialog" class="btnBase btnModal btnCancel" onclick="Asw.Dialog.getInstance('prebookConfirmDialog').close(event, Asw.DialogSequencer.isActiveSequencerNotDisplayedLastDialog());  return false;" />
							</li>
						
						<li class="btnArrowNext"><input type="submit" name="prebookConfirmDialog:j_idt422" value="OK" aria-controls="prebookConfirmDialog" class="btnBase btnModal btnMainStream " onclick="Asw.Dialog.getInstance('prebookConfirmDialog').callOpener('NORMAL', event); return false;" />
						</li>
					</ul><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:1" value="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" autocomplete="off" />
</form>
		</div>
		<!--===== /dialogMessages =====--><div id="cmnContainer">
		<!--=========== .cmnContainer ============--><div id="cmnEmergencyNotice"></div><div id="cmnTitle" class="hgroup ">
		<h1 class="visuallyHidden">お客様情報入力</h1></div>

		<div id="cmnWrapper">
		<!--===== cmnWrapper =====-->
		<ol class="mainFlow" aria-label="完了までの手順、">
			<li><img src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/image/icon_flow_01.png?717d3c0" alt="1" height="20" width="28" />フライト検索</li><!--
			--><li><img src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/image/icon_flow_02_on.png?717d3c0" alt="2" height="20" width="28" /><span class="visuallyHidden">現在のステップ、</span>お客様情報入力</li><!--
			--><li><img src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/image/icon_flow_03.png?717d3c0" alt="3" height="20" width="28" />お支払い情報</li><!--
			--><li class="flowLast"><img src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/image/icon_flow4_04.png?717d3c0" alt="4" height="20" width="28" />完了</li>
		</ol>

	<div id="main">
	<!--=========== main =============-->
		<ul class="iconExample">
				<li class="requiredIconExample">
					<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="" height="7" width="7" /></span>必須項目です	
				</li>
		</ul>
<form id="j_idt1529" name="j_idt1529" method="post" action="https://aswbe-i.ana.co.jp/rei22a/international_asw/pages/award/reservation/award_mandatory_passenger_information_input.xhtml?aswcid=1&amp;rand=20250619202605SvWH8B2G65" autocomplete="off" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="j_idt1529" value="j_idt1529" />
<input type="hidden" name="j_idt1529_operationTicket" value="dljdmx+860d2541309d864e7bd94d62fdbedd80~0Rj1MBp0CZEBT8ag1Jg3LQWZOZMmAK98Oum7BJd6!1750332354977.aere-xml-controller-67d4778877-r8c8p" /><input type="hidden" name="j_idt1529_cmnPageTicket" value="4" />

<div class="section">
	<h2>搭乗者選択<a href="https://www.ana.co.jp/other/int/meta/0086.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="特典のご利用に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a></h2>
	<table class="passengerInfo">
		<thead>
			<tr>
				
				<th class="name">氏名</th>
				
				<th class="dateOfBirthWide" id="dateOfBirthHeader">生年月日</th>
				
				<th class="gender lastCol" id="genderHeader">性別<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span></th>
				
			</tr>
		</thead>
		<tbody>
				
				<tr>
					<th class="parent_01" id="parentHeader01">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:0:passenger" type="checkbox" name="passengers:0:passenger" checked="checked" data-number="1" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:0:passenger">NAOKI SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader01">1963年06月05日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader01">				
							<span class="formWrapper">
							<label for="passengers:0:gender" class="visuallyHidden">NAOKI SATOの性別</label><select id="passengers:0:gender" name="passengers:0:gender" size="1">	<option value="">選択してください</option>
	<option value="M" selected="selected">男性</option>
	<option value="F">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_02" id="parentHeader02">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:1:passenger" type="checkbox" name="passengers:1:passenger" data-number="2" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:1:passenger">REO SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader02">1995年05月18日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader02">				
							<span class="formWrapper">
							<label for="passengers:1:gender" class="visuallyHidden">REO SATOの性別</label><select id="passengers:1:gender" name="passengers:1:gender" size="1">	<option value="">選択してください</option>
	<option value="M" selected="selected">男性</option>
	<option value="F">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_03" id="parentHeader03">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:2:passenger" type="checkbox" name="passengers:2:passenger" data-number="3" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:2:passenger">ERI SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader03">1963年08月14日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader03">				
							<span class="formWrapper">
							<label for="passengers:2:gender" class="visuallyHidden">ERI SATOの性別</label><select id="passengers:2:gender" name="passengers:2:gender" size="1">	<option value="">選択してください</option>
	<option value="M">男性</option>
	<option value="F" selected="selected">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_04" id="parentHeader04">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:3:passenger" type="checkbox" name="passengers:3:passenger" data-number="4" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:3:passenger">TAISHI SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader04">1992年12月03日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader04">				
							<span class="formWrapper">
							<label for="passengers:3:gender" class="visuallyHidden">TAISHI SATOの性別</label><select id="passengers:3:gender" name="passengers:3:gender" size="1">	<option value="">選択してください</option>
	<option value="M" selected="selected">男性</option>
	<option value="F">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_05" id="parentHeader05">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:4:passenger" type="checkbox" name="passengers:4:passenger" data-number="5" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:4:passenger">HIROMI FUJIMOTO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader05">1961年11月23日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader05">				
							<span class="formWrapper">
							<label for="passengers:4:gender" class="visuallyHidden">HIROMI FUJIMOTOの性別</label><select id="passengers:4:gender" name="passengers:4:gender" size="1">	<option value="">選択してください</option>
	<option value="M">男性</option>
	<option value="F" selected="selected">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
		</tbody>
	</table>
</div>
		<div class="section">
			<h2 class="midstream">連絡先情報
			</h2>
			<h3>代表者
			</h3>
	
	
	
	<div class="formInput">
		<fieldset>
			<legend><label>代表者</label></legend>
			<dl id="contactEmailAddressInfo">
				<dt class="hasRequiredIcon">メールアドレス<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span><a href="https://www.ana.co.jp/other/int/meta/0057.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="メールの宛先と内容に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a>
				</dt>
				<dd>
					<ul class="verticalList">
						<li class="nonMarginTop"><label for="representativeEmail" class="visuallyHidden">代表者メールアドレス</label><input id="representativeEmail" type="text" name="representativeEmail" value="<EMAIL>" class="widthLarge" placeholder="例：<EMAIL>" />
						</li>
						<li>
							<span class="supplementTxt" id="confirmMailAddressForRepresentative">確認のため再度ご入力ください。
							</span><label for="confirmrepresentativeEmail" class="visuallyHidden">確認用メールアドレス</label><input id="confirmrepresentativeEmail" type="text" name="confirmrepresentativeEmail" value="<EMAIL>" class="widthLarge jsNotPasteInput" placeholder="例：<EMAIL>" aria-describedby="confirmMailAddressForRepresentative" />
						</li>
					</ul>
				</dd>
					<dd style="display:none;"><input id="updateAlwaysInfo" type="checkbox" name="updateAlwaysInfo" /><label for="updateAlwaysInfo">ログイン中の会員情報へこのメールアドレスを登録</label>
					</dd>
			</dl>
			<dl>
				<dt>電話番号<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span>
				</dt>
				<dd class="js-countryWrap">
					<ul>
						<li><label for="representativeTelType" class="visuallyHidden">電話番号種別</label><select id="representativeTelType" name="representativeTelType" class="basicWidth js-typeSelect" size="1" onchange="changeTelType(this,'representativeTel')">	<option value="M1" selected="selected">携帯</option>
	<option value="B1">会社</option>
	<option value="H1">自宅</option>
</select>
						</li>
						<li><label for="representativeTelCountry" class="visuallyHidden">電話番号の国/地区選択</label><select id="representativeTelCountry" name="representativeTelCountry" class="js-countrySelect" size="1">	<option value="" data-sms-possible="true" data-country-code="---">選択してください</option>
	<option value="JP" selected="selected" data-sms-possible="true">日本</option>
	<option value="-" data-sms-possible="true" data-country-code="---">-----------</option>
	<option value="IS" data-sms-possible="true">アイスランド</option>
	<option value="IE" data-sms-possible="true">アイルランド</option>
	<option value="AZ" data-sms-possible="true">アゼルバイジャン</option>
	<option value="AF" data-sms-possible="true">アフガニスタン</option>
	<option value="US" data-sms-possible="true">アメリカ合衆国/カナダ</option>
	<option value="AE" data-sms-possible="true">アラブ首長国連邦</option>
	<option value="DZ" data-sms-possible="true">アルジェリア</option>
	<option value="AR" data-sms-possible="true">アルゼンチン</option>
	<option value="AW" data-sms-possible="true">アルバ</option>
	<option value="AL" data-sms-possible="true">アルバニア</option>
	<option value="AM" data-sms-possible="true">アルメニア</option>
	<option value="AO" data-sms-possible="true">アンゴラ</option>
	<option value="AG" data-sms-possible="true">アンティグア・バーブーダ</option>
	<option value="AD" data-sms-possible="true">アンドラ</option>
	<option value="YE" data-sms-possible="true">イエメン</option>
	<option value="GB" data-sms-possible="true">イギリス</option>
	<option value="IL" data-sms-possible="true">イスラエル</option>
	<option value="IT" data-sms-possible="true">イタリア</option>
	<option value="IQ" data-sms-possible="true">イラク</option>
	<option value="IR" data-sms-possible="true">イラン</option>
	<option value="IN" data-sms-possible="true">インド</option>
	<option value="ID" data-sms-possible="true">インドネシア</option>
	<option value="UG" data-sms-possible="true">ウガンダ</option>
	<option value="UA" data-sms-possible="true">ウクライナ</option>
	<option value="UZ" data-sms-possible="true">ウズベキスタン</option>
	<option value="UY" data-sms-possible="true">ウルグアイ</option>
	<option value="EC" data-sms-possible="true">エクアドル</option>
	<option value="EG" data-sms-possible="true">エジプト</option>
	<option value="EE" data-sms-possible="true">エストニア</option>
	<option value="SZ" data-sms-possible="true">エスワティニ</option>
	<option value="ET" data-sms-possible="true">エチオピア</option>
	<option value="ER" data-sms-possible="true">エリトリア</option>
	<option value="SV" data-sms-possible="true">エルサルバドル</option>
	<option value="AU" data-sms-possible="true">オーストラリア</option>
	<option value="AT" data-sms-possible="true">オーストリア</option>
	<option value="OM" data-sms-possible="true">オマーン</option>
	<option value="NL" data-sms-possible="true">オランダ</option>
	<option value="GH" data-sms-possible="true">ガーナ</option>
	<option value="CV" data-sms-possible="true">カーボベルデ</option>
	<option value="GY" data-sms-possible="true">ガイアナ</option>
	<option value="KZ" data-sms-possible="true">カザフスタン</option>
	<option value="QA" data-sms-possible="true">カタール</option>
	<option value="CA" data-sms-possible="true">カナダ/アメリカ合衆国</option>
	<option value="GA" data-sms-possible="true">ガボン</option>
	<option value="CM" data-sms-possible="true">カメルーン</option>
	<option value="KR" data-sms-possible="true">韓国</option>
	<option value="GM" data-sms-possible="true">ガンビア</option>
	<option value="KH" data-sms-possible="true">カンボジア</option>
	<option value="KP" data-sms-possible="true">北朝鮮</option>
	<option value="MK" data-sms-possible="true">北マケドニア</option>
	<option value="GN" data-sms-possible="true">ギニア</option>
	<option value="GW" data-sms-possible="true">ギニアビサウ</option>
	<option value="CY" data-sms-possible="true">キプロス</option>
	<option value="CU" data-sms-possible="true">キューバ</option>
	<option value="GR" data-sms-possible="true">ギリシャ</option>
	<option value="KI" data-sms-possible="true">キリバス</option>
	<option value="KG" data-sms-possible="true">キルギス</option>
	<option value="GT" data-sms-possible="true">グアテマラ</option>
	<option value="GU" data-sms-possible="true">グアム</option>
	<option value="KW" data-sms-possible="true">クウェート</option>
	<option value="GD" data-sms-possible="true">グレナダ</option>
	<option value="HR" data-sms-possible="true">クロアチア</option>
	<option value="KY" data-sms-possible="true">ケイマン諸島</option>
	<option value="KE" data-sms-possible="true">ケニア</option>
	<option value="CI" data-sms-possible="true">コートジボワール</option>
	<option value="CR" data-sms-possible="true">コスタリカ</option>
	<option value="KM" data-sms-possible="true">コモロ</option>
	<option value="CO" data-sms-possible="true">コロンビア</option>
	<option value="CG" data-sms-possible="true">コンゴ共和国</option>
	<option value="CD" data-sms-possible="true">コンゴ民主共和国</option>
	<option value="SA" data-sms-possible="true">サウジアラビア</option>
	<option value="ZM" data-sms-possible="true">ザンビア</option>
	<option value="SM" data-sms-possible="true">サンマリノ</option>
	<option value="SL" data-sms-possible="true">シエラレオネ</option>
	<option value="DJ" data-sms-possible="true">ジブチ</option>
	<option value="JM" data-sms-possible="true">ジャマイカ</option>
	<option value="GE" data-sms-possible="true">ジョージア</option>
	<option value="SY" data-sms-possible="true">シリア</option>
	<option value="SG" data-sms-possible="true">シンガポール</option>
	<option value="ZW" data-sms-possible="true">ジンバブエ</option>
	<option value="CH" data-sms-possible="true">スイス</option>
	<option value="SE" data-sms-possible="true">スウェーデン</option>
	<option value="SD" data-sms-possible="true">スーダン</option>
	<option value="ES" data-sms-possible="true">スペイン</option>
	<option value="SR" data-sms-possible="true">スリナム</option>
	<option value="LK" data-sms-possible="true">スリランカ</option>
	<option value="SK" data-sms-possible="true">スロバキア</option>
	<option value="SI" data-sms-possible="true">スロベニア</option>
	<option value="SC" data-sms-possible="true">セイシェル</option>
	<option value="GQ" data-sms-possible="true">赤道ギニア</option>
	<option value="SN" data-sms-possible="true">セネガル</option>
	<option value="RS" data-sms-possible="true">セルビア</option>
	<option value="KN" data-sms-possible="true">セントクリストファー・ネイビス</option>
	<option value="LC" data-sms-possible="true">セントルシア</option>
	<option value="SB" data-sms-possible="true">ソロモン諸島</option>
	<option value="TH" data-sms-possible="true">タイ</option>
	<option value="TW" data-sms-possible="true">台湾</option>
	<option value="TJ" data-sms-possible="true">タジキスタン</option>
	<option value="TZ" data-sms-possible="true">タンザニア</option>
	<option value="CZ" data-sms-possible="true">チェコ</option>
	<option value="TD" data-sms-possible="true">チャド</option>
	<option value="CF" data-sms-possible="true">中央アフリカ共和国</option>
	<option value="CN" data-sms-possible="true">中国</option>
	<option value="TN" data-sms-possible="true">チュニジア</option>
	<option value="CL" data-sms-possible="true">チリ</option>
	<option value="TV" data-sms-possible="true">ツバル</option>
	<option value="DK" data-sms-possible="true">デンマーク</option>
	<option value="DE" data-sms-possible="true">ドイツ</option>
	<option value="TG" data-sms-possible="true">トーゴ</option>
	<option value="DO" data-sms-possible="true">ドミニカ共和国</option>
	<option value="DM" data-sms-possible="true">ドミニカ国</option>
	<option value="TT" data-sms-possible="true">トリニダード・トバゴ</option>
	<option value="TM" data-sms-possible="true">トルクメニスタン</option>
	<option value="TR" data-sms-possible="true">トルコ</option>
	<option value="TO" data-sms-possible="true">トンガ</option>
	<option value="NG" data-sms-possible="true">ナイジェリア</option>
	<option value="NR" data-sms-possible="true">ナウル</option>
	<option value="NA" data-sms-possible="true">ナミビア</option>
	<option value="NI" data-sms-possible="true">ニカラグア</option>
	<option value="NE" data-sms-possible="true">ニジェール</option>
	<option value="NZ" data-sms-possible="true">ニュージーランド</option>
	<option value="NP" data-sms-possible="true">ネパール</option>
	<option value="NO" data-sms-possible="true">ノルウェー</option>
	<option value="BH" data-sms-possible="true">バーレーン</option>
	<option value="HT" data-sms-possible="true">ハイチ</option>
	<option value="PK" data-sms-possible="true">パキスタン</option>
	<option value="VA" data-sms-possible="true">バチカン</option>
	<option value="PA" data-sms-possible="true">パナマ</option>
	<option value="VU" data-sms-possible="true">バヌアツ</option>
	<option value="BS" data-sms-possible="true">バハマ</option>
	<option value="PG" data-sms-possible="true">パプアニューギニア</option>
	<option value="BM" data-sms-possible="true">バミューダ</option>
	<option value="PW" data-sms-possible="true">パラオ</option>
	<option value="PY" data-sms-possible="true">パラグアイ</option>
	<option value="BB" data-sms-possible="true">バルバドス</option>
	<option value="PS" data-sms-possible="true">パレスチナ</option>
	<option value="HU" data-sms-possible="true">ハンガリー</option>
	<option value="BD" data-sms-possible="true">バングラデシュ</option>
	<option value="TL" data-sms-possible="true">東ティモール</option>
	<option value="FJ" data-sms-possible="true">フィジー</option>
	<option value="PH" data-sms-possible="true">フィリピン</option>
	<option value="FI" data-sms-possible="true">フィンランド</option>
	<option value="BT" data-sms-possible="true">ブータン</option>
	<option value="PR" data-sms-possible="true">プエルトリコ</option>
	<option value="BR" data-sms-possible="true">ブラジル</option>
	<option value="FR" data-sms-possible="true">フランス</option>
	<option value="PF" data-sms-possible="true">フランス領ポリネシア</option>
	<option value="BG" data-sms-possible="true">ブルガリア</option>
	<option value="BF" data-sms-possible="true">ブルキナファソ</option>
	<option value="BN" data-sms-possible="true">ブルネイ</option>
	<option value="BI" data-sms-possible="true">ブルンジ</option>
	<option value="VN" data-sms-possible="true">ベトナム</option>
	<option value="BJ" data-sms-possible="true">ベナン</option>
	<option value="VE" data-sms-possible="true">ベネズエラ</option>
	<option value="BY" data-sms-possible="true">ベラルーシ</option>
	<option value="BZ" data-sms-possible="true">ベリーズ</option>
	<option value="PE" data-sms-possible="true">ペルー</option>
	<option value="BE" data-sms-possible="true">ベルギー</option>
	<option value="PL" data-sms-possible="true">ポーランド</option>
	<option value="BA" data-sms-possible="true">ボスニア・ヘルツェゴビナ</option>
	<option value="BW" data-sms-possible="true">ボツワナ</option>
	<option value="BO" data-sms-possible="true">ボリビア</option>
	<option value="PT" data-sms-possible="true">ポルトガル</option>
	<option value="HK" data-sms-possible="true">香港</option>
	<option value="HN" data-sms-possible="true">ホンジュラス</option>
	<option value="MH" data-sms-possible="true">マーシャル諸島</option>
	<option value="MO" data-sms-possible="true">マカオ</option>
	<option value="MG" data-sms-possible="true">マダガスカル</option>
	<option value="MW" data-sms-possible="true">マラウイ</option>
	<option value="ML" data-sms-possible="true">マリ</option>
	<option value="MT" data-sms-possible="true">マルタ</option>
	<option value="MY" data-sms-possible="true">マレーシア</option>
	<option value="FM" data-sms-possible="true">ミクロネシア連邦</option>
	<option value="ZA" data-sms-possible="true">南アフリカ</option>
	<option value="MM" data-sms-possible="true">ミャンマー</option>
	<option value="MX" data-sms-possible="true">メキシコ</option>
	<option value="MU" data-sms-possible="true">モーリシャス</option>
	<option value="MR" data-sms-possible="true">モーリタニア</option>
	<option value="MZ" data-sms-possible="true">モザンビーク</option>
	<option value="MC" data-sms-possible="true">モナコ</option>
	<option value="MV" data-sms-possible="true">モルディヴ</option>
	<option value="MD" data-sms-possible="true">モルドバ</option>
	<option value="MA" data-sms-possible="true">モロッコ</option>
	<option value="MN" data-sms-possible="true">モンゴル</option>
	<option value="ME" data-sms-possible="true">モンテネグロ</option>
	<option value="JO" data-sms-possible="true">ヨルダン</option>
	<option value="LA" data-sms-possible="true">ラオス人民民主共和国</option>
	<option value="LV" data-sms-possible="true">ラトビア</option>
	<option value="LT" data-sms-possible="true">リトアニア</option>
	<option value="LY" data-sms-possible="true">リビア</option>
	<option value="LI" data-sms-possible="true">リヒテンシュタイン</option>
	<option value="LR" data-sms-possible="true">リベリア</option>
	<option value="RO" data-sms-possible="true">ルーマニア</option>
	<option value="LU" data-sms-possible="true">ルクセンブルク</option>
	<option value="RW" data-sms-possible="true">ルワンダ</option>
	<option value="LS" data-sms-possible="true">レソト</option>
	<option value="LB" data-sms-possible="true">レバノン</option>
	<option value="RU" data-sms-possible="true">ロシア</option>
</select>
						</li>
						<li class="numberInputWrap">
							<span id="numberInputInfo1" class="supplementTxt marginTop">先頭に0(ゼロ)がある場合、0(ゼロ)を抜いてください(一部、国・地域除く)。携帯電話番号の場合は、携帯電話を契約された国を選択ください。</span>
							<div class="numberRowWrap">
								<div class="block">
									
									<span class="visuallyHidden">国番号</span>
									<span class="viewInput widthMini js-countryCode"></span>
								</div>
								<div class="block"><label for="representativeTel" class="visuallyHidden">電話番号</label><input id="representativeTel" type="text" name="representativeTel" value="9042504176" class="widthMiddle" placeholder="例：9012345678" maxlength="30" aria-describedby="numberInputInfo1" />
								</div>
							</div>
						</li>
					</ul>
				</dd>
			</dl>
		</fieldset>
	</div>
	<div class="titleWrapper">
		<h3>
			<span class="text">eメール・SMSに関するご案内<a href="https://www.ana.co.jp/other/int/meta/0057.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="eメール・SMSに関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a></span>
		</h3>
	</div>
	<div class="formInput">
	<h4 class="formAreaTitle">主に搭乗・運航に関わるeメールの送付先設定（米国へ通知いたします）<a href="https://www.ana.co.jp/other/int/meta/0629.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="米国入国用eメール・SMS・電話番号に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a>
	</h4>
	<div class="paxSettingArea js-mailFormWrap onePax is-open">
			<div class="settingArea">
					<dl>
						<dt class="breakWord">NAOKI SATO
						</dt>
						<dd>
							<fieldset>
								<legend>NAOKI SATOのeメール送付先および米国到着時に提供するメールアドレス</legend>代表者と同じ
							</fieldset>
						</dd>
					</dl>
			</div>
	</div>
	<h4 class="formAreaTitle hasTopLine">主に搭乗・運航に関わるSMSの送付先および米国で受信できる電話番号の設定（米国へ通知いたします）<a href="https://www.ana.co.jp/other/int/meta/0629.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="米国入国用eメール・SMS・電話番号に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a>
	</h4>
	<div class="paxSettingArea js-smsFormWrap onePax is-open">
			<div class="settingArea">
					<dl>
						<dt class="breakWord">NAOKI SATO<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span>
						</dt>
						<dd>
							<fieldset>
								<legend>NAOKI SATOの1つ目の電話番号</legend>
									<ul class="verticalList js-formItem">
										<li><input type="radio" id="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSameAsRepresentativeSms:radioItem" name="contactsSms:0:recipientSmsEachPersonSelection:radioGroup" value="0" checked="checked" /><label for="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSameAsRepresentativeSms:radioItem">代表者電話番号と同じ</label>
										</li>
										<li><input type="radio" id="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSmsPerPassenger:radioItem" name="contactsSms:0:recipientSmsEachPersonSelection:radioGroup" value="1" /><label for="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSmsPerPassenger:radioItem">電話番号を指定</label>
											<div class="indentArea js-formImputArea">
												<span id="confirmTelNumberForRepresentative:0" class="supplementTxt marginTop">先頭に0(ゼロ)がある場合、0(ゼロ)を抜いてください(一部、国・地域除く)。携帯電話番号の場合は、携帯電話を契約された国を選択ください。</span>
												<ul class="countryNumberInput js-countryWrap">
													<li><label for="contactsSms:0:recipientSmsEachPersonSelection:passengerSmsCountry" class="visuallyHidden">電話番号の国/地区選択</label><select id="contactsSms:0:recipientSmsEachPersonSelection:passengerSmsCountry" name="contactsSms:0:recipientSmsEachPersonSelection:passengerSmsCountry" class="countrySelect js-countrySelect" size="1">	<option value="" data-country-code="---">選択してください</option>
	<option value="JP">日本</option>
	<option value="-" data-country-code="---">-----------</option>
	<option value="IE">アイルランド</option>
	<option value="AZ">アゼルバイジャン</option>
	<option value="AF">アフガニスタン</option>
	<option value="US">アメリカ合衆国/カナダ</option>
	<option value="AE">アラブ首長国連邦</option>
	<option value="DZ">アルジェリア</option>
	<option value="AR">アルゼンチン</option>
	<option value="AW">アルバ</option>
	<option value="AL">アルバニア</option>
	<option value="AM">アルメニア</option>
	<option value="AG">アンティグア・バーブーダ</option>
	<option value="AD">アンドラ</option>
	<option value="YE">イエメン</option>
	<option value="GB">イギリス</option>
	<option value="IL">イスラエル</option>
	<option value="IT">イタリア</option>
	<option value="IQ">イラク</option>
	<option value="IR">イラン</option>
	<option value="IN">インド</option>
	<option value="ID">インドネシア</option>
	<option value="UG">ウガンダ</option>
	<option value="UA">ウクライナ</option>
	<option value="UZ">ウズベキスタン</option>
	<option value="UY">ウルグアイ</option>
	<option value="EC">エクアドル</option>
	<option value="EG">エジプト</option>
	<option value="EE">エストニア</option>
	<option value="ER">エリトリア</option>
	<option value="SV">エルサルバドル</option>
	<option value="AU">オーストラリア</option>
	<option value="AT">オーストリア</option>
	<option value="OM">オマーン</option>
	<option value="NL">オランダ</option>
	<option value="GH">ガーナ</option>
	<option value="GY">ガイアナ</option>
	<option value="KZ">カザフスタン</option>
	<option value="QA">カタール</option>
	<option value="CA">カナダ/アメリカ合衆国</option>
	<option value="GA">ガボン</option>
	<option value="CM">カメルーン</option>
	<option value="KR">韓国</option>
	<option value="GM">ガンビア</option>
	<option value="KH">カンボジア</option>
	<option value="GN">ギニア</option>
	<option value="GW">ギニアビサウ</option>
	<option value="CY">キプロス</option>
	<option value="CU">キューバ</option>
	<option value="GR">ギリシャ</option>
	<option value="KG">キルギス</option>
	<option value="GT">グアテマラ</option>
	<option value="GU">グアム</option>
	<option value="KW">クウェート</option>
	<option value="GD">グレナダ</option>
	<option value="HR">クロアチア</option>
	<option value="KY">ケイマン諸島</option>
	<option value="KE">ケニア</option>
	<option value="CI">コートジボワール</option>
	<option value="CR">コスタリカ</option>
	<option value="KM">コモロ</option>
	<option value="CO">コロンビア</option>
	<option value="CD">コンゴ民主共和国</option>
	<option value="SA">サウジアラビア</option>
	<option value="ZM">ザンビア</option>
	<option value="SL">シエラレオネ</option>
	<option value="JM">ジャマイカ</option>
	<option value="GE">ジョージア</option>
	<option value="SY">シリア</option>
	<option value="SG">シンガポール</option>
	<option value="ZW">ジンバブエ</option>
	<option value="CH">スイス</option>
	<option value="SE">スウェーデン</option>
	<option value="SD">スーダン</option>
	<option value="ES">スペイン</option>
	<option value="SR">スリナム</option>
	<option value="LK">スリランカ</option>
	<option value="SK">スロバキア</option>
	<option value="SI">スロベニア</option>
	<option value="SN">セネガル</option>
	<option value="RS">セルビア</option>
	<option value="KN">セントクリストファー・ネイビス</option>
	<option value="LC">セントルシア</option>
	<option value="TH">タイ</option>
	<option value="TW">台湾</option>
	<option value="TJ">タジキスタン</option>
	<option value="TZ">タンザニア</option>
	<option value="CZ">チェコ</option>
	<option value="TD">チャド</option>
	<option value="CF">中央アフリカ共和国</option>
	<option value="CN">中国</option>
	<option value="TN">チュニジア</option>
	<option value="CL">チリ</option>
	<option value="DK">デンマーク</option>
	<option value="DE">ドイツ</option>
	<option value="TG">トーゴ</option>
	<option value="DO">ドミニカ共和国</option>
	<option value="TT">トリニダード・トバゴ</option>
	<option value="TR">トルコ</option>
	<option value="NG">ナイジェリア</option>
	<option value="NI">ニカラグア</option>
	<option value="NE">ニジェール</option>
	<option value="NZ">ニュージーランド</option>
	<option value="NP">ネパール</option>
	<option value="NO">ノルウェー</option>
	<option value="HT">ハイチ</option>
	<option value="PK">パキスタン</option>
	<option value="PA">パナマ</option>
	<option value="BS">バハマ</option>
	<option value="PG">パプアニューギニア</option>
	<option value="BM">バミューダ</option>
	<option value="PW">パラオ</option>
	<option value="PY">パラグアイ</option>
	<option value="BB">バルバドス</option>
	<option value="PS">パレスチナ</option>
	<option value="HU">ハンガリー</option>
	<option value="BD">バングラデシュ</option>
	<option value="TL">東ティモール</option>
	<option value="FJ">フィジー</option>
	<option value="PH">フィリピン</option>
	<option value="FI">フィンランド</option>
	<option value="BT">ブータン</option>
	<option value="PR">プエルトリコ</option>
	<option value="BR">ブラジル</option>
	<option value="FR">フランス</option>
	<option value="PF">フランス領ポリネシア</option>
	<option value="BG">ブルガリア</option>
	<option value="BF">ブルキナファソ</option>
	<option value="BN">ブルネイ</option>
	<option value="BI">ブルンジ</option>
	<option value="VN">ベトナム</option>
	<option value="BJ">ベナン</option>
	<option value="VE">ベネズエラ</option>
	<option value="BY">ベラルーシ</option>
	<option value="PE">ペルー</option>
	<option value="BE">ベルギー</option>
	<option value="PL">ポーランド</option>
	<option value="BA">ボスニア・ヘルツェゴビナ</option>
	<option value="BW">ボツワナ</option>
	<option value="HK">香港</option>
	<option value="MH">マーシャル諸島</option>
	<option value="MO">マカオ</option>
	<option value="MG">マダガスカル</option>
	<option value="MW">マラウイ</option>
	<option value="ML">マリ</option>
	<option value="MT">マルタ</option>
	<option value="MY">マレーシア</option>
	<option value="ZA">南アフリカ</option>
	<option value="MM">ミャンマー</option>
	<option value="MX">メキシコ</option>
	<option value="MU">モーリシャス</option>
	<option value="MR">モーリタニア</option>
	<option value="MZ">モザンビーク</option>
	<option value="MC">モナコ</option>
	<option value="MV">モルディヴ</option>
	<option value="MA">モロッコ</option>
	<option value="MN">モンゴル</option>
	<option value="ME">モンテネグロ</option>
	<option value="JO">ヨルダン</option>
	<option value="LA">ラオス人民民主共和国</option>
	<option value="LV">ラトビア</option>
	<option value="LT">リトアニア</option>
	<option value="LY">リビア</option>
	<option value="LR">リベリア</option>
	<option value="RO">ルーマニア</option>
	<option value="LU">ルクセンブルク</option>
	<option value="RW">ルワンダ</option>
	<option value="LB">レバノン</option>
	<option value="RU">ロシア</option>
</select>
													</li>
													<li>
														
														<div class="numberRowWrap">
															<div class="block">
																<span class="visuallyHidden">国番号</span>
																<span class="countryCode viewInput js-countryCode"></span>
															</div>
															
															<div class="block"><label for="contactsSms:0:recipientSmsEachPersonSelection:flightStatusNotificationContactPointSmsDescription" class="visuallyHidden">電話番号</label><input id="contactsSms:0:recipientSmsEachPersonSelection:flightStatusNotificationContactPointSmsDescription" type="text" name="contactsSms:0:recipientSmsEachPersonSelection:flightStatusNotificationContactPointSmsDescription" class="telNumber" placeholder="例：9012345678" maxlength="30" aria-describedby="confirmTelNumberForRepresentative:0" />
															</div>
														</div>
													</li>
												</ul>
											</div>
										</li>
									</ul>
							</fieldset>
						</dd>
					</dl>
			</div>
	</div>
	</div>
	
	<div class="formInput articleLine">
		<h3>滞在中の連絡先(任意)
		</h3>
		<dl>
			
			<dt><label for="accommodationOne">現地連絡先1</label>
			</dt>
			<dd><input id="accommodationOne" type="text" name="accommodationOne" class="widthLarge" placeholder="例：09012345678 ABC HOTEL" maxlength="28" title="例：09012345678 ABC HOTEL" />
			</dd>
		</dl>
		<dl>
			
			<dt><label for="accommodationTwo">現地連絡先2</label>
			</dt>
			<dd><input id="accommodationTwo" type="text" name="accommodationTwo" class="widthLarge" placeholder="例：09012345678 ABC HOTEL" maxlength="28" title="例：09012345678 ABC HOTEL" />
			</dd>
		</dl>
	</div>
		</div>
		
		<p class="btnAreaSubmit btnArrowNext "><input id="next" type="submit" name="next" value="次へ" class="btnBase btnMainStream btnVerticalMain btnWidthVariable" onclick="return Asw.Dialog.getInstance('prebookConfirmDialog').toggle(event);return Asw.LoadingWindow.open(&quot;NORMAL&quot;, event)" />
		</p><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:2" value="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" autocomplete="off" />
</form><div id="cmnDynamicMessages" class="infoBoxSection">
				<dl class="infoBox importantInfo jsAccordionSwitch">
					<dt><strong>重要なご案内</strong></dt>
					<dd>
						<ul class="jsAccordionSwitchList" id="informationMessages">
								<li>個人情報の利用目的<a href="https://www.ana.co.jp/other/int/meta/0012.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="ANAプライバシーポリシー(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">ANAプライバシーポリシー<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>を必ずお読みいただき、内容に同意いただいた上で、お進みください。</li>
								<li><a href="https://www.ana.co.jp/other/int/meta/0058.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="Secure Flight Programについて(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">米国Secure Flightプログラム<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>に基づき、パスポート名、生年月日、性別などの情報は事前に米国へ通知いたします。</li>
								<li>入力したeメールアドレス、電話番号は入国に必要な情報として米国に通知される可能性があります。</li>
									<li class="jsHiddenFlg toggleContents">お名前のスペルがパスポート名と一致しているかご確認ください。異なる場合はインターネットではお取り扱いできません。<a href="https://www.ana.co.jp/other/int/meta/intbe0001.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="ANAお問い合わせ先(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">ANA<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>へ名前の変更をお申し出ください。</li>
									<li class="jsHiddenFlg toggleContents">二重国籍のお客様は、ご搭乗時に使用されるパスポートのお名前でご予約ください。<br />パスポートごとにお名前が異なる場合は、お客様ご自身で入国管理事務所・外務省・大使館などで各種条件をご確認のうえ、使用されるパスポートを決めてください。<br />なお、入国可否については国によって異なるため、お客様ご自身で各入国管理事務所などでご確認ください。</li>
									<li class="jsHiddenFlg toggleContents">旅行開始日時点で12歳以上の場合は大人、2歳以上12歳未満の場合は小児、2歳未満の場合は幼児の扱いとなります。なお、小児は大人と同マイル数が必要です。</li>
									<li class="jsHiddenFlg toggleContents">運航に関する情報をお客様へ通知するため、提携航空会社にもお客様のメールアドレスが提供されます。あらかじめご了承ください。</li>
									<li class="jsHiddenFlg toggleContents"><dl><dt>携帯電話へのメール送信をご希望のお客様へ</dt><dd>ドメイン指定受信をご利用の場合は、ana.co.jpとamadeus.comからのメールが受信できるようあらかじめ設定をお願いいたします。</dd></dl></li>
									<li class="jsHiddenFlg toggleContents">SMSはスマートフォンの番号に配信されます。一部の国ではSMSでのご案内をご利用いただけません。詳細は<a href="https://www.ana.co.jp/other/int/meta/0737.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="ショートメッセージ(SMS)でのご案内対象外の国について(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">ショートメッセージ(SMS)でのご案内対象外の国について<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>をご覧ください。</li>
									<li class="jsHiddenFlg toggleContents">ログインされている会員ご本人が搭乗しない場合は、再度フライト検索画面より「ログインされている会員ご本人は搭乗しない」のチェックボックスを選択してお手続きください。</li>
						</ul>
							<a href="#" class="toggleSwitch jsTriggerSwitch" role="button" aria-controls="informationMessages" aria-expanded="false"><img src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/image/toggle_open.png?7bec38a" alt="開閉ボタン" height="25" width="25" class="jsRollOver" />
							</a>
					</dd>
				</dl></div>
	<!--=========== /main =============-->
	</div>

	<!--=========== Script ============-->
	<script type="text/javascript">
	$(window).on('load', function() {
		// アコーディオンのメソッドと設定
		Asw.AccordionInfo('.jsAccordionSwitch', {
			'triggerSwitch': '.jsAccordionSwitch .jsTriggerSwitch',
			'listClass': '.jsAccordionSwitchList',
			'visibilityHidden': 'toggleContents'
		});
	});
	</script>
	<!--=========== Script ============-->
	<script type="text/javascript">
		//プラットフォーム判別（サマリーエリア追従用として）
		var Asw = Asw || {};
	
	  	Asw.ClientInfo = Asw.ClientInfo  || {};
	  	Asw.ClientInfo.DeviceType = Asw.ClientInfo.DeviceType || {};
	
	  	Asw.ClientInfo.DeviceType.PC = 'PC';
	  	Asw.ClientInfo.DeviceType.SP = 'SP';
	  	Asw.ClientInfo.DeviceType.TC = 'TC';
	
	  	Asw.ClientInfo.deviceType = Asw.ClientInfo.DeviceType.PC;
	
	 	if((navigator.userAgent.indexOf('Android') > 0 && navigator.userAgent.indexOf('Mobile') == -1) || navigator.userAgent.indexOf('iPad') > 0){
	    	Asw.ClientInfo.deviceType = Asw.ClientInfo.DeviceType.TC;
	  	}
	</script>
	<!--=========== /Script ============--><div id="summaryArea">
			<div class="flightSummaryArea">
						<div class="flightSummarySection">
							<h2>区間 1
							</h2>
							<p class="flightDate"><em>6</em>月<em class="hasLeft">24</em>日（火）
							</p>
							<div class="flightSummaryRoute">
									<div class="flightSummaryRouteSection">
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation1_1">
													<span>東京(成田)
													</span>
												</p>
												<p class="time">
													<span>17:00
													</span>
												</p>
											</div>
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation1_1">
													<span>シカゴ(ORD)
													</span>
												</p>
												<p class="time">
													<span>14:55
													</span>
												</p>
											</div>
										<p class="flightNumberClass">
												<span>NH012</span><span><img src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/image/flight_ana_summaryarea.png?717d3c0" alt="ANA運航便" height="11" width="41" /></span>
												<br />特典ビジネス
										</p>
									</div>
							</div>
						</div>
						<div class="flightSummarySection">
							<h2 class="midstream">区間 2
							</h2>
							<p class="flightDate"><em>7</em>月<em class="hasLeft">23</em>日（水）
							</p>
							<div class="flightSummaryRoute">
									<div class="flightSummaryRouteSection">
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation2_1">
													<span>ホノルル(オアフ島)
													</span>
												</p>
												<p class="time">
													<span>11:35
													</span>
												</p>
											</div>
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation2_1">
													<span>東京(成田)
													</span>
												</p>
												<p class="time">
													<span>14:50
																	<span>翌日
																	</span>
													</span>
												</p>
											</div>
										<p class="flightNumberClass">
												<span>NH183</span><span><img src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/image/flight_ana_summaryarea.png?717d3c0" alt="ANA運航便" height="11" width="41" /></span>
												<br />特典エコノミー
										</p>
									</div>
							</div>
						</div>
					<div class="total">
						<p class="passenger">大人 x1
						</p>
						<div class="totalArea">
					  		<dl class="mealAmountWrap">
									<dt>有料機内食サービス
									</dt>
									<dd><em class="mealAmount">0</em><span class="currencyCode">円</span>
									</dd>
							</dl>
									<div>
										<div class="mileageAndTotal">
												<dl class="requiredMileage">
													<dt>必要マイル
													</dt>
													<dd><em class="requiredMileage">74,000</em><span class="currencyCode">マイル</span>
													</dd>
												</dl>
												<dl>
													<dt>総額
													</dt>
													<dd><em class="awardTotalPayment">68,130</em>円
													</dd>
												</dl>
												<p class="includeFareAndFuelCost">※各種税金、燃油特別付加運賃等を含みます。
												</p>
										</div>
									</div>
						</div>
					</div>
				
			</div></div>
		<!--===== /cmnWrapper =====-->
		</div>
			
		<span id="message" class="visuallyHidden" role="alert" aria-live="assertive"></span>
		<span id="politeMessage" class="visuallyHidden" role="log" aria-live="polite"></span><script type="text/javascript">
		var Asw = Asw || {};
		Asw.BaseOutput = {"sessionID":"SvWH8B2G65","language":"ja","siteCatalystLanguageCode":"J","operationDate":"20250619","operationDateTime":"20250619202605","office":{"officeCode":"TYONH08ZZ","country2letterCode":"JP"},"bookingType":"A","pageID":"A04_P01","siteCatalystPageName":"INT_BE_AWARD_J_A04特典予約_P01旅客情報入力（必須情報）","device":{"deviceType":"PC"}};
	</script><div id="promotionArea">
		<!--===== promotionArea =====-->
		<!--===== /promotionArea =====--></div>
		<!--=========== /cmnContainer ============--></div><div id="cmnBaloonArea" aria-live="polite"></div><div id="cmnPopupArea" aria-live="polite"></div>

	

	<div id="cmnFooterWrapper">
		<!--=========== .cmnFooterWrapper ============-->
		<div id="cmnFooter">
			<!--===== cmnFooter =====-->
			<ul>
				<li><a href="https://www.ana.co.jp/other/int/meta/0012.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">プライバシーポリシー<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
					<li><a href="https://www.ana.co.jp/other/int/meta/0670.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">利用者情報の外部送信について<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
					</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/0052.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">サイト利用規約<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/be0238.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">推奨環境<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/0133.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">サイトマップ<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/0732.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">運送約款<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
					<li class="lastChild"><a href="https://www.ana.co.jp/other/int/meta/0556.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">ウェブアクセシビリティについて<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
					</li>
			</ul>
			<p class="blankText" aria-hidden="true"><img src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="" height="10" width="10" />外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。</p>
			<p class="copyright"><span lang="en" xml:lang="en">Copyright&#169;ANA</span>
			</p>
			<p class="starAllianceLogo"><a href="http://www.staralliance.com/ja/" target="_blank"><img src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/image/star_alliance_logo.png?0fe73b5" alt="A STAR ALLIANCE MEMBER" height="18" width="175" /><span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
			</p>
			<!--===== cmnFooter =====-->
		</div>
		<!--=========== /cmnFooterWrapper ============-->
	</div>

		
		<div id="cmnLoadingForAjax"><img src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/image/bg_modalWindow.png?717d3c0" alt="" height="0" width="0" />
			<div class="loadingArea">
				<p class="loadingImage"><img src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/image/loading.gif?717d3c0" alt="リクエストを処理しています...しばらくお待ちください。" height="33" width="33" />
				</p>
			</div>
		</div>
		
		
		<div id="popupAltSentence" style="display: none" aria-hidden="true">別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。</div>
	<div id="cmnSiteCatalyst" aria-hidden="true"><div id="cmnSiteCatalystParamArea"><script type="text/javascript">
				// ▼SiteCatalyst
				// SiteCatalyst code version: H.2.
				// Copyright 1997-2005 Omniture, Inc. More info available at http://www.omniture.com
				SiteCatalystReportSuites   = "INT";
				SiteCatalystCharSet        = "UTF-8";
				SiteCatalystChannel        = "BE_AWARD_J";
				SiteCatalystPageName       = "INT_BE_AWARD_J_A04\u7279\u5178\u4E88\u7D04_P01\u65C5\u5BA2\u60C5\u5831\u5165\u529B\uFF08\u5FC5\u9808\u60C5\u5831\uFF09";
				SiteCatalystERR            = "";
				SiteCatalystEventNum       = "";
				SiteCatalystPurchase       = "FALSE";
				SiteCatalystProductPrice   = "";
				SiteCatalystCurrencyCode   = "";
				SiteCatalystSeatAvailabilityINT   = "";
				SiteCatalystDepartDate     = "";
				SiteCatalystDepart         = "";
				SiteCatalystArrive         = "";
				SiteCatalystFlightNo       = "";
				SiteCatalystClass          = "";
				SiteCatalystFare           = "";
				SiteCatalystPurchaseID     = "";
				
				SiteCatalystLocater        = "";
				SiteCatalystPayment        = "";
				SiteCatalystIncome         = "";
				
				SiteCatalystPax            = "";
				SiteCatalystManipulateDay  = "";
				SiteCatalystCompartmentClass   = "";
				SiteCatalystOptionInfo     = "";
				SiteCatalystOutboundUpsellPrice   = "";
				SiteCatalystInboundUpsellPrice    = "";
				
				SiteCatalystSearchInput    = "";
				SiteCatalystSegment        = "";
				SiteCatalystOfficeCode     = "";
				SiteCatalystPassengerCount = "";
				SiteCatalystPassenger      = "";
				SiteCatalystAncillaryService = "";
				// End SiteCatalyst code version: H.2.
				// ▲SiteCatalyst
			</script></div><script type="text/javascript"><!-- if(navigator.appVersion.indexOf('MSIE')>=0)document.write(unescape('%3C')+'\!-'+'-') // --></script>
		<!--/DO NOT REMOVE/-->
		

		
		<!-- Google Tag Manager -->
		<script type="text/javascript" charset="Shift_JIS" src="https://www.ana.co.jp/common/js/GTMINTBE.js"></script>
		<!-- End Google Tag Manager -->
		<!-- Yahoo Tag Manager -->
		<script type="text/javascript" charset="Shift_JIS" src="https://www.ana.co.jp/common/js/YTMINTBE.js"></script>
		<!-- End Yahoo Tag Manager -->
		

	</div>

	

	<div class="mboxDefault"></div><script type="text/javascript">mboxCreate("ASW_common_A04_P01");</script>
			<script type="text/javascript" src="https://www.ana.co.jp/common/js/tealium/tealium.js"></script>
			<script type="text/javascript" src="//cdn.evgnet.com/beacon/allnipponairways/nonamcprd/scripts/evergage.min.js"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22a/international_asw/javax.faces.resource/jsf.js.xhtml?ln=javax.faces"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/script/jquery.mousewheel.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/script/jquery.jscrollpane.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/script/jquery-ui-1.10.4.custom.min.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/script/jquery.ui.touch-punch.min.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/script/asw-common.js?beb6a50"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/script/asw-common-pc.js?fd88d7d"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/script/asw-format.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/script/mask.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/script/loading.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/script/asw-loadingwindow-pc.js?9e233c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/script/asw-loadingwindow.js?51db600"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/script/dialog-pc.js?25d36a4"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/script/asw-dialog-sequencer.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/script/asw-event.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/script/asw-date-select.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/script/asw-calendar-pc.js?9b86b8a"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/script/asw-calendar.js?db624c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/script/heightLine.js?0e7a1ce"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/script/asw-summaryarea.js?b21c6d7"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/script/asw-fingerprints.js?99001fc"></script><script type="text/javascript">
			Asw.init("https://aswbe-i.ana.co.jp/rei22a/international_asw", "ja");
			Asw.getCalendarHolidayList = function() {return [20250721,20250811,20250915,20250923,20251013,20251103,20251123,20251124,20260101,20260112,20260211,20260223,20260320,20260429,20260503,20260504,20260505,20260506,20260720,20260811,20260921,20260922,20260923,20261012,20261103,20261123,20270101,20270111,20270211,20270223,20270321,20270322,20270429,20270503,20270504,20270505,20270719,20270811,20270920,20270923,20271011,20271103,20271123,20280101,20280110,20280211,20280223,20280320,20280429,20280503,20280504,20280505,20280717,20280811,20280918,20280922,20281009,20281103,20281123,20290101,20290108,20290211,20290212,20290223,20290320,20290429,20290430,20290503,20290504,20290505,20290716,20290811,20290917,20290923,20290924,20291008,20291103,20291123];}
			Asw.ClientInfo.deviceType = "PC";
			Asw.ClientInfo.mobileDeviceType = "UNKNOWN";
			Asw.ClientInfo.osVersion = "";
			Asw.Format.init("https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/{1}/{2}/{3}?{0}");
			Asw.overwriteTargetAttributeValue("false");
		</script><script type="text/javascript">
		
		var Asw = Asw || {};
		Asw.ContactInput = Asw.ContactInput || {};
		Asw.ContactInput.Const = {
			/** data-属性名「data-type」 @constant @type {String} @default 'data-type' */
			DATA_ATTRIBUTE_NAME_TYPE: 'data-type',
			/** data-属性名「data-sms-possible」 @constant @type {String} @default 'data-sms-possible' */
			DATA_ATTRIBUTE_NAME_SMS_POSSIBLE: 'data-sms-possible',
			/** data-属性名「data-country-code」 @constant @type {String} @default 'data-country-code' */
			DATA_ATTRIBUTE_NAME_COUNTRY_CODE: 'data-country-code',
			/** data-type属性値「sms」 @constant @type {String} @default 'sms' */
			DATA_TYPE_VALUE_SMS: 'sms'
		};

		
		Asw.ContactInput.countryInfoMap = {"IS":{"internationalCallingCode":"+354","smsPossible":false},"IE":{"internationalCallingCode":"+353","smsPossible":true},"AZ":{"internationalCallingCode":"+994","smsPossible":true},"AF":{"internationalCallingCode":"+93","smsPossible":true},"US":{"internationalCallingCode":"+1","smsPossible":true},"AE":{"internationalCallingCode":"+971","smsPossible":true},"DZ":{"internationalCallingCode":"+213","smsPossible":true},"AR":{"internationalCallingCode":"+54","smsPossible":true},"AW":{"internationalCallingCode":"+297","smsPossible":true},"AL":{"internationalCallingCode":"+355","smsPossible":true},"AM":{"internationalCallingCode":"+374","smsPossible":true},"AO":{"internationalCallingCode":"+244","smsPossible":false},"AG":{"internationalCallingCode":"+1","smsPossible":true},"AD":{"internationalCallingCode":"+376","smsPossible":true},"YE":{"internationalCallingCode":"+967","smsPossible":true},"GB":{"internationalCallingCode":"+44","smsPossible":true},"IL":{"internationalCallingCode":"+972","smsPossible":true},"IT":{"internationalCallingCode":"+39","smsPossible":true},"IQ":{"internationalCallingCode":"+964","smsPossible":true},"IR":{"internationalCallingCode":"+98","smsPossible":true},"IN":{"internationalCallingCode":"+91","smsPossible":true},"ID":{"internationalCallingCode":"+62","smsPossible":true},"UG":{"internationalCallingCode":"+256","smsPossible":true},"UA":{"internationalCallingCode":"+380","smsPossible":true},"UZ":{"internationalCallingCode":"+998","smsPossible":true},"UY":{"internationalCallingCode":"+598","smsPossible":true},"EC":{"internationalCallingCode":"+593","smsPossible":true},"EG":{"internationalCallingCode":"+20","smsPossible":true},"EE":{"internationalCallingCode":"+372","smsPossible":true},"SZ":{"internationalCallingCode":"+268","smsPossible":false},"ET":{"internationalCallingCode":"+251","smsPossible":false},"ER":{"internationalCallingCode":"+291","smsPossible":true},"SV":{"internationalCallingCode":"+503","smsPossible":true},"AU":{"internationalCallingCode":"+61","smsPossible":true},"AT":{"internationalCallingCode":"+43","smsPossible":true},"OM":{"internationalCallingCode":"+968","smsPossible":true},"NL":{"internationalCallingCode":"+31","smsPossible":true},"GH":{"internationalCallingCode":"+233","smsPossible":true},"CV":{"internationalCallingCode":"+238","smsPossible":false},"GY":{"internationalCallingCode":"+592","smsPossible":true},"KZ":{"internationalCallingCode":"+7","smsPossible":true},"QA":{"internationalCallingCode":"+974","smsPossible":true},"CA":{"internationalCallingCode":"+1","smsPossible":true},"GA":{"internationalCallingCode":"+241","smsPossible":true},"CM":{"internationalCallingCode":"+237","smsPossible":true},"KR":{"internationalCallingCode":"+82","smsPossible":true},"KH":{"internationalCallingCode":"+855","smsPossible":true},"GM":{"internationalCallingCode":"+220","smsPossible":true},"KP":{"internationalCallingCode":"+850","smsPossible":false},"MK":{"internationalCallingCode":"+389","smsPossible":false},"GN":{"internationalCallingCode":"+224","smsPossible":true},"GW":{"internationalCallingCode":"+245","smsPossible":true},"CY":{"internationalCallingCode":"+357","smsPossible":true},"CU":{"internationalCallingCode":"+53","smsPossible":true},"GR":{"internationalCallingCode":"+30","smsPossible":true},"KI":{"internationalCallingCode":"+686","smsPossible":false},"KG":{"internationalCallingCode":"+996","smsPossible":true},"GT":{"internationalCallingCode":"+502","smsPossible":true},"GU":{"internationalCallingCode":"+1","smsPossible":true},"KW":{"internationalCallingCode":"+965","smsPossible":true},"KY":{"internationalCallingCode":"+1","smsPossible":true},"GD":{"internationalCallingCode":"+1","smsPossible":true},"HR":{"internationalCallingCode":"+385","smsPossible":true},"KE":{"internationalCallingCode":"+254","smsPossible":true},"CI":{"internationalCallingCode":"+225","smsPossible":true},"CR":{"internationalCallingCode":"+506","smsPossible":true},"SA":{"internationalCallingCode":"+966","smsPossible":true},"KM":{"internationalCallingCode":"+269","smsPossible":true},"CO":{"internationalCallingCode":"+57","smsPossible":true},"CG":{"internationalCallingCode":"+242","smsPossible":false},"CD":{"internationalCallingCode":"+243","smsPossible":true},"ZM":{"internationalCallingCode":"+260","smsPossible":true},"SM":{"internationalCallingCode":"+378","smsPossible":false},"SL":{"internationalCallingCode":"+232","smsPossible":true},"DJ":{"internationalCallingCode":"+253","smsPossible":false},"JM":{"internationalCallingCode":"+1","smsPossible":true},"CH":{"internationalCallingCode":"+41","smsPossible":true},"GE":{"internationalCallingCode":"+995","smsPossible":true},"SE":{"internationalCallingCode":"+46","smsPossible":true},"SY":{"internationalCallingCode":"+963","smsPossible":true},"ES":{"internationalCallingCode":"+34","smsPossible":true},"SG":{"internationalCallingCode":"+65","smsPossible":true},"LK":{"internationalCallingCode":"+94","smsPossible":true},"SK":{"internationalCallingCode":"+421","smsPossible":true},"SI":{"internationalCallingCode":"+386","smsPossible":true},"ZW":{"internationalCallingCode":"+263","smsPossible":true},"SD":{"internationalCallingCode":"+249","smsPossible":true},"KN":{"internationalCallingCode":"+1","smsPossible":true},"SR":{"internationalCallingCode":"+597","smsPossible":true},"LC":{"internationalCallingCode":"+1","smsPossible":true},"SC":{"internationalCallingCode":"+248","smsPossible":false},"SN":{"internationalCallingCode":"+221","smsPossible":true},"SB":{"internationalCallingCode":"+677","smsPossible":false},"RS":{"internationalCallingCode":"+381","smsPossible":true},"TH":{"internationalCallingCode":"+66","smsPossible":true},"TW":{"internationalCallingCode":"+886","smsPossible":true},"TJ":{"internationalCallingCode":"+992","smsPossible":true},"TZ":{"internationalCallingCode":"+255","smsPossible":true},"CZ":{"internationalCallingCode":"+420","smsPossible":true},"TD":{"internationalCallingCode":"+235","smsPossible":true},"CF":{"internationalCallingCode":"+236","smsPossible":true},"TN":{"internationalCallingCode":"+216","smsPossible":true},"CL":{"internationalCallingCode":"+56","smsPossible":true},"TV":{"internationalCallingCode":"+688","smsPossible":false},"DK":{"internationalCallingCode":"+45","smsPossible":true},"DE":{"internationalCallingCode":"+49","smsPossible":true},"TG":{"internationalCallingCode":"+228","smsPossible":true},"DO":{"internationalCallingCode":"+1","smsPossible":true},"DM":{"internationalCallingCode":"+1","smsPossible":false},"TT":{"internationalCallingCode":"+1","smsPossible":true},"TM":{"internationalCallingCode":"+993","smsPossible":false},"TR":{"internationalCallingCode":"+90","smsPossible":true},"TO":{"internationalCallingCode":"+676","smsPossible":false},"NG":{"internationalCallingCode":"+234","smsPossible":true},"NR":{"internationalCallingCode":"+674","smsPossible":false},"NA":{"internationalCallingCode":"+264","smsPossible":false},"NI":{"internationalCallingCode":"+505","smsPossible":true},"BH":{"internationalCallingCode":"+973","smsPossible":false},"NE":{"internationalCallingCode":"+227","smsPossible":true},"PK":{"internationalCallingCode":"+92","smsPossible":true},"JP":{"internationalCallingCode":"+81","smsPossible":true},"BS":{"internationalCallingCode":"+1","smsPossible":true},"NZ":{"internationalCallingCode":"+64","smsPossible":true},"BM":{"internationalCallingCode":"+1","smsPossible":true},"NP":{"internationalCallingCode":"+977","smsPossible":true},"BB":{"internationalCallingCode":"+246","smsPossible":true},"NO":{"internationalCallingCode":"+47","smsPossible":true},"HT":{"internationalCallingCode":"+509","smsPossible":true},"BD":{"internationalCallingCode":"+880","smsPossible":true},"VA":{"internationalCallingCode":"+39","smsPossible":false},"PA":{"internationalCallingCode":"+507","smsPossible":true},"VU":{"internationalCallingCode":"+678","smsPossible":false},"PG":{"internationalCallingCode":"+675","smsPossible":true},"PW":{"internationalCallingCode":"+680","smsPossible":true},"PY":{"internationalCallingCode":"+595","smsPossible":true},"PS":{"internationalCallingCode":"+970","smsPossible":true},"BR":{"internationalCallingCode":"+55","smsPossible":true},"HU":{"internationalCallingCode":"+36","smsPossible":true},"TL":{"internationalCallingCode":"+670","smsPossible":true},"BG":{"internationalCallingCode":"+359","smsPossible":true},"BF":{"internationalCallingCode":"+226","smsPossible":true},"BN":{"internationalCallingCode":"+673","smsPossible":true},"FJ":{"internationalCallingCode":"+679","smsPossible":true},"PH":{"internationalCallingCode":"+63","smsPossible":true},"FI":{"internationalCallingCode":"+358","smsPossible":true},"BT":{"internationalCallingCode":"+975","smsPossible":true},"BY":{"internationalCallingCode":"+375","smsPossible":true},"BZ":{"internationalCallingCode":"+501","smsPossible":false},"PR":{"internationalCallingCode":"+1","smsPossible":true},"BE":{"internationalCallingCode":"+32","smsPossible":true},"BA":{"internationalCallingCode":"+387","smsPossible":true},"FR":{"internationalCallingCode":"+33","smsPossible":true},"BO":{"internationalCallingCode":"+591","smsPossible":false},"PF":{"internationalCallingCode":"+689","smsPossible":true},"BI":{"internationalCallingCode":"+257","smsPossible":true},"VN":{"internationalCallingCode":"+84","smsPossible":true},"BJ":{"internationalCallingCode":"+229","smsPossible":true},"VE":{"internationalCallingCode":"+58","smsPossible":true},"PE":{"internationalCallingCode":"+51","smsPossible":true},"PL":{"internationalCallingCode":"+48","smsPossible":true},"BW":{"internationalCallingCode":"+267","smsPossible":true},"PT":{"internationalCallingCode":"+351","smsPossible":false},"HK":{"internationalCallingCode":"+852","smsPossible":true},"HN":{"internationalCallingCode":"+504","smsPossible":false},"MH":{"internationalCallingCode":"+692","smsPossible":true},"MO":{"internationalCallingCode":"+853","smsPossible":true},"MG":{"internationalCallingCode":"+261","smsPossible":true},"MW":{"internationalCallingCode":"+265","smsPossible":true},"ML":{"internationalCallingCode":"+223","smsPossible":true},"MT":{"internationalCallingCode":"+356","smsPossible":true},"MY":{"internationalCallingCode":"+60","smsPossible":true},"FM":{"internationalCallingCode":"+691","smsPossible":false},"MM":{"internationalCallingCode":"+95","smsPossible":true},"MX":{"internationalCallingCode":"+52","smsPossible":true},"MU":{"internationalCallingCode":"+230","smsPossible":true},"MR":{"internationalCallingCode":"+222","smsPossible":true},"MZ":{"internationalCallingCode":"+258","smsPossible":true},"MC":{"internationalCallingCode":"+377","smsPossible":true},"MV":{"internationalCallingCode":"+960","smsPossible":true},"MD":{"internationalCallingCode":"+373","smsPossible":false},"RO":{"internationalCallingCode":"+40","smsPossible":true},"MA":{"internationalCallingCode":"+212","smsPossible":true},"MN":{"internationalCallingCode":"+976","smsPossible":true},"ME":{"internationalCallingCode":"+382","smsPossible":true},"JO":{"internationalCallingCode":"+962","smsPossible":true},"LA":{"internationalCallingCode":"+856","smsPossible":true},"RU":{"internationalCallingCode":"+7","smsPossible":true},"LV":{"internationalCallingCode":"+371","smsPossible":true},"LT":{"internationalCallingCode":"+370","smsPossible":true},"LY":{"internationalCallingCode":"+218","smsPossible":true},"GQ":{"internationalCallingCode":"+240","smsPossible":false},"LI":{"internationalCallingCode":"+423","smsPossible":false},"LR":{"internationalCallingCode":"+231","smsPossible":true},"CN":{"internationalCallingCode":"+86","smsPossible":true},"LU":{"internationalCallingCode":"+352","smsPossible":true},"ZA":{"internationalCallingCode":"+27","smsPossible":true},"RW":{"internationalCallingCode":"+250","smsPossible":true},"LS":{"internationalCallingCode":"+266","smsPossible":false},"LB":{"internationalCallingCode":"+961","smsPossible":true}};

		$(function () {
			contactInfoInit();
		});
		
		function contactInfoInit() {
			
			
			Asw.get('representativeTelType').find('option[value="M1"]').attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_TYPE,
					Asw.ContactInput.Const.DATA_TYPE_VALUE_SMS);
			Asw.get('representativeTelCountry').children('option').each(function(i,option){
				var countryInfo = Asw.ContactInput.countryInfoMap[option.value];
				if (countryInfo === void 0) {
					
					return true;
				}
				$(option).attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_SMS_POSSIBLE, countryInfo.smsPossible);
				$(option).attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_COUNTRY_CODE, countryInfo.internationalCallingCode);
			});
			
			var smsCountrySelectSelector = 'select.js-countrySelect:not(#representativeTelCountry)'
			$(smsCountrySelectSelector).eq(0).children('option').each(function(i,option){
				var countryInfo = Asw.ContactInput.countryInfoMap[option.value];
				if (countryInfo === void 0) {
					
					return true;
				}
				$(smsCountrySelectSelector + ' option:nth-child(' + (i+1) + ')').attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_COUNTRY_CODE,
						countryInfo.internationalCallingCode);
			});
			
			var emergencyCountrySelectSelector = 'select.passengerEmergencyCountry.js-countrySelect:not(#representativeTelCountry)'
			$(emergencyCountrySelectSelector).eq(0).children('option').each(function(i,option){
				var countryInfo = Asw.ContactInput.countryInfoMap[option.value];
				if (countryInfo === void 0) {
					
					return true;
				}
				$(emergencyCountrySelectSelector + ' option:nth-child(' + (i+1) + ')').attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_COUNTRY_CODE,
						countryInfo.internationalCallingCode);
			});
		}
	</script><script type="text/javascript">
		var isInit = true;
		function changeTelType(_this, telNoInputId) {
			var $telNoInput = Asw.get(telNoInputId);
			var $telNoKind = Asw.get("representativeTelType");
			var telNo = Asw.getValueComparedToPlaceholder(telNoInputId);
			var telNoCountry = Asw.get("representativeTelCountry").val();

			if(true && !isInit) {
				if(telInputCheck(telNo, telNoCountry)) {
					if (_this.value == "M1") {
						telNo = exceptDomesticPrefix("09042504176", telNoCountry);
					} else if (_this.value == "B1") {
						telNo = exceptDomesticPrefix("03-5405-2172", telNoCountry);
					} else {
						telNo = exceptDomesticPrefix("090-4250-4176", telNoCountry);
					}
					$telNoInput.val(telNo);
				}
			}
			isInit = false;
		}
		
		function telInputCheck(telNo, telNoCountry) {
	        checkResult = false;
	        
	        var amcHndyTelNo = exceptDomesticPrefix("09042504176", telNoCountry);
	        var amcCmpTelNo = exceptDomesticPrefix("03-5405-2172", telNoCountry);
	        var amcTelNo = exceptDomesticPrefix("090-4250-4176", telNoCountry);
	
	        if(telNo == "" || telNo == amcHndyTelNo || telNo == amcCmpTelNo || telNo == amcTelNo) {
	        	checkResult = true;
	        }
	        return checkResult;
    	}
    	
    	function exceptDomesticPrefix(telNo, telNoCountry) {
    		if(telNoCountry == 'JP') {
    			if(telNo.substr(0, 1) === '0') {
    				return telNo.slice(1);
    			}
    		}
    		return telNo;
    	}
	</script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/script/asw-customerinfomationform.js?6e91953"></script><script type="text/javascript">
		$(document).ready(function(){
				Asw.enableTextWhenCheckBox('.cascadedSwitch');
				Asw.notPasteInput('.jsNotPasteInput');
			$(function(){
				Asw.alignHeightToParentTdElement( '.passengerInfoInputArea' );
				
					Asw.alignHeightToParentTdElement( '.passengerInfoInputText' );
				
				
				$('select.js-countrySelect').trigger('change');
			});
		});
	</script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/script/asw-session-keeper.js?6ec0730"></script><script type="text/javascript">
			$(document).ready(function() {
				Asw.SessionKeeper.initAndStart("sessionKeeperContainer", "520", "570", "PC");
			});
			</script><script type="text/javascript">
		Asw.addAjaxCallback(function(data) {
			// アコーディオンのメソッドと設定 
			if($('.jsAccordionSwitch .jsTriggerSwitch').length > 0){
				var events = $._data($('.jsAccordionSwitch .jsTriggerSwitch').get(0), "events");
				if(events === undefined || events.click === undefined) {
					Asw.AccordionInfo('.jsAccordionSwitch', {
						'triggerSwitch': '.jsAccordionSwitch .jsTriggerSwitch',
						'listClass': '.jsAccordionSwitchList',
						'visibilityHidden': 'toggleContents'
					});
				}
			}
		});
	</script><script type="text/javascript">
	Asw.Price = Asw.Price || {};
	// 整数の区切り文字
	Asw.Price.separateString = ',';
	
	// 整数の区切り桁数
	Asw.Price.separateDigitNumber = 3;
	
	// 小数点
	Asw.Price.decimalPointString = '.';
	
	// 小数点以降の有効桁数
	Asw.Price.significantDigit = 0;
	
	// 表示金額の単位
	Asw.Price.currencyUnit = '円';
	</script><script type="text/javascript">
			$(document).ready(function() {
				$('#preLoadingArea').css('visibility', 'hidden');
			});
		</script><noscript><img src="https://aswbe-i.ana.co.jp/akam/13/pixel_79e970a6?a=dD1lNzBjYjc4N2Y3NzRlYTJkZjlkMTU2ODdlZGExZWEzMjlkOWY5MDBiJmpzPW9mZg==" style="visibility: hidden; position: absolute; left: -999px; top: -999px;" /></noscript><script type="text/javascript"  src="/RcS9yzCRgrNnM/gY/v0Wf9fQwtpdQ/7NE3X2ruOE2SL9YE/HyNpQmYB/RWt/XOHcnHAU"></script></body>
</html>