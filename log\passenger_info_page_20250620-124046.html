<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="ja-<PERSON>" xml:lang="ja-<PERSON>">

	
	<!-- skey = 2025/06/20 13:40:46 rei22h piSLo3FSSm dljdmx+6a3  --><head id="j_idt53">
    <base href="about:blank">
    <style>
        /* Hide elements that might try to load external resources, as a fallback. */
        img, svg, video, iframe, link[rel="stylesheet"], script { display: none !important; }
    </style>

		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
			<meta http-equiv="X-UA-Compatible" content="IE=EmulateIE9; IE=EmulateIE10" />
			<meta http-equiv="X-UA-Compatible" content="IE=7" />
			<meta http-equiv="X-UA-Compatible" content="IE=edge" />
		<meta name="description" content="" />
		<meta name="keywords" content="" />
		<meta name="format-detection" content="telephone=no" />
		<script type="text/javascript" src="/international_asw/dynatrace/ruxitagentjs_ICANVfhqrux_10273230920145353.js" data-dtconfig="rid=RID_-1071566797|rpid=-*********|domain=aswbe-i.ana.co.jp|reportUrl=https://bf90419wvr.bf.dynatrace.com/bf|app=78bf0b58acf6ed13|cors=1|owasp=1|featureHash=ICANVfhqrux|xb=www^bs.google-analytics^bs.com^pana^bs.demdex^bs.net^pcollect^bs.tealiumiq^bs.com^pallnipponairways^bs.australia-3^bs.evergage^bs.com^pallnipponairwayscolt^bs.tt^bs.omtrdc^bs.net|rdnt=1|uxrgce=1|cuc=k8vpgh61|mel=100000|expw=1|dpvc=1|md=mdcc1=bAsw.PnrOutput.recordLocator,mdcc2=bAsw.PnrOutput.paymentDetailInfoList[0].fopCode,mdcc3=bAsw.PnrOutput.ticketedFlag,mdcc4=bAsw.PnrOutput.nhPnrFlag,mdcc5=bAsw.PnrOutput.itinerarySummary.departureDateYyyymmdd,mdcc6=bAsw.SearchCriteriaOutput.cffCodeInput,mdcc7=a#main ^rb div.section.sectionNoFlow ^rb table ^rb tbody ^rb tr ^rb td.flightNumber.lastCol ^rb span ^rb span:nth-child(2),mdcc8=cunique|lastModification=1750184416409|tp=500,50,0|agentUri=/international_asw/dynatrace/ruxitagentjs_ICANVfhqrux_10273230920145353.js"></script><link rel="shortcut icon" href="https://www.ana.co.jp/favicon.ico" />
		<title>お客様情報入力 | ANA</title>
		<noscript><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/css/asw_noscript_pc.css?99001fc" />
		</noscript>

		<!--  [ Adobe Target ] -->
		<script type="text/javascript" src="https://www.ana.co.jp/behavior/adobetarget/target_intbe.js"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/jquery-1.8.3.min.js?717d3c0"></script><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/ja/css/asw_initialize.css?99001fc" /><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/css/asw_screen_pc.css?99001fc" /><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/css/asw_common_pc.css?99001fc" /><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/css/asw_forminput_pc.css?99001fc" /><script type="text/javascript">
		var Asw = Asw || {};
		Asw.GlobalWord = Asw.GlobalWord || {};
		
		Asw.GlobalWord.toolTipCloseIconAltText = "閉じる";
		
		</script><script >bazadebezolkohpepadr="223621419"</script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/akam/13/d54330b"  defer></script></head><body>
		
		<noscript>当サイトをご利用になるためには、JavaScript対応のブラウザが必要です。設定でJavaScriptを有効にしてください。有効にできない場合はANA電話窓口へお問い合わせください。
		</noscript>
		<div id="preLoadingArea"></div>
	
	<div id="cmnHeader" class="platinum">
		<!--===== cmnHeader =====-->
		<div class="headerWrapper">
			<div class="header">
				<p class="logo"><a href="https://www.ana.co.jp/asw/AMCTopServlet?type=a" tabindex="-1" aria-hidden="true"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/ana_logo_navy.png?717d3c0" alt="ANA Inspiration of JAPAN。リンク先はマイレージクラブトップページです。" height="28" width="274" /></a>
				</p>
					<ul class="headerMenu">
						<li><a href="https://www.ana.co.jp/asw/AMCTopServlet?type=a">ANAマイレージクラブトップページ</a>
						</li>
						<li><a href="https://www.ana.co.jp/other/int/meta/0005.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">お問い合わせ<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
						</li>
							<li class="btnLogoutArea"><a href="https://aswbe-i.ana.co.jp/rei22h/international_asw/rest/logout" role="button" class="btnBase btnLogout">ログアウト</a>
							</li>
					</ul>
			</div>
		</div>
			<div class="amcUserInfoWrapper">
				<div class="amcUserInfo">
					<div class="userNameArea">
						<table>
							<tr>	
									<td class="statusIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_status_05.png?717d3c0" alt="プラチナサービスメンバー" height="46" width="60" />
									</td>
								<td class="userName">佐藤 直樹 <span>様</span>
								</td>
							</tr>					
						</table>
					</div>
						<div class="balance">
								<dl class="upgradePointBalance">
									<dt>アップグレードポイント残高
									</dt>
									<dd>
											<dl>
												<dt>(2025年度)
												</dt>
												<dd>
													<em>20
													</em>ポイント
												</dd>
											</dl>
									</dd>
								</dl>
							<dl class="mileBalance">
								<dt>マイル口座残高
								</dt>
								<dd><em>75,663</em><span class="currencyCode">マイル</span>
								</dd>
							</dl>
						</div>
				</div>
					<p class="nowTime">6月20日13時40分現在
					</p>
			</div>
		<!--===== /cmnHeader =====-->
	</div>
		<div id="sessionKeeperContainer" class="sessionKeepingContainer"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/bg_modalWindow.png?717d3c0" alt="" height="0" width="0" />
			<div id="extendSessionExpiredDialog" class="modalExpire modalSmall modalContainer" role="dialog" aria-labelledby="extendSessionExpiredDialogTitle" aria-hidden="true">
<form id="sessionKeeperContainer:j_idt183" name="sessionKeeperContainer:j_idt183" method="post" action="https://aswbe-i.ana.co.jp/rei22h/international_asw/pages/award/reservation/award_mandatory_passenger_information_input.xhtml?aswcid=1&amp;rand=20250620134046piSLo3FSSm" autocomplete="off" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="sessionKeeperContainer:j_idt183" value="sessionKeeperContainer:j_idt183" />
<input type="hidden" name="sessionKeeperContainer:j_idt183_operationTicket" value="dljdmx+6a365d7f0f6638c20e3ce717bee9817c~VM0aLfFKvCQlMe5MTEVkCU9RNPxPX1rzfjxCbi3A!1750394437140.aere-xml-controller-67d4778877-tmwgp" /><input type="hidden" name="sessionKeeperContainer:j_idt183_cmnPageTicket" value="4" />
					<h1 id="extendSessionExpiredDialogTitle" class="dialogTitle">まもなくセッションが終了します</h1>
					<div id="extendSessionExpiredDialogMessage" class="dialogMessage" tabindex="-1">このままお手続きを進める場合は、［延長］ボタンを押してください。</div>
					<p class="modalButton"><input id="sessionKeeperContainer:cmnSessionKeepingButton" type="submit" name="sessionKeeperContainer:cmnSessionKeepingButton" value="延長" class="btnBase btnModal btnMainStream" onclick="mojarra.ab(this,event,'action',0,0,{'onevent':function(data) {Asw.doCommonAjaxCallback(data,false, event); Asw.SessionKeeper.doSessionKeep(data);},'onerror':function(data) {Asw.doCommonAjaxErrorCallback(data); }});return false" />
					</p><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:0" value="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" autocomplete="off" />
</form>
			</div>
			<div id="showSessionExpiredDialog" class="modalExpire modalSmall modalContainer" role="dialog" aria-labelledby="showSessionExpiredDialogTitle" aria-hidden="true">
				<h1 id="showSessionExpiredDialogTitle" class="dialogTitle">セッションが終了しました</h1>
				<div id="showSessionExpiredDialogMessage" class="dialogMessage" tabindex="-1">一定時間操作されなかったため、お手続きを進めることができません。お手数ですが、再度トップページからお手続きください。</div>
				<p class="modalButton">
					<input type="submit" value="閉じる" class="btnBase btnModal btnMainStream" onclick="Asw.SessionKeeper.closeTimeoutDialog(); return false;" />
				</p>
			</div>
		</div>
		<!--===== dialogMessages =====--><div id="cmnModalMessages"></div>
		<div id="prebookConfirmDialog" class="modalContainer modalConfirm modalSmall " aria-hidden="true" role="dialog">
<form id="prebookConfirmDialog:j_idt403" name="prebookConfirmDialog:j_idt403" method="post" action="https://aswbe-i.ana.co.jp/rei22h/international_asw/pages/award/reservation/award_mandatory_passenger_information_input.xhtml?aswcid=1&amp;rand=20250620134046piSLo3FSSm" autocomplete="off" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="prebookConfirmDialog:j_idt403" value="prebookConfirmDialog:j_idt403" />
<input type="hidden" name="prebookConfirmDialog:j_idt403_operationTicket" value="dljdmx+6a365d7f0f6638c20e3ce717bee9817c~VM0aLfFKvCQlMe5MTEVkCU9RNPxPX1rzfjxCbi3A!1750394437140.aere-xml-controller-67d4778877-tmwgp" /><input type="hidden" name="prebookConfirmDialog:j_idt403_cmnPageTicket" value="4" /><div class="dialogMessage" tabindex="0"><p>この後、名・姓の修正や変更ができません。</p><p>「搭乗者選択」欄の氏名が「名」「姓」の順番で表示されていることを再確認ください。</p><p>例）<strong class="highlight">空野 太郎</strong> 様 → MR <strong class="highlight">TARO SORANO</strong></p><p>保安上の理由により、航空券とパスポートのお名前が同じ表記でない場合はご搭乗いただくことができませんので必ず再度ご確認ください。</p></div>
					
					<ul class="modalBtnHorizontal">
							
							<li><input type="submit" name="prebookConfirmDialog:j_idt419" value="戻る" aria-controls="prebookConfirmDialog" class="btnBase btnModal btnCancel" onclick="Asw.Dialog.getInstance('prebookConfirmDialog').close(event, Asw.DialogSequencer.isActiveSequencerNotDisplayedLastDialog());  return false;" />
							</li>
						
						<li class="btnArrowNext"><input type="submit" name="prebookConfirmDialog:j_idt422" value="OK" aria-controls="prebookConfirmDialog" class="btnBase btnModal btnMainStream " onclick="Asw.Dialog.getInstance('prebookConfirmDialog').callOpener('NORMAL', event); return false;" />
						</li>
					</ul><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:1" value="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" autocomplete="off" />
</form>
		</div>
		<!--===== /dialogMessages =====--><div id="cmnContainer">
		<!--=========== .cmnContainer ============--><div id="cmnEmergencyNotice"></div><div id="cmnTitle" class="hgroup ">
		<h1 class="visuallyHidden">お客様情報入力</h1></div>

		<div id="cmnWrapper">
		<!--===== cmnWrapper =====-->
		<ol class="mainFlow" aria-label="完了までの手順、">
			<li><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_flow_01.png?717d3c0" alt="1" height="20" width="28" />フライト検索</li><!--
			--><li><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_flow_02_on.png?717d3c0" alt="2" height="20" width="28" /><span class="visuallyHidden">現在のステップ、</span>お客様情報入力</li><!--
			--><li><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_flow_03.png?717d3c0" alt="3" height="20" width="28" />お支払い情報</li><!--
			--><li class="flowLast"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_flow4_04.png?717d3c0" alt="4" height="20" width="28" />完了</li>
		</ol>

	<div id="main">
	<!--=========== main =============-->
		<ul class="iconExample">
				<li class="requiredIconExample">
					<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="" height="7" width="7" /></span>必須項目です	
				</li>
		</ul>
<form id="j_idt526" name="j_idt526" method="post" action="https://aswbe-i.ana.co.jp/rei22h/international_asw/pages/award/reservation/award_mandatory_passenger_information_input.xhtml?aswcid=1&amp;rand=20250620134046piSLo3FSSm" autocomplete="off" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="j_idt526" value="j_idt526" />
<input type="hidden" name="j_idt526_operationTicket" value="dljdmx+6a365d7f0f6638c20e3ce717bee9817c~VM0aLfFKvCQlMe5MTEVkCU9RNPxPX1rzfjxCbi3A!1750394437140.aere-xml-controller-67d4778877-tmwgp" /><input type="hidden" name="j_idt526_cmnPageTicket" value="4" />

<div class="section">
	<h2>搭乗者選択<a href="https://www.ana.co.jp/other/int/meta/0086.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="特典のご利用に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a></h2>
	<table class="passengerInfo">
		<thead>
			<tr>
				
				<th class="name">氏名</th>
				
				<th class="dateOfBirthWide" id="dateOfBirthHeader">生年月日</th>
				
				<th class="gender lastCol" id="genderHeader">性別<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span></th>
				
			</tr>
		</thead>
		<tbody>
				
				<tr>
					<th class="parent_01" id="parentHeader01">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:0:passenger" type="checkbox" name="passengers:0:passenger" checked="checked" data-number="1" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:0:passenger">NAOKI SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader01">1963年06月05日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader01">				
							<span class="formWrapper">
							<label for="passengers:0:gender" class="visuallyHidden">NAOKI SATOの性別</label><select id="passengers:0:gender" name="passengers:0:gender" size="1">	<option value="">選択してください</option>
	<option value="M" selected="selected">男性</option>
	<option value="F">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_02" id="parentHeader02">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:1:passenger" type="checkbox" name="passengers:1:passenger" data-number="2" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:1:passenger">REO SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader02">1995年05月18日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader02">				
							<span class="formWrapper">
							<label for="passengers:1:gender" class="visuallyHidden">REO SATOの性別</label><select id="passengers:1:gender" name="passengers:1:gender" size="1">	<option value="">選択してください</option>
	<option value="M" selected="selected">男性</option>
	<option value="F">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_03" id="parentHeader03">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:2:passenger" type="checkbox" name="passengers:2:passenger" data-number="3" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:2:passenger">ERI SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader03">1963年08月14日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader03">				
							<span class="formWrapper">
							<label for="passengers:2:gender" class="visuallyHidden">ERI SATOの性別</label><select id="passengers:2:gender" name="passengers:2:gender" size="1">	<option value="">選択してください</option>
	<option value="M">男性</option>
	<option value="F" selected="selected">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_04" id="parentHeader04">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:3:passenger" type="checkbox" name="passengers:3:passenger" data-number="4" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:3:passenger">TAISHI SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader04">1992年12月03日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader04">				
							<span class="formWrapper">
							<label for="passengers:3:gender" class="visuallyHidden">TAISHI SATOの性別</label><select id="passengers:3:gender" name="passengers:3:gender" size="1">	<option value="">選択してください</option>
	<option value="M" selected="selected">男性</option>
	<option value="F">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_05" id="parentHeader05">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:4:passenger" type="checkbox" name="passengers:4:passenger" data-number="5" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:4:passenger">HIROMI FUJIMOTO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader05">1961年11月23日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader05">				
							<span class="formWrapper">
							<label for="passengers:4:gender" class="visuallyHidden">HIROMI FUJIMOTOの性別</label><select id="passengers:4:gender" name="passengers:4:gender" size="1">	<option value="">選択してください</option>
	<option value="M">男性</option>
	<option value="F" selected="selected">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
		</tbody>
	</table>
</div>
		<div class="section">
			<h2 class="midstream">連絡先情報
			</h2>
			<h3>代表者
			</h3>
	
	
	
	<div class="formInput">
		<fieldset>
			<legend><label>代表者</label></legend>
			<dl id="contactEmailAddressInfo">
				<dt class="hasRequiredIcon">メールアドレス<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span><a href="https://www.ana.co.jp/other/int/meta/0057.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="メールの宛先と内容に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a>
				</dt>
				<dd>
					<ul class="verticalList">
						<li class="nonMarginTop"><label for="representativeEmail" class="visuallyHidden">代表者メールアドレス</label><input id="representativeEmail" type="text" name="representativeEmail" value="<EMAIL>" class="widthLarge" placeholder="例：<EMAIL>" />
						</li>
						<li>
							<span class="supplementTxt" id="confirmMailAddressForRepresentative">確認のため再度ご入力ください。
							</span><label for="confirmrepresentativeEmail" class="visuallyHidden">確認用メールアドレス</label><input id="confirmrepresentativeEmail" type="text" name="confirmrepresentativeEmail" value="<EMAIL>" class="widthLarge jsNotPasteInput" placeholder="例：<EMAIL>" aria-describedby="confirmMailAddressForRepresentative" />
						</li>
					</ul>
				</dd>
					<dd style="display:none;"><input id="updateAlwaysInfo" type="checkbox" name="updateAlwaysInfo" /><label for="updateAlwaysInfo">ログイン中の会員情報へこのメールアドレスを登録</label>
					</dd>
			</dl>
			<dl>
				<dt>電話番号<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span>
				</dt>
				<dd class="js-countryWrap">
					<ul>
						<li><label for="representativeTelType" class="visuallyHidden">電話番号種別</label><select id="representativeTelType" name="representativeTelType" class="basicWidth js-typeSelect" size="1" onchange="changeTelType(this,'representativeTel')">	<option value="M1" selected="selected">携帯</option>
	<option value="B1">会社</option>
	<option value="H1">自宅</option>
</select>
						</li>
						<li><label for="representativeTelCountry" class="visuallyHidden">電話番号の国/地区選択</label><select id="representativeTelCountry" name="representativeTelCountry" class="js-countrySelect" size="1">	<option value="" data-sms-possible="true" data-country-code="---">選択してください</option>
	<option value="JP" selected="selected" data-sms-possible="true">日本</option>
	<option value="-" data-sms-possible="true" data-country-code="---">-----------</option>
	<option value="IS" data-sms-possible="true">アイスランド</option>
	<option value="IE" data-sms-possible="true">アイルランド</option>
	<option value="AZ" data-sms-possible="true">アゼルバイジャン</option>
	<option value="AF" data-sms-possible="true">アフガニスタン</option>
	<option value="US" data-sms-possible="true">アメリカ合衆国/カナダ</option>
	<option value="AE" data-sms-possible="true">アラブ首長国連邦</option>
	<option value="DZ" data-sms-possible="true">アルジェリア</option>
	<option value="AR" data-sms-possible="true">アルゼンチン</option>
	<option value="AW" data-sms-possible="true">アルバ</option>
	<option value="AL" data-sms-possible="true">アルバニア</option>
	<option value="AM" data-sms-possible="true">アルメニア</option>
	<option value="AO" data-sms-possible="true">アンゴラ</option>
	<option value="AG" data-sms-possible="true">アンティグア・バーブーダ</option>
	<option value="AD" data-sms-possible="true">アンドラ</option>
	<option value="YE" data-sms-possible="true">イエメン</option>
	<option value="GB" data-sms-possible="true">イギリス</option>
	<option value="IL" data-sms-possible="true">イスラエル</option>
	<option value="IT" data-sms-possible="true">イタリア</option>
	<option value="IQ" data-sms-possible="true">イラク</option>
	<option value="IR" data-sms-possible="true">イラン</option>
	<option value="IN" data-sms-possible="true">インド</option>
	<option value="ID" data-sms-possible="true">インドネシア</option>
	<option value="UG" data-sms-possible="true">ウガンダ</option>
	<option value="UA" data-sms-possible="true">ウクライナ</option>
	<option value="UZ" data-sms-possible="true">ウズベキスタン</option>
	<option value="UY" data-sms-possible="true">ウルグアイ</option>
	<option value="EC" data-sms-possible="true">エクアドル</option>
	<option value="EG" data-sms-possible="true">エジプト</option>
	<option value="EE" data-sms-possible="true">エストニア</option>
	<option value="SZ" data-sms-possible="true">エスワティニ</option>
	<option value="ET" data-sms-possible="true">エチオピア</option>
	<option value="ER" data-sms-possible="true">エリトリア</option>
	<option value="SV" data-sms-possible="true">エルサルバドル</option>
	<option value="AU" data-sms-possible="true">オーストラリア</option>
	<option value="AT" data-sms-possible="true">オーストリア</option>
	<option value="OM" data-sms-possible="true">オマーン</option>
	<option value="NL" data-sms-possible="true">オランダ</option>
	<option value="GH" data-sms-possible="true">ガーナ</option>
	<option value="CV" data-sms-possible="true">カーボベルデ</option>
	<option value="GY" data-sms-possible="true">ガイアナ</option>
	<option value="KZ" data-sms-possible="true">カザフスタン</option>
	<option value="QA" data-sms-possible="true">カタール</option>
	<option value="CA" data-sms-possible="true">カナダ/アメリカ合衆国</option>
	<option value="GA" data-sms-possible="true">ガボン</option>
	<option value="CM" data-sms-possible="true">カメルーン</option>
	<option value="KR" data-sms-possible="true">韓国</option>
	<option value="GM" data-sms-possible="true">ガンビア</option>
	<option value="KH" data-sms-possible="true">カンボジア</option>
	<option value="KP" data-sms-possible="true">北朝鮮</option>
	<option value="MK" data-sms-possible="true">北マケドニア</option>
	<option value="GN" data-sms-possible="true">ギニア</option>
	<option value="GW" data-sms-possible="true">ギニアビサウ</option>
	<option value="CY" data-sms-possible="true">キプロス</option>
	<option value="CU" data-sms-possible="true">キューバ</option>
	<option value="GR" data-sms-possible="true">ギリシャ</option>
	<option value="KI" data-sms-possible="true">キリバス</option>
	<option value="KG" data-sms-possible="true">キルギス</option>
	<option value="GT" data-sms-possible="true">グアテマラ</option>
	<option value="GU" data-sms-possible="true">グアム</option>
	<option value="KW" data-sms-possible="true">クウェート</option>
	<option value="GD" data-sms-possible="true">グレナダ</option>
	<option value="HR" data-sms-possible="true">クロアチア</option>
	<option value="KY" data-sms-possible="true">ケイマン諸島</option>
	<option value="KE" data-sms-possible="true">ケニア</option>
	<option value="CI" data-sms-possible="true">コートジボワール</option>
	<option value="CR" data-sms-possible="true">コスタリカ</option>
	<option value="KM" data-sms-possible="true">コモロ</option>
	<option value="CO" data-sms-possible="true">コロンビア</option>
	<option value="CG" data-sms-possible="true">コンゴ共和国</option>
	<option value="CD" data-sms-possible="true">コンゴ民主共和国</option>
	<option value="SA" data-sms-possible="true">サウジアラビア</option>
	<option value="ZM" data-sms-possible="true">ザンビア</option>
	<option value="SM" data-sms-possible="true">サンマリノ</option>
	<option value="SL" data-sms-possible="true">シエラレオネ</option>
	<option value="DJ" data-sms-possible="true">ジブチ</option>
	<option value="JM" data-sms-possible="true">ジャマイカ</option>
	<option value="GE" data-sms-possible="true">ジョージア</option>
	<option value="SY" data-sms-possible="true">シリア</option>
	<option value="SG" data-sms-possible="true">シンガポール</option>
	<option value="ZW" data-sms-possible="true">ジンバブエ</option>
	<option value="CH" data-sms-possible="true">スイス</option>
	<option value="SE" data-sms-possible="true">スウェーデン</option>
	<option value="SD" data-sms-possible="true">スーダン</option>
	<option value="ES" data-sms-possible="true">スペイン</option>
	<option value="SR" data-sms-possible="true">スリナム</option>
	<option value="LK" data-sms-possible="true">スリランカ</option>
	<option value="SK" data-sms-possible="true">スロバキア</option>
	<option value="SI" data-sms-possible="true">スロベニア</option>
	<option value="SC" data-sms-possible="true">セイシェル</option>
	<option value="GQ" data-sms-possible="true">赤道ギニア</option>
	<option value="SN" data-sms-possible="true">セネガル</option>
	<option value="RS" data-sms-possible="true">セルビア</option>
	<option value="KN" data-sms-possible="true">セントクリストファー・ネイビス</option>
	<option value="LC" data-sms-possible="true">セントルシア</option>
	<option value="SB" data-sms-possible="true">ソロモン諸島</option>
	<option value="TH" data-sms-possible="true">タイ</option>
	<option value="TW" data-sms-possible="true">台湾</option>
	<option value="TJ" data-sms-possible="true">タジキスタン</option>
	<option value="TZ" data-sms-possible="true">タンザニア</option>
	<option value="CZ" data-sms-possible="true">チェコ</option>
	<option value="TD" data-sms-possible="true">チャド</option>
	<option value="CF" data-sms-possible="true">中央アフリカ共和国</option>
	<option value="CN" data-sms-possible="true">中国</option>
	<option value="TN" data-sms-possible="true">チュニジア</option>
	<option value="CL" data-sms-possible="true">チリ</option>
	<option value="TV" data-sms-possible="true">ツバル</option>
	<option value="DK" data-sms-possible="true">デンマーク</option>
	<option value="DE" data-sms-possible="true">ドイツ</option>
	<option value="TG" data-sms-possible="true">トーゴ</option>
	<option value="DO" data-sms-possible="true">ドミニカ共和国</option>
	<option value="DM" data-sms-possible="true">ドミニカ国</option>
	<option value="TT" data-sms-possible="true">トリニダード・トバゴ</option>
	<option value="TM" data-sms-possible="true">トルクメニスタン</option>
	<option value="TR" data-sms-possible="true">トルコ</option>
	<option value="TO" data-sms-possible="true">トンガ</option>
	<option value="NG" data-sms-possible="true">ナイジェリア</option>
	<option value="NR" data-sms-possible="true">ナウル</option>
	<option value="NA" data-sms-possible="true">ナミビア</option>
	<option value="NI" data-sms-possible="true">ニカラグア</option>
	<option value="NE" data-sms-possible="true">ニジェール</option>
	<option value="NZ" data-sms-possible="true">ニュージーランド</option>
	<option value="NP" data-sms-possible="true">ネパール</option>
	<option value="NO" data-sms-possible="true">ノルウェー</option>
	<option value="BH" data-sms-possible="true">バーレーン</option>
	<option value="HT" data-sms-possible="true">ハイチ</option>
	<option value="PK" data-sms-possible="true">パキスタン</option>
	<option value="VA" data-sms-possible="true">バチカン</option>
	<option value="PA" data-sms-possible="true">パナマ</option>
	<option value="VU" data-sms-possible="true">バヌアツ</option>
	<option value="BS" data-sms-possible="true">バハマ</option>
	<option value="PG" data-sms-possible="true">パプアニューギニア</option>
	<option value="BM" data-sms-possible="true">バミューダ</option>
	<option value="PW" data-sms-possible="true">パラオ</option>
	<option value="PY" data-sms-possible="true">パラグアイ</option>
	<option value="BB" data-sms-possible="true">バルバドス</option>
	<option value="PS" data-sms-possible="true">パレスチナ</option>
	<option value="HU" data-sms-possible="true">ハンガリー</option>
	<option value="BD" data-sms-possible="true">バングラデシュ</option>
	<option value="TL" data-sms-possible="true">東ティモール</option>
	<option value="FJ" data-sms-possible="true">フィジー</option>
	<option value="PH" data-sms-possible="true">フィリピン</option>
	<option value="FI" data-sms-possible="true">フィンランド</option>
	<option value="BT" data-sms-possible="true">ブータン</option>
	<option value="PR" data-sms-possible="true">プエルトリコ</option>
	<option value="BR" data-sms-possible="true">ブラジル</option>
	<option value="FR" data-sms-possible="true">フランス</option>
	<option value="PF" data-sms-possible="true">フランス領ポリネシア</option>
	<option value="BG" data-sms-possible="true">ブルガリア</option>
	<option value="BF" data-sms-possible="true">ブルキナファソ</option>
	<option value="BN" data-sms-possible="true">ブルネイ</option>
	<option value="BI" data-sms-possible="true">ブルンジ</option>
	<option value="VN" data-sms-possible="true">ベトナム</option>
	<option value="BJ" data-sms-possible="true">ベナン</option>
	<option value="VE" data-sms-possible="true">ベネズエラ</option>
	<option value="BY" data-sms-possible="true">ベラルーシ</option>
	<option value="BZ" data-sms-possible="true">ベリーズ</option>
	<option value="PE" data-sms-possible="true">ペルー</option>
	<option value="BE" data-sms-possible="true">ベルギー</option>
	<option value="PL" data-sms-possible="true">ポーランド</option>
	<option value="BA" data-sms-possible="true">ボスニア・ヘルツェゴビナ</option>
	<option value="BW" data-sms-possible="true">ボツワナ</option>
	<option value="BO" data-sms-possible="true">ボリビア</option>
	<option value="PT" data-sms-possible="true">ポルトガル</option>
	<option value="HK" data-sms-possible="true">香港</option>
	<option value="HN" data-sms-possible="true">ホンジュラス</option>
	<option value="MH" data-sms-possible="true">マーシャル諸島</option>
	<option value="MO" data-sms-possible="true">マカオ</option>
	<option value="MG" data-sms-possible="true">マダガスカル</option>
	<option value="MW" data-sms-possible="true">マラウイ</option>
	<option value="ML" data-sms-possible="true">マリ</option>
	<option value="MT" data-sms-possible="true">マルタ</option>
	<option value="MY" data-sms-possible="true">マレーシア</option>
	<option value="FM" data-sms-possible="true">ミクロネシア連邦</option>
	<option value="ZA" data-sms-possible="true">南アフリカ</option>
	<option value="MM" data-sms-possible="true">ミャンマー</option>
	<option value="MX" data-sms-possible="true">メキシコ</option>
	<option value="MU" data-sms-possible="true">モーリシャス</option>
	<option value="MR" data-sms-possible="true">モーリタニア</option>
	<option value="MZ" data-sms-possible="true">モザンビーク</option>
	<option value="MC" data-sms-possible="true">モナコ</option>
	<option value="MV" data-sms-possible="true">モルディヴ</option>
	<option value="MD" data-sms-possible="true">モルドバ</option>
	<option value="MA" data-sms-possible="true">モロッコ</option>
	<option value="MN" data-sms-possible="true">モンゴル</option>
	<option value="ME" data-sms-possible="true">モンテネグロ</option>
	<option value="JO" data-sms-possible="true">ヨルダン</option>
	<option value="LA" data-sms-possible="true">ラオス人民民主共和国</option>
	<option value="LV" data-sms-possible="true">ラトビア</option>
	<option value="LT" data-sms-possible="true">リトアニア</option>
	<option value="LY" data-sms-possible="true">リビア</option>
	<option value="LI" data-sms-possible="true">リヒテンシュタイン</option>
	<option value="LR" data-sms-possible="true">リベリア</option>
	<option value="RO" data-sms-possible="true">ルーマニア</option>
	<option value="LU" data-sms-possible="true">ルクセンブルク</option>
	<option value="RW" data-sms-possible="true">ルワンダ</option>
	<option value="LS" data-sms-possible="true">レソト</option>
	<option value="LB" data-sms-possible="true">レバノン</option>
	<option value="RU" data-sms-possible="true">ロシア</option>
</select>
						</li>
						<li class="numberInputWrap">
							<span id="numberInputInfo1" class="supplementTxt marginTop">先頭に0(ゼロ)がある場合、0(ゼロ)を抜いてください(一部、国・地域除く)。携帯電話番号の場合は、携帯電話を契約された国を選択ください。</span>
							<div class="numberRowWrap">
								<div class="block">
									
									<span class="visuallyHidden">国番号</span>
									<span class="viewInput widthMini js-countryCode"></span>
								</div>
								<div class="block"><label for="representativeTel" class="visuallyHidden">電話番号</label><input id="representativeTel" type="text" name="representativeTel" value="9042504176" class="widthMiddle" placeholder="例：9012345678" maxlength="30" aria-describedby="numberInputInfo1" />
								</div>
							</div>
						</li>
					</ul>
				</dd>
			</dl>
		</fieldset>
	</div>
	<div class="titleWrapper">
		<h3>
			<span class="text">eメール・SMSに関するご案内<a href="https://www.ana.co.jp/other/int/meta/0057.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="eメール・SMSに関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a></span>
		</h3>
	</div>
	<div class="formInput">
	<h4 class="formAreaTitle">主に搭乗・運航に関わるeメールの送付先設定（米国へ通知いたします）<a href="https://www.ana.co.jp/other/int/meta/0629.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="米国入国用eメール・SMS・電話番号に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a>
	</h4>
	<div class="paxSettingArea js-mailFormWrap onePax is-open">
			<div class="settingArea">
					<dl>
						<dt class="breakWord">NAOKI SATO
						</dt>
						<dd>
							<fieldset>
								<legend>NAOKI SATOのeメール送付先および米国到着時に提供するメールアドレス</legend>代表者と同じ
							</fieldset>
						</dd>
					</dl>
			</div>
	</div>
	<h4 class="formAreaTitle hasTopLine">主に搭乗・運航に関わるSMSの送付先および米国で受信できる電話番号の設定（米国へ通知いたします）<a href="https://www.ana.co.jp/other/int/meta/0629.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="米国入国用eメール・SMS・電話番号に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a>
	</h4>
	<div class="paxSettingArea js-smsFormWrap onePax is-open">
			<div class="settingArea">
					<dl>
						<dt class="breakWord">NAOKI SATO<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span>
						</dt>
						<dd>
							<fieldset>
								<legend>NAOKI SATOの1つ目の電話番号</legend>
									<ul class="verticalList js-formItem">
										<li><input type="radio" id="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSameAsRepresentativeSms:radioItem" name="contactsSms:0:recipientSmsEachPersonSelection:radioGroup" value="0" checked="checked" /><label for="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSameAsRepresentativeSms:radioItem">代表者電話番号と同じ</label>
										</li>
										<li><input type="radio" id="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSmsPerPassenger:radioItem" name="contactsSms:0:recipientSmsEachPersonSelection:radioGroup" value="1" /><label for="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSmsPerPassenger:radioItem">電話番号を指定</label>
											<div class="indentArea js-formImputArea">
												<span id="confirmTelNumberForRepresentative:0" class="supplementTxt marginTop">先頭に0(ゼロ)がある場合、0(ゼロ)を抜いてください(一部、国・地域除く)。携帯電話番号の場合は、携帯電話を契約された国を選択ください。</span>
												<ul class="countryNumberInput js-countryWrap">
													<li><label for="contactsSms:0:recipientSmsEachPersonSelection:passengerSmsCountry" class="visuallyHidden">電話番号の国/地区選択</label><select id="contactsSms:0:recipientSmsEachPersonSelection:passengerSmsCountry" name="contactsSms:0:recipientSmsEachPersonSelection:passengerSmsCountry" class="countrySelect js-countrySelect" size="1">	<option value="" data-country-code="---">選択してください</option>
	<option value="JP">日本</option>
	<option value="-" data-country-code="---">-----------</option>
	<option value="IE">アイルランド</option>
	<option value="AZ">アゼルバイジャン</option>
	<option value="AF">アフガニスタン</option>
	<option value="US">アメリカ合衆国/カナダ</option>
	<option value="AE">アラブ首長国連邦</option>
	<option value="DZ">アルジェリア</option>
	<option value="AR">アルゼンチン</option>
	<option value="AW">アルバ</option>
	<option value="AL">アルバニア</option>
	<option value="AM">アルメニア</option>
	<option value="AG">アンティグア・バーブーダ</option>
	<option value="AD">アンドラ</option>
	<option value="YE">イエメン</option>
	<option value="GB">イギリス</option>
	<option value="IL">イスラエル</option>
	<option value="IT">イタリア</option>
	<option value="IQ">イラク</option>
	<option value="IR">イラン</option>
	<option value="IN">インド</option>
	<option value="ID">インドネシア</option>
	<option value="UG">ウガンダ</option>
	<option value="UA">ウクライナ</option>
	<option value="UZ">ウズベキスタン</option>
	<option value="UY">ウルグアイ</option>
	<option value="EC">エクアドル</option>
	<option value="EG">エジプト</option>
	<option value="EE">エストニア</option>
	<option value="ER">エリトリア</option>
	<option value="SV">エルサルバドル</option>
	<option value="AU">オーストラリア</option>
	<option value="AT">オーストリア</option>
	<option value="OM">オマーン</option>
	<option value="NL">オランダ</option>
	<option value="GH">ガーナ</option>
	<option value="GY">ガイアナ</option>
	<option value="KZ">カザフスタン</option>
	<option value="QA">カタール</option>
	<option value="CA">カナダ/アメリカ合衆国</option>
	<option value="GA">ガボン</option>
	<option value="CM">カメルーン</option>
	<option value="KR">韓国</option>
	<option value="GM">ガンビア</option>
	<option value="KH">カンボジア</option>
	<option value="GN">ギニア</option>
	<option value="GW">ギニアビサウ</option>
	<option value="CY">キプロス</option>
	<option value="CU">キューバ</option>
	<option value="GR">ギリシャ</option>
	<option value="KG">キルギス</option>
	<option value="GT">グアテマラ</option>
	<option value="GU">グアム</option>
	<option value="KW">クウェート</option>
	<option value="GD">グレナダ</option>
	<option value="HR">クロアチア</option>
	<option value="KY">ケイマン諸島</option>
	<option value="KE">ケニア</option>
	<option value="CI">コートジボワール</option>
	<option value="CR">コスタリカ</option>
	<option value="KM">コモロ</option>
	<option value="CO">コロンビア</option>
	<option value="CD">コンゴ民主共和国</option>
	<option value="SA">サウジアラビア</option>
	<option value="ZM">ザンビア</option>
	<option value="SL">シエラレオネ</option>
	<option value="JM">ジャマイカ</option>
	<option value="GE">ジョージア</option>
	<option value="SY">シリア</option>
	<option value="SG">シンガポール</option>
	<option value="ZW">ジンバブエ</option>
	<option value="CH">スイス</option>
	<option value="SE">スウェーデン</option>
	<option value="SD">スーダン</option>
	<option value="ES">スペイン</option>
	<option value="SR">スリナム</option>
	<option value="LK">スリランカ</option>
	<option value="SK">スロバキア</option>
	<option value="SI">スロベニア</option>
	<option value="SN">セネガル</option>
	<option value="RS">セルビア</option>
	<option value="KN">セントクリストファー・ネイビス</option>
	<option value="LC">セントルシア</option>
	<option value="TH">タイ</option>
	<option value="TW">台湾</option>
	<option value="TJ">タジキスタン</option>
	<option value="TZ">タンザニア</option>
	<option value="CZ">チェコ</option>
	<option value="TD">チャド</option>
	<option value="CF">中央アフリカ共和国</option>
	<option value="CN">中国</option>
	<option value="TN">チュニジア</option>
	<option value="CL">チリ</option>
	<option value="DK">デンマーク</option>
	<option value="DE">ドイツ</option>
	<option value="TG">トーゴ</option>
	<option value="DO">ドミニカ共和国</option>
	<option value="TT">トリニダード・トバゴ</option>
	<option value="TR">トルコ</option>
	<option value="NG">ナイジェリア</option>
	<option value="NI">ニカラグア</option>
	<option value="NE">ニジェール</option>
	<option value="NZ">ニュージーランド</option>
	<option value="NP">ネパール</option>
	<option value="NO">ノルウェー</option>
	<option value="HT">ハイチ</option>
	<option value="PK">パキスタン</option>
	<option value="PA">パナマ</option>
	<option value="BS">バハマ</option>
	<option value="PG">パプアニューギニア</option>
	<option value="BM">バミューダ</option>
	<option value="PW">パラオ</option>
	<option value="PY">パラグアイ</option>
	<option value="BB">バルバドス</option>
	<option value="PS">パレスチナ</option>
	<option value="HU">ハンガリー</option>
	<option value="BD">バングラデシュ</option>
	<option value="TL">東ティモール</option>
	<option value="FJ">フィジー</option>
	<option value="PH">フィリピン</option>
	<option value="FI">フィンランド</option>
	<option value="BT">ブータン</option>
	<option value="PR">プエルトリコ</option>
	<option value="BR">ブラジル</option>
	<option value="FR">フランス</option>
	<option value="PF">フランス領ポリネシア</option>
	<option value="BG">ブルガリア</option>
	<option value="BF">ブルキナファソ</option>
	<option value="BN">ブルネイ</option>
	<option value="BI">ブルンジ</option>
	<option value="VN">ベトナム</option>
	<option value="BJ">ベナン</option>
	<option value="VE">ベネズエラ</option>
	<option value="BY">ベラルーシ</option>
	<option value="PE">ペルー</option>
	<option value="BE">ベルギー</option>
	<option value="PL">ポーランド</option>
	<option value="BA">ボスニア・ヘルツェゴビナ</option>
	<option value="BW">ボツワナ</option>
	<option value="HK">香港</option>
	<option value="MH">マーシャル諸島</option>
	<option value="MO">マカオ</option>
	<option value="MG">マダガスカル</option>
	<option value="MW">マラウイ</option>
	<option value="ML">マリ</option>
	<option value="MT">マルタ</option>
	<option value="MY">マレーシア</option>
	<option value="ZA">南アフリカ</option>
	<option value="MM">ミャンマー</option>
	<option value="MX">メキシコ</option>
	<option value="MU">モーリシャス</option>
	<option value="MR">モーリタニア</option>
	<option value="MZ">モザンビーク</option>
	<option value="MC">モナコ</option>
	<option value="MV">モルディヴ</option>
	<option value="MA">モロッコ</option>
	<option value="MN">モンゴル</option>
	<option value="ME">モンテネグロ</option>
	<option value="JO">ヨルダン</option>
	<option value="LA">ラオス人民民主共和国</option>
	<option value="LV">ラトビア</option>
	<option value="LT">リトアニア</option>
	<option value="LY">リビア</option>
	<option value="LR">リベリア</option>
	<option value="RO">ルーマニア</option>
	<option value="LU">ルクセンブルク</option>
	<option value="RW">ルワンダ</option>
	<option value="LB">レバノン</option>
	<option value="RU">ロシア</option>
</select>
													</li>
													<li>
														
														<div class="numberRowWrap">
															<div class="block">
																<span class="visuallyHidden">国番号</span>
																<span class="countryCode viewInput js-countryCode"></span>
															</div>
															
															<div class="block"><label for="contactsSms:0:recipientSmsEachPersonSelection:flightStatusNotificationContactPointSmsDescription" class="visuallyHidden">電話番号</label><input id="contactsSms:0:recipientSmsEachPersonSelection:flightStatusNotificationContactPointSmsDescription" type="text" name="contactsSms:0:recipientSmsEachPersonSelection:flightStatusNotificationContactPointSmsDescription" class="telNumber" placeholder="例：9012345678" maxlength="30" aria-describedby="confirmTelNumberForRepresentative:0" />
															</div>
														</div>
													</li>
												</ul>
											</div>
										</li>
									</ul>
							</fieldset>
						</dd>
					</dl>
			</div>
	</div>
	</div>
	
	<div class="formInput articleLine">
		<h3>滞在中の連絡先(任意)
		</h3>
		<dl>
			
			<dt><label for="accommodationOne">現地連絡先1</label>
			</dt>
			<dd><input id="accommodationOne" type="text" name="accommodationOne" class="widthLarge" placeholder="例：09012345678 ABC HOTEL" maxlength="28" title="例：09012345678 ABC HOTEL" />
			</dd>
		</dl>
		<dl>
			
			<dt><label for="accommodationTwo">現地連絡先2</label>
			</dt>
			<dd><input id="accommodationTwo" type="text" name="accommodationTwo" class="widthLarge" placeholder="例：09012345678 ABC HOTEL" maxlength="28" title="例：09012345678 ABC HOTEL" />
			</dd>
		</dl>
	</div>
		</div>
		
		<p class="btnAreaSubmit btnArrowNext "><input id="next" type="submit" name="next" value="次へ" class="btnBase btnMainStream btnVerticalMain btnWidthVariable" onclick="return Asw.Dialog.getInstance('prebookConfirmDialog').toggle(event);return Asw.LoadingWindow.open(&quot;NORMAL&quot;, event)" />
		</p><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:2" value="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" autocomplete="off" />
</form><div id="cmnDynamicMessages" class="infoBoxSection">
				<dl class="infoBox importantInfo jsAccordionSwitch">
					<dt><strong>重要なご案内</strong></dt>
					<dd>
						<ul class="jsAccordionSwitchList" id="informationMessages">
								<li>個人情報の利用目的<a href="https://www.ana.co.jp/other/int/meta/0012.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="ANAプライバシーポリシー(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">ANAプライバシーポリシー<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>を必ずお読みいただき、内容に同意いただいた上で、お進みください。</li>
								<li><a href="https://www.ana.co.jp/other/int/meta/0058.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="Secure Flight Programについて(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">米国Secure Flightプログラム<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>に基づき、パスポート名、生年月日、性別などの情報は事前に米国へ通知いたします。</li>
								<li>入力したeメールアドレス、電話番号は入国に必要な情報として米国に通知される可能性があります。</li>
									<li class="jsHiddenFlg toggleContents">お名前のスペルがパスポート名と一致しているかご確認ください。異なる場合はインターネットではお取り扱いできません。<a href="https://www.ana.co.jp/other/int/meta/intbe0001.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="ANAお問い合わせ先(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">ANA<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>へ名前の変更をお申し出ください。</li>
									<li class="jsHiddenFlg toggleContents">二重国籍のお客様は、ご搭乗時に使用されるパスポートのお名前でご予約ください。<br />パスポートごとにお名前が異なる場合は、お客様ご自身で入国管理事務所・外務省・大使館などで各種条件をご確認のうえ、使用されるパスポートを決めてください。<br />なお、入国可否については国によって異なるため、お客様ご自身で各入国管理事務所などでご確認ください。</li>
									<li class="jsHiddenFlg toggleContents">旅行開始日時点で12歳以上の場合は大人、2歳以上12歳未満の場合は小児、2歳未満の場合は幼児の扱いとなります。なお、小児は大人と同マイル数が必要です。</li>
									<li class="jsHiddenFlg toggleContents">運航に関する情報をお客様へ通知するため、提携航空会社にもお客様のメールアドレスが提供されます。あらかじめご了承ください。</li>
									<li class="jsHiddenFlg toggleContents"><dl><dt>携帯電話へのメール送信をご希望のお客様へ</dt><dd>ドメイン指定受信をご利用の場合は、ana.co.jpとamadeus.comからのメールが受信できるようあらかじめ設定をお願いいたします。</dd></dl></li>
									<li class="jsHiddenFlg toggleContents">SMSはスマートフォンの番号に配信されます。一部の国ではSMSでのご案内をご利用いただけません。詳細は<a href="https://www.ana.co.jp/other/int/meta/0737.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="ショートメッセージ(SMS)でのご案内対象外の国について(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">ショートメッセージ(SMS)でのご案内対象外の国について<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>をご覧ください。</li>
									<li class="jsHiddenFlg toggleContents">ログインされている会員ご本人が搭乗しない場合は、再度フライト検索画面より「ログインされている会員ご本人は搭乗しない」のチェックボックスを選択してお手続きください。</li>
						</ul>
							<a href="#" class="toggleSwitch jsTriggerSwitch" role="button" aria-controls="informationMessages" aria-expanded="false"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/toggle_open.png?7bec38a" alt="開閉ボタン" height="25" width="25" class="jsRollOver" />
							</a>
					</dd>
				</dl></div>
	<!--=========== /main =============-->
	</div>

	<!--=========== Script ============-->
	<script type="text/javascript">
	$(window).on('load', function() {
		// アコーディオンのメソッドと設定
		Asw.AccordionInfo('.jsAccordionSwitch', {
			'triggerSwitch': '.jsAccordionSwitch .jsTriggerSwitch',
			'listClass': '.jsAccordionSwitchList',
			'visibilityHidden': 'toggleContents'
		});
	});
	</script>
	<!--=========== Script ============-->
	<script type="text/javascript">
		//プラットフォーム判別（サマリーエリア追従用として）
		var Asw = Asw || {};
	
	  	Asw.ClientInfo = Asw.ClientInfo  || {};
	  	Asw.ClientInfo.DeviceType = Asw.ClientInfo.DeviceType || {};
	
	  	Asw.ClientInfo.DeviceType.PC = 'PC';
	  	Asw.ClientInfo.DeviceType.SP = 'SP';
	  	Asw.ClientInfo.DeviceType.TC = 'TC';
	
	  	Asw.ClientInfo.deviceType = Asw.ClientInfo.DeviceType.PC;
	
	 	if((navigator.userAgent.indexOf('Android') > 0 && navigator.userAgent.indexOf('Mobile') == -1) || navigator.userAgent.indexOf('iPad') > 0){
	    	Asw.ClientInfo.deviceType = Asw.ClientInfo.DeviceType.TC;
	  	}
	</script>
	<!--=========== /Script ============--><div id="summaryArea">
			<div class="flightSummaryArea">
						<div class="flightSummarySection">
							<h2>区間 1
							</h2>
							<p class="flightDate"><em>6</em>月<em class="hasLeft">28</em>日（土）
							</p>
							<div class="flightSummaryRoute">
									<div class="flightSummaryRouteSection">
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation1_1">
													<span>東京(成田)
													</span>
												</p>
												<p class="time">
													<span>17:00
													</span>
												</p>
											</div>
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation1_1">
													<span>シカゴ(ORD)
													</span>
												</p>
												<p class="time">
													<span>14:55
													</span>
												</p>
											</div>
										<p class="flightNumberClass">
												<span>NH012</span><span><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/flight_ana_summaryarea.png?717d3c0" alt="ANA運航便" height="11" width="41" /></span>
												<br />特典ビジネス
										</p>
									</div>
							</div>
						</div>
						<div class="flightSummarySection">
							<h2 class="midstream">区間 2
							</h2>
							<p class="flightDate"><em>7</em>月<em class="hasLeft">23</em>日（水）
							</p>
							<div class="flightSummaryRoute">
									<div class="flightSummaryRouteSection">
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation2_1">
													<span>ホノルル(オアフ島)
													</span>
												</p>
												<p class="time">
													<span>11:35
													</span>
												</p>
											</div>
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation2_1">
													<span>東京(成田)
													</span>
												</p>
												<p class="time">
													<span>14:50
																	<span>翌日
																	</span>
													</span>
												</p>
											</div>
										<p class="flightNumberClass">
												<span>NH183</span><span><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/flight_ana_summaryarea.png?717d3c0" alt="ANA運航便" height="11" width="41" /></span>
												<br />特典エコノミー
										</p>
									</div>
							</div>
						</div>
					<div class="total">
						<p class="passenger">大人 x1
						</p>
						<div class="totalArea">
					  		<dl class="mealAmountWrap">
									<dt>有料機内食サービス
									</dt>
									<dd><em class="mealAmount">0</em><span class="currencyCode">円</span>
									</dd>
							</dl>
									<div>
										<div class="mileageAndTotal">
												<dl class="requiredMileage">
													<dt>必要マイル
													</dt>
													<dd><em class="requiredMileage">74,000</em><span class="currencyCode">マイル</span>
													</dd>
												</dl>
												<dl>
													<dt>総額
													</dt>
													<dd><em class="awardTotalPayment">68,130</em>円
													</dd>
												</dl>
												<p class="includeFareAndFuelCost">※各種税金、燃油特別付加運賃等を含みます。
												</p>
										</div>
									</div>
						</div>
					</div>
				
			</div></div>
		<!--===== /cmnWrapper =====-->
		</div>
			
		<span id="message" class="visuallyHidden" role="alert" aria-live="assertive"></span>
		<span id="politeMessage" class="visuallyHidden" role="log" aria-live="polite"></span><script type="text/javascript">
		var Asw = Asw || {};
		Asw.BaseOutput = {"sessionID":"piSLo3FSSm","language":"ja","siteCatalystLanguageCode":"J","operationDate":"20250620","operationDateTime":"20250620134046","office":{"officeCode":"TYONH08ZZ","country2letterCode":"JP"},"bookingType":"A","pageID":"A04_P01","siteCatalystPageName":"INT_BE_AWARD_J_A04特典予約_P01旅客情報入力（必須情報）","device":{"deviceType":"PC"}};
	</script><div id="promotionArea">
		<!--===== promotionArea =====-->
		<!--===== /promotionArea =====--></div>
		<!--=========== /cmnContainer ============--></div><div id="cmnBaloonArea" aria-live="polite"></div><div id="cmnPopupArea" aria-live="polite"></div>

	

	<div id="cmnFooterWrapper">
		<!--=========== .cmnFooterWrapper ============-->
		<div id="cmnFooter">
			<!--===== cmnFooter =====-->
			<ul>
				<li><a href="https://www.ana.co.jp/other/int/meta/0012.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">プライバシーポリシー<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
					<li><a href="https://www.ana.co.jp/other/int/meta/0670.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">利用者情報の外部送信について<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
					</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/0052.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">サイト利用規約<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/be0238.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">推奨環境<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/0133.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">サイトマップ<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/0732.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">運送約款<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
					<li class="lastChild"><a href="https://www.ana.co.jp/other/int/meta/0556.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">ウェブアクセシビリティについて<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
					</li>
			</ul>
			<p class="blankText" aria-hidden="true"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="" height="10" width="10" />外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。</p>
			<p class="copyright"><span lang="en" xml:lang="en">Copyright&#169;ANA</span>
			</p>
			<p class="starAllianceLogo"><a href="http://www.staralliance.com/ja/" target="_blank"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/star_alliance_logo.png?0fe73b5" alt="A STAR ALLIANCE MEMBER" height="18" width="175" /><span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
			</p>
			<!--===== cmnFooter =====-->
		</div>
		<!--=========== /cmnFooterWrapper ============-->
	</div>

		
		<div id="cmnLoadingForAjax"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/bg_modalWindow.png?717d3c0" alt="" height="0" width="0" />
			<div class="loadingArea">
				<p class="loadingImage"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/loading.gif?717d3c0" alt="リクエストを処理しています...しばらくお待ちください。" height="33" width="33" />
				</p>
			</div>
		</div>
		
		
		<div id="popupAltSentence" style="display: none" aria-hidden="true">別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。</div>
	<div id="cmnSiteCatalyst" aria-hidden="true"><div id="cmnSiteCatalystParamArea"><script type="text/javascript">
				// ▼SiteCatalyst
				// SiteCatalyst code version: H.2.
				// Copyright 1997-2005 Omniture, Inc. More info available at http://www.omniture.com
				SiteCatalystReportSuites   = "INT";
				SiteCatalystCharSet        = "UTF-8";
				SiteCatalystChannel        = "BE_AWARD_J";
				SiteCatalystPageName       = "INT_BE_AWARD_J_A04\u7279\u5178\u4E88\u7D04_P01\u65C5\u5BA2\u60C5\u5831\u5165\u529B\uFF08\u5FC5\u9808\u60C5\u5831\uFF09";
				SiteCatalystERR            = "";
				SiteCatalystEventNum       = "";
				SiteCatalystPurchase       = "FALSE";
				SiteCatalystProductPrice   = "";
				SiteCatalystCurrencyCode   = "";
				SiteCatalystSeatAvailabilityINT   = "";
				SiteCatalystDepartDate     = "";
				SiteCatalystDepart         = "";
				SiteCatalystArrive         = "";
				SiteCatalystFlightNo       = "";
				SiteCatalystClass          = "";
				SiteCatalystFare           = "";
				SiteCatalystPurchaseID     = "";
				
				SiteCatalystLocater        = "";
				SiteCatalystPayment        = "";
				SiteCatalystIncome         = "";
				
				SiteCatalystPax            = "";
				SiteCatalystManipulateDay  = "";
				SiteCatalystCompartmentClass   = "";
				SiteCatalystOptionInfo     = "";
				SiteCatalystOutboundUpsellPrice   = "";
				SiteCatalystInboundUpsellPrice    = "";
				
				SiteCatalystSearchInput    = "";
				SiteCatalystSegment        = "";
				SiteCatalystOfficeCode     = "";
				SiteCatalystPassengerCount = "";
				SiteCatalystPassenger      = "";
				SiteCatalystAncillaryService = "";
				// End SiteCatalyst code version: H.2.
				// ▲SiteCatalyst
			</script></div><script type="text/javascript"><!-- if(navigator.appVersion.indexOf('MSIE')>=0)document.write(unescape('%3C')+'\!-'+'-') // --></script>
		<!--/DO NOT REMOVE/-->
		

		
		<!-- Google Tag Manager -->
		<script type="text/javascript" charset="Shift_JIS" src="https://www.ana.co.jp/common/js/GTMINTBE.js"></script>
		<!-- End Google Tag Manager -->
		<!-- Yahoo Tag Manager -->
		<script type="text/javascript" charset="Shift_JIS" src="https://www.ana.co.jp/common/js/YTMINTBE.js"></script>
		<!-- End Yahoo Tag Manager -->
		

	</div>

	

	<div class="mboxDefault"></div><script type="text/javascript">mboxCreate("ASW_common_A04_P01");</script>
			<script type="text/javascript" src="https://www.ana.co.jp/common/js/tealium/tealium.js"></script>
			<script type="text/javascript" src="//cdn.evgnet.com/beacon/allnipponairways/nonamcprd/scripts/evergage.min.js"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/javax.faces.resource/jsf.js.xhtml?ln=javax.faces"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/jquery.mousewheel.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/jquery.jscrollpane.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/jquery-ui-1.10.4.custom.min.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/jquery.ui.touch-punch.min.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/asw-common.js?beb6a50"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/asw-common-pc.js?fd88d7d"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/asw-format.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/mask.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/loading.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/asw-loadingwindow-pc.js?9e233c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/asw-loadingwindow.js?51db600"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/dialog-pc.js?25d36a4"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/asw-dialog-sequencer.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/asw-event.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/asw-date-select.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/asw-calendar-pc.js?9b86b8a"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/asw-calendar.js?db624c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/heightLine.js?0e7a1ce"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/asw-summaryarea.js?b21c6d7"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/asw-fingerprints.js?99001fc"></script><script type="text/javascript">
			Asw.init("https://aswbe-i.ana.co.jp/rei22h/international_asw", "ja");
			Asw.getCalendarHolidayList = function() {return [20250721,20250811,20250915,20250923,20251013,20251103,20251123,20251124,20260101,20260112,20260211,20260223,20260320,20260429,20260503,20260504,20260505,20260506,20260720,20260811,20260921,20260922,20260923,20261012,20261103,20261123,20270101,20270111,20270211,20270223,20270321,20270322,20270429,20270503,20270504,20270505,20270719,20270811,20270920,20270923,20271011,20271103,20271123,20280101,20280110,20280211,20280223,20280320,20280429,20280503,20280504,20280505,20280717,20280811,20280918,20280922,20281009,20281103,20281123,20290101,20290108,20290211,20290212,20290223,20290320,20290429,20290430,20290503,20290504,20290505,20290716,20290811,20290917,20290923,20290924,20291008,20291103,20291123];}
			Asw.ClientInfo.deviceType = "PC";
			Asw.ClientInfo.mobileDeviceType = "UNKNOWN";
			Asw.ClientInfo.osVersion = "";
			Asw.Format.init("https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/{1}/{2}/{3}?{0}");
			Asw.overwriteTargetAttributeValue("false");
		</script><script type="text/javascript">
		
		var Asw = Asw || {};
		Asw.ContactInput = Asw.ContactInput || {};
		Asw.ContactInput.Const = {
			/** data-属性名「data-type」 @constant @type {String} @default 'data-type' */
			DATA_ATTRIBUTE_NAME_TYPE: 'data-type',
			/** data-属性名「data-sms-possible」 @constant @type {String} @default 'data-sms-possible' */
			DATA_ATTRIBUTE_NAME_SMS_POSSIBLE: 'data-sms-possible',
			/** data-属性名「data-country-code」 @constant @type {String} @default 'data-country-code' */
			DATA_ATTRIBUTE_NAME_COUNTRY_CODE: 'data-country-code',
			/** data-type属性値「sms」 @constant @type {String} @default 'sms' */
			DATA_TYPE_VALUE_SMS: 'sms'
		};

		
		Asw.ContactInput.countryInfoMap = {"IS":{"internationalCallingCode":"+354","smsPossible":false},"IE":{"internationalCallingCode":"+353","smsPossible":true},"AZ":{"internationalCallingCode":"+994","smsPossible":true},"AF":{"internationalCallingCode":"+93","smsPossible":true},"US":{"internationalCallingCode":"+1","smsPossible":true},"AE":{"internationalCallingCode":"+971","smsPossible":true},"DZ":{"internationalCallingCode":"+213","smsPossible":true},"AR":{"internationalCallingCode":"+54","smsPossible":true},"AW":{"internationalCallingCode":"+297","smsPossible":true},"AL":{"internationalCallingCode":"+355","smsPossible":true},"AM":{"internationalCallingCode":"+374","smsPossible":true},"AO":{"internationalCallingCode":"+244","smsPossible":false},"AG":{"internationalCallingCode":"+1","smsPossible":true},"AD":{"internationalCallingCode":"+376","smsPossible":true},"YE":{"internationalCallingCode":"+967","smsPossible":true},"GB":{"internationalCallingCode":"+44","smsPossible":true},"IL":{"internationalCallingCode":"+972","smsPossible":true},"IT":{"internationalCallingCode":"+39","smsPossible":true},"IQ":{"internationalCallingCode":"+964","smsPossible":true},"IR":{"internationalCallingCode":"+98","smsPossible":true},"IN":{"internationalCallingCode":"+91","smsPossible":true},"ID":{"internationalCallingCode":"+62","smsPossible":true},"UG":{"internationalCallingCode":"+256","smsPossible":true},"UA":{"internationalCallingCode":"+380","smsPossible":true},"UZ":{"internationalCallingCode":"+998","smsPossible":true},"UY":{"internationalCallingCode":"+598","smsPossible":true},"EC":{"internationalCallingCode":"+593","smsPossible":true},"EG":{"internationalCallingCode":"+20","smsPossible":true},"EE":{"internationalCallingCode":"+372","smsPossible":true},"SZ":{"internationalCallingCode":"+268","smsPossible":false},"ET":{"internationalCallingCode":"+251","smsPossible":false},"ER":{"internationalCallingCode":"+291","smsPossible":true},"SV":{"internationalCallingCode":"+503","smsPossible":true},"AU":{"internationalCallingCode":"+61","smsPossible":true},"AT":{"internationalCallingCode":"+43","smsPossible":true},"OM":{"internationalCallingCode":"+968","smsPossible":true},"NL":{"internationalCallingCode":"+31","smsPossible":true},"GH":{"internationalCallingCode":"+233","smsPossible":true},"CV":{"internationalCallingCode":"+238","smsPossible":false},"GY":{"internationalCallingCode":"+592","smsPossible":true},"KZ":{"internationalCallingCode":"+7","smsPossible":true},"QA":{"internationalCallingCode":"+974","smsPossible":true},"CA":{"internationalCallingCode":"+1","smsPossible":true},"GA":{"internationalCallingCode":"+241","smsPossible":true},"CM":{"internationalCallingCode":"+237","smsPossible":true},"KR":{"internationalCallingCode":"+82","smsPossible":true},"KH":{"internationalCallingCode":"+855","smsPossible":true},"GM":{"internationalCallingCode":"+220","smsPossible":true},"KP":{"internationalCallingCode":"+850","smsPossible":false},"MK":{"internationalCallingCode":"+389","smsPossible":false},"GN":{"internationalCallingCode":"+224","smsPossible":true},"GW":{"internationalCallingCode":"+245","smsPossible":true},"CY":{"internationalCallingCode":"+357","smsPossible":true},"CU":{"internationalCallingCode":"+53","smsPossible":true},"GR":{"internationalCallingCode":"+30","smsPossible":true},"KI":{"internationalCallingCode":"+686","smsPossible":false},"KG":{"internationalCallingCode":"+996","smsPossible":true},"GT":{"internationalCallingCode":"+502","smsPossible":true},"GU":{"internationalCallingCode":"+1","smsPossible":true},"KW":{"internationalCallingCode":"+965","smsPossible":true},"KY":{"internationalCallingCode":"+1","smsPossible":true},"GD":{"internationalCallingCode":"+1","smsPossible":true},"HR":{"internationalCallingCode":"+385","smsPossible":true},"KE":{"internationalCallingCode":"+254","smsPossible":true},"CI":{"internationalCallingCode":"+225","smsPossible":true},"CR":{"internationalCallingCode":"+506","smsPossible":true},"SA":{"internationalCallingCode":"+966","smsPossible":true},"KM":{"internationalCallingCode":"+269","smsPossible":true},"CO":{"internationalCallingCode":"+57","smsPossible":true},"CG":{"internationalCallingCode":"+242","smsPossible":false},"CD":{"internationalCallingCode":"+243","smsPossible":true},"ZM":{"internationalCallingCode":"+260","smsPossible":true},"SM":{"internationalCallingCode":"+378","smsPossible":false},"SL":{"internationalCallingCode":"+232","smsPossible":true},"DJ":{"internationalCallingCode":"+253","smsPossible":false},"JM":{"internationalCallingCode":"+1","smsPossible":true},"CH":{"internationalCallingCode":"+41","smsPossible":true},"GE":{"internationalCallingCode":"+995","smsPossible":true},"SE":{"internationalCallingCode":"+46","smsPossible":true},"SY":{"internationalCallingCode":"+963","smsPossible":true},"ES":{"internationalCallingCode":"+34","smsPossible":true},"SG":{"internationalCallingCode":"+65","smsPossible":true},"LK":{"internationalCallingCode":"+94","smsPossible":true},"SK":{"internationalCallingCode":"+421","smsPossible":true},"SI":{"internationalCallingCode":"+386","smsPossible":true},"ZW":{"internationalCallingCode":"+263","smsPossible":true},"SD":{"internationalCallingCode":"+249","smsPossible":true},"KN":{"internationalCallingCode":"+1","smsPossible":true},"SR":{"internationalCallingCode":"+597","smsPossible":true},"LC":{"internationalCallingCode":"+1","smsPossible":true},"SC":{"internationalCallingCode":"+248","smsPossible":false},"SN":{"internationalCallingCode":"+221","smsPossible":true},"SB":{"internationalCallingCode":"+677","smsPossible":false},"RS":{"internationalCallingCode":"+381","smsPossible":true},"TH":{"internationalCallingCode":"+66","smsPossible":true},"TW":{"internationalCallingCode":"+886","smsPossible":true},"TJ":{"internationalCallingCode":"+992","smsPossible":true},"TZ":{"internationalCallingCode":"+255","smsPossible":true},"CZ":{"internationalCallingCode":"+420","smsPossible":true},"TD":{"internationalCallingCode":"+235","smsPossible":true},"CF":{"internationalCallingCode":"+236","smsPossible":true},"TN":{"internationalCallingCode":"+216","smsPossible":true},"CL":{"internationalCallingCode":"+56","smsPossible":true},"TV":{"internationalCallingCode":"+688","smsPossible":false},"DK":{"internationalCallingCode":"+45","smsPossible":true},"DE":{"internationalCallingCode":"+49","smsPossible":true},"TG":{"internationalCallingCode":"+228","smsPossible":true},"DO":{"internationalCallingCode":"+1","smsPossible":true},"DM":{"internationalCallingCode":"+1","smsPossible":false},"TT":{"internationalCallingCode":"+1","smsPossible":true},"TM":{"internationalCallingCode":"+993","smsPossible":false},"TR":{"internationalCallingCode":"+90","smsPossible":true},"TO":{"internationalCallingCode":"+676","smsPossible":false},"NG":{"internationalCallingCode":"+234","smsPossible":true},"NR":{"internationalCallingCode":"+674","smsPossible":false},"NA":{"internationalCallingCode":"+264","smsPossible":false},"NI":{"internationalCallingCode":"+505","smsPossible":true},"BH":{"internationalCallingCode":"+973","smsPossible":false},"NE":{"internationalCallingCode":"+227","smsPossible":true},"PK":{"internationalCallingCode":"+92","smsPossible":true},"JP":{"internationalCallingCode":"+81","smsPossible":true},"BS":{"internationalCallingCode":"+1","smsPossible":true},"NZ":{"internationalCallingCode":"+64","smsPossible":true},"BM":{"internationalCallingCode":"+1","smsPossible":true},"NP":{"internationalCallingCode":"+977","smsPossible":true},"BB":{"internationalCallingCode":"+246","smsPossible":true},"NO":{"internationalCallingCode":"+47","smsPossible":true},"HT":{"internationalCallingCode":"+509","smsPossible":true},"BD":{"internationalCallingCode":"+880","smsPossible":true},"VA":{"internationalCallingCode":"+39","smsPossible":false},"PA":{"internationalCallingCode":"+507","smsPossible":true},"VU":{"internationalCallingCode":"+678","smsPossible":false},"PG":{"internationalCallingCode":"+675","smsPossible":true},"PW":{"internationalCallingCode":"+680","smsPossible":true},"PY":{"internationalCallingCode":"+595","smsPossible":true},"PS":{"internationalCallingCode":"+970","smsPossible":true},"BR":{"internationalCallingCode":"+55","smsPossible":true},"HU":{"internationalCallingCode":"+36","smsPossible":true},"TL":{"internationalCallingCode":"+670","smsPossible":true},"BG":{"internationalCallingCode":"+359","smsPossible":true},"BF":{"internationalCallingCode":"+226","smsPossible":true},"BN":{"internationalCallingCode":"+673","smsPossible":true},"FJ":{"internationalCallingCode":"+679","smsPossible":true},"PH":{"internationalCallingCode":"+63","smsPossible":true},"FI":{"internationalCallingCode":"+358","smsPossible":true},"BT":{"internationalCallingCode":"+975","smsPossible":true},"BY":{"internationalCallingCode":"+375","smsPossible":true},"BZ":{"internationalCallingCode":"+501","smsPossible":false},"PR":{"internationalCallingCode":"+1","smsPossible":true},"BE":{"internationalCallingCode":"+32","smsPossible":true},"BA":{"internationalCallingCode":"+387","smsPossible":true},"FR":{"internationalCallingCode":"+33","smsPossible":true},"BO":{"internationalCallingCode":"+591","smsPossible":false},"PF":{"internationalCallingCode":"+689","smsPossible":true},"BI":{"internationalCallingCode":"+257","smsPossible":true},"VN":{"internationalCallingCode":"+84","smsPossible":true},"BJ":{"internationalCallingCode":"+229","smsPossible":true},"VE":{"internationalCallingCode":"+58","smsPossible":true},"PE":{"internationalCallingCode":"+51","smsPossible":true},"PL":{"internationalCallingCode":"+48","smsPossible":true},"BW":{"internationalCallingCode":"+267","smsPossible":true},"PT":{"internationalCallingCode":"+351","smsPossible":false},"HK":{"internationalCallingCode":"+852","smsPossible":true},"HN":{"internationalCallingCode":"+504","smsPossible":false},"MH":{"internationalCallingCode":"+692","smsPossible":true},"MO":{"internationalCallingCode":"+853","smsPossible":true},"MG":{"internationalCallingCode":"+261","smsPossible":true},"MW":{"internationalCallingCode":"+265","smsPossible":true},"ML":{"internationalCallingCode":"+223","smsPossible":true},"MT":{"internationalCallingCode":"+356","smsPossible":true},"MY":{"internationalCallingCode":"+60","smsPossible":true},"FM":{"internationalCallingCode":"+691","smsPossible":false},"MM":{"internationalCallingCode":"+95","smsPossible":true},"MX":{"internationalCallingCode":"+52","smsPossible":true},"MU":{"internationalCallingCode":"+230","smsPossible":true},"MR":{"internationalCallingCode":"+222","smsPossible":true},"MZ":{"internationalCallingCode":"+258","smsPossible":true},"MC":{"internationalCallingCode":"+377","smsPossible":true},"MV":{"internationalCallingCode":"+960","smsPossible":true},"MD":{"internationalCallingCode":"+373","smsPossible":false},"RO":{"internationalCallingCode":"+40","smsPossible":true},"MA":{"internationalCallingCode":"+212","smsPossible":true},"MN":{"internationalCallingCode":"+976","smsPossible":true},"ME":{"internationalCallingCode":"+382","smsPossible":true},"JO":{"internationalCallingCode":"+962","smsPossible":true},"LA":{"internationalCallingCode":"+856","smsPossible":true},"RU":{"internationalCallingCode":"+7","smsPossible":true},"LV":{"internationalCallingCode":"+371","smsPossible":true},"LT":{"internationalCallingCode":"+370","smsPossible":true},"LY":{"internationalCallingCode":"+218","smsPossible":true},"GQ":{"internationalCallingCode":"+240","smsPossible":false},"LI":{"internationalCallingCode":"+423","smsPossible":false},"LR":{"internationalCallingCode":"+231","smsPossible":true},"CN":{"internationalCallingCode":"+86","smsPossible":true},"LU":{"internationalCallingCode":"+352","smsPossible":true},"ZA":{"internationalCallingCode":"+27","smsPossible":true},"RW":{"internationalCallingCode":"+250","smsPossible":true},"LS":{"internationalCallingCode":"+266","smsPossible":false},"LB":{"internationalCallingCode":"+961","smsPossible":true}};

		$(function () {
			contactInfoInit();
		});
		
		function contactInfoInit() {
			
			
			Asw.get('representativeTelType').find('option[value="M1"]').attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_TYPE,
					Asw.ContactInput.Const.DATA_TYPE_VALUE_SMS);
			Asw.get('representativeTelCountry').children('option').each(function(i,option){
				var countryInfo = Asw.ContactInput.countryInfoMap[option.value];
				if (countryInfo === void 0) {
					
					return true;
				}
				$(option).attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_SMS_POSSIBLE, countryInfo.smsPossible);
				$(option).attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_COUNTRY_CODE, countryInfo.internationalCallingCode);
			});
			
			var smsCountrySelectSelector = 'select.js-countrySelect:not(#representativeTelCountry)'
			$(smsCountrySelectSelector).eq(0).children('option').each(function(i,option){
				var countryInfo = Asw.ContactInput.countryInfoMap[option.value];
				if (countryInfo === void 0) {
					
					return true;
				}
				$(smsCountrySelectSelector + ' option:nth-child(' + (i+1) + ')').attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_COUNTRY_CODE,
						countryInfo.internationalCallingCode);
			});
			
			var emergencyCountrySelectSelector = 'select.passengerEmergencyCountry.js-countrySelect:not(#representativeTelCountry)'
			$(emergencyCountrySelectSelector).eq(0).children('option').each(function(i,option){
				var countryInfo = Asw.ContactInput.countryInfoMap[option.value];
				if (countryInfo === void 0) {
					
					return true;
				}
				$(emergencyCountrySelectSelector + ' option:nth-child(' + (i+1) + ')').attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_COUNTRY_CODE,
						countryInfo.internationalCallingCode);
			});
		}
	</script><script type="text/javascript">
		var isInit = true;
		function changeTelType(_this, telNoInputId) {
			var $telNoInput = Asw.get(telNoInputId);
			var $telNoKind = Asw.get("representativeTelType");
			var telNo = Asw.getValueComparedToPlaceholder(telNoInputId);
			var telNoCountry = Asw.get("representativeTelCountry").val();

			if(true && !isInit) {
				if(telInputCheck(telNo, telNoCountry)) {
					if (_this.value == "M1") {
						telNo = exceptDomesticPrefix("09042504176", telNoCountry);
					} else if (_this.value == "B1") {
						telNo = exceptDomesticPrefix("03-5405-2172", telNoCountry);
					} else {
						telNo = exceptDomesticPrefix("090-4250-4176", telNoCountry);
					}
					$telNoInput.val(telNo);
				}
			}
			isInit = false;
		}
		
		function telInputCheck(telNo, telNoCountry) {
	        checkResult = false;
	        
	        var amcHndyTelNo = exceptDomesticPrefix("09042504176", telNoCountry);
	        var amcCmpTelNo = exceptDomesticPrefix("03-5405-2172", telNoCountry);
	        var amcTelNo = exceptDomesticPrefix("090-4250-4176", telNoCountry);
	
	        if(telNo == "" || telNo == amcHndyTelNo || telNo == amcCmpTelNo || telNo == amcTelNo) {
	        	checkResult = true;
	        }
	        return checkResult;
    	}
    	
    	function exceptDomesticPrefix(telNo, telNoCountry) {
    		if(telNoCountry == 'JP') {
    			if(telNo.substr(0, 1) === '0') {
    				return telNo.slice(1);
    			}
    		}
    		return telNo;
    	}
	</script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/asw-customerinfomationform.js?6e91953"></script><script type="text/javascript">
		$(document).ready(function(){
				Asw.enableTextWhenCheckBox('.cascadedSwitch');
				Asw.notPasteInput('.jsNotPasteInput');
			$(function(){
				Asw.alignHeightToParentTdElement( '.passengerInfoInputArea' );
				
					Asw.alignHeightToParentTdElement( '.passengerInfoInputText' );
				
				
				$('select.js-countrySelect').trigger('change');
			});
		});
	</script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/asw-session-keeper.js?6ec0730"></script><script type="text/javascript">
			$(document).ready(function() {
				Asw.SessionKeeper.initAndStart("sessionKeeperContainer", "520", "570", "PC");
			});
			</script><script type="text/javascript">
		Asw.addAjaxCallback(function(data) {
			// アコーディオンのメソッドと設定 
			if($('.jsAccordionSwitch .jsTriggerSwitch').length > 0){
				var events = $._data($('.jsAccordionSwitch .jsTriggerSwitch').get(0), "events");
				if(events === undefined || events.click === undefined) {
					Asw.AccordionInfo('.jsAccordionSwitch', {
						'triggerSwitch': '.jsAccordionSwitch .jsTriggerSwitch',
						'listClass': '.jsAccordionSwitchList',
						'visibilityHidden': 'toggleContents'
					});
				}
			}
		});
	</script><script type="text/javascript">
	Asw.Price = Asw.Price || {};
	// 整数の区切り文字
	Asw.Price.separateString = ',';
	
	// 整数の区切り桁数
	Asw.Price.separateDigitNumber = 3;
	
	// 小数点
	Asw.Price.decimalPointString = '.';
	
	// 小数点以降の有効桁数
	Asw.Price.significantDigit = 0;
	
	// 表示金額の単位
	Asw.Price.currencyUnit = '円';
	</script><script type="text/javascript">
			$(document).ready(function() {
				$('#preLoadingArea').css('visibility', 'hidden');
			});
		</script><noscript><img src="https://aswbe-i.ana.co.jp/akam/13/pixel_d54330b?a=dD1hOTU0MThmYjg1OWM3YzljYzY1NzJlMWI3NzhmODY3ZjJlYjBmMzM1JmpzPW9mZg==" style="visibility: hidden; position: absolute; left: -999px; top: -999px;" /></noscript><script type="text/javascript"  src="/Q1XS4o/Cn_ls/KfDO7/xA/t9OfhmEwSiGQaG/NG4dCQE/bw/QJBChQIQEB"></script></body>
</html>