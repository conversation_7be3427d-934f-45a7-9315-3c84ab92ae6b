<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="ja-JP" xml:lang="ja-<PERSON>">

	
	<!-- skey = 2025/06/20 13:51:45 rei21h mC6LrYEfi7 dljdmx+929  --><head id="j_idt53">
    <base href="about:blank">
    <style>
        /* Hide elements that might try to load external resources, as a fallback. */
        img, svg, video, iframe, link[rel="stylesheet"], script { display: none !important; }
    </style>

		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
			<meta http-equiv="X-UA-Compatible" content="IE=EmulateIE9; IE=EmulateIE10" />
			<meta http-equiv="X-UA-Compatible" content="IE=7" />
			<meta http-equiv="X-UA-Compatible" content="IE=edge" />
		<meta name="description" content="" />
		<meta name="keywords" content="" />
		<meta name="format-detection" content="telephone=no" />
		<script type="text/javascript" src="/international_asw/dynatrace/ruxitagentjs_ICANVfhqrux_10273230920145353.js" data-dtconfig="rid=RID_1214840848|rpid=1818238136|domain=aswbe-i.ana.co.jp|reportUrl=https://bf90419wvr.bf.dynatrace.com/bf|app=78bf0b58acf6ed13|cors=1|owasp=1|featureHash=ICANVfhqrux|xb=www^bs.google-analytics^bs.com^pana^bs.demdex^bs.net^pcollect^bs.tealiumiq^bs.com^pallnipponairways^bs.australia-3^bs.evergage^bs.com^pallnipponairwayscolt^bs.tt^bs.omtrdc^bs.net|rdnt=1|uxrgce=1|cuc=k8vpgh61|mel=100000|expw=1|dpvc=1|md=mdcc1=bAsw.PnrOutput.recordLocator,mdcc2=bAsw.PnrOutput.paymentDetailInfoList[0].fopCode,mdcc3=bAsw.PnrOutput.ticketedFlag,mdcc4=bAsw.PnrOutput.nhPnrFlag,mdcc5=bAsw.PnrOutput.itinerarySummary.departureDateYyyymmdd,mdcc6=bAsw.SearchCriteriaOutput.cffCodeInput,mdcc7=a#main ^rb div.section.sectionNoFlow ^rb table ^rb tbody ^rb tr ^rb td.flightNumber.lastCol ^rb span ^rb span:nth-child(2),mdcc8=cunique|lastModification=1750184416409|tp=500,50,0|agentUri=/international_asw/dynatrace/ruxitagentjs_ICANVfhqrux_10273230920145353.js"></script><link rel="shortcut icon" href="https://www.ana.co.jp/favicon.ico" />
		<title>お客様情報入力 | ANA</title>
		<noscript><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei21h/international_asw/contents/cmn/css/asw_noscript_pc.css?99001fc" />
		</noscript>

		<!--  [ Adobe Target ] -->
		<script type="text/javascript" src="https://www.ana.co.jp/behavior/adobetarget/target_intbe.js"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21h/international_asw/contents/cmn/script/jquery-1.8.3.min.js?717d3c0"></script><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei21h/international_asw/contents/ja/css/asw_initialize.css?99001fc" /><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei21h/international_asw/contents/cmn/css/asw_screen_pc.css?99001fc" /><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei21h/international_asw/contents/cmn/css/asw_common_pc.css?99001fc" /><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei21h/international_asw/contents/cmn/css/asw_forminput_pc.css?99001fc" /><script type="text/javascript">
		var Asw = Asw || {};
		Asw.GlobalWord = Asw.GlobalWord || {};
		
		Asw.GlobalWord.toolTipCloseIconAltText = "閉じる";
		
		</script><script >bazadebezolkohpepadr="74677246"</script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/akam/13/4737b82"  defer></script></head><body>
		
		<noscript>当サイトをご利用になるためには、JavaScript対応のブラウザが必要です。設定でJavaScriptを有効にしてください。有効にできない場合はANA電話窓口へお問い合わせください。
		</noscript>
		<div id="preLoadingArea"></div>
	
	<div id="cmnHeader" class="platinum">
		<!--===== cmnHeader =====-->
		<div class="headerWrapper">
			<div class="header">
				<p class="logo"><a href="https://www.ana.co.jp/asw/AMCTopServlet?type=a" tabindex="-1" aria-hidden="true"><img src="https://aswbe-i.ana.co.jp/rei21h/international_asw/contents/cmn/image/ana_logo_navy.png?717d3c0" alt="ANA Inspiration of JAPAN。リンク先はマイレージクラブトップページです。" height="28" width="274" /></a>
				</p>
					<ul class="headerMenu">
						<li><a href="https://www.ana.co.jp/asw/AMCTopServlet?type=a">ANAマイレージクラブトップページ</a>
						</li>
						<li><a href="https://www.ana.co.jp/other/int/meta/0005.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">お問い合わせ<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21h/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
						</li>
							<li class="btnLogoutArea"><a href="https://aswbe-i.ana.co.jp/rei21h/international_asw/rest/logout" role="button" class="btnBase btnLogout">ログアウト</a>
							</li>
					</ul>
			</div>
		</div>
			<div class="amcUserInfoWrapper">
				<div class="amcUserInfo">
					<div class="userNameArea">
						<table>
							<tr>	
									<td class="statusIcon"><img src="https://aswbe-i.ana.co.jp/rei21h/international_asw/contents/cmn/image/icon_status_05.png?717d3c0" alt="プラチナサービスメンバー" height="46" width="60" />
									</td>
								<td class="userName">佐藤 直樹 <span>様</span>
								</td>
							</tr>					
						</table>
					</div>
						<div class="balance">
								<dl class="upgradePointBalance">
									<dt>アップグレードポイント残高
									</dt>
									<dd>
											<dl>
												<dt>(2025年度)
												</dt>
												<dd>
													<em>20
													</em>ポイント
												</dd>
											</dl>
									</dd>
								</dl>
							<dl class="mileBalance">
								<dt>マイル口座残高
								</dt>
								<dd><em>75,663</em><span class="currencyCode">マイル</span>
								</dd>
							</dl>
						</div>
				</div>
					<p class="nowTime">6月20日13時51分現在
					</p>
			</div>
		<!--===== /cmnHeader =====-->
	</div>
		<div id="sessionKeeperContainer" class="sessionKeepingContainer"><img src="https://aswbe-i.ana.co.jp/rei21h/international_asw/contents/cmn/image/bg_modalWindow.png?717d3c0" alt="" height="0" width="0" />
			<div id="extendSessionExpiredDialog" class="modalExpire modalSmall modalContainer" role="dialog" aria-labelledby="extendSessionExpiredDialogTitle" aria-hidden="true">
<form id="sessionKeeperContainer:j_idt183" name="sessionKeeperContainer:j_idt183" method="post" action="https://aswbe-i.ana.co.jp/rei21h/international_asw/pages/award/reservation/award_mandatory_passenger_information_input.xhtml?aswcid=1&amp;rand=20250620135145mC6LrYEfi7" autocomplete="off" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="sessionKeeperContainer:j_idt183" value="sessionKeeperContainer:j_idt183" />
<input type="hidden" name="sessionKeeperContainer:j_idt183_operationTicket" value="dljdmx+92940089a6198db5b9cb7fbb1702cc3a~eAfbC5ncNchEExfHq-yHqiJTaoo6Y-iOH370j7yy!1750395096605.aere-xml-controller-67d4778877-2rkqw" /><input type="hidden" name="sessionKeeperContainer:j_idt183_cmnPageTicket" value="4" />
					<h1 id="extendSessionExpiredDialogTitle" class="dialogTitle">まもなくセッションが終了します</h1>
					<div id="extendSessionExpiredDialogMessage" class="dialogMessage" tabindex="-1">このままお手続きを進める場合は、［延長］ボタンを押してください。</div>
					<p class="modalButton"><input id="sessionKeeperContainer:cmnSessionKeepingButton" type="submit" name="sessionKeeperContainer:cmnSessionKeepingButton" value="延長" class="btnBase btnModal btnMainStream" onclick="mojarra.ab(this,event,'action',0,0,{'onevent':function(data) {Asw.doCommonAjaxCallback(data,false, event); Asw.SessionKeeper.doSessionKeep(data);},'onerror':function(data) {Asw.doCommonAjaxErrorCallback(data); }});return false" />
					</p><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:0" value="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" autocomplete="off" />
</form>
			</div>
			<div id="showSessionExpiredDialog" class="modalExpire modalSmall modalContainer" role="dialog" aria-labelledby="showSessionExpiredDialogTitle" aria-hidden="true">
				<h1 id="showSessionExpiredDialogTitle" class="dialogTitle">セッションが終了しました</h1>
				<div id="showSessionExpiredDialogMessage" class="dialogMessage" tabindex="-1">一定時間操作されなかったため、お手続きを進めることができません。お手数ですが、再度トップページからお手続きください。</div>
				<p class="modalButton">
					<input type="submit" value="閉じる" class="btnBase btnModal btnMainStream" onclick="Asw.SessionKeeper.closeTimeoutDialog(); return false;" />
				</p>
			</div>
		</div>
		<!--===== dialogMessages =====--><div id="cmnModalMessages"></div>
		<div id="prebookConfirmDialog" class="modalContainer modalConfirm modalSmall " aria-hidden="true" role="dialog">
<form id="prebookConfirmDialog:j_idt403" name="prebookConfirmDialog:j_idt403" method="post" action="https://aswbe-i.ana.co.jp/rei21h/international_asw/pages/award/reservation/award_mandatory_passenger_information_input.xhtml?aswcid=1&amp;rand=20250620135145mC6LrYEfi7" autocomplete="off" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="prebookConfirmDialog:j_idt403" value="prebookConfirmDialog:j_idt403" />
<input type="hidden" name="prebookConfirmDialog:j_idt403_operationTicket" value="dljdmx+92940089a6198db5b9cb7fbb1702cc3a~eAfbC5ncNchEExfHq-yHqiJTaoo6Y-iOH370j7yy!1750395096605.aere-xml-controller-67d4778877-2rkqw" /><input type="hidden" name="prebookConfirmDialog:j_idt403_cmnPageTicket" value="4" /><div class="dialogMessage" tabindex="0"><p>この後、名・姓の修正や変更ができません。</p><p>「搭乗者選択」欄の氏名が「名」「姓」の順番で表示されていることを再確認ください。</p><p>例）<strong class="highlight">空野 太郎</strong> 様 → MR <strong class="highlight">TARO SORANO</strong></p><p>保安上の理由により、航空券とパスポートのお名前が同じ表記でない場合はご搭乗いただくことができませんので必ず再度ご確認ください。</p></div>
					
					<ul class="modalBtnHorizontal">
							
							<li><input type="submit" name="prebookConfirmDialog:j_idt419" value="戻る" aria-controls="prebookConfirmDialog" class="btnBase btnModal btnCancel" onclick="Asw.Dialog.getInstance('prebookConfirmDialog').close(event, Asw.DialogSequencer.isActiveSequencerNotDisplayedLastDialog());  return false;" />
							</li>
						
						<li class="btnArrowNext"><input type="submit" name="prebookConfirmDialog:j_idt422" value="OK" aria-controls="prebookConfirmDialog" class="btnBase btnModal btnMainStream " onclick="Asw.Dialog.getInstance('prebookConfirmDialog').callOpener('NORMAL', event); return false;" />
						</li>
					</ul><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:1" value="02PuI873CGKXHXCgT+5RPEfETq+1kptM9f/B/mhrTB7jzMj+JM2PWQuq6YeZwBTliQf8mCdNMYy9mRt70Qox/WFuOben1Bh9K1ELb0AQQRaoowdWp0OKAIo41WeI17WthbGNDp0XQjZe50mfQTwoe6T8OQyb8eRGXRm6FqbomBunpCIIXl7rnHZycrBUMfzC6bZos5GekXKc1WO3gJ2MH69dNgOuutpuQXKjqKpoB8zDgpeVRe010ljK9LeKSWcvALyUvP5wHNwfIUVSF5FfoHdzlwTPva7qTMwfPEngvoOaa506Cl+Qlk+xWHRUxSUmj9+lGycwzDO1kGIC5hRsijAQGab3k6Hj1h/5bbk/ltkKaTimccFCD1xhkzj9C1mpHooz2VHhrpr/5tZqd36siC+bJfOyS5B6uL4RaCg8lQNq6sVAZXUFBc4eDPHMV2r+JUopudw6BJ/de1CqgAN8t06j0+rdmfBHSHNo/6z/OScIGhw3UwyHKR2BzinCB/6xFNgy2AcrlisXqtTWS6j4Zk4NW2YiYuWCZY5IqSPEB3hTcNrdhK8v7lyppLbgP/TTTE7BNfulGu18Q+fO5oM4ftua+yH3PlJ3AmwsZxQjp+XwQdW0rn1eVyL8jLs84xCh5zu/SSyNoJFLlBOgGSbt3Rqwoz/H9R8oIdAjicX0SD7bLtKFgtm5jBj7rPzE10zeaaWbkyaTOOyXkFgsmOBpfxFagiKm7LiJl9fk6DF4QcRJ9kBUJEqCw9VV2+jwhfj4usdai5b1i6Bbbf4kB7Z+aVlYxrkCsuI84P845kePmO9ZAIQmZEQIOPP4kk2PPSE5LWC6V67wnV+VjKc6+VKqYbPtecXN7WHTvbZN51JYjZ/0GLxGWm2Wr6igjmuhVafY1jggfAeCTft6HBebUdsoKz/82kYl0qsOAvWA9rujgXDo/qSyHlndhcgBmyWzNAr2DzViQppoxHKvFswtWX3bD1+HoonoDNQtRimHNhnmfLjIQQrTlAlc5d6rtfxWQf0Oo2gO3OjFet8QM9W2DOQgFcEb3cpvOOoFxBxZZ8eueQsZjEe6qcEM4duc7H/mMmtJyscb8xde6CAcbvvfeKSuFbUbbSbGy0Y8DKPowntzfp/dt99Z4I8/WRBMDQTooiwnIak/F5dMDICwHWzjfdfChRcnSEkBvc+YshuEwFrwSMgpAxtDlSkJ1jA4ah8I4GV/HGXCPM5/eSDA0F+0YiBrfPuW8aAcvED7oexxMxVma99lFQqH4kW/SZWqHpKJGgFmFkm2prkvszRVAlI41rdUlx6AZCe1YP+Y76A/djS5+mYxwvLAF9gahGmDjGX0JB8axnmkg+crqPp+RHTWimEIF0fGreQLfOEIK6itEmz1pRHaXxrerWDfF0EUCoh7q9/vHkxItHo/ax5gCdKTsnF2D8odLBocMf8CrU3JK0VexCBEge/Dt8P0U5TLVSRD37lUidoLaZAM1mo/14qqS1u7hvZvqVtVp2z8D392ueqRI27xUjTDacHUanC9trffPfi9l4hIxXmlS7ftC4vLnqPtDnTTrsIDt+oBTg13u6CfAaGrTjzwB4lzTHPtAI8AeVjQ/2cIQbjCtvNzDPuh683XAOadSkHbCav/fgMyGu3E9PW5rwvKw0/c7eFyKMYFrdxoqQTKylsyYIoF3nyZk12Cy9aMyfqWfVSTtRw7CQOb8oi0uaNxQJh1K+AJmngC7mp0eXw4nIcA4WMG2t2eZbwNi277EVzXl4kmXQDduZPDB7aV1pC2wYFDLlE04j3FrNqRkkbisf9xgeaRTAsMJmc2bmSul5wQuOen0+bYTVyd1dESyDy8oELzlLL+xCWrPC4tpUxV+063QzFfvY49Vj9RYPrYsPDirJYIffrAAGKhDbxH4En0s3VKuYqtIXPq/xLvXaXOakH8Z8ZNRmecY2A5MT/3PvLeGu+uLi8FfhrnLu3HrbiEGJ8o8I+yyXCw+Mdvg9dW4lFlrZZs5Uv+sHHfA1j7f/3/qEGCeIp971gioL708UH5Xy4DlCJoJlYoCWvR8abBulr39ALUkTCNzlgrXeLn6fbzLHDFz5L9QWcjtGi03SN2PfUg4Ae+WE/EMSE/TvpAXOZFTryrkdRWSEN+Gt+h1fw3r31hcM6cqYDDBTKBUR7rql4yEIlSS9yiBMBJLIiXIGs+ViDkPyQT2n/+on+7B7P7qirtfZAkubuLav4spHLGpzVC9x8dvZOPR26WzsF7fsVcqhrufK6IutnSdcriLDB32YpxiakADRFbJG+6OAWs6hHG3D3i4oruQetlvAc9Dr9962LcdXg0NRRWh3rT8ge7n5DVoUd4sqc7UJuCwVdsRzZq2Bdu4Q/BZMgRegO9Gb1+Dr8wDmg1ts4t1h2KbHyPUKiO1XXAlxjmUJTcf4ZKCl7sjoAzAMYXUNnKWS2YoYgtOTJ8DP+V8kCBOjqg12it5epx+BGbhuFnN2u1fklKATHbbQA+LQjpkoLI9DfczI4AMUu/O+kCHOrGMekne+t383GyYvHZODFx9WdzplmF1n0CACfSPY8fh3McLWgoggP58+jSvNE6It0F7x51ESjGFh8XBYBf1WEpQgMPII2enSWz6W7ndMYW5fEEPXHCcKQQef1v0I2a+u9xe1wwTsWByu6T85TQJAs9/fcqmlVP5EWr20NytQ/Y4Ut5FEIZchSIYIsRtO6yvEknUFWzFLM0dk16osqyQr7wuxNF3n7Ot+lrdTsltIfi5ZMTEfwywyFQkbICQyJ8ew6np6c2pdsfSG4p2F00gAhWpPBu6K+M3rcG+RvDsYtXEPS3EVJhLnCeIa1T9D8cz8byHVEsEHOCFBxl61iuBD56xhXgDjV01vNpsJ9tNXo5nE1D+Rmr+ekMNlSyoncvKW2Vc7EjeN0hI9+JzR3TFaJb2H43ePfiUAXAbdATefLh1vl1if0p9+R4zT6NNdM5SK9hPmTcrEdZJ/wWT9P0dP0cAvQgSM69vBVBR6b+YS+riO5AG7DLPFBV1QjHgRIyAaFPJHoAnemZOBqRt0Z3n3Mto25zA0FLr+pg4NGQzlLUFy9It28zp3sntwJihvwp+zEhTMpaN/khB5Jws5J0bwTMsyrjXiRFieJq4QJdprShsnA0czBZ2mOIWaInqGENVki6vh1YSu6UanWbTPHRQfAFD/BSdt364PxHUAPDTWhIhsovDfe06a02ao/Doc+iISoRT6Fb/+yO1aJz+fUBtLZvqQQ1Ko6omygRg4Yn9pGWdBIPTazjJo03cARll8whVSN5O5uSYhs0S/VojOJsVpK7AuD9OAVxR1gYFAHi3MTEOkB4HU7khQK8z6yYY1JuZs8NAHmQqYmG18/4Ri1LwkuYbo4kXE1NSzLifRSqONq3GZJf9je4HINznXofl719SVPvLSjaYR0GfXlK4Z4ivPUp8dlBnafC/+KK0QY49/9y1dElyUEFItQepLG7/ebDoR46YmzDLTNW6oh23x6o47GS6LyDmgJ8zr3Cuw+HSYKOch+yLoVA0itrO0Ee5TZlJnAWOTeUEruo7A7sK3j7FGZQr4a2pEuU7kRV0S4f9hb7tSfNX3pBaQjuqE0H8Oj9O1eLo28OTR7MhaN7r8wjX6guRmAYVRp03e7BntUB9u/ObomZnVk75ExQTkA9TqMd5bJ3E+LfKw2543AdVmi7X3Xl8wlfebQNdJYFN66L1Bybws5Bfzju5Rgb2S+gmuaDzDSGRLgAtV1GcHQ+M1qAfkG8FnQt0LsyNGYh+NENQJMCY++KPNR85oT54XSf8MAKXQSFUZ/9wv8a0IAweXlwEuP/BCxyvmw6JH4zOzhkrS7NKxAfEeeWfpidW0xzdPsSCxf32AgzJRLdYAZpx+UVHcWacqXExPnFua0VgJpgRsewmvCsDP+KzqFkWcwMXFJMbxHucMiTQ3siCguPNEuIDGG+KmZY9dqBYBUj7GbK3JrhVGUJb63s4H+nA6tvMF6Fhi+aG+9LjGjzxTrI4+NknC5S1Oy8JCV2aKO4jib0Wze+397ORdDs5K4DDi1Xfsom+nL9zkw8Wa9oetH+4ngRAtJ7F+Or2k2tbHvcRGwY2exk2TuCsC0Ci6vNsxr89FqmoYkTEnpALhZO1hW4HS4r0GYUCsgQGx68Qg/K1ptCsOD6ye8sfeVBk7XwqWFM/mLflPtsyMHIRp0+M9UwFh9WHlGKb3IQ+k3OlW7fonas0eqknyWqM7ZEBjFu4kYlDYgnjVqtKasErr8qUknVdIuDVwPzaMcPLY89LbX1ryYklhy6g1q6tvTL6SsY4AEs3m/RRs5hYiixfH+NZtwkCZNN5FVVxLs0C3bQ0Ff8/iKvVeh+cL3OuIssSufDj57M/TobnIkyUMX9CO7kMXXMvAFSC94Dum+OZnC1qVpZEr3eeM30XZbLlfdtbFP7KSB7FNrZjf8Ub8v6lKOMAT6TBY1+DuF5cUQkXlptU295aNrvrY5XryO0q5rSmyHLNx2tDR+7Wf604Gln1W2czyJDGztCwEKg9NtFPdYq30jg5bMRWsui1Gg2sW4BIkbq4hdzeIzhGAz9DqWG7aVv1pqhxiteeh7cJe5PFzQwcjL+tv29mFdz2eJpaX2/Nn4heLLdd56jSM8gsnsBvM8H4ZEldf0ICxgm/cfLrcbV+B0kO8Il88ubR1Yhnb1+Oyr4T1xHhXJdozyb+ZMroyVXKvkMT0q133uisYnOEClZVuzIhMheBKy9snZscacEtAfxlSO1+fR7OlpT9ExeR2ZJxBpVPN+grcLk0CqiAR8+xIfU2fjC2wK45v9o1GU5JF8tgOgkYQUSbI5/IsvUo1+4bpidRECrKrVg5YHTqCuWbV18cjxcCIFNonnznfcBmqqzDr5RJFj12C6grzDJBCZmJpgw0hNOVqEkzvzr6BV+GzMMVyZJz3MVFlvq928+2hMcVrbWk4bQqrjOzOVXUaThC4x0/lRMcim+K5xWVq7FL0zC8gUhCKqk4PYfNq80oZly5Tq64i6ORCNF9c+LYRgMClMf+Ut2d2HDKH5H8eZq3gORjKr+5pCBxGrl8SwPutD6eCIpxikiYXAILLop+GJELsqAKlcAfg/g7yZm0vqPnSpfrp8wW6BihPhSSfy/g2RfHRgusZ4EZ/E0QYwQ14SJLhA0ECo/Q1MpU7Bn1Q6Pd/7mSorAvVVTLZnY0XxnO2QVGIakfkFJInDWlbVsKDCyQj0qU3Db/CfCa+c+Zm7Q42xmVrazKBvwnB7nN83KoC8fQ+GnCQsn9r+Fu2wjJTi7t8VSen+QlOAxCxGmPNkKGKO2DhrNNBq78gZ+yacxMtKssRYJk4reS6c9wsTXMynV2SZcXdoPL1imeEQaLTf7QGzRVphuRX9FxBSSIVKPoL+apMaR04b/rgdV64Nh1CSqWdGoMR8G/nVe55vA1YjRHki/UdHukTrITj8I02w1x9XicK2ZeSqAlo1kPmZauNviMVk3ugNqT099vzYBzGDzwIupz+K+NlzqoQzGB44Z+ZNwwyAEfweZmPu5k8aqX4extBtDgBp07vdB7aQjvaq+aZ0I7n2nAD1S+Efxs5Fqm64G3/KTrSqoKhMn+rPvXadyBd+O+2T1xHPRPwDdDCzAxyBXLkkLcv7BhDEydKhtlHmjzsslf+IMLhKSxATBXY6IXx3QD5BOwOEnPT4s3324JWPX1oudz0zyLsYkg44m49pv/WThczwDGK9BSoKVNPxRLPSnNR9WOzEmApKZmO7zBS9DuiMELlkqB1XpPokVDG2xwreTNJficbUSXaoeLZgFbWm8O2mefDP/r8Sf3He2osaArrdHuxgOBysgQHZIUTozOSrJyrjNCHfzHjGySiHsgAX+F3mKBW3ROEbyMYihWPwzjtbjhU9Zy8pXzTaWfyK3txzui12F9QVpXlFZLOMHBWf4dXy2jP7DKbSqHAQzyVFGh+Y9D/tCc6NPrObSl9XMfUAFJlZ/XUUgZjOxcyO3BlROMRMZ8pfn9jpKin7sYp8Or0KaR/pHDvNtQmqa2GmBTB+n/23f2Z0mCYwkF9C+etZ0LyBK2/7RspY6qehotiSTboV/4GkeOgo9SRSA369p9ShRtqMB7Q4lGTlZah0jfiKfjonAz7PK9tPDP6MYFOQ4c7xAscmje/t3VaTFFSSTohPNIC36y83dmxpdW4ahajrD/5jUF0VL7Qid9dc9LiEou5Tz8ijEsDUyQK1ObcXIj05593cBusVQoguoicwjE3cvnqmCqGXAwGycoFC47ZCt4t3im9IGIUJ3tFz8j9pbWvfFkBMnkkEeLDjowF2AxKUfL+z/m6MoIYgNY9FKpVPf03A12EYaqUqWufLT5OV8YL+JWBgersB0EHbxgqjekmL5l88TmxbP6Z6q1lOojxaTlq7hxGioKdAT2xZ56EYBGj2xO8HVbFobzA0kGFgW3JSB9euOhnU+Qdi9Gs5K76HHJIsSfZmultlsYo3sRGCXxkoBGV3lLtyso2uTBk9Van/h50YCtHfPuDSPsEKsQpJHr7YS/MJ9L6DyC5q3ThYq/fW2ythiedfA0dHQLgYmd6dBT3IwjItB3RhpRcIX9jBE+DBiFWRFGo/OHIfqY0rt4mPfJ3Bg4mg3jlpxl8B3RTRF9fKcg0nZFabc7jvyfsGQW/GWvm6tO6jZ2K7Pwj0RLU4MUs1xoEfQdrpr3KTCF7B6aH9H6VMOvsue+Y41Z5sZE4Vh0tH15Ehmd48qdzXj1zwWzDpb68yc93hUPeSVxv/0d520cvjKFKLpvu0PXgbDj9AHzgE+tN97IbFkq2MTJHRo2aUQ0+IpV259k775KruY0prdznYD9W4sT3VxJD6xtcVtnA2EpxNF2WIa2vj7MYlmLL0EEjPL2GkOq5A/8h/ReW5bmrD+Vl/nUubUl0+oXZTpsoVV8SGsxcPyvHdUaV+wGR009NNX11Or755ZwOPap6s/qGsMZtuZ1qKPpeaE4uRe/bkAvpyq40nPTet+rLhbnlT4KV6J4d9GGFtRxxx7MIwQjMfvOap1JzFtFtDXhWZUX6zxs78ntHNIBW1sJN/nbx0OekFHh2Uy+eOnjia6dLv1leL98Xv711aEzJyVKa8FJrmkHF6xX1t0oglfaO29LuXLaayQtLN00KcxmIvutB2OEHYQfSdjHIxO3rblc7KKD77u8Fe3fdUCNGKZxM9q3DAiSdrzjFgTTX977vCH7UJ4YodjadzqgjsvI8liC3dgd4SHiu6p//qIet4+A8Hd2HfZhPH6QON7CzKM5I8NlfOzoopUqjzoofS/iMVXPA6ScdabLXTb1QXtH7rHXLruuhNAtgMzanaGq3a7hkK/vnEqX9KBo4siCfsT/VRFhLyhgMpaVDnyfYSm0xf87wrHsdxka+hAGORQ9bz4dIvNfMhLYVCBja2J/gyQslbCo6SELlCd82ypaRXwgBEZTiVjGOdLiWSV2JoAzEiPIu3r1QeiTRb0bf8rHNXsIti7ivpPgiz7iuew8Zt4xGz/6xLislfidjgMu6efPyi6FR61ea+CkTr6wgQmGvIGSMzMhV+kS9bxIQKZ6uvcfXx9kIer2MO0YXyKZatEYCm8xpIibGnJRbhX2219Osj5mibcoD71i6srjRPsfUg7PEYSecfNldxnqlo4uwmXq3RfsK0XyVqkKsRVsVkrSWpDCSmLyMDO4lanW4GZiqc2iiFEUJSdYMr4PHx2BUrOKPGRrJ0KOk4FuW9yIeqYi7qIj7wUG/xK6kZuysu/epncdXSfyZ8WadduzD5IKS755nSVRyS/y4xvzutQtqVLXj2Ii37c5/tq8nfulBb3pzWJsS/H3MJ5m7Sts5aKbdbGgpnbEYMnBIamY/MTjnKNnL1ndGhz5VtkkQ3n973tITMgUDoRZVcVwKCunpoZBzSQ2PMcGARATOI9xsR+mtoPCnS9EhLFCRpxEAEz5q9MWNrAuDdwtv8s7OOLhuLCLRnitARLgy8UZMtD+oSq4MRGOrYldfmOcPLMAw3al9MDiRp+u4JKDWA5n51oyT1kp6cTy9R8F5ZlY4ivTdUHiTrErxBWry5Ey7jynxqDQt8B4NPUvtseADdZtQrMVLvmQvOGfYn3vXz6XgtBa3QIxW+biGRyNxFJD6bEXTs2466NjSBOzP91037LXSKBodNU6FHayCTWsNcJ5FDzWHtrQku4aB6Tr31bat0Igq3Xhbi6G5hu2p5TL1v41V3lfS2roZUpI15nznvEbxODZDs+Qvj6TcUJVgkPlcBaqt+/r+TlbzUiaF6XLPxwuvbcq+vQnXNQOiMe31+XR103XgM3+bTNHtst/dtXqAvzE7muXiVsxLJCCjaIwCpg5LEnIrp5Y8S5IDzfebJRroZ/6t+Tx1w=" autocomplete="off" />
</form>
		</div>
		<!--===== /dialogMessages =====--><div id="cmnContainer">
		<!--=========== .cmnContainer ============--><div id="cmnEmergencyNotice"></div><div id="cmnTitle" class="hgroup ">
		<h1 class="visuallyHidden">お客様情報入力</h1></div>

		<div id="cmnWrapper">
		<!--===== cmnWrapper =====-->
		<ol class="mainFlow" aria-label="完了までの手順、">
			<li><img src="https://aswbe-i.ana.co.jp/rei21h/international_asw/contents/cmn/image/icon_flow_01.png?717d3c0" alt="1" height="20" width="28" />フライト検索</li><!--
			--><li><img src="https://aswbe-i.ana.co.jp/rei21h/international_asw/contents/cmn/image/icon_flow_02_on.png?717d3c0" alt="2" height="20" width="28" /><span class="visuallyHidden">現在のステップ、</span>お客様情報入力</li><!--
			--><li><img src="https://aswbe-i.ana.co.jp/rei21h/international_asw/contents/cmn/image/icon_flow_03.png?717d3c0" alt="3" height="20" width="28" />お支払い情報</li><!--
			--><li class="flowLast"><img src="https://aswbe-i.ana.co.jp/rei21h/international_asw/contents/cmn/image/icon_flow4_04.png?717d3c0" alt="4" height="20" width="28" />完了</li>
		</ol>

	<div id="main">
	<!--=========== main =============-->
		<ul class="iconExample">
				<li class="requiredIconExample">
					<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei21h/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="" height="7" width="7" /></span>必須項目です	
				</li>
		</ul>
<form id="j_idt526" name="j_idt526" method="post" action="https://aswbe-i.ana.co.jp/rei21h/international_asw/pages/award/reservation/award_mandatory_passenger_information_input.xhtml?aswcid=1&amp;rand=20250620135145mC6LrYEfi7" autocomplete="off" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="j_idt526" value="j_idt526" />
<input type="hidden" name="j_idt526_operationTicket" value="dljdmx+92940089a6198db5b9cb7fbb1702cc3a~eAfbC5ncNchEExfHq-yHqiJTaoo6Y-iOH370j7yy!1750395096605.aere-xml-controller-67d4778877-2rkqw" /><input type="hidden" name="j_idt526_cmnPageTicket" value="4" />

<div class="section">
	<h2>搭乗者選択<a href="https://www.ana.co.jp/other/int/meta/0086.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="特典のご利用に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei21h/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a></h2>
	<table class="passengerInfo">
		<thead>
			<tr>
				
				<th class="name">氏名</th>
				
				<th class="dateOfBirthWide" id="dateOfBirthHeader">生年月日</th>
				
				<th class="gender lastCol" id="genderHeader">性別<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei21h/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span></th>
				
			</tr>
		</thead>
		<tbody>
				
				<tr>
					<th class="parent_01" id="parentHeader01">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:0:passenger" type="checkbox" name="passengers:0:passenger" checked="checked" data-number="1" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:0:passenger">NAOKI SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader01">1963年06月05日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader01">				
							<span class="formWrapper">
							<label for="passengers:0:gender" class="visuallyHidden">NAOKI SATOの性別</label><select id="passengers:0:gender" name="passengers:0:gender" size="1">	<option value="">選択してください</option>
	<option value="M" selected="selected">男性</option>
	<option value="F">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_02" id="parentHeader02">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:1:passenger" type="checkbox" name="passengers:1:passenger" data-number="2" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:1:passenger">REO SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader02">1995年05月18日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader02">				
							<span class="formWrapper">
							<label for="passengers:1:gender" class="visuallyHidden">REO SATOの性別</label><select id="passengers:1:gender" name="passengers:1:gender" size="1">	<option value="">選択してください</option>
	<option value="M" selected="selected">男性</option>
	<option value="F">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_03" id="parentHeader03">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:2:passenger" type="checkbox" name="passengers:2:passenger" data-number="3" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:2:passenger">ERI SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader03">1963年08月14日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader03">				
							<span class="formWrapper">
							<label for="passengers:2:gender" class="visuallyHidden">ERI SATOの性別</label><select id="passengers:2:gender" name="passengers:2:gender" size="1">	<option value="">選択してください</option>
	<option value="M">男性</option>
	<option value="F" selected="selected">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_04" id="parentHeader04">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:3:passenger" type="checkbox" name="passengers:3:passenger" data-number="4" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:3:passenger">TAISHI SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader04">1992年12月03日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader04">				
							<span class="formWrapper">
							<label for="passengers:3:gender" class="visuallyHidden">TAISHI SATOの性別</label><select id="passengers:3:gender" name="passengers:3:gender" size="1">	<option value="">選択してください</option>
	<option value="M" selected="selected">男性</option>
	<option value="F">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_05" id="parentHeader05">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:4:passenger" type="checkbox" name="passengers:4:passenger" data-number="5" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:4:passenger">HIROMI FUJIMOTO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader05">1961年11月23日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader05">				
							<span class="formWrapper">
							<label for="passengers:4:gender" class="visuallyHidden">HIROMI FUJIMOTOの性別</label><select id="passengers:4:gender" name="passengers:4:gender" size="1">	<option value="">選択してください</option>
	<option value="M">男性</option>
	<option value="F" selected="selected">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
		</tbody>
	</table>
</div>
		<div class="section">
			<h2 class="midstream">連絡先情報
			</h2>
			<h3>代表者
			</h3>
	
	
	
	<div class="formInput">
		<fieldset>
			<legend><label>代表者</label></legend>
			<dl id="contactEmailAddressInfo">
				<dt class="hasRequiredIcon">メールアドレス<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei21h/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span><a href="https://www.ana.co.jp/other/int/meta/0057.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="メールの宛先と内容に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei21h/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a>
				</dt>
				<dd>
					<ul class="verticalList">
						<li class="nonMarginTop"><label for="representativeEmail" class="visuallyHidden">代表者メールアドレス</label><input id="representativeEmail" type="text" name="representativeEmail" value="<EMAIL>" class="widthLarge" placeholder="例：<EMAIL>" />
						</li>
						<li>
							<span class="supplementTxt" id="confirmMailAddressForRepresentative">確認のため再度ご入力ください。
							</span><label for="confirmrepresentativeEmail" class="visuallyHidden">確認用メールアドレス</label><input id="confirmrepresentativeEmail" type="text" name="confirmrepresentativeEmail" value="<EMAIL>" class="widthLarge jsNotPasteInput" placeholder="例：<EMAIL>" aria-describedby="confirmMailAddressForRepresentative" />
						</li>
					</ul>
				</dd>
					<dd style="display:none;"><input id="updateAlwaysInfo" type="checkbox" name="updateAlwaysInfo" /><label for="updateAlwaysInfo">ログイン中の会員情報へこのメールアドレスを登録</label>
					</dd>
			</dl>
			<dl>
				<dt>電話番号<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei21h/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span>
				</dt>
				<dd class="js-countryWrap">
					<ul>
						<li><label for="representativeTelType" class="visuallyHidden">電話番号種別</label><select id="representativeTelType" name="representativeTelType" class="basicWidth js-typeSelect" size="1" onchange="changeTelType(this,'representativeTel')">	<option value="M1" selected="selected">携帯</option>
	<option value="B1">会社</option>
	<option value="H1">自宅</option>
</select>
						</li>
						<li><label for="representativeTelCountry" class="visuallyHidden">電話番号の国/地区選択</label><select id="representativeTelCountry" name="representativeTelCountry" class="js-countrySelect" size="1">	<option value="" data-sms-possible="true" data-country-code="---">選択してください</option>
	<option value="JP" selected="selected" data-sms-possible="true">日本</option>
	<option value="-" data-sms-possible="true" data-country-code="---">-----------</option>
	<option value="IS" data-sms-possible="true">アイスランド</option>
	<option value="IE" data-sms-possible="true">アイルランド</option>
	<option value="AZ" data-sms-possible="true">アゼルバイジャン</option>
	<option value="AF" data-sms-possible="true">アフガニスタン</option>
	<option value="US" data-sms-possible="true">アメリカ合衆国/カナダ</option>
	<option value="AE" data-sms-possible="true">アラブ首長国連邦</option>
	<option value="DZ" data-sms-possible="true">アルジェリア</option>
	<option value="AR" data-sms-possible="true">アルゼンチン</option>
	<option value="AW" data-sms-possible="true">アルバ</option>
	<option value="AL" data-sms-possible="true">アルバニア</option>
	<option value="AM" data-sms-possible="true">アルメニア</option>
	<option value="AO" data-sms-possible="true">アンゴラ</option>
	<option value="AG" data-sms-possible="true">アンティグア・バーブーダ</option>
	<option value="AD" data-sms-possible="true">アンドラ</option>
	<option value="YE" data-sms-possible="true">イエメン</option>
	<option value="GB" data-sms-possible="true">イギリス</option>
	<option value="IL" data-sms-possible="true">イスラエル</option>
	<option value="IT" data-sms-possible="true">イタリア</option>
	<option value="IQ" data-sms-possible="true">イラク</option>
	<option value="IR" data-sms-possible="true">イラン</option>
	<option value="IN" data-sms-possible="true">インド</option>
	<option value="ID" data-sms-possible="true">インドネシア</option>
	<option value="UG" data-sms-possible="true">ウガンダ</option>
	<option value="UA" data-sms-possible="true">ウクライナ</option>
	<option value="UZ" data-sms-possible="true">ウズベキスタン</option>
	<option value="UY" data-sms-possible="true">ウルグアイ</option>
	<option value="EC" data-sms-possible="true">エクアドル</option>
	<option value="EG" data-sms-possible="true">エジプト</option>
	<option value="EE" data-sms-possible="true">エストニア</option>
	<option value="SZ" data-sms-possible="true">エスワティニ</option>
	<option value="ET" data-sms-possible="true">エチオピア</option>
	<option value="ER" data-sms-possible="true">エリトリア</option>
	<option value="SV" data-sms-possible="true">エルサルバドル</option>
	<option value="AU" data-sms-possible="true">オーストラリア</option>
	<option value="AT" data-sms-possible="true">オーストリア</option>
	<option value="OM" data-sms-possible="true">オマーン</option>
	<option value="NL" data-sms-possible="true">オランダ</option>
	<option value="GH" data-sms-possible="true">ガーナ</option>
	<option value="CV" data-sms-possible="true">カーボベルデ</option>
	<option value="GY" data-sms-possible="true">ガイアナ</option>
	<option value="KZ" data-sms-possible="true">カザフスタン</option>
	<option value="QA" data-sms-possible="true">カタール</option>
	<option value="CA" data-sms-possible="true">カナダ/アメリカ合衆国</option>
	<option value="GA" data-sms-possible="true">ガボン</option>
	<option value="CM" data-sms-possible="true">カメルーン</option>
	<option value="KR" data-sms-possible="true">韓国</option>
	<option value="GM" data-sms-possible="true">ガンビア</option>
	<option value="KH" data-sms-possible="true">カンボジア</option>
	<option value="KP" data-sms-possible="true">北朝鮮</option>
	<option value="MK" data-sms-possible="true">北マケドニア</option>
	<option value="GN" data-sms-possible="true">ギニア</option>
	<option value="GW" data-sms-possible="true">ギニアビサウ</option>
	<option value="CY" data-sms-possible="true">キプロス</option>
	<option value="CU" data-sms-possible="true">キューバ</option>
	<option value="GR" data-sms-possible="true">ギリシャ</option>
	<option value="KI" data-sms-possible="true">キリバス</option>
	<option value="KG" data-sms-possible="true">キルギス</option>
	<option value="GT" data-sms-possible="true">グアテマラ</option>
	<option value="GU" data-sms-possible="true">グアム</option>
	<option value="KW" data-sms-possible="true">クウェート</option>
	<option value="GD" data-sms-possible="true">グレナダ</option>
	<option value="HR" data-sms-possible="true">クロアチア</option>
	<option value="KY" data-sms-possible="true">ケイマン諸島</option>
	<option value="KE" data-sms-possible="true">ケニア</option>
	<option value="CI" data-sms-possible="true">コートジボワール</option>
	<option value="CR" data-sms-possible="true">コスタリカ</option>
	<option value="KM" data-sms-possible="true">コモロ</option>
	<option value="CO" data-sms-possible="true">コロンビア</option>
	<option value="CG" data-sms-possible="true">コンゴ共和国</option>
	<option value="CD" data-sms-possible="true">コンゴ民主共和国</option>
	<option value="SA" data-sms-possible="true">サウジアラビア</option>
	<option value="ZM" data-sms-possible="true">ザンビア</option>
	<option value="SM" data-sms-possible="true">サンマリノ</option>
	<option value="SL" data-sms-possible="true">シエラレオネ</option>
	<option value="DJ" data-sms-possible="true">ジブチ</option>
	<option value="JM" data-sms-possible="true">ジャマイカ</option>
	<option value="GE" data-sms-possible="true">ジョージア</option>
	<option value="SY" data-sms-possible="true">シリア</option>
	<option value="SG" data-sms-possible="true">シンガポール</option>
	<option value="ZW" data-sms-possible="true">ジンバブエ</option>
	<option value="CH" data-sms-possible="true">スイス</option>
	<option value="SE" data-sms-possible="true">スウェーデン</option>
	<option value="SD" data-sms-possible="true">スーダン</option>
	<option value="ES" data-sms-possible="true">スペイン</option>
	<option value="SR" data-sms-possible="true">スリナム</option>
	<option value="LK" data-sms-possible="true">スリランカ</option>
	<option value="SK" data-sms-possible="true">スロバキア</option>
	<option value="SI" data-sms-possible="true">スロベニア</option>
	<option value="SC" data-sms-possible="true">セイシェル</option>
	<option value="GQ" data-sms-possible="true">赤道ギニア</option>
	<option value="SN" data-sms-possible="true">セネガル</option>
	<option value="RS" data-sms-possible="true">セルビア</option>
	<option value="KN" data-sms-possible="true">セントクリストファー・ネイビス</option>
	<option value="LC" data-sms-possible="true">セントルシア</option>
	<option value="SB" data-sms-possible="true">ソロモン諸島</option>
	<option value="TH" data-sms-possible="true">タイ</option>
	<option value="TW" data-sms-possible="true">台湾</option>
	<option value="TJ" data-sms-possible="true">タジキスタン</option>
	<option value="TZ" data-sms-possible="true">タンザニア</option>
	<option value="CZ" data-sms-possible="true">チェコ</option>
	<option value="TD" data-sms-possible="true">チャド</option>
	<option value="CF" data-sms-possible="true">中央アフリカ共和国</option>
	<option value="CN" data-sms-possible="true">中国</option>
	<option value="TN" data-sms-possible="true">チュニジア</option>
	<option value="CL" data-sms-possible="true">チリ</option>
	<option value="TV" data-sms-possible="true">ツバル</option>
	<option value="DK" data-sms-possible="true">デンマーク</option>
	<option value="DE" data-sms-possible="true">ドイツ</option>
	<option value="TG" data-sms-possible="true">トーゴ</option>
	<option value="DO" data-sms-possible="true">ドミニカ共和国</option>
	<option value="DM" data-sms-possible="true">ドミニカ国</option>
	<option value="TT" data-sms-possible="true">トリニダード・トバゴ</option>
	<option value="TM" data-sms-possible="true">トルクメニスタン</option>
	<option value="TR" data-sms-possible="true">トルコ</option>
	<option value="TO" data-sms-possible="true">トンガ</option>
	<option value="NG" data-sms-possible="true">ナイジェリア</option>
	<option value="NR" data-sms-possible="true">ナウル</option>
	<option value="NA" data-sms-possible="true">ナミビア</option>
	<option value="NI" data-sms-possible="true">ニカラグア</option>
	<option value="NE" data-sms-possible="true">ニジェール</option>
	<option value="NZ" data-sms-possible="true">ニュージーランド</option>
	<option value="NP" data-sms-possible="true">ネパール</option>
	<option value="NO" data-sms-possible="true">ノルウェー</option>
	<option value="BH" data-sms-possible="true">バーレーン</option>
	<option value="HT" data-sms-possible="true">ハイチ</option>
	<option value="PK" data-sms-possible="true">パキスタン</option>
	<option value="VA" data-sms-possible="true">バチカン</option>
	<option value="PA" data-sms-possible="true">パナマ</option>
	<option value="VU" data-sms-possible="true">バヌアツ</option>
	<option value="BS" data-sms-possible="true">バハマ</option>
	<option value="PG" data-sms-possible="true">パプアニューギニア</option>
	<option value="BM" data-sms-possible="true">バミューダ</option>
	<option value="PW" data-sms-possible="true">パラオ</option>
	<option value="PY" data-sms-possible="true">パラグアイ</option>
	<option value="BB" data-sms-possible="true">バルバドス</option>
	<option value="PS" data-sms-possible="true">パレスチナ</option>
	<option value="HU" data-sms-possible="true">ハンガリー</option>
	<option value="BD" data-sms-possible="true">バングラデシュ</option>
	<option value="TL" data-sms-possible="true">東ティモール</option>
	<option value="FJ" data-sms-possible="true">フィジー</option>
	<option value="PH" data-sms-possible="true">フィリピン</option>
	<option value="FI" data-sms-possible="true">フィンランド</option>
	<option value="BT" data-sms-possible="true">ブータン</option>
	<option value="PR" data-sms-possible="true">プエルトリコ</option>
	<option value="BR" data-sms-possible="true">ブラジル</option>
	<option value="FR" data-sms-possible="true">フランス</option>
	<option value="PF" data-sms-possible="true">フランス領ポリネシア</option>
	<option value="BG" data-sms-possible="true">ブルガリア</option>
	<option value="BF" data-sms-possible="true">ブルキナファソ</option>
	<option value="BN" data-sms-possible="true">ブルネイ</option>
	<option value="BI" data-sms-possible="true">ブルンジ</option>
	<option value="VN" data-sms-possible="true">ベトナム</option>
	<option value="BJ" data-sms-possible="true">ベナン</option>
	<option value="VE" data-sms-possible="true">ベネズエラ</option>
	<option value="BY" data-sms-possible="true">ベラルーシ</option>
	<option value="BZ" data-sms-possible="true">ベリーズ</option>
	<option value="PE" data-sms-possible="true">ペルー</option>
	<option value="BE" data-sms-possible="true">ベルギー</option>
	<option value="PL" data-sms-possible="true">ポーランド</option>
	<option value="BA" data-sms-possible="true">ボスニア・ヘルツェゴビナ</option>
	<option value="BW" data-sms-possible="true">ボツワナ</option>
	<option value="BO" data-sms-possible="true">ボリビア</option>
	<option value="PT" data-sms-possible="true">ポルトガル</option>
	<option value="HK" data-sms-possible="true">香港</option>
	<option value="HN" data-sms-possible="true">ホンジュラス</option>
	<option value="MH" data-sms-possible="true">マーシャル諸島</option>
	<option value="MO" data-sms-possible="true">マカオ</option>
	<option value="MG" data-sms-possible="true">マダガスカル</option>
	<option value="MW" data-sms-possible="true">マラウイ</option>
	<option value="ML" data-sms-possible="true">マリ</option>
	<option value="MT" data-sms-possible="true">マルタ</option>
	<option value="MY" data-sms-possible="true">マレーシア</option>
	<option value="FM" data-sms-possible="true">ミクロネシア連邦</option>
	<option value="ZA" data-sms-possible="true">南アフリカ</option>
	<option value="MM" data-sms-possible="true">ミャンマー</option>
	<option value="MX" data-sms-possible="true">メキシコ</option>
	<option value="MU" data-sms-possible="true">モーリシャス</option>
	<option value="MR" data-sms-possible="true">モーリタニア</option>
	<option value="MZ" data-sms-possible="true">モザンビーク</option>
	<option value="MC" data-sms-possible="true">モナコ</option>
	<option value="MV" data-sms-possible="true">モルディヴ</option>
	<option value="MD" data-sms-possible="true">モルドバ</option>
	<option value="MA" data-sms-possible="true">モロッコ</option>
	<option value="MN" data-sms-possible="true">モンゴル</option>
	<option value="ME" data-sms-possible="true">モンテネグロ</option>
	<option value="JO" data-sms-possible="true">ヨルダン</option>
	<option value="LA" data-sms-possible="true">ラオス人民民主共和国</option>
	<option value="LV" data-sms-possible="true">ラトビア</option>
	<option value="LT" data-sms-possible="true">リトアニア</option>
	<option value="LY" data-sms-possible="true">リビア</option>
	<option value="LI" data-sms-possible="true">リヒテンシュタイン</option>
	<option value="LR" data-sms-possible="true">リベリア</option>
	<option value="RO" data-sms-possible="true">ルーマニア</option>
	<option value="LU" data-sms-possible="true">ルクセンブルク</option>
	<option value="RW" data-sms-possible="true">ルワンダ</option>
	<option value="LS" data-sms-possible="true">レソト</option>
	<option value="LB" data-sms-possible="true">レバノン</option>
	<option value="RU" data-sms-possible="true">ロシア</option>
</select>
						</li>
						<li class="numberInputWrap">
							<span id="numberInputInfo1" class="supplementTxt marginTop">先頭に0(ゼロ)がある場合、0(ゼロ)を抜いてください(一部、国・地域除く)。携帯電話番号の場合は、携帯電話を契約された国を選択ください。</span>
							<div class="numberRowWrap">
								<div class="block">
									
									<span class="visuallyHidden">国番号</span>
									<span class="viewInput widthMini js-countryCode"></span>
								</div>
								<div class="block"><label for="representativeTel" class="visuallyHidden">電話番号</label><input id="representativeTel" type="text" name="representativeTel" value="9042504176" class="widthMiddle" placeholder="例：9012345678" maxlength="30" aria-describedby="numberInputInfo1" />
								</div>
							</div>
						</li>
					</ul>
				</dd>
			</dl>
		</fieldset>
	</div>
	<div class="titleWrapper">
		<h3>
			<span class="text">eメール・SMSに関するご案内<a href="https://www.ana.co.jp/other/int/meta/0057.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="eメール・SMSに関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei21h/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a></span>
		</h3>
	</div>
	<div class="formInput">
	<h4 class="formAreaTitle">主に搭乗・運航に関わるeメールの送付先設定（米国へ通知いたします）<a href="https://www.ana.co.jp/other/int/meta/0629.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="米国入国用eメール・SMS・電話番号に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei21h/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a>
	</h4>
	<div class="paxSettingArea js-mailFormWrap onePax is-open">
			<div class="settingArea">
					<dl>
						<dt class="breakWord">NAOKI SATO
						</dt>
						<dd>
							<fieldset>
								<legend>NAOKI SATOのeメール送付先および米国到着時に提供するメールアドレス</legend>代表者と同じ
							</fieldset>
						</dd>
					</dl>
			</div>
	</div>
	<h4 class="formAreaTitle hasTopLine">主に搭乗・運航に関わるSMSの送付先および米国で受信できる電話番号の設定（米国へ通知いたします）<a href="https://www.ana.co.jp/other/int/meta/0629.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="米国入国用eメール・SMS・電話番号に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei21h/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a>
	</h4>
	<div class="paxSettingArea js-smsFormWrap onePax is-open">
			<div class="settingArea">
					<dl>
						<dt class="breakWord">NAOKI SATO<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei21h/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span>
						</dt>
						<dd>
							<fieldset>
								<legend>NAOKI SATOの1つ目の電話番号</legend>
									<ul class="verticalList js-formItem">
										<li><input type="radio" id="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSameAsRepresentativeSms:radioItem" name="contactsSms:0:recipientSmsEachPersonSelection:radioGroup" value="0" checked="checked" /><label for="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSameAsRepresentativeSms:radioItem">代表者電話番号と同じ</label>
										</li>
										<li><input type="radio" id="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSmsPerPassenger:radioItem" name="contactsSms:0:recipientSmsEachPersonSelection:radioGroup" value="1" /><label for="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSmsPerPassenger:radioItem">電話番号を指定</label>
											<div class="indentArea js-formImputArea">
												<span id="confirmTelNumberForRepresentative:0" class="supplementTxt marginTop">先頭に0(ゼロ)がある場合、0(ゼロ)を抜いてください(一部、国・地域除く)。携帯電話番号の場合は、携帯電話を契約された国を選択ください。</span>
												<ul class="countryNumberInput js-countryWrap">
													<li><label for="contactsSms:0:recipientSmsEachPersonSelection:passengerSmsCountry" class="visuallyHidden">電話番号の国/地区選択</label><select id="contactsSms:0:recipientSmsEachPersonSelection:passengerSmsCountry" name="contactsSms:0:recipientSmsEachPersonSelection:passengerSmsCountry" class="countrySelect js-countrySelect" size="1">	<option value="" data-country-code="---">選択してください</option>
	<option value="JP">日本</option>
	<option value="-" data-country-code="---">-----------</option>
	<option value="IE">アイルランド</option>
	<option value="AZ">アゼルバイジャン</option>
	<option value="AF">アフガニスタン</option>
	<option value="US">アメリカ合衆国/カナダ</option>
	<option value="AE">アラブ首長国連邦</option>
	<option value="DZ">アルジェリア</option>
	<option value="AR">アルゼンチン</option>
	<option value="AW">アルバ</option>
	<option value="AL">アルバニア</option>
	<option value="AM">アルメニア</option>
	<option value="AG">アンティグア・バーブーダ</option>
	<option value="AD">アンドラ</option>
	<option value="YE">イエメン</option>
	<option value="GB">イギリス</option>
	<option value="IL">イスラエル</option>
	<option value="IT">イタリア</option>
	<option value="IQ">イラク</option>
	<option value="IR">イラン</option>
	<option value="IN">インド</option>
	<option value="ID">インドネシア</option>
	<option value="UG">ウガンダ</option>
	<option value="UA">ウクライナ</option>
	<option value="UZ">ウズベキスタン</option>
	<option value="UY">ウルグアイ</option>
	<option value="EC">エクアドル</option>
	<option value="EG">エジプト</option>
	<option value="EE">エストニア</option>
	<option value="ER">エリトリア</option>
	<option value="SV">エルサルバドル</option>
	<option value="AU">オーストラリア</option>
	<option value="AT">オーストリア</option>
	<option value="OM">オマーン</option>
	<option value="NL">オランダ</option>
	<option value="GH">ガーナ</option>
	<option value="GY">ガイアナ</option>
	<option value="KZ">カザフスタン</option>
	<option value="QA">カタール</option>
	<option value="CA">カナダ/アメリカ合衆国</option>
	<option value="GA">ガボン</option>
	<option value="CM">カメルーン</option>
	<option value="KR">韓国</option>
	<option value="GM">ガンビア</option>
	<option value="KH">カンボジア</option>
	<option value="GN">ギニア</option>
	<option value="GW">ギニアビサウ</option>
	<option value="CY">キプロス</option>
	<option value="CU">キューバ</option>
	<option value="GR">ギリシャ</option>
	<option value="KG">キルギス</option>
	<option value="GT">グアテマラ</option>
	<option value="GU">グアム</option>
	<option value="KW">クウェート</option>
	<option value="GD">グレナダ</option>
	<option value="HR">クロアチア</option>
	<option value="KY">ケイマン諸島</option>
	<option value="KE">ケニア</option>
	<option value="CI">コートジボワール</option>
	<option value="CR">コスタリカ</option>
	<option value="KM">コモロ</option>
	<option value="CO">コロンビア</option>
	<option value="CD">コンゴ民主共和国</option>
	<option value="SA">サウジアラビア</option>
	<option value="ZM">ザンビア</option>
	<option value="SL">シエラレオネ</option>
	<option value="JM">ジャマイカ</option>
	<option value="GE">ジョージア</option>
	<option value="SY">シリア</option>
	<option value="SG">シンガポール</option>
	<option value="ZW">ジンバブエ</option>
	<option value="CH">スイス</option>
	<option value="SE">スウェーデン</option>
	<option value="SD">スーダン</option>
	<option value="ES">スペイン</option>
	<option value="SR">スリナム</option>
	<option value="LK">スリランカ</option>
	<option value="SK">スロバキア</option>
	<option value="SI">スロベニア</option>
	<option value="SN">セネガル</option>
	<option value="RS">セルビア</option>
	<option value="KN">セントクリストファー・ネイビス</option>
	<option value="LC">セントルシア</option>
	<option value="TH">タイ</option>
	<option value="TW">台湾</option>
	<option value="TJ">タジキスタン</option>
	<option value="TZ">タンザニア</option>
	<option value="CZ">チェコ</option>
	<option value="TD">チャド</option>
	<option value="CF">中央アフリカ共和国</option>
	<option value="CN">中国</option>
	<option value="TN">チュニジア</option>
	<option value="CL">チリ</option>
	<option value="DK">デンマーク</option>
	<option value="DE">ドイツ</option>
	<option value="TG">トーゴ</option>
	<option value="DO">ドミニカ共和国</option>
	<option value="TT">トリニダード・トバゴ</option>
	<option value="TR">トルコ</option>
	<option value="NG">ナイジェリア</option>
	<option value="NI">ニカラグア</option>
	<option value="NE">ニジェール</option>
	<option value="NZ">ニュージーランド</option>
	<option value="NP">ネパール</option>
	<option value="NO">ノルウェー</option>
	<option value="HT">ハイチ</option>
	<option value="PK">パキスタン</option>
	<option value="PA">パナマ</option>
	<option value="BS">バハマ</option>
	<option value="PG">パプアニューギニア</option>
	<option value="BM">バミューダ</option>
	<option value="PW">パラオ</option>
	<option value="PY">パラグアイ</option>
	<option value="BB">バルバドス</option>
	<option value="PS">パレスチナ</option>
	<option value="HU">ハンガリー</option>
	<option value="BD">バングラデシュ</option>
	<option value="TL">東ティモール</option>
	<option value="FJ">フィジー</option>
	<option value="PH">フィリピン</option>
	<option value="FI">フィンランド</option>
	<option value="BT">ブータン</option>
	<option value="PR">プエルトリコ</option>
	<option value="BR">ブラジル</option>
	<option value="FR">フランス</option>
	<option value="PF">フランス領ポリネシア</option>
	<option value="BG">ブルガリア</option>
	<option value="BF">ブルキナファソ</option>
	<option value="BN">ブルネイ</option>
	<option value="BI">ブルンジ</option>
	<option value="VN">ベトナム</option>
	<option value="BJ">ベナン</option>
	<option value="VE">ベネズエラ</option>
	<option value="BY">ベラルーシ</option>
	<option value="PE">ペルー</option>
	<option value="BE">ベルギー</option>
	<option value="PL">ポーランド</option>
	<option value="BA">ボスニア・ヘルツェゴビナ</option>
	<option value="BW">ボツワナ</option>
	<option value="HK">香港</option>
	<option value="MH">マーシャル諸島</option>
	<option value="MO">マカオ</option>
	<option value="MG">マダガスカル</option>
	<option value="MW">マラウイ</option>
	<option value="ML">マリ</option>
	<option value="MT">マルタ</option>
	<option value="MY">マレーシア</option>
	<option value="ZA">南アフリカ</option>
	<option value="MM">ミャンマー</option>
	<option value="MX">メキシコ</option>
	<option value="MU">モーリシャス</option>
	<option value="MR">モーリタニア</option>
	<option value="MZ">モザンビーク</option>
	<option value="MC">モナコ</option>
	<option value="MV">モルディヴ</option>
	<option value="MA">モロッコ</option>
	<option value="MN">モンゴル</option>
	<option value="ME">モンテネグロ</option>
	<option value="JO">ヨルダン</option>
	<option value="LA">ラオス人民民主共和国</option>
	<option value="LV">ラトビア</option>
	<option value="LT">リトアニア</option>
	<option value="LY">リビア</option>
	<option value="LR">リベリア</option>
	<option value="RO">ルーマニア</option>
	<option value="LU">ルクセンブルク</option>
	<option value="RW">ルワンダ</option>
	<option value="LB">レバノン</option>
	<option value="RU">ロシア</option>
</select>
													</li>
													<li>
														
														<div class="numberRowWrap">
															<div class="block">
																<span class="visuallyHidden">国番号</span>
																<span class="countryCode viewInput js-countryCode"></span>
															</div>
															
															<div class="block"><label for="contactsSms:0:recipientSmsEachPersonSelection:flightStatusNotificationContactPointSmsDescription" class="visuallyHidden">電話番号</label><input id="contactsSms:0:recipientSmsEachPersonSelection:flightStatusNotificationContactPointSmsDescription" type="text" name="contactsSms:0:recipientSmsEachPersonSelection:flightStatusNotificationContactPointSmsDescription" class="telNumber" placeholder="例：9012345678" maxlength="30" aria-describedby="confirmTelNumberForRepresentative:0" />
															</div>
														</div>
													</li>
												</ul>
											</div>
										</li>
									</ul>
							</fieldset>
						</dd>
					</dl>
			</div>
	</div>
	</div>
	
	<div class="formInput articleLine">
		<h3>滞在中の連絡先(任意)
		</h3>
		<dl>
			
			<dt><label for="accommodationOne">現地連絡先1</label>
			</dt>
			<dd><input id="accommodationOne" type="text" name="accommodationOne" class="widthLarge" placeholder="例：09012345678 ABC HOTEL" maxlength="28" title="例：09012345678 ABC HOTEL" />
			</dd>
		</dl>
		<dl>
			
			<dt><label for="accommodationTwo">現地連絡先2</label>
			</dt>
			<dd><input id="accommodationTwo" type="text" name="accommodationTwo" class="widthLarge" placeholder="例：09012345678 ABC HOTEL" maxlength="28" title="例：09012345678 ABC HOTEL" />
			</dd>
		</dl>
	</div>
		</div>
		
		<p class="btnAreaSubmit btnArrowNext "><input id="next" type="submit" name="next" value="次へ" class="btnBase btnMainStream btnVerticalMain btnWidthVariable" onclick="return Asw.Dialog.getInstance('prebookConfirmDialog').toggle(event);return Asw.LoadingWindow.open(&quot;NORMAL&quot;, event)" />
		</p><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:2" value="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" autocomplete="off" />
</form><div id="cmnDynamicMessages" class="infoBoxSection">
				<dl class="infoBox importantInfo jsAccordionSwitch">
					<dt><strong>重要なご案内</strong></dt>
					<dd>
						<ul class="jsAccordionSwitchList" id="informationMessages">
								<li>個人情報の利用目的<a href="https://www.ana.co.jp/other/int/meta/0012.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="ANAプライバシーポリシー(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">ANAプライバシーポリシー<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei21h/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>を必ずお読みいただき、内容に同意いただいた上で、お進みください。</li>
								<li><a href="https://www.ana.co.jp/other/int/meta/0058.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="Secure Flight Programについて(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">米国Secure Flightプログラム<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei21h/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>に基づき、パスポート名、生年月日、性別などの情報は事前に米国へ通知いたします。</li>
								<li>入力したeメールアドレス、電話番号は入国に必要な情報として米国に通知される可能性があります。</li>
									<li class="jsHiddenFlg toggleContents">お名前のスペルがパスポート名と一致しているかご確認ください。異なる場合はインターネットではお取り扱いできません。<a href="https://www.ana.co.jp/other/int/meta/intbe0001.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="ANAお問い合わせ先(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">ANA<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei21h/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>へ名前の変更をお申し出ください。</li>
									<li class="jsHiddenFlg toggleContents">二重国籍のお客様は、ご搭乗時に使用されるパスポートのお名前でご予約ください。<br />パスポートごとにお名前が異なる場合は、お客様ご自身で入国管理事務所・外務省・大使館などで各種条件をご確認のうえ、使用されるパスポートを決めてください。<br />なお、入国可否については国によって異なるため、お客様ご自身で各入国管理事務所などでご確認ください。</li>
									<li class="jsHiddenFlg toggleContents">旅行開始日時点で12歳以上の場合は大人、2歳以上12歳未満の場合は小児、2歳未満の場合は幼児の扱いとなります。なお、小児は大人と同マイル数が必要です。</li>
									<li class="jsHiddenFlg toggleContents">運航に関する情報をお客様へ通知するため、提携航空会社にもお客様のメールアドレスが提供されます。あらかじめご了承ください。</li>
									<li class="jsHiddenFlg toggleContents"><dl><dt>携帯電話へのメール送信をご希望のお客様へ</dt><dd>ドメイン指定受信をご利用の場合は、ana.co.jpとamadeus.comからのメールが受信できるようあらかじめ設定をお願いいたします。</dd></dl></li>
									<li class="jsHiddenFlg toggleContents">SMSはスマートフォンの番号に配信されます。一部の国ではSMSでのご案内をご利用いただけません。詳細は<a href="https://www.ana.co.jp/other/int/meta/0737.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="ショートメッセージ(SMS)でのご案内対象外の国について(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">ショートメッセージ(SMS)でのご案内対象外の国について<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei21h/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>をご覧ください。</li>
									<li class="jsHiddenFlg toggleContents">ログインされている会員ご本人が搭乗しない場合は、再度フライト検索画面より「ログインされている会員ご本人は搭乗しない」のチェックボックスを選択してお手続きください。</li>
						</ul>
							<a href="#" class="toggleSwitch jsTriggerSwitch" role="button" aria-controls="informationMessages" aria-expanded="false"><img src="https://aswbe-i.ana.co.jp/rei21h/international_asw/contents/cmn/image/toggle_open.png?7bec38a" alt="開閉ボタン" height="25" width="25" class="jsRollOver" />
							</a>
					</dd>
				</dl></div>
	<!--=========== /main =============-->
	</div>

	<!--=========== Script ============-->
	<script type="text/javascript">
	$(window).on('load', function() {
		// アコーディオンのメソッドと設定
		Asw.AccordionInfo('.jsAccordionSwitch', {
			'triggerSwitch': '.jsAccordionSwitch .jsTriggerSwitch',
			'listClass': '.jsAccordionSwitchList',
			'visibilityHidden': 'toggleContents'
		});
	});
	</script>
	<!--=========== Script ============-->
	<script type="text/javascript">
		//プラットフォーム判別（サマリーエリア追従用として）
		var Asw = Asw || {};
	
	  	Asw.ClientInfo = Asw.ClientInfo  || {};
	  	Asw.ClientInfo.DeviceType = Asw.ClientInfo.DeviceType || {};
	
	  	Asw.ClientInfo.DeviceType.PC = 'PC';
	  	Asw.ClientInfo.DeviceType.SP = 'SP';
	  	Asw.ClientInfo.DeviceType.TC = 'TC';
	
	  	Asw.ClientInfo.deviceType = Asw.ClientInfo.DeviceType.PC;
	
	 	if((navigator.userAgent.indexOf('Android') > 0 && navigator.userAgent.indexOf('Mobile') == -1) || navigator.userAgent.indexOf('iPad') > 0){
	    	Asw.ClientInfo.deviceType = Asw.ClientInfo.DeviceType.TC;
	  	}
	</script>
	<!--=========== /Script ============--><div id="summaryArea">
			<div class="flightSummaryArea">
						<div class="flightSummarySection">
							<h2>区間 1
							</h2>
							<p class="flightDate"><em>6</em>月<em class="hasLeft">28</em>日（土）
							</p>
							<div class="flightSummaryRoute">
									<div class="flightSummaryRouteSection">
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation1_1">
													<span>東京(成田)
													</span>
												</p>
												<p class="time">
													<span>17:00
													</span>
												</p>
											</div>
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation1_1">
													<span>シカゴ(ORD)
													</span>
												</p>
												<p class="time">
													<span>14:55
													</span>
												</p>
											</div>
										<p class="flightNumberClass">
												<span>NH012</span><span><img src="https://aswbe-i.ana.co.jp/rei21h/international_asw/contents/cmn/image/flight_ana_summaryarea.png?717d3c0" alt="ANA運航便" height="11" width="41" /></span>
												<br />特典ビジネス
										</p>
									</div>
							</div>
						</div>
						<div class="flightSummarySection">
							<h2 class="midstream">区間 2
							</h2>
							<p class="flightDate"><em>7</em>月<em class="hasLeft">23</em>日（水）
							</p>
							<div class="flightSummaryRoute">
									<div class="flightSummaryRouteSection">
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation2_1">
													<span>ホノルル(オアフ島)
													</span>
												</p>
												<p class="time">
													<span>11:35
													</span>
												</p>
											</div>
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation2_1">
													<span>東京(成田)
													</span>
												</p>
												<p class="time">
													<span>14:50
																	<span>翌日
																	</span>
													</span>
												</p>
											</div>
										<p class="flightNumberClass">
												<span>NH183</span><span><img src="https://aswbe-i.ana.co.jp/rei21h/international_asw/contents/cmn/image/flight_ana_summaryarea.png?717d3c0" alt="ANA運航便" height="11" width="41" /></span>
												<br />特典エコノミー
										</p>
									</div>
							</div>
						</div>
					<div class="total">
						<p class="passenger">大人 x1
						</p>
						<div class="totalArea">
					  		<dl class="mealAmountWrap">
									<dt>有料機内食サービス
									</dt>
									<dd><em class="mealAmount">0</em><span class="currencyCode">円</span>
									</dd>
							</dl>
									<div>
										<div class="mileageAndTotal">
												<dl class="requiredMileage">
													<dt>必要マイル
													</dt>
													<dd><em class="requiredMileage">74,000</em><span class="currencyCode">マイル</span>
													</dd>
												</dl>
												<dl>
													<dt>総額
													</dt>
													<dd><em class="awardTotalPayment">68,130</em>円
													</dd>
												</dl>
												<p class="includeFareAndFuelCost">※各種税金、燃油特別付加運賃等を含みます。
												</p>
										</div>
									</div>
						</div>
					</div>
				
			</div></div>
		<!--===== /cmnWrapper =====-->
		</div>
			
		<span id="message" class="visuallyHidden" role="alert" aria-live="assertive"></span>
		<span id="politeMessage" class="visuallyHidden" role="log" aria-live="polite"></span><script type="text/javascript">
		var Asw = Asw || {};
		Asw.BaseOutput = {"sessionID":"mC6LrYEfi7","language":"ja","siteCatalystLanguageCode":"J","operationDate":"20250620","operationDateTime":"20250620135145","office":{"officeCode":"TYONH08ZZ","country2letterCode":"JP"},"bookingType":"A","pageID":"A04_P01","siteCatalystPageName":"INT_BE_AWARD_J_A04特典予約_P01旅客情報入力（必須情報）","device":{"deviceType":"PC"}};
	</script><div id="promotionArea">
		<!--===== promotionArea =====-->
		<!--===== /promotionArea =====--></div>
		<!--=========== /cmnContainer ============--></div><div id="cmnBaloonArea" aria-live="polite"></div><div id="cmnPopupArea" aria-live="polite"></div>

	

	<div id="cmnFooterWrapper">
		<!--=========== .cmnFooterWrapper ============-->
		<div id="cmnFooter">
			<!--===== cmnFooter =====-->
			<ul>
				<li><a href="https://www.ana.co.jp/other/int/meta/0012.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">プライバシーポリシー<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21h/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
					<li><a href="https://www.ana.co.jp/other/int/meta/0670.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">利用者情報の外部送信について<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21h/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
					</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/0052.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">サイト利用規約<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21h/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/be0238.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">推奨環境<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21h/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/0133.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">サイトマップ<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21h/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/0732.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">運送約款<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21h/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
					<li class="lastChild"><a href="https://www.ana.co.jp/other/int/meta/0556.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">ウェブアクセシビリティについて<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21h/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
					</li>
			</ul>
			<p class="blankText" aria-hidden="true"><img src="https://aswbe-i.ana.co.jp/rei21h/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="" height="10" width="10" />外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。</p>
			<p class="copyright"><span lang="en" xml:lang="en">Copyright&#169;ANA</span>
			</p>
			<p class="starAllianceLogo"><a href="http://www.staralliance.com/ja/" target="_blank"><img src="https://aswbe-i.ana.co.jp/rei21h/international_asw/contents/cmn/image/star_alliance_logo.png?0fe73b5" alt="A STAR ALLIANCE MEMBER" height="18" width="175" /><span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21h/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
			</p>
			<!--===== cmnFooter =====-->
		</div>
		<!--=========== /cmnFooterWrapper ============-->
	</div>

		
		<div id="cmnLoadingForAjax"><img src="https://aswbe-i.ana.co.jp/rei21h/international_asw/contents/cmn/image/bg_modalWindow.png?717d3c0" alt="" height="0" width="0" />
			<div class="loadingArea">
				<p class="loadingImage"><img src="https://aswbe-i.ana.co.jp/rei21h/international_asw/contents/cmn/image/loading.gif?717d3c0" alt="リクエストを処理しています...しばらくお待ちください。" height="33" width="33" />
				</p>
			</div>
		</div>
		
		
		<div id="popupAltSentence" style="display: none" aria-hidden="true">別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。</div>
	<div id="cmnSiteCatalyst" aria-hidden="true"><div id="cmnSiteCatalystParamArea"><script type="text/javascript">
				// ▼SiteCatalyst
				// SiteCatalyst code version: H.2.
				// Copyright 1997-2005 Omniture, Inc. More info available at http://www.omniture.com
				SiteCatalystReportSuites   = "INT";
				SiteCatalystCharSet        = "UTF-8";
				SiteCatalystChannel        = "BE_AWARD_J";
				SiteCatalystPageName       = "INT_BE_AWARD_J_A04\u7279\u5178\u4E88\u7D04_P01\u65C5\u5BA2\u60C5\u5831\u5165\u529B\uFF08\u5FC5\u9808\u60C5\u5831\uFF09";
				SiteCatalystERR            = "";
				SiteCatalystEventNum       = "";
				SiteCatalystPurchase       = "FALSE";
				SiteCatalystProductPrice   = "";
				SiteCatalystCurrencyCode   = "";
				SiteCatalystSeatAvailabilityINT   = "";
				SiteCatalystDepartDate     = "";
				SiteCatalystDepart         = "";
				SiteCatalystArrive         = "";
				SiteCatalystFlightNo       = "";
				SiteCatalystClass          = "";
				SiteCatalystFare           = "";
				SiteCatalystPurchaseID     = "";
				
				SiteCatalystLocater        = "";
				SiteCatalystPayment        = "";
				SiteCatalystIncome         = "";
				
				SiteCatalystPax            = "";
				SiteCatalystManipulateDay  = "";
				SiteCatalystCompartmentClass   = "";
				SiteCatalystOptionInfo     = "";
				SiteCatalystOutboundUpsellPrice   = "";
				SiteCatalystInboundUpsellPrice    = "";
				
				SiteCatalystSearchInput    = "";
				SiteCatalystSegment        = "";
				SiteCatalystOfficeCode     = "";
				SiteCatalystPassengerCount = "";
				SiteCatalystPassenger      = "";
				SiteCatalystAncillaryService = "";
				// End SiteCatalyst code version: H.2.
				// ▲SiteCatalyst
			</script></div><script type="text/javascript"><!-- if(navigator.appVersion.indexOf('MSIE')>=0)document.write(unescape('%3C')+'\!-'+'-') // --></script>
		<!--/DO NOT REMOVE/-->
		

		
		<!-- Google Tag Manager -->
		<script type="text/javascript" charset="Shift_JIS" src="https://www.ana.co.jp/common/js/GTMINTBE.js"></script>
		<!-- End Google Tag Manager -->
		<!-- Yahoo Tag Manager -->
		<script type="text/javascript" charset="Shift_JIS" src="https://www.ana.co.jp/common/js/YTMINTBE.js"></script>
		<!-- End Yahoo Tag Manager -->
		

	</div>

	

	<div class="mboxDefault"></div><script type="text/javascript">mboxCreate("ASW_common_A04_P01");</script>
			<script type="text/javascript" src="https://www.ana.co.jp/common/js/tealium/tealium.js"></script>
			<script type="text/javascript" src="//cdn.evgnet.com/beacon/allnipponairways/nonamcprd/scripts/evergage.min.js"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21h/international_asw/javax.faces.resource/jsf.js.xhtml?ln=javax.faces"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21h/international_asw/contents/cmn/script/jquery.mousewheel.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21h/international_asw/contents/cmn/script/jquery.jscrollpane.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21h/international_asw/contents/cmn/script/jquery-ui-1.10.4.custom.min.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21h/international_asw/contents/cmn/script/jquery.ui.touch-punch.min.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21h/international_asw/contents/cmn/script/asw-common.js?beb6a50"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21h/international_asw/contents/cmn/script/asw-common-pc.js?fd88d7d"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21h/international_asw/contents/cmn/script/asw-format.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21h/international_asw/contents/cmn/script/mask.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21h/international_asw/contents/cmn/script/loading.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21h/international_asw/contents/cmn/script/asw-loadingwindow-pc.js?9e233c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21h/international_asw/contents/cmn/script/asw-loadingwindow.js?51db600"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21h/international_asw/contents/cmn/script/dialog-pc.js?25d36a4"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21h/international_asw/contents/cmn/script/asw-dialog-sequencer.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21h/international_asw/contents/cmn/script/asw-event.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21h/international_asw/contents/cmn/script/asw-date-select.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21h/international_asw/contents/cmn/script/asw-calendar-pc.js?9b86b8a"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21h/international_asw/contents/cmn/script/asw-calendar.js?db624c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21h/international_asw/contents/cmn/script/heightLine.js?0e7a1ce"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21h/international_asw/contents/cmn/script/asw-summaryarea.js?b21c6d7"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21h/international_asw/contents/cmn/script/asw-fingerprints.js?99001fc"></script><script type="text/javascript">
			Asw.init("https://aswbe-i.ana.co.jp/rei21h/international_asw", "ja");
			Asw.getCalendarHolidayList = function() {return [20250721,20250811,20250915,20250923,20251013,20251103,20251123,20251124,20260101,20260112,20260211,20260223,20260320,20260429,20260503,20260504,20260505,20260506,20260720,20260811,20260921,20260922,20260923,20261012,20261103,20261123,20270101,20270111,20270211,20270223,20270321,20270322,20270429,20270503,20270504,20270505,20270719,20270811,20270920,20270923,20271011,20271103,20271123,20280101,20280110,20280211,20280223,20280320,20280429,20280503,20280504,20280505,20280717,20280811,20280918,20280922,20281009,20281103,20281123,20290101,20290108,20290211,20290212,20290223,20290320,20290429,20290430,20290503,20290504,20290505,20290716,20290811,20290917,20290923,20290924,20291008,20291103,20291123];}
			Asw.ClientInfo.deviceType = "PC";
			Asw.ClientInfo.mobileDeviceType = "UNKNOWN";
			Asw.ClientInfo.osVersion = "";
			Asw.Format.init("https://aswbe-i.ana.co.jp/rei21h/international_asw/contents/{1}/{2}/{3}?{0}");
			Asw.overwriteTargetAttributeValue("false");
		</script><script type="text/javascript">
		
		var Asw = Asw || {};
		Asw.ContactInput = Asw.ContactInput || {};
		Asw.ContactInput.Const = {
			/** data-属性名「data-type」 @constant @type {String} @default 'data-type' */
			DATA_ATTRIBUTE_NAME_TYPE: 'data-type',
			/** data-属性名「data-sms-possible」 @constant @type {String} @default 'data-sms-possible' */
			DATA_ATTRIBUTE_NAME_SMS_POSSIBLE: 'data-sms-possible',
			/** data-属性名「data-country-code」 @constant @type {String} @default 'data-country-code' */
			DATA_ATTRIBUTE_NAME_COUNTRY_CODE: 'data-country-code',
			/** data-type属性値「sms」 @constant @type {String} @default 'sms' */
			DATA_TYPE_VALUE_SMS: 'sms'
		};

		
		Asw.ContactInput.countryInfoMap = {"IS":{"internationalCallingCode":"+354","smsPossible":false},"IE":{"internationalCallingCode":"+353","smsPossible":true},"AZ":{"internationalCallingCode":"+994","smsPossible":true},"AF":{"internationalCallingCode":"+93","smsPossible":true},"US":{"internationalCallingCode":"+1","smsPossible":true},"AE":{"internationalCallingCode":"+971","smsPossible":true},"DZ":{"internationalCallingCode":"+213","smsPossible":true},"AR":{"internationalCallingCode":"+54","smsPossible":true},"AW":{"internationalCallingCode":"+297","smsPossible":true},"AL":{"internationalCallingCode":"+355","smsPossible":true},"AM":{"internationalCallingCode":"+374","smsPossible":true},"AO":{"internationalCallingCode":"+244","smsPossible":false},"AG":{"internationalCallingCode":"+1","smsPossible":true},"AD":{"internationalCallingCode":"+376","smsPossible":true},"YE":{"internationalCallingCode":"+967","smsPossible":true},"GB":{"internationalCallingCode":"+44","smsPossible":true},"IL":{"internationalCallingCode":"+972","smsPossible":true},"IT":{"internationalCallingCode":"+39","smsPossible":true},"IQ":{"internationalCallingCode":"+964","smsPossible":true},"IR":{"internationalCallingCode":"+98","smsPossible":true},"IN":{"internationalCallingCode":"+91","smsPossible":true},"ID":{"internationalCallingCode":"+62","smsPossible":true},"UG":{"internationalCallingCode":"+256","smsPossible":true},"UA":{"internationalCallingCode":"+380","smsPossible":true},"UZ":{"internationalCallingCode":"+998","smsPossible":true},"UY":{"internationalCallingCode":"+598","smsPossible":true},"EC":{"internationalCallingCode":"+593","smsPossible":true},"EG":{"internationalCallingCode":"+20","smsPossible":true},"EE":{"internationalCallingCode":"+372","smsPossible":true},"SZ":{"internationalCallingCode":"+268","smsPossible":false},"ET":{"internationalCallingCode":"+251","smsPossible":false},"ER":{"internationalCallingCode":"+291","smsPossible":true},"SV":{"internationalCallingCode":"+503","smsPossible":true},"AU":{"internationalCallingCode":"+61","smsPossible":true},"AT":{"internationalCallingCode":"+43","smsPossible":true},"OM":{"internationalCallingCode":"+968","smsPossible":true},"NL":{"internationalCallingCode":"+31","smsPossible":true},"GH":{"internationalCallingCode":"+233","smsPossible":true},"CV":{"internationalCallingCode":"+238","smsPossible":false},"GY":{"internationalCallingCode":"+592","smsPossible":true},"KZ":{"internationalCallingCode":"+7","smsPossible":true},"QA":{"internationalCallingCode":"+974","smsPossible":true},"CA":{"internationalCallingCode":"+1","smsPossible":true},"GA":{"internationalCallingCode":"+241","smsPossible":true},"CM":{"internationalCallingCode":"+237","smsPossible":true},"KR":{"internationalCallingCode":"+82","smsPossible":true},"KH":{"internationalCallingCode":"+855","smsPossible":true},"GM":{"internationalCallingCode":"+220","smsPossible":true},"KP":{"internationalCallingCode":"+850","smsPossible":false},"MK":{"internationalCallingCode":"+389","smsPossible":false},"GN":{"internationalCallingCode":"+224","smsPossible":true},"GW":{"internationalCallingCode":"+245","smsPossible":true},"CY":{"internationalCallingCode":"+357","smsPossible":true},"CU":{"internationalCallingCode":"+53","smsPossible":true},"GR":{"internationalCallingCode":"+30","smsPossible":true},"KI":{"internationalCallingCode":"+686","smsPossible":false},"KG":{"internationalCallingCode":"+996","smsPossible":true},"GT":{"internationalCallingCode":"+502","smsPossible":true},"GU":{"internationalCallingCode":"+1","smsPossible":true},"KW":{"internationalCallingCode":"+965","smsPossible":true},"KY":{"internationalCallingCode":"+1","smsPossible":true},"GD":{"internationalCallingCode":"+1","smsPossible":true},"HR":{"internationalCallingCode":"+385","smsPossible":true},"KE":{"internationalCallingCode":"+254","smsPossible":true},"CI":{"internationalCallingCode":"+225","smsPossible":true},"CR":{"internationalCallingCode":"+506","smsPossible":true},"SA":{"internationalCallingCode":"+966","smsPossible":true},"KM":{"internationalCallingCode":"+269","smsPossible":true},"CO":{"internationalCallingCode":"+57","smsPossible":true},"CG":{"internationalCallingCode":"+242","smsPossible":false},"CD":{"internationalCallingCode":"+243","smsPossible":true},"ZM":{"internationalCallingCode":"+260","smsPossible":true},"SM":{"internationalCallingCode":"+378","smsPossible":false},"SL":{"internationalCallingCode":"+232","smsPossible":true},"DJ":{"internationalCallingCode":"+253","smsPossible":false},"JM":{"internationalCallingCode":"+1","smsPossible":true},"CH":{"internationalCallingCode":"+41","smsPossible":true},"GE":{"internationalCallingCode":"+995","smsPossible":true},"SE":{"internationalCallingCode":"+46","smsPossible":true},"SY":{"internationalCallingCode":"+963","smsPossible":true},"ES":{"internationalCallingCode":"+34","smsPossible":true},"SG":{"internationalCallingCode":"+65","smsPossible":true},"LK":{"internationalCallingCode":"+94","smsPossible":true},"SK":{"internationalCallingCode":"+421","smsPossible":true},"SI":{"internationalCallingCode":"+386","smsPossible":true},"ZW":{"internationalCallingCode":"+263","smsPossible":true},"SD":{"internationalCallingCode":"+249","smsPossible":true},"KN":{"internationalCallingCode":"+1","smsPossible":true},"SR":{"internationalCallingCode":"+597","smsPossible":true},"LC":{"internationalCallingCode":"+1","smsPossible":true},"SC":{"internationalCallingCode":"+248","smsPossible":false},"SN":{"internationalCallingCode":"+221","smsPossible":true},"SB":{"internationalCallingCode":"+677","smsPossible":false},"RS":{"internationalCallingCode":"+381","smsPossible":true},"TH":{"internationalCallingCode":"+66","smsPossible":true},"TW":{"internationalCallingCode":"+886","smsPossible":true},"TJ":{"internationalCallingCode":"+992","smsPossible":true},"TZ":{"internationalCallingCode":"+255","smsPossible":true},"CZ":{"internationalCallingCode":"+420","smsPossible":true},"TD":{"internationalCallingCode":"+235","smsPossible":true},"CF":{"internationalCallingCode":"+236","smsPossible":true},"TN":{"internationalCallingCode":"+216","smsPossible":true},"CL":{"internationalCallingCode":"+56","smsPossible":true},"TV":{"internationalCallingCode":"+688","smsPossible":false},"DK":{"internationalCallingCode":"+45","smsPossible":true},"DE":{"internationalCallingCode":"+49","smsPossible":true},"TG":{"internationalCallingCode":"+228","smsPossible":true},"DO":{"internationalCallingCode":"+1","smsPossible":true},"DM":{"internationalCallingCode":"+1","smsPossible":false},"TT":{"internationalCallingCode":"+1","smsPossible":true},"TM":{"internationalCallingCode":"+993","smsPossible":false},"TR":{"internationalCallingCode":"+90","smsPossible":true},"TO":{"internationalCallingCode":"+676","smsPossible":false},"NG":{"internationalCallingCode":"+234","smsPossible":true},"NR":{"internationalCallingCode":"+674","smsPossible":false},"NA":{"internationalCallingCode":"+264","smsPossible":false},"NI":{"internationalCallingCode":"+505","smsPossible":true},"BH":{"internationalCallingCode":"+973","smsPossible":false},"NE":{"internationalCallingCode":"+227","smsPossible":true},"PK":{"internationalCallingCode":"+92","smsPossible":true},"JP":{"internationalCallingCode":"+81","smsPossible":true},"BS":{"internationalCallingCode":"+1","smsPossible":true},"NZ":{"internationalCallingCode":"+64","smsPossible":true},"BM":{"internationalCallingCode":"+1","smsPossible":true},"NP":{"internationalCallingCode":"+977","smsPossible":true},"BB":{"internationalCallingCode":"+246","smsPossible":true},"NO":{"internationalCallingCode":"+47","smsPossible":true},"HT":{"internationalCallingCode":"+509","smsPossible":true},"BD":{"internationalCallingCode":"+880","smsPossible":true},"VA":{"internationalCallingCode":"+39","smsPossible":false},"PA":{"internationalCallingCode":"+507","smsPossible":true},"VU":{"internationalCallingCode":"+678","smsPossible":false},"PG":{"internationalCallingCode":"+675","smsPossible":true},"PW":{"internationalCallingCode":"+680","smsPossible":true},"PY":{"internationalCallingCode":"+595","smsPossible":true},"PS":{"internationalCallingCode":"+970","smsPossible":true},"BR":{"internationalCallingCode":"+55","smsPossible":true},"HU":{"internationalCallingCode":"+36","smsPossible":true},"TL":{"internationalCallingCode":"+670","smsPossible":true},"BG":{"internationalCallingCode":"+359","smsPossible":true},"BF":{"internationalCallingCode":"+226","smsPossible":true},"BN":{"internationalCallingCode":"+673","smsPossible":true},"FJ":{"internationalCallingCode":"+679","smsPossible":true},"PH":{"internationalCallingCode":"+63","smsPossible":true},"FI":{"internationalCallingCode":"+358","smsPossible":true},"BT":{"internationalCallingCode":"+975","smsPossible":true},"BY":{"internationalCallingCode":"+375","smsPossible":true},"BZ":{"internationalCallingCode":"+501","smsPossible":false},"PR":{"internationalCallingCode":"+1","smsPossible":true},"BE":{"internationalCallingCode":"+32","smsPossible":true},"BA":{"internationalCallingCode":"+387","smsPossible":true},"FR":{"internationalCallingCode":"+33","smsPossible":true},"BO":{"internationalCallingCode":"+591","smsPossible":false},"PF":{"internationalCallingCode":"+689","smsPossible":true},"BI":{"internationalCallingCode":"+257","smsPossible":true},"VN":{"internationalCallingCode":"+84","smsPossible":true},"BJ":{"internationalCallingCode":"+229","smsPossible":true},"VE":{"internationalCallingCode":"+58","smsPossible":true},"PE":{"internationalCallingCode":"+51","smsPossible":true},"PL":{"internationalCallingCode":"+48","smsPossible":true},"BW":{"internationalCallingCode":"+267","smsPossible":true},"PT":{"internationalCallingCode":"+351","smsPossible":false},"HK":{"internationalCallingCode":"+852","smsPossible":true},"HN":{"internationalCallingCode":"+504","smsPossible":false},"MH":{"internationalCallingCode":"+692","smsPossible":true},"MO":{"internationalCallingCode":"+853","smsPossible":true},"MG":{"internationalCallingCode":"+261","smsPossible":true},"MW":{"internationalCallingCode":"+265","smsPossible":true},"ML":{"internationalCallingCode":"+223","smsPossible":true},"MT":{"internationalCallingCode":"+356","smsPossible":true},"MY":{"internationalCallingCode":"+60","smsPossible":true},"FM":{"internationalCallingCode":"+691","smsPossible":false},"MM":{"internationalCallingCode":"+95","smsPossible":true},"MX":{"internationalCallingCode":"+52","smsPossible":true},"MU":{"internationalCallingCode":"+230","smsPossible":true},"MR":{"internationalCallingCode":"+222","smsPossible":true},"MZ":{"internationalCallingCode":"+258","smsPossible":true},"MC":{"internationalCallingCode":"+377","smsPossible":true},"MV":{"internationalCallingCode":"+960","smsPossible":true},"MD":{"internationalCallingCode":"+373","smsPossible":false},"RO":{"internationalCallingCode":"+40","smsPossible":true},"MA":{"internationalCallingCode":"+212","smsPossible":true},"MN":{"internationalCallingCode":"+976","smsPossible":true},"ME":{"internationalCallingCode":"+382","smsPossible":true},"JO":{"internationalCallingCode":"+962","smsPossible":true},"LA":{"internationalCallingCode":"+856","smsPossible":true},"RU":{"internationalCallingCode":"+7","smsPossible":true},"LV":{"internationalCallingCode":"+371","smsPossible":true},"LT":{"internationalCallingCode":"+370","smsPossible":true},"LY":{"internationalCallingCode":"+218","smsPossible":true},"GQ":{"internationalCallingCode":"+240","smsPossible":false},"LI":{"internationalCallingCode":"+423","smsPossible":false},"LR":{"internationalCallingCode":"+231","smsPossible":true},"CN":{"internationalCallingCode":"+86","smsPossible":true},"LU":{"internationalCallingCode":"+352","smsPossible":true},"ZA":{"internationalCallingCode":"+27","smsPossible":true},"RW":{"internationalCallingCode":"+250","smsPossible":true},"LS":{"internationalCallingCode":"+266","smsPossible":false},"LB":{"internationalCallingCode":"+961","smsPossible":true}};

		$(function () {
			contactInfoInit();
		});
		
		function contactInfoInit() {
			
			
			Asw.get('representativeTelType').find('option[value="M1"]').attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_TYPE,
					Asw.ContactInput.Const.DATA_TYPE_VALUE_SMS);
			Asw.get('representativeTelCountry').children('option').each(function(i,option){
				var countryInfo = Asw.ContactInput.countryInfoMap[option.value];
				if (countryInfo === void 0) {
					
					return true;
				}
				$(option).attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_SMS_POSSIBLE, countryInfo.smsPossible);
				$(option).attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_COUNTRY_CODE, countryInfo.internationalCallingCode);
			});
			
			var smsCountrySelectSelector = 'select.js-countrySelect:not(#representativeTelCountry)'
			$(smsCountrySelectSelector).eq(0).children('option').each(function(i,option){
				var countryInfo = Asw.ContactInput.countryInfoMap[option.value];
				if (countryInfo === void 0) {
					
					return true;
				}
				$(smsCountrySelectSelector + ' option:nth-child(' + (i+1) + ')').attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_COUNTRY_CODE,
						countryInfo.internationalCallingCode);
			});
			
			var emergencyCountrySelectSelector = 'select.passengerEmergencyCountry.js-countrySelect:not(#representativeTelCountry)'
			$(emergencyCountrySelectSelector).eq(0).children('option').each(function(i,option){
				var countryInfo = Asw.ContactInput.countryInfoMap[option.value];
				if (countryInfo === void 0) {
					
					return true;
				}
				$(emergencyCountrySelectSelector + ' option:nth-child(' + (i+1) + ')').attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_COUNTRY_CODE,
						countryInfo.internationalCallingCode);
			});
		}
	</script><script type="text/javascript">
		var isInit = true;
		function changeTelType(_this, telNoInputId) {
			var $telNoInput = Asw.get(telNoInputId);
			var $telNoKind = Asw.get("representativeTelType");
			var telNo = Asw.getValueComparedToPlaceholder(telNoInputId);
			var telNoCountry = Asw.get("representativeTelCountry").val();

			if(true && !isInit) {
				if(telInputCheck(telNo, telNoCountry)) {
					if (_this.value == "M1") {
						telNo = exceptDomesticPrefix("09042504176", telNoCountry);
					} else if (_this.value == "B1") {
						telNo = exceptDomesticPrefix("03-5405-2172", telNoCountry);
					} else {
						telNo = exceptDomesticPrefix("090-4250-4176", telNoCountry);
					}
					$telNoInput.val(telNo);
				}
			}
			isInit = false;
		}
		
		function telInputCheck(telNo, telNoCountry) {
	        checkResult = false;
	        
	        var amcHndyTelNo = exceptDomesticPrefix("09042504176", telNoCountry);
	        var amcCmpTelNo = exceptDomesticPrefix("03-5405-2172", telNoCountry);
	        var amcTelNo = exceptDomesticPrefix("090-4250-4176", telNoCountry);
	
	        if(telNo == "" || telNo == amcHndyTelNo || telNo == amcCmpTelNo || telNo == amcTelNo) {
	        	checkResult = true;
	        }
	        return checkResult;
    	}
    	
    	function exceptDomesticPrefix(telNo, telNoCountry) {
    		if(telNoCountry == 'JP') {
    			if(telNo.substr(0, 1) === '0') {
    				return telNo.slice(1);
    			}
    		}
    		return telNo;
    	}
	</script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21h/international_asw/contents/cmn/script/asw-customerinfomationform.js?6e91953"></script><script type="text/javascript">
		$(document).ready(function(){
				Asw.enableTextWhenCheckBox('.cascadedSwitch');
				Asw.notPasteInput('.jsNotPasteInput');
			$(function(){
				Asw.alignHeightToParentTdElement( '.passengerInfoInputArea' );
				
					Asw.alignHeightToParentTdElement( '.passengerInfoInputText' );
				
				
				$('select.js-countrySelect').trigger('change');
			});
		});
	</script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21h/international_asw/contents/cmn/script/asw-session-keeper.js?6ec0730"></script><script type="text/javascript">
			$(document).ready(function() {
				Asw.SessionKeeper.initAndStart("sessionKeeperContainer", "520", "570", "PC");
			});
			</script><script type="text/javascript">
		Asw.addAjaxCallback(function(data) {
			// アコーディオンのメソッドと設定 
			if($('.jsAccordionSwitch .jsTriggerSwitch').length > 0){
				var events = $._data($('.jsAccordionSwitch .jsTriggerSwitch').get(0), "events");
				if(events === undefined || events.click === undefined) {
					Asw.AccordionInfo('.jsAccordionSwitch', {
						'triggerSwitch': '.jsAccordionSwitch .jsTriggerSwitch',
						'listClass': '.jsAccordionSwitchList',
						'visibilityHidden': 'toggleContents'
					});
				}
			}
		});
	</script><script type="text/javascript">
	Asw.Price = Asw.Price || {};
	// 整数の区切り文字
	Asw.Price.separateString = ',';
	
	// 整数の区切り桁数
	Asw.Price.separateDigitNumber = 3;
	
	// 小数点
	Asw.Price.decimalPointString = '.';
	
	// 小数点以降の有効桁数
	Asw.Price.significantDigit = 0;
	
	// 表示金額の単位
	Asw.Price.currencyUnit = '円';
	</script><script type="text/javascript">
			$(document).ready(function() {
				$('#preLoadingArea').css('visibility', 'hidden');
			});
		</script><noscript><img src="https://aswbe-i.ana.co.jp/akam/13/pixel_4737b82?a=dD0wZmUxN2NmZTYyNTFkMTJlYjhlMDZjYmI3YjI0MGMzYjgzZTk0M2UxJmpzPW9mZg==" style="visibility: hidden; position: absolute; left: -999px; top: -999px;" /></noscript><script type="text/javascript"  src="/Q1XS4o/Cn_ls/KfDO7/xA/t9OfhmEwSiGQaG/NG4dCQE/bw/QJBChQIQEB"></script></body>
</html>