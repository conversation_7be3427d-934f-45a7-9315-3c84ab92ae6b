<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="ja-<PERSON>" xml:lang="ja-<PERSON>">

	
	<!-- skey = 2025/06/20 14:32:41 rei21c 7lWL0s7KRO dljdmx+929  --><head id="j_idt53">
    <base href="about:blank">
    <style>
        /* Hide elements that might try to load external resources, as a fallback. */
        img, svg, video, iframe, link[rel="stylesheet"], script { display: none !important; }
    </style>

		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
			<meta http-equiv="X-UA-Compatible" content="IE=EmulateIE9; IE=EmulateIE10" />
			<meta http-equiv="X-UA-Compatible" content="IE=7" />
			<meta http-equiv="X-UA-Compatible" content="IE=edge" />
		<meta name="description" content="" />
		<meta name="keywords" content="" />
		<meta name="format-detection" content="telephone=no" />
		<script type="text/javascript" src="/international_asw/dynatrace/ruxitagentjs_ICANVfhqrux_10273230920145353.js" data-dtconfig="rid=RID_101963162|rpid=-**********|domain=aswbe-i.ana.co.jp|reportUrl=https://bf90419wvr.bf.dynatrace.com/bf|app=78bf0b58acf6ed13|cors=1|owasp=1|featureHash=ICANVfhqrux|xb=www^bs.google-analytics^bs.com^pana^bs.demdex^bs.net^pcollect^bs.tealiumiq^bs.com^pallnipponairways^bs.australia-3^bs.evergage^bs.com^pallnipponairwayscolt^bs.tt^bs.omtrdc^bs.net|rdnt=1|uxrgce=1|cuc=k8vpgh61|mel=100000|expw=1|dpvc=1|md=mdcc1=bAsw.PnrOutput.recordLocator,mdcc2=bAsw.PnrOutput.paymentDetailInfoList[0].fopCode,mdcc3=bAsw.PnrOutput.ticketedFlag,mdcc4=bAsw.PnrOutput.nhPnrFlag,mdcc5=bAsw.PnrOutput.itinerarySummary.departureDateYyyymmdd,mdcc6=bAsw.SearchCriteriaOutput.cffCodeInput,mdcc7=a#main ^rb div.section.sectionNoFlow ^rb table ^rb tbody ^rb tr ^rb td.flightNumber.lastCol ^rb span ^rb span:nth-child(2),mdcc8=cunique|lastModification=1750184416409|tp=500,50,0|agentUri=/international_asw/dynatrace/ruxitagentjs_ICANVfhqrux_10273230920145353.js"></script><link rel="shortcut icon" href="https://www.ana.co.jp/favicon.ico" />
		<title>お客様情報入力 | ANA</title>
		<noscript><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/css/asw_noscript_pc.css?99001fc" />
		</noscript>

		<!--  [ Adobe Target ] -->
		<script type="text/javascript" src="https://www.ana.co.jp/behavior/adobetarget/target_intbe.js"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/jquery-1.8.3.min.js?717d3c0"></script><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/ja/css/asw_initialize.css?99001fc" /><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/css/asw_screen_pc.css?99001fc" /><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/css/asw_common_pc.css?99001fc" /><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/css/asw_forminput_pc.css?99001fc" /><script type="text/javascript">
		var Asw = Asw || {};
		Asw.GlobalWord = Asw.GlobalWord || {};
		
		Asw.GlobalWord.toolTipCloseIconAltText = "閉じる";
		
		</script><script >bazadebezolkohpepadr="574777229"</script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/akam/13/224265f6"  defer></script></head><body>
		
		<noscript>当サイトをご利用になるためには、JavaScript対応のブラウザが必要です。設定でJavaScriptを有効にしてください。有効にできない場合はANA電話窓口へお問い合わせください。
		</noscript>
		<div id="preLoadingArea"></div>
	
	<div id="cmnHeader" class="platinum">
		<!--===== cmnHeader =====-->
		<div class="headerWrapper">
			<div class="header">
				<p class="logo"><a href="https://www.ana.co.jp/asw/AMCTopServlet?type=a" tabindex="-1" aria-hidden="true"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/ana_logo_navy.png?717d3c0" alt="ANA Inspiration of JAPAN。リンク先はマイレージクラブトップページです。" height="28" width="274" /></a>
				</p>
					<ul class="headerMenu">
						<li><a href="https://www.ana.co.jp/asw/AMCTopServlet?type=a">ANAマイレージクラブトップページ</a>
						</li>
						<li><a href="https://www.ana.co.jp/other/int/meta/0005.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">お問い合わせ<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
						</li>
							<li class="btnLogoutArea"><a href="https://aswbe-i.ana.co.jp/rei21c/international_asw/rest/logout" role="button" class="btnBase btnLogout">ログアウト</a>
							</li>
					</ul>
			</div>
		</div>
			<div class="amcUserInfoWrapper">
				<div class="amcUserInfo">
					<div class="userNameArea">
						<table>
							<tr>	
									<td class="statusIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_status_05.png?717d3c0" alt="プラチナサービスメンバー" height="46" width="60" />
									</td>
								<td class="userName">佐藤 直樹 <span>様</span>
								</td>
							</tr>					
						</table>
					</div>
						<div class="balance">
								<dl class="upgradePointBalance">
									<dt>アップグレードポイント残高
									</dt>
									<dd>
											<dl>
												<dt>(2025年度)
												</dt>
												<dd>
													<em>20
													</em>ポイント
												</dd>
											</dl>
									</dd>
								</dl>
							<dl class="mileBalance">
								<dt>マイル口座残高
								</dt>
								<dd><em>75,663</em><span class="currencyCode">マイル</span>
								</dd>
							</dl>
						</div>
				</div>
					<p class="nowTime">6月20日14時32分現在
					</p>
			</div>
		<!--===== /cmnHeader =====-->
	</div>
		<div id="sessionKeeperContainer" class="sessionKeepingContainer"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/bg_modalWindow.png?717d3c0" alt="" height="0" width="0" />
			<div id="extendSessionExpiredDialog" class="modalExpire modalSmall modalContainer" role="dialog" aria-labelledby="extendSessionExpiredDialogTitle" aria-hidden="true">
<form id="sessionKeeperContainer:j_idt183" name="sessionKeeperContainer:j_idt183" method="post" action="https://aswbe-i.ana.co.jp/rei21c/international_asw/pages/award/reservation/award_mandatory_passenger_information_input.xhtml?aswcid=1&amp;rand=202506201432417lWL0s7KRO" autocomplete="off" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="sessionKeeperContainer:j_idt183" value="sessionKeeperContainer:j_idt183" />
<input type="hidden" name="sessionKeeperContainer:j_idt183_operationTicket" value="dljdmx+92940089a6198db5b9cb7fbb1702cc3a~AfrGa5LFU4IfaezQC0DH_qL4qn1MsmoZxiAHGkzn!1750397545899.aere-xml-controller-67d4778877-2rkqw" /><input type="hidden" name="sessionKeeperContainer:j_idt183_cmnPageTicket" value="4" />
					<h1 id="extendSessionExpiredDialogTitle" class="dialogTitle">まもなくセッションが終了します</h1>
					<div id="extendSessionExpiredDialogMessage" class="dialogMessage" tabindex="-1">このままお手続きを進める場合は、［延長］ボタンを押してください。</div>
					<p class="modalButton"><input id="sessionKeeperContainer:cmnSessionKeepingButton" type="submit" name="sessionKeeperContainer:cmnSessionKeepingButton" value="延長" class="btnBase btnModal btnMainStream" onclick="mojarra.ab(this,event,'action',0,0,{'onevent':function(data) {Asw.doCommonAjaxCallback(data,false, event); Asw.SessionKeeper.doSessionKeep(data);},'onerror':function(data) {Asw.doCommonAjaxErrorCallback(data); }});return false" />
					</p><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:0" value="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" autocomplete="off" />
</form>
			</div>
			<div id="showSessionExpiredDialog" class="modalExpire modalSmall modalContainer" role="dialog" aria-labelledby="showSessionExpiredDialogTitle" aria-hidden="true">
				<h1 id="showSessionExpiredDialogTitle" class="dialogTitle">セッションが終了しました</h1>
				<div id="showSessionExpiredDialogMessage" class="dialogMessage" tabindex="-1">一定時間操作されなかったため、お手続きを進めることができません。お手数ですが、再度トップページからお手続きください。</div>
				<p class="modalButton">
					<input type="submit" value="閉じる" class="btnBase btnModal btnMainStream" onclick="Asw.SessionKeeper.closeTimeoutDialog(); return false;" />
				</p>
			</div>
		</div>
		<!--===== dialogMessages =====--><div id="cmnModalMessages"></div>
		<div id="prebookConfirmDialog" class="modalContainer modalConfirm modalSmall " aria-hidden="true" role="dialog">
<form id="prebookConfirmDialog:j_idt403" name="prebookConfirmDialog:j_idt403" method="post" action="https://aswbe-i.ana.co.jp/rei21c/international_asw/pages/award/reservation/award_mandatory_passenger_information_input.xhtml?aswcid=1&amp;rand=202506201432417lWL0s7KRO" autocomplete="off" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="prebookConfirmDialog:j_idt403" value="prebookConfirmDialog:j_idt403" />
<input type="hidden" name="prebookConfirmDialog:j_idt403_operationTicket" value="dljdmx+92940089a6198db5b9cb7fbb1702cc3a~AfrGa5LFU4IfaezQC0DH_qL4qn1MsmoZxiAHGkzn!1750397545899.aere-xml-controller-67d4778877-2rkqw" /><input type="hidden" name="prebookConfirmDialog:j_idt403_cmnPageTicket" value="4" /><div class="dialogMessage" tabindex="0"><p>この後、名・姓の修正や変更ができません。</p><p>「搭乗者選択」欄の氏名が「名」「姓」の順番で表示されていることを再確認ください。</p><p>例）<strong class="highlight">空野 太郎</strong> 様 → MR <strong class="highlight">TARO SORANO</strong></p><p>保安上の理由により、航空券とパスポートのお名前が同じ表記でない場合はご搭乗いただくことができませんので必ず再度ご確認ください。</p></div>
					
					<ul class="modalBtnHorizontal">
							
							<li><input type="submit" name="prebookConfirmDialog:j_idt419" value="戻る" aria-controls="prebookConfirmDialog" class="btnBase btnModal btnCancel" onclick="Asw.Dialog.getInstance('prebookConfirmDialog').close(event, Asw.DialogSequencer.isActiveSequencerNotDisplayedLastDialog());  return false;" />
							</li>
						
						<li class="btnArrowNext"><input type="submit" name="prebookConfirmDialog:j_idt422" value="OK" aria-controls="prebookConfirmDialog" class="btnBase btnModal btnMainStream " onclick="Asw.Dialog.getInstance('prebookConfirmDialog').callOpener('NORMAL', event); return false;" />
						</li>
					</ul><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:1" value="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" autocomplete="off" />
</form>
		</div>
		<!--===== /dialogMessages =====--><div id="cmnContainer">
		<!--=========== .cmnContainer ============--><div id="cmnEmergencyNotice"></div><div id="cmnTitle" class="hgroup ">
		<h1 class="visuallyHidden">お客様情報入力</h1></div>

		<div id="cmnWrapper">
		<!--===== cmnWrapper =====-->
		<ol class="mainFlow" aria-label="完了までの手順、">
			<li><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_flow_01.png?717d3c0" alt="1" height="20" width="28" />フライト検索</li><!--
			--><li><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_flow_02_on.png?717d3c0" alt="2" height="20" width="28" /><span class="visuallyHidden">現在のステップ、</span>お客様情報入力</li><!--
			--><li><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_flow_03.png?717d3c0" alt="3" height="20" width="28" />お支払い情報</li><!--
			--><li class="flowLast"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_flow4_04.png?717d3c0" alt="4" height="20" width="28" />完了</li>
		</ol>

	<div id="main">
	<!--=========== main =============-->
		<ul class="iconExample">
				<li class="requiredIconExample">
					<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="" height="7" width="7" /></span>必須項目です	
				</li>
		</ul>
<form id="j_idt526" name="j_idt526" method="post" action="https://aswbe-i.ana.co.jp/rei21c/international_asw/pages/award/reservation/award_mandatory_passenger_information_input.xhtml?aswcid=1&amp;rand=202506201432417lWL0s7KRO" autocomplete="off" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="j_idt526" value="j_idt526" />
<input type="hidden" name="j_idt526_operationTicket" value="dljdmx+92940089a6198db5b9cb7fbb1702cc3a~AfrGa5LFU4IfaezQC0DH_qL4qn1MsmoZxiAHGkzn!1750397545899.aere-xml-controller-67d4778877-2rkqw" /><input type="hidden" name="j_idt526_cmnPageTicket" value="4" />

<div class="section">
	<h2>搭乗者選択<a href="https://www.ana.co.jp/other/int/meta/0086.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="特典のご利用に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a></h2>
	<table class="passengerInfo">
		<thead>
			<tr>
				
				<th class="name">氏名</th>
				
				<th class="dateOfBirthWide" id="dateOfBirthHeader">生年月日</th>
				
				<th class="gender lastCol" id="genderHeader">性別<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span></th>
				
			</tr>
		</thead>
		<tbody>
				
				<tr>
					<th class="parent_01" id="parentHeader01">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:0:passenger" type="checkbox" name="passengers:0:passenger" checked="checked" data-number="1" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:0:passenger">NAOKI SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader01">1963年06月05日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader01">				
							<span class="formWrapper">
							<label for="passengers:0:gender" class="visuallyHidden">NAOKI SATOの性別</label><select id="passengers:0:gender" name="passengers:0:gender" size="1">	<option value="">選択してください</option>
	<option value="M" selected="selected">男性</option>
	<option value="F">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_02" id="parentHeader02">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:1:passenger" type="checkbox" name="passengers:1:passenger" data-number="2" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:1:passenger">REO SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader02">1995年05月18日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader02">				
							<span class="formWrapper">
							<label for="passengers:1:gender" class="visuallyHidden">REO SATOの性別</label><select id="passengers:1:gender" name="passengers:1:gender" size="1">	<option value="">選択してください</option>
	<option value="M" selected="selected">男性</option>
	<option value="F">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_03" id="parentHeader03">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:2:passenger" type="checkbox" name="passengers:2:passenger" data-number="3" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:2:passenger">ERI SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader03">1963年08月14日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader03">				
							<span class="formWrapper">
							<label for="passengers:2:gender" class="visuallyHidden">ERI SATOの性別</label><select id="passengers:2:gender" name="passengers:2:gender" size="1">	<option value="">選択してください</option>
	<option value="M">男性</option>
	<option value="F" selected="selected">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_04" id="parentHeader04">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:3:passenger" type="checkbox" name="passengers:3:passenger" data-number="4" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:3:passenger">TAISHI SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader04">1992年12月03日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader04">				
							<span class="formWrapper">
							<label for="passengers:3:gender" class="visuallyHidden">TAISHI SATOの性別</label><select id="passengers:3:gender" name="passengers:3:gender" size="1">	<option value="">選択してください</option>
	<option value="M" selected="selected">男性</option>
	<option value="F">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_05" id="parentHeader05">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:4:passenger" type="checkbox" name="passengers:4:passenger" data-number="5" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:4:passenger">HIROMI FUJIMOTO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader05">1961年11月23日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader05">				
							<span class="formWrapper">
							<label for="passengers:4:gender" class="visuallyHidden">HIROMI FUJIMOTOの性別</label><select id="passengers:4:gender" name="passengers:4:gender" size="1">	<option value="">選択してください</option>
	<option value="M">男性</option>
	<option value="F" selected="selected">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
		</tbody>
	</table>
</div>
		<div class="section">
			<h2 class="midstream">連絡先情報
			</h2>
			<h3>代表者
			</h3>
	
	
	
	<div class="formInput">
		<fieldset>
			<legend><label>代表者</label></legend>
			<dl id="contactEmailAddressInfo">
				<dt class="hasRequiredIcon">メールアドレス<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span><a href="https://www.ana.co.jp/other/int/meta/0057.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="メールの宛先と内容に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a>
				</dt>
				<dd>
					<ul class="verticalList">
						<li class="nonMarginTop"><label for="representativeEmail" class="visuallyHidden">代表者メールアドレス</label><input id="representativeEmail" type="text" name="representativeEmail" value="<EMAIL>" class="widthLarge" placeholder="例：<EMAIL>" />
						</li>
						<li>
							<span class="supplementTxt" id="confirmMailAddressForRepresentative">確認のため再度ご入力ください。
							</span><label for="confirmrepresentativeEmail" class="visuallyHidden">確認用メールアドレス</label><input id="confirmrepresentativeEmail" type="text" name="confirmrepresentativeEmail" value="<EMAIL>" class="widthLarge jsNotPasteInput" placeholder="例：<EMAIL>" aria-describedby="confirmMailAddressForRepresentative" />
						</li>
					</ul>
				</dd>
					<dd style="display:none;"><input id="updateAlwaysInfo" type="checkbox" name="updateAlwaysInfo" /><label for="updateAlwaysInfo">ログイン中の会員情報へこのメールアドレスを登録</label>
					</dd>
			</dl>
			<dl>
				<dt>電話番号<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span>
				</dt>
				<dd class="js-countryWrap">
					<ul>
						<li><label for="representativeTelType" class="visuallyHidden">電話番号種別</label><select id="representativeTelType" name="representativeTelType" class="basicWidth js-typeSelect" size="1" onchange="changeTelType(this,'representativeTel')">	<option value="M1" selected="selected">携帯</option>
	<option value="B1">会社</option>
	<option value="H1">自宅</option>
</select>
						</li>
						<li><label for="representativeTelCountry" class="visuallyHidden">電話番号の国/地区選択</label><select id="representativeTelCountry" name="representativeTelCountry" class="js-countrySelect" size="1">	<option value="" data-sms-possible="true" data-country-code="---">選択してください</option>
	<option value="JP" selected="selected" data-sms-possible="true">日本</option>
	<option value="-" data-sms-possible="true" data-country-code="---">-----------</option>
	<option value="IS" data-sms-possible="true">アイスランド</option>
	<option value="IE" data-sms-possible="true">アイルランド</option>
	<option value="AZ" data-sms-possible="true">アゼルバイジャン</option>
	<option value="AF" data-sms-possible="true">アフガニスタン</option>
	<option value="US" data-sms-possible="true">アメリカ合衆国/カナダ</option>
	<option value="AE" data-sms-possible="true">アラブ首長国連邦</option>
	<option value="DZ" data-sms-possible="true">アルジェリア</option>
	<option value="AR" data-sms-possible="true">アルゼンチン</option>
	<option value="AW" data-sms-possible="true">アルバ</option>
	<option value="AL" data-sms-possible="true">アルバニア</option>
	<option value="AM" data-sms-possible="true">アルメニア</option>
	<option value="AO" data-sms-possible="true">アンゴラ</option>
	<option value="AG" data-sms-possible="true">アンティグア・バーブーダ</option>
	<option value="AD" data-sms-possible="true">アンドラ</option>
	<option value="YE" data-sms-possible="true">イエメン</option>
	<option value="GB" data-sms-possible="true">イギリス</option>
	<option value="IL" data-sms-possible="true">イスラエル</option>
	<option value="IT" data-sms-possible="true">イタリア</option>
	<option value="IQ" data-sms-possible="true">イラク</option>
	<option value="IR" data-sms-possible="true">イラン</option>
	<option value="IN" data-sms-possible="true">インド</option>
	<option value="ID" data-sms-possible="true">インドネシア</option>
	<option value="UG" data-sms-possible="true">ウガンダ</option>
	<option value="UA" data-sms-possible="true">ウクライナ</option>
	<option value="UZ" data-sms-possible="true">ウズベキスタン</option>
	<option value="UY" data-sms-possible="true">ウルグアイ</option>
	<option value="EC" data-sms-possible="true">エクアドル</option>
	<option value="EG" data-sms-possible="true">エジプト</option>
	<option value="EE" data-sms-possible="true">エストニア</option>
	<option value="SZ" data-sms-possible="true">エスワティニ</option>
	<option value="ET" data-sms-possible="true">エチオピア</option>
	<option value="ER" data-sms-possible="true">エリトリア</option>
	<option value="SV" data-sms-possible="true">エルサルバドル</option>
	<option value="AU" data-sms-possible="true">オーストラリア</option>
	<option value="AT" data-sms-possible="true">オーストリア</option>
	<option value="OM" data-sms-possible="true">オマーン</option>
	<option value="NL" data-sms-possible="true">オランダ</option>
	<option value="GH" data-sms-possible="true">ガーナ</option>
	<option value="CV" data-sms-possible="true">カーボベルデ</option>
	<option value="GY" data-sms-possible="true">ガイアナ</option>
	<option value="KZ" data-sms-possible="true">カザフスタン</option>
	<option value="QA" data-sms-possible="true">カタール</option>
	<option value="CA" data-sms-possible="true">カナダ/アメリカ合衆国</option>
	<option value="GA" data-sms-possible="true">ガボン</option>
	<option value="CM" data-sms-possible="true">カメルーン</option>
	<option value="KR" data-sms-possible="true">韓国</option>
	<option value="GM" data-sms-possible="true">ガンビア</option>
	<option value="KH" data-sms-possible="true">カンボジア</option>
	<option value="KP" data-sms-possible="true">北朝鮮</option>
	<option value="MK" data-sms-possible="true">北マケドニア</option>
	<option value="GN" data-sms-possible="true">ギニア</option>
	<option value="GW" data-sms-possible="true">ギニアビサウ</option>
	<option value="CY" data-sms-possible="true">キプロス</option>
	<option value="CU" data-sms-possible="true">キューバ</option>
	<option value="GR" data-sms-possible="true">ギリシャ</option>
	<option value="KI" data-sms-possible="true">キリバス</option>
	<option value="KG" data-sms-possible="true">キルギス</option>
	<option value="GT" data-sms-possible="true">グアテマラ</option>
	<option value="GU" data-sms-possible="true">グアム</option>
	<option value="KW" data-sms-possible="true">クウェート</option>
	<option value="GD" data-sms-possible="true">グレナダ</option>
	<option value="HR" data-sms-possible="true">クロアチア</option>
	<option value="KY" data-sms-possible="true">ケイマン諸島</option>
	<option value="KE" data-sms-possible="true">ケニア</option>
	<option value="CI" data-sms-possible="true">コートジボワール</option>
	<option value="CR" data-sms-possible="true">コスタリカ</option>
	<option value="KM" data-sms-possible="true">コモロ</option>
	<option value="CO" data-sms-possible="true">コロンビア</option>
	<option value="CG" data-sms-possible="true">コンゴ共和国</option>
	<option value="CD" data-sms-possible="true">コンゴ民主共和国</option>
	<option value="SA" data-sms-possible="true">サウジアラビア</option>
	<option value="ZM" data-sms-possible="true">ザンビア</option>
	<option value="SM" data-sms-possible="true">サンマリノ</option>
	<option value="SL" data-sms-possible="true">シエラレオネ</option>
	<option value="DJ" data-sms-possible="true">ジブチ</option>
	<option value="JM" data-sms-possible="true">ジャマイカ</option>
	<option value="GE" data-sms-possible="true">ジョージア</option>
	<option value="SY" data-sms-possible="true">シリア</option>
	<option value="SG" data-sms-possible="true">シンガポール</option>
	<option value="ZW" data-sms-possible="true">ジンバブエ</option>
	<option value="CH" data-sms-possible="true">スイス</option>
	<option value="SE" data-sms-possible="true">スウェーデン</option>
	<option value="SD" data-sms-possible="true">スーダン</option>
	<option value="ES" data-sms-possible="true">スペイン</option>
	<option value="SR" data-sms-possible="true">スリナム</option>
	<option value="LK" data-sms-possible="true">スリランカ</option>
	<option value="SK" data-sms-possible="true">スロバキア</option>
	<option value="SI" data-sms-possible="true">スロベニア</option>
	<option value="SC" data-sms-possible="true">セイシェル</option>
	<option value="GQ" data-sms-possible="true">赤道ギニア</option>
	<option value="SN" data-sms-possible="true">セネガル</option>
	<option value="RS" data-sms-possible="true">セルビア</option>
	<option value="KN" data-sms-possible="true">セントクリストファー・ネイビス</option>
	<option value="LC" data-sms-possible="true">セントルシア</option>
	<option value="SB" data-sms-possible="true">ソロモン諸島</option>
	<option value="TH" data-sms-possible="true">タイ</option>
	<option value="TW" data-sms-possible="true">台湾</option>
	<option value="TJ" data-sms-possible="true">タジキスタン</option>
	<option value="TZ" data-sms-possible="true">タンザニア</option>
	<option value="CZ" data-sms-possible="true">チェコ</option>
	<option value="TD" data-sms-possible="true">チャド</option>
	<option value="CF" data-sms-possible="true">中央アフリカ共和国</option>
	<option value="CN" data-sms-possible="true">中国</option>
	<option value="TN" data-sms-possible="true">チュニジア</option>
	<option value="CL" data-sms-possible="true">チリ</option>
	<option value="TV" data-sms-possible="true">ツバル</option>
	<option value="DK" data-sms-possible="true">デンマーク</option>
	<option value="DE" data-sms-possible="true">ドイツ</option>
	<option value="TG" data-sms-possible="true">トーゴ</option>
	<option value="DO" data-sms-possible="true">ドミニカ共和国</option>
	<option value="DM" data-sms-possible="true">ドミニカ国</option>
	<option value="TT" data-sms-possible="true">トリニダード・トバゴ</option>
	<option value="TM" data-sms-possible="true">トルクメニスタン</option>
	<option value="TR" data-sms-possible="true">トルコ</option>
	<option value="TO" data-sms-possible="true">トンガ</option>
	<option value="NG" data-sms-possible="true">ナイジェリア</option>
	<option value="NR" data-sms-possible="true">ナウル</option>
	<option value="NA" data-sms-possible="true">ナミビア</option>
	<option value="NI" data-sms-possible="true">ニカラグア</option>
	<option value="NE" data-sms-possible="true">ニジェール</option>
	<option value="NZ" data-sms-possible="true">ニュージーランド</option>
	<option value="NP" data-sms-possible="true">ネパール</option>
	<option value="NO" data-sms-possible="true">ノルウェー</option>
	<option value="BH" data-sms-possible="true">バーレーン</option>
	<option value="HT" data-sms-possible="true">ハイチ</option>
	<option value="PK" data-sms-possible="true">パキスタン</option>
	<option value="VA" data-sms-possible="true">バチカン</option>
	<option value="PA" data-sms-possible="true">パナマ</option>
	<option value="VU" data-sms-possible="true">バヌアツ</option>
	<option value="BS" data-sms-possible="true">バハマ</option>
	<option value="PG" data-sms-possible="true">パプアニューギニア</option>
	<option value="BM" data-sms-possible="true">バミューダ</option>
	<option value="PW" data-sms-possible="true">パラオ</option>
	<option value="PY" data-sms-possible="true">パラグアイ</option>
	<option value="BB" data-sms-possible="true">バルバドス</option>
	<option value="PS" data-sms-possible="true">パレスチナ</option>
	<option value="HU" data-sms-possible="true">ハンガリー</option>
	<option value="BD" data-sms-possible="true">バングラデシュ</option>
	<option value="TL" data-sms-possible="true">東ティモール</option>
	<option value="FJ" data-sms-possible="true">フィジー</option>
	<option value="PH" data-sms-possible="true">フィリピン</option>
	<option value="FI" data-sms-possible="true">フィンランド</option>
	<option value="BT" data-sms-possible="true">ブータン</option>
	<option value="PR" data-sms-possible="true">プエルトリコ</option>
	<option value="BR" data-sms-possible="true">ブラジル</option>
	<option value="FR" data-sms-possible="true">フランス</option>
	<option value="PF" data-sms-possible="true">フランス領ポリネシア</option>
	<option value="BG" data-sms-possible="true">ブルガリア</option>
	<option value="BF" data-sms-possible="true">ブルキナファソ</option>
	<option value="BN" data-sms-possible="true">ブルネイ</option>
	<option value="BI" data-sms-possible="true">ブルンジ</option>
	<option value="VN" data-sms-possible="true">ベトナム</option>
	<option value="BJ" data-sms-possible="true">ベナン</option>
	<option value="VE" data-sms-possible="true">ベネズエラ</option>
	<option value="BY" data-sms-possible="true">ベラルーシ</option>
	<option value="BZ" data-sms-possible="true">ベリーズ</option>
	<option value="PE" data-sms-possible="true">ペルー</option>
	<option value="BE" data-sms-possible="true">ベルギー</option>
	<option value="PL" data-sms-possible="true">ポーランド</option>
	<option value="BA" data-sms-possible="true">ボスニア・ヘルツェゴビナ</option>
	<option value="BW" data-sms-possible="true">ボツワナ</option>
	<option value="BO" data-sms-possible="true">ボリビア</option>
	<option value="PT" data-sms-possible="true">ポルトガル</option>
	<option value="HK" data-sms-possible="true">香港</option>
	<option value="HN" data-sms-possible="true">ホンジュラス</option>
	<option value="MH" data-sms-possible="true">マーシャル諸島</option>
	<option value="MO" data-sms-possible="true">マカオ</option>
	<option value="MG" data-sms-possible="true">マダガスカル</option>
	<option value="MW" data-sms-possible="true">マラウイ</option>
	<option value="ML" data-sms-possible="true">マリ</option>
	<option value="MT" data-sms-possible="true">マルタ</option>
	<option value="MY" data-sms-possible="true">マレーシア</option>
	<option value="FM" data-sms-possible="true">ミクロネシア連邦</option>
	<option value="ZA" data-sms-possible="true">南アフリカ</option>
	<option value="MM" data-sms-possible="true">ミャンマー</option>
	<option value="MX" data-sms-possible="true">メキシコ</option>
	<option value="MU" data-sms-possible="true">モーリシャス</option>
	<option value="MR" data-sms-possible="true">モーリタニア</option>
	<option value="MZ" data-sms-possible="true">モザンビーク</option>
	<option value="MC" data-sms-possible="true">モナコ</option>
	<option value="MV" data-sms-possible="true">モルディヴ</option>
	<option value="MD" data-sms-possible="true">モルドバ</option>
	<option value="MA" data-sms-possible="true">モロッコ</option>
	<option value="MN" data-sms-possible="true">モンゴル</option>
	<option value="ME" data-sms-possible="true">モンテネグロ</option>
	<option value="JO" data-sms-possible="true">ヨルダン</option>
	<option value="LA" data-sms-possible="true">ラオス人民民主共和国</option>
	<option value="LV" data-sms-possible="true">ラトビア</option>
	<option value="LT" data-sms-possible="true">リトアニア</option>
	<option value="LY" data-sms-possible="true">リビア</option>
	<option value="LI" data-sms-possible="true">リヒテンシュタイン</option>
	<option value="LR" data-sms-possible="true">リベリア</option>
	<option value="RO" data-sms-possible="true">ルーマニア</option>
	<option value="LU" data-sms-possible="true">ルクセンブルク</option>
	<option value="RW" data-sms-possible="true">ルワンダ</option>
	<option value="LS" data-sms-possible="true">レソト</option>
	<option value="LB" data-sms-possible="true">レバノン</option>
	<option value="RU" data-sms-possible="true">ロシア</option>
</select>
						</li>
						<li class="numberInputWrap">
							<span id="numberInputInfo1" class="supplementTxt marginTop">先頭に0(ゼロ)がある場合、0(ゼロ)を抜いてください(一部、国・地域除く)。携帯電話番号の場合は、携帯電話を契約された国を選択ください。</span>
							<div class="numberRowWrap">
								<div class="block">
									
									<span class="visuallyHidden">国番号</span>
									<span class="viewInput widthMini js-countryCode"></span>
								</div>
								<div class="block"><label for="representativeTel" class="visuallyHidden">電話番号</label><input id="representativeTel" type="text" name="representativeTel" value="9042504176" class="widthMiddle" placeholder="例：9012345678" maxlength="30" aria-describedby="numberInputInfo1" />
								</div>
							</div>
						</li>
					</ul>
				</dd>
			</dl>
		</fieldset>
	</div>
	<div class="titleWrapper">
		<h3>
			<span class="text">eメール・SMSに関するご案内<a href="https://www.ana.co.jp/other/int/meta/0057.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="eメール・SMSに関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a></span>
		</h3>
	</div>
	<div class="formInput">
	<h4 class="formAreaTitle">主に搭乗・運航に関わるeメールの送付先設定（米国へ通知いたします）<a href="https://www.ana.co.jp/other/int/meta/0629.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="米国入国用eメール・SMS・電話番号に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a>
	</h4>
	<div class="paxSettingArea js-mailFormWrap onePax is-open">
			<div class="settingArea">
					<dl>
						<dt class="breakWord">NAOKI SATO
						</dt>
						<dd>
							<fieldset>
								<legend>NAOKI SATOのeメール送付先および米国到着時に提供するメールアドレス</legend>代表者と同じ
							</fieldset>
						</dd>
					</dl>
			</div>
	</div>
	<h4 class="formAreaTitle hasTopLine">主に搭乗・運航に関わるSMSの送付先および米国で受信できる電話番号の設定（米国へ通知いたします）<a href="https://www.ana.co.jp/other/int/meta/0629.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="米国入国用eメール・SMS・電話番号に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a>
	</h4>
	<div class="paxSettingArea js-smsFormWrap onePax is-open">
			<div class="settingArea">
					<dl>
						<dt class="breakWord">NAOKI SATO<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span>
						</dt>
						<dd>
							<fieldset>
								<legend>NAOKI SATOの1つ目の電話番号</legend>
									<ul class="verticalList js-formItem">
										<li><input type="radio" id="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSameAsRepresentativeSms:radioItem" name="contactsSms:0:recipientSmsEachPersonSelection:radioGroup" value="0" checked="checked" /><label for="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSameAsRepresentativeSms:radioItem">代表者電話番号と同じ</label>
										</li>
										<li><input type="radio" id="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSmsPerPassenger:radioItem" name="contactsSms:0:recipientSmsEachPersonSelection:radioGroup" value="1" /><label for="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSmsPerPassenger:radioItem">電話番号を指定</label>
											<div class="indentArea js-formImputArea">
												<span id="confirmTelNumberForRepresentative:0" class="supplementTxt marginTop">先頭に0(ゼロ)がある場合、0(ゼロ)を抜いてください(一部、国・地域除く)。携帯電話番号の場合は、携帯電話を契約された国を選択ください。</span>
												<ul class="countryNumberInput js-countryWrap">
													<li><label for="contactsSms:0:recipientSmsEachPersonSelection:passengerSmsCountry" class="visuallyHidden">電話番号の国/地区選択</label><select id="contactsSms:0:recipientSmsEachPersonSelection:passengerSmsCountry" name="contactsSms:0:recipientSmsEachPersonSelection:passengerSmsCountry" class="countrySelect js-countrySelect" size="1">	<option value="" data-country-code="---">選択してください</option>
	<option value="JP">日本</option>
	<option value="-" data-country-code="---">-----------</option>
	<option value="IE">アイルランド</option>
	<option value="AZ">アゼルバイジャン</option>
	<option value="AF">アフガニスタン</option>
	<option value="US">アメリカ合衆国/カナダ</option>
	<option value="AE">アラブ首長国連邦</option>
	<option value="DZ">アルジェリア</option>
	<option value="AR">アルゼンチン</option>
	<option value="AW">アルバ</option>
	<option value="AL">アルバニア</option>
	<option value="AM">アルメニア</option>
	<option value="AG">アンティグア・バーブーダ</option>
	<option value="AD">アンドラ</option>
	<option value="YE">イエメン</option>
	<option value="GB">イギリス</option>
	<option value="IL">イスラエル</option>
	<option value="IT">イタリア</option>
	<option value="IQ">イラク</option>
	<option value="IR">イラン</option>
	<option value="IN">インド</option>
	<option value="ID">インドネシア</option>
	<option value="UG">ウガンダ</option>
	<option value="UA">ウクライナ</option>
	<option value="UZ">ウズベキスタン</option>
	<option value="UY">ウルグアイ</option>
	<option value="EC">エクアドル</option>
	<option value="EG">エジプト</option>
	<option value="EE">エストニア</option>
	<option value="ER">エリトリア</option>
	<option value="SV">エルサルバドル</option>
	<option value="AU">オーストラリア</option>
	<option value="AT">オーストリア</option>
	<option value="OM">オマーン</option>
	<option value="NL">オランダ</option>
	<option value="GH">ガーナ</option>
	<option value="GY">ガイアナ</option>
	<option value="KZ">カザフスタン</option>
	<option value="QA">カタール</option>
	<option value="CA">カナダ/アメリカ合衆国</option>
	<option value="GA">ガボン</option>
	<option value="CM">カメルーン</option>
	<option value="KR">韓国</option>
	<option value="GM">ガンビア</option>
	<option value="KH">カンボジア</option>
	<option value="GN">ギニア</option>
	<option value="GW">ギニアビサウ</option>
	<option value="CY">キプロス</option>
	<option value="CU">キューバ</option>
	<option value="GR">ギリシャ</option>
	<option value="KG">キルギス</option>
	<option value="GT">グアテマラ</option>
	<option value="GU">グアム</option>
	<option value="KW">クウェート</option>
	<option value="GD">グレナダ</option>
	<option value="HR">クロアチア</option>
	<option value="KY">ケイマン諸島</option>
	<option value="KE">ケニア</option>
	<option value="CI">コートジボワール</option>
	<option value="CR">コスタリカ</option>
	<option value="KM">コモロ</option>
	<option value="CO">コロンビア</option>
	<option value="CD">コンゴ民主共和国</option>
	<option value="SA">サウジアラビア</option>
	<option value="ZM">ザンビア</option>
	<option value="SL">シエラレオネ</option>
	<option value="JM">ジャマイカ</option>
	<option value="GE">ジョージア</option>
	<option value="SY">シリア</option>
	<option value="SG">シンガポール</option>
	<option value="ZW">ジンバブエ</option>
	<option value="CH">スイス</option>
	<option value="SE">スウェーデン</option>
	<option value="SD">スーダン</option>
	<option value="ES">スペイン</option>
	<option value="SR">スリナム</option>
	<option value="LK">スリランカ</option>
	<option value="SK">スロバキア</option>
	<option value="SI">スロベニア</option>
	<option value="SN">セネガル</option>
	<option value="RS">セルビア</option>
	<option value="KN">セントクリストファー・ネイビス</option>
	<option value="LC">セントルシア</option>
	<option value="TH">タイ</option>
	<option value="TW">台湾</option>
	<option value="TJ">タジキスタン</option>
	<option value="TZ">タンザニア</option>
	<option value="CZ">チェコ</option>
	<option value="TD">チャド</option>
	<option value="CF">中央アフリカ共和国</option>
	<option value="CN">中国</option>
	<option value="TN">チュニジア</option>
	<option value="CL">チリ</option>
	<option value="DK">デンマーク</option>
	<option value="DE">ドイツ</option>
	<option value="TG">トーゴ</option>
	<option value="DO">ドミニカ共和国</option>
	<option value="TT">トリニダード・トバゴ</option>
	<option value="TR">トルコ</option>
	<option value="NG">ナイジェリア</option>
	<option value="NI">ニカラグア</option>
	<option value="NE">ニジェール</option>
	<option value="NZ">ニュージーランド</option>
	<option value="NP">ネパール</option>
	<option value="NO">ノルウェー</option>
	<option value="HT">ハイチ</option>
	<option value="PK">パキスタン</option>
	<option value="PA">パナマ</option>
	<option value="BS">バハマ</option>
	<option value="PG">パプアニューギニア</option>
	<option value="BM">バミューダ</option>
	<option value="PW">パラオ</option>
	<option value="PY">パラグアイ</option>
	<option value="BB">バルバドス</option>
	<option value="PS">パレスチナ</option>
	<option value="HU">ハンガリー</option>
	<option value="BD">バングラデシュ</option>
	<option value="TL">東ティモール</option>
	<option value="FJ">フィジー</option>
	<option value="PH">フィリピン</option>
	<option value="FI">フィンランド</option>
	<option value="BT">ブータン</option>
	<option value="PR">プエルトリコ</option>
	<option value="BR">ブラジル</option>
	<option value="FR">フランス</option>
	<option value="PF">フランス領ポリネシア</option>
	<option value="BG">ブルガリア</option>
	<option value="BF">ブルキナファソ</option>
	<option value="BN">ブルネイ</option>
	<option value="BI">ブルンジ</option>
	<option value="VN">ベトナム</option>
	<option value="BJ">ベナン</option>
	<option value="VE">ベネズエラ</option>
	<option value="BY">ベラルーシ</option>
	<option value="PE">ペルー</option>
	<option value="BE">ベルギー</option>
	<option value="PL">ポーランド</option>
	<option value="BA">ボスニア・ヘルツェゴビナ</option>
	<option value="BW">ボツワナ</option>
	<option value="HK">香港</option>
	<option value="MH">マーシャル諸島</option>
	<option value="MO">マカオ</option>
	<option value="MG">マダガスカル</option>
	<option value="MW">マラウイ</option>
	<option value="ML">マリ</option>
	<option value="MT">マルタ</option>
	<option value="MY">マレーシア</option>
	<option value="ZA">南アフリカ</option>
	<option value="MM">ミャンマー</option>
	<option value="MX">メキシコ</option>
	<option value="MU">モーリシャス</option>
	<option value="MR">モーリタニア</option>
	<option value="MZ">モザンビーク</option>
	<option value="MC">モナコ</option>
	<option value="MV">モルディヴ</option>
	<option value="MA">モロッコ</option>
	<option value="MN">モンゴル</option>
	<option value="ME">モンテネグロ</option>
	<option value="JO">ヨルダン</option>
	<option value="LA">ラオス人民民主共和国</option>
	<option value="LV">ラトビア</option>
	<option value="LT">リトアニア</option>
	<option value="LY">リビア</option>
	<option value="LR">リベリア</option>
	<option value="RO">ルーマニア</option>
	<option value="LU">ルクセンブルク</option>
	<option value="RW">ルワンダ</option>
	<option value="LB">レバノン</option>
	<option value="RU">ロシア</option>
</select>
													</li>
													<li>
														
														<div class="numberRowWrap">
															<div class="block">
																<span class="visuallyHidden">国番号</span>
																<span class="countryCode viewInput js-countryCode"></span>
															</div>
															
															<div class="block"><label for="contactsSms:0:recipientSmsEachPersonSelection:flightStatusNotificationContactPointSmsDescription" class="visuallyHidden">電話番号</label><input id="contactsSms:0:recipientSmsEachPersonSelection:flightStatusNotificationContactPointSmsDescription" type="text" name="contactsSms:0:recipientSmsEachPersonSelection:flightStatusNotificationContactPointSmsDescription" class="telNumber" placeholder="例：9012345678" maxlength="30" aria-describedby="confirmTelNumberForRepresentative:0" />
															</div>
														</div>
													</li>
												</ul>
											</div>
										</li>
									</ul>
							</fieldset>
						</dd>
					</dl>
			</div>
	</div>
	</div>
	
	<div class="formInput articleLine">
		<h3>滞在中の連絡先(任意)
		</h3>
		<dl>
			
			<dt><label for="accommodationOne">現地連絡先1</label>
			</dt>
			<dd><input id="accommodationOne" type="text" name="accommodationOne" class="widthLarge" placeholder="例：09012345678 ABC HOTEL" maxlength="28" title="例：09012345678 ABC HOTEL" />
			</dd>
		</dl>
		<dl>
			
			<dt><label for="accommodationTwo">現地連絡先2</label>
			</dt>
			<dd><input id="accommodationTwo" type="text" name="accommodationTwo" class="widthLarge" placeholder="例：09012345678 ABC HOTEL" maxlength="28" title="例：09012345678 ABC HOTEL" />
			</dd>
		</dl>
	</div>
		</div>
		
		<p class="btnAreaSubmit btnArrowNext "><input id="next" type="submit" name="next" value="次へ" class="btnBase btnMainStream btnVerticalMain btnWidthVariable" onclick="return Asw.Dialog.getInstance('prebookConfirmDialog').toggle(event);return Asw.LoadingWindow.open(&quot;NORMAL&quot;, event)" />
		</p><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:2" value="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" autocomplete="off" />
</form><div id="cmnDynamicMessages" class="infoBoxSection">
				<dl class="infoBox importantInfo jsAccordionSwitch">
					<dt><strong>重要なご案内</strong></dt>
					<dd>
						<ul class="jsAccordionSwitchList" id="informationMessages">
								<li>個人情報の利用目的<a href="https://www.ana.co.jp/other/int/meta/0012.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="ANAプライバシーポリシー(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">ANAプライバシーポリシー<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>を必ずお読みいただき、内容に同意いただいた上で、お進みください。</li>
								<li><a href="https://www.ana.co.jp/other/int/meta/0058.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="Secure Flight Programについて(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">米国Secure Flightプログラム<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>に基づき、パスポート名、生年月日、性別などの情報は事前に米国へ通知いたします。</li>
								<li>入力したeメールアドレス、電話番号は入国に必要な情報として米国に通知される可能性があります。</li>
									<li class="jsHiddenFlg toggleContents">お名前のスペルがパスポート名と一致しているかご確認ください。異なる場合はインターネットではお取り扱いできません。<a href="https://www.ana.co.jp/other/int/meta/intbe0001.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="ANAお問い合わせ先(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">ANA<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>へ名前の変更をお申し出ください。</li>
									<li class="jsHiddenFlg toggleContents">二重国籍のお客様は、ご搭乗時に使用されるパスポートのお名前でご予約ください。<br />パスポートごとにお名前が異なる場合は、お客様ご自身で入国管理事務所・外務省・大使館などで各種条件をご確認のうえ、使用されるパスポートを決めてください。<br />なお、入国可否については国によって異なるため、お客様ご自身で各入国管理事務所などでご確認ください。</li>
									<li class="jsHiddenFlg toggleContents">旅行開始日時点で12歳以上の場合は大人、2歳以上12歳未満の場合は小児、2歳未満の場合は幼児の扱いとなります。なお、小児は大人と同マイル数が必要です。</li>
									<li class="jsHiddenFlg toggleContents">運航に関する情報をお客様へ通知するため、提携航空会社にもお客様のメールアドレスが提供されます。あらかじめご了承ください。</li>
									<li class="jsHiddenFlg toggleContents"><dl><dt>携帯電話へのメール送信をご希望のお客様へ</dt><dd>ドメイン指定受信をご利用の場合は、ana.co.jpとamadeus.comからのメールが受信できるようあらかじめ設定をお願いいたします。</dd></dl></li>
									<li class="jsHiddenFlg toggleContents">SMSはスマートフォンの番号に配信されます。一部の国ではSMSでのご案内をご利用いただけません。詳細は<a href="https://www.ana.co.jp/other/int/meta/0737.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="ショートメッセージ(SMS)でのご案内対象外の国について(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">ショートメッセージ(SMS)でのご案内対象外の国について<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>をご覧ください。</li>
									<li class="jsHiddenFlg toggleContents">ログインされている会員ご本人が搭乗しない場合は、再度フライト検索画面より「ログインされている会員ご本人は搭乗しない」のチェックボックスを選択してお手続きください。</li>
						</ul>
							<a href="#" class="toggleSwitch jsTriggerSwitch" role="button" aria-controls="informationMessages" aria-expanded="false"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/toggle_open.png?7bec38a" alt="開閉ボタン" height="25" width="25" class="jsRollOver" />
							</a>
					</dd>
				</dl></div>
	<!--=========== /main =============-->
	</div>

	<!--=========== Script ============-->
	<script type="text/javascript">
	$(window).on('load', function() {
		// アコーディオンのメソッドと設定
		Asw.AccordionInfo('.jsAccordionSwitch', {
			'triggerSwitch': '.jsAccordionSwitch .jsTriggerSwitch',
			'listClass': '.jsAccordionSwitchList',
			'visibilityHidden': 'toggleContents'
		});
	});
	</script>
	<!--=========== Script ============-->
	<script type="text/javascript">
		//プラットフォーム判別（サマリーエリア追従用として）
		var Asw = Asw || {};
	
	  	Asw.ClientInfo = Asw.ClientInfo  || {};
	  	Asw.ClientInfo.DeviceType = Asw.ClientInfo.DeviceType || {};
	
	  	Asw.ClientInfo.DeviceType.PC = 'PC';
	  	Asw.ClientInfo.DeviceType.SP = 'SP';
	  	Asw.ClientInfo.DeviceType.TC = 'TC';
	
	  	Asw.ClientInfo.deviceType = Asw.ClientInfo.DeviceType.PC;
	
	 	if((navigator.userAgent.indexOf('Android') > 0 && navigator.userAgent.indexOf('Mobile') == -1) || navigator.userAgent.indexOf('iPad') > 0){
	    	Asw.ClientInfo.deviceType = Asw.ClientInfo.DeviceType.TC;
	  	}
	</script>
	<!--=========== /Script ============--><div id="summaryArea">
			<div class="flightSummaryArea">
						<div class="flightSummarySection">
							<h2>区間 1
							</h2>
							<p class="flightDate"><em>6</em>月<em class="hasLeft">28</em>日（土）
							</p>
							<div class="flightSummaryRoute">
									<div class="flightSummaryRouteSection">
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation1_1">
													<span>東京(成田)
													</span>
												</p>
												<p class="time">
													<span>17:00
													</span>
												</p>
											</div>
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation1_1">
													<span>シカゴ(ORD)
													</span>
												</p>
												<p class="time">
													<span>14:55
													</span>
												</p>
											</div>
										<p class="flightNumberClass">
												<span>NH012</span><span><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/flight_ana_summaryarea.png?717d3c0" alt="ANA運航便" height="11" width="41" /></span>
												<br />特典ビジネス
										</p>
									</div>
							</div>
						</div>
						<div class="flightSummarySection">
							<h2 class="midstream">区間 2
							</h2>
							<p class="flightDate"><em>7</em>月<em class="hasLeft">23</em>日（水）
							</p>
							<div class="flightSummaryRoute">
									<div class="flightSummaryRouteSection">
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation2_1">
													<span>ホノルル(オアフ島)
													</span>
												</p>
												<p class="time">
													<span>11:35
													</span>
												</p>
											</div>
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation2_1">
													<span>東京(成田)
													</span>
												</p>
												<p class="time">
													<span>14:50
																	<span>翌日
																	</span>
													</span>
												</p>
											</div>
										<p class="flightNumberClass">
												<span>NH183</span><span><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/flight_ana_summaryarea.png?717d3c0" alt="ANA運航便" height="11" width="41" /></span>
												<br />特典エコノミー
										</p>
									</div>
							</div>
						</div>
					<div class="total">
						<p class="passenger">大人 x1
						</p>
						<div class="totalArea">
					  		<dl class="mealAmountWrap">
									<dt>有料機内食サービス
									</dt>
									<dd><em class="mealAmount">0</em><span class="currencyCode">円</span>
									</dd>
							</dl>
									<div>
										<div class="mileageAndTotal">
												<dl class="requiredMileage">
													<dt>必要マイル
													</dt>
													<dd><em class="requiredMileage">74,000</em><span class="currencyCode">マイル</span>
													</dd>
												</dl>
												<dl>
													<dt>総額
													</dt>
													<dd><em class="awardTotalPayment">68,130</em>円
													</dd>
												</dl>
												<p class="includeFareAndFuelCost">※各種税金、燃油特別付加運賃等を含みます。
												</p>
										</div>
									</div>
						</div>
					</div>
				
			</div></div>
		<!--===== /cmnWrapper =====-->
		</div>
			
		<span id="message" class="visuallyHidden" role="alert" aria-live="assertive"></span>
		<span id="politeMessage" class="visuallyHidden" role="log" aria-live="polite"></span><script type="text/javascript">
		var Asw = Asw || {};
		Asw.BaseOutput = {"sessionID":"7lWL0s7KRO","language":"ja","siteCatalystLanguageCode":"J","operationDate":"20250620","operationDateTime":"20250620143241","office":{"officeCode":"TYONH08ZZ","country2letterCode":"JP"},"bookingType":"A","pageID":"A04_P01","siteCatalystPageName":"INT_BE_AWARD_J_A04特典予約_P01旅客情報入力（必須情報）","device":{"deviceType":"PC"}};
	</script><div id="promotionArea">
		<!--===== promotionArea =====-->
		<!--===== /promotionArea =====--></div>
		<!--=========== /cmnContainer ============--></div><div id="cmnBaloonArea" aria-live="polite"></div><div id="cmnPopupArea" aria-live="polite"></div>

	

	<div id="cmnFooterWrapper">
		<!--=========== .cmnFooterWrapper ============-->
		<div id="cmnFooter">
			<!--===== cmnFooter =====-->
			<ul>
				<li><a href="https://www.ana.co.jp/other/int/meta/0012.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">プライバシーポリシー<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
					<li><a href="https://www.ana.co.jp/other/int/meta/0670.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">利用者情報の外部送信について<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
					</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/0052.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">サイト利用規約<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/be0238.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">推奨環境<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/0133.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">サイトマップ<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/0732.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">運送約款<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
					<li class="lastChild"><a href="https://www.ana.co.jp/other/int/meta/0556.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">ウェブアクセシビリティについて<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
					</li>
			</ul>
			<p class="blankText" aria-hidden="true"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="" height="10" width="10" />外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。</p>
			<p class="copyright"><span lang="en" xml:lang="en">Copyright&#169;ANA</span>
			</p>
			<p class="starAllianceLogo"><a href="http://www.staralliance.com/ja/" target="_blank"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/star_alliance_logo.png?0fe73b5" alt="A STAR ALLIANCE MEMBER" height="18" width="175" /><span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
			</p>
			<!--===== cmnFooter =====-->
		</div>
		<!--=========== /cmnFooterWrapper ============-->
	</div>

		
		<div id="cmnLoadingForAjax"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/bg_modalWindow.png?717d3c0" alt="" height="0" width="0" />
			<div class="loadingArea">
				<p class="loadingImage"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/loading.gif?717d3c0" alt="リクエストを処理しています...しばらくお待ちください。" height="33" width="33" />
				</p>
			</div>
		</div>
		
		
		<div id="popupAltSentence" style="display: none" aria-hidden="true">別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。</div>
	<div id="cmnSiteCatalyst" aria-hidden="true"><div id="cmnSiteCatalystParamArea"><script type="text/javascript">
				// ▼SiteCatalyst
				// SiteCatalyst code version: H.2.
				// Copyright 1997-2005 Omniture, Inc. More info available at http://www.omniture.com
				SiteCatalystReportSuites   = "INT";
				SiteCatalystCharSet        = "UTF-8";
				SiteCatalystChannel        = "BE_AWARD_J";
				SiteCatalystPageName       = "INT_BE_AWARD_J_A04\u7279\u5178\u4E88\u7D04_P01\u65C5\u5BA2\u60C5\u5831\u5165\u529B\uFF08\u5FC5\u9808\u60C5\u5831\uFF09";
				SiteCatalystERR            = "";
				SiteCatalystEventNum       = "";
				SiteCatalystPurchase       = "FALSE";
				SiteCatalystProductPrice   = "";
				SiteCatalystCurrencyCode   = "";
				SiteCatalystSeatAvailabilityINT   = "";
				SiteCatalystDepartDate     = "";
				SiteCatalystDepart         = "";
				SiteCatalystArrive         = "";
				SiteCatalystFlightNo       = "";
				SiteCatalystClass          = "";
				SiteCatalystFare           = "";
				SiteCatalystPurchaseID     = "";
				
				SiteCatalystLocater        = "";
				SiteCatalystPayment        = "";
				SiteCatalystIncome         = "";
				
				SiteCatalystPax            = "";
				SiteCatalystManipulateDay  = "";
				SiteCatalystCompartmentClass   = "";
				SiteCatalystOptionInfo     = "";
				SiteCatalystOutboundUpsellPrice   = "";
				SiteCatalystInboundUpsellPrice    = "";
				
				SiteCatalystSearchInput    = "";
				SiteCatalystSegment        = "";
				SiteCatalystOfficeCode     = "";
				SiteCatalystPassengerCount = "";
				SiteCatalystPassenger      = "";
				SiteCatalystAncillaryService = "";
				// End SiteCatalyst code version: H.2.
				// ▲SiteCatalyst
			</script></div><script type="text/javascript"><!-- if(navigator.appVersion.indexOf('MSIE')>=0)document.write(unescape('%3C')+'\!-'+'-') // --></script>
		<!--/DO NOT REMOVE/-->
		

		
		<!-- Google Tag Manager -->
		<script type="text/javascript" charset="Shift_JIS" src="https://www.ana.co.jp/common/js/GTMINTBE.js"></script>
		<!-- End Google Tag Manager -->
		<!-- Yahoo Tag Manager -->
		<script type="text/javascript" charset="Shift_JIS" src="https://www.ana.co.jp/common/js/YTMINTBE.js"></script>
		<!-- End Yahoo Tag Manager -->
		

	</div>

	

	<div class="mboxDefault"></div><script type="text/javascript">mboxCreate("ASW_common_A04_P01");</script>
			<script type="text/javascript" src="https://www.ana.co.jp/common/js/tealium/tealium.js"></script>
			<script type="text/javascript" src="//cdn.evgnet.com/beacon/allnipponairways/nonamcprd/scripts/evergage.min.js"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/javax.faces.resource/jsf.js.xhtml?ln=javax.faces"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/jquery.mousewheel.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/jquery.jscrollpane.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/jquery-ui-1.10.4.custom.min.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/jquery.ui.touch-punch.min.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-common.js?beb6a50"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-common-pc.js?fd88d7d"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-format.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/mask.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/loading.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-loadingwindow-pc.js?9e233c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-loadingwindow.js?51db600"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/dialog-pc.js?25d36a4"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-dialog-sequencer.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-event.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-date-select.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-calendar-pc.js?9b86b8a"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-calendar.js?db624c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/heightLine.js?0e7a1ce"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-summaryarea.js?b21c6d7"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-fingerprints.js?99001fc"></script><script type="text/javascript">
			Asw.init("https://aswbe-i.ana.co.jp/rei21c/international_asw", "ja");
			Asw.getCalendarHolidayList = function() {return [20250721,20250811,20250915,20250923,20251013,20251103,20251123,20251124,20260101,20260112,20260211,20260223,20260320,20260429,20260503,20260504,20260505,20260506,20260720,20260811,20260921,20260922,20260923,20261012,20261103,20261123,20270101,20270111,20270211,20270223,20270321,20270322,20270429,20270503,20270504,20270505,20270719,20270811,20270920,20270923,20271011,20271103,20271123,20280101,20280110,20280211,20280223,20280320,20280429,20280503,20280504,20280505,20280717,20280811,20280918,20280922,20281009,20281103,20281123,20290101,20290108,20290211,20290212,20290223,20290320,20290429,20290430,20290503,20290504,20290505,20290716,20290811,20290917,20290923,20290924,20291008,20291103,20291123];}
			Asw.ClientInfo.deviceType = "PC";
			Asw.ClientInfo.mobileDeviceType = "UNKNOWN";
			Asw.ClientInfo.osVersion = "";
			Asw.Format.init("https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/{1}/{2}/{3}?{0}");
			Asw.overwriteTargetAttributeValue("false");
		</script><script type="text/javascript">
		
		var Asw = Asw || {};
		Asw.ContactInput = Asw.ContactInput || {};
		Asw.ContactInput.Const = {
			/** data-属性名「data-type」 @constant @type {String} @default 'data-type' */
			DATA_ATTRIBUTE_NAME_TYPE: 'data-type',
			/** data-属性名「data-sms-possible」 @constant @type {String} @default 'data-sms-possible' */
			DATA_ATTRIBUTE_NAME_SMS_POSSIBLE: 'data-sms-possible',
			/** data-属性名「data-country-code」 @constant @type {String} @default 'data-country-code' */
			DATA_ATTRIBUTE_NAME_COUNTRY_CODE: 'data-country-code',
			/** data-type属性値「sms」 @constant @type {String} @default 'sms' */
			DATA_TYPE_VALUE_SMS: 'sms'
		};

		
		Asw.ContactInput.countryInfoMap = {"IS":{"internationalCallingCode":"+354","smsPossible":false},"IE":{"internationalCallingCode":"+353","smsPossible":true},"AZ":{"internationalCallingCode":"+994","smsPossible":true},"AF":{"internationalCallingCode":"+93","smsPossible":true},"US":{"internationalCallingCode":"+1","smsPossible":true},"AE":{"internationalCallingCode":"+971","smsPossible":true},"DZ":{"internationalCallingCode":"+213","smsPossible":true},"AR":{"internationalCallingCode":"+54","smsPossible":true},"AW":{"internationalCallingCode":"+297","smsPossible":true},"AL":{"internationalCallingCode":"+355","smsPossible":true},"AM":{"internationalCallingCode":"+374","smsPossible":true},"AO":{"internationalCallingCode":"+244","smsPossible":false},"AG":{"internationalCallingCode":"+1","smsPossible":true},"AD":{"internationalCallingCode":"+376","smsPossible":true},"YE":{"internationalCallingCode":"+967","smsPossible":true},"GB":{"internationalCallingCode":"+44","smsPossible":true},"IL":{"internationalCallingCode":"+972","smsPossible":true},"IT":{"internationalCallingCode":"+39","smsPossible":true},"IQ":{"internationalCallingCode":"+964","smsPossible":true},"IR":{"internationalCallingCode":"+98","smsPossible":true},"IN":{"internationalCallingCode":"+91","smsPossible":true},"ID":{"internationalCallingCode":"+62","smsPossible":true},"UG":{"internationalCallingCode":"+256","smsPossible":true},"UA":{"internationalCallingCode":"+380","smsPossible":true},"UZ":{"internationalCallingCode":"+998","smsPossible":true},"UY":{"internationalCallingCode":"+598","smsPossible":true},"EC":{"internationalCallingCode":"+593","smsPossible":true},"EG":{"internationalCallingCode":"+20","smsPossible":true},"EE":{"internationalCallingCode":"+372","smsPossible":true},"SZ":{"internationalCallingCode":"+268","smsPossible":false},"ET":{"internationalCallingCode":"+251","smsPossible":false},"ER":{"internationalCallingCode":"+291","smsPossible":true},"SV":{"internationalCallingCode":"+503","smsPossible":true},"AU":{"internationalCallingCode":"+61","smsPossible":true},"AT":{"internationalCallingCode":"+43","smsPossible":true},"OM":{"internationalCallingCode":"+968","smsPossible":true},"NL":{"internationalCallingCode":"+31","smsPossible":true},"GH":{"internationalCallingCode":"+233","smsPossible":true},"CV":{"internationalCallingCode":"+238","smsPossible":false},"GY":{"internationalCallingCode":"+592","smsPossible":true},"KZ":{"internationalCallingCode":"+7","smsPossible":true},"QA":{"internationalCallingCode":"+974","smsPossible":true},"CA":{"internationalCallingCode":"+1","smsPossible":true},"GA":{"internationalCallingCode":"+241","smsPossible":true},"CM":{"internationalCallingCode":"+237","smsPossible":true},"KR":{"internationalCallingCode":"+82","smsPossible":true},"KH":{"internationalCallingCode":"+855","smsPossible":true},"GM":{"internationalCallingCode":"+220","smsPossible":true},"KP":{"internationalCallingCode":"+850","smsPossible":false},"MK":{"internationalCallingCode":"+389","smsPossible":false},"GN":{"internationalCallingCode":"+224","smsPossible":true},"GW":{"internationalCallingCode":"+245","smsPossible":true},"CY":{"internationalCallingCode":"+357","smsPossible":true},"CU":{"internationalCallingCode":"+53","smsPossible":true},"GR":{"internationalCallingCode":"+30","smsPossible":true},"KI":{"internationalCallingCode":"+686","smsPossible":false},"KG":{"internationalCallingCode":"+996","smsPossible":true},"GT":{"internationalCallingCode":"+502","smsPossible":true},"GU":{"internationalCallingCode":"+1","smsPossible":true},"KW":{"internationalCallingCode":"+965","smsPossible":true},"KY":{"internationalCallingCode":"+1","smsPossible":true},"GD":{"internationalCallingCode":"+1","smsPossible":true},"HR":{"internationalCallingCode":"+385","smsPossible":true},"KE":{"internationalCallingCode":"+254","smsPossible":true},"CI":{"internationalCallingCode":"+225","smsPossible":true},"CR":{"internationalCallingCode":"+506","smsPossible":true},"SA":{"internationalCallingCode":"+966","smsPossible":true},"KM":{"internationalCallingCode":"+269","smsPossible":true},"CO":{"internationalCallingCode":"+57","smsPossible":true},"CG":{"internationalCallingCode":"+242","smsPossible":false},"CD":{"internationalCallingCode":"+243","smsPossible":true},"ZM":{"internationalCallingCode":"+260","smsPossible":true},"SM":{"internationalCallingCode":"+378","smsPossible":false},"SL":{"internationalCallingCode":"+232","smsPossible":true},"DJ":{"internationalCallingCode":"+253","smsPossible":false},"JM":{"internationalCallingCode":"+1","smsPossible":true},"CH":{"internationalCallingCode":"+41","smsPossible":true},"GE":{"internationalCallingCode":"+995","smsPossible":true},"SE":{"internationalCallingCode":"+46","smsPossible":true},"SY":{"internationalCallingCode":"+963","smsPossible":true},"ES":{"internationalCallingCode":"+34","smsPossible":true},"SG":{"internationalCallingCode":"+65","smsPossible":true},"LK":{"internationalCallingCode":"+94","smsPossible":true},"SK":{"internationalCallingCode":"+421","smsPossible":true},"SI":{"internationalCallingCode":"+386","smsPossible":true},"ZW":{"internationalCallingCode":"+263","smsPossible":true},"SD":{"internationalCallingCode":"+249","smsPossible":true},"KN":{"internationalCallingCode":"+1","smsPossible":true},"SR":{"internationalCallingCode":"+597","smsPossible":true},"LC":{"internationalCallingCode":"+1","smsPossible":true},"SC":{"internationalCallingCode":"+248","smsPossible":false},"SN":{"internationalCallingCode":"+221","smsPossible":true},"SB":{"internationalCallingCode":"+677","smsPossible":false},"RS":{"internationalCallingCode":"+381","smsPossible":true},"TH":{"internationalCallingCode":"+66","smsPossible":true},"TW":{"internationalCallingCode":"+886","smsPossible":true},"TJ":{"internationalCallingCode":"+992","smsPossible":true},"TZ":{"internationalCallingCode":"+255","smsPossible":true},"CZ":{"internationalCallingCode":"+420","smsPossible":true},"TD":{"internationalCallingCode":"+235","smsPossible":true},"CF":{"internationalCallingCode":"+236","smsPossible":true},"TN":{"internationalCallingCode":"+216","smsPossible":true},"CL":{"internationalCallingCode":"+56","smsPossible":true},"TV":{"internationalCallingCode":"+688","smsPossible":false},"DK":{"internationalCallingCode":"+45","smsPossible":true},"DE":{"internationalCallingCode":"+49","smsPossible":true},"TG":{"internationalCallingCode":"+228","smsPossible":true},"DO":{"internationalCallingCode":"+1","smsPossible":true},"DM":{"internationalCallingCode":"+1","smsPossible":false},"TT":{"internationalCallingCode":"+1","smsPossible":true},"TM":{"internationalCallingCode":"+993","smsPossible":false},"TR":{"internationalCallingCode":"+90","smsPossible":true},"TO":{"internationalCallingCode":"+676","smsPossible":false},"NG":{"internationalCallingCode":"+234","smsPossible":true},"NR":{"internationalCallingCode":"+674","smsPossible":false},"NA":{"internationalCallingCode":"+264","smsPossible":false},"NI":{"internationalCallingCode":"+505","smsPossible":true},"BH":{"internationalCallingCode":"+973","smsPossible":false},"NE":{"internationalCallingCode":"+227","smsPossible":true},"PK":{"internationalCallingCode":"+92","smsPossible":true},"JP":{"internationalCallingCode":"+81","smsPossible":true},"BS":{"internationalCallingCode":"+1","smsPossible":true},"NZ":{"internationalCallingCode":"+64","smsPossible":true},"BM":{"internationalCallingCode":"+1","smsPossible":true},"NP":{"internationalCallingCode":"+977","smsPossible":true},"BB":{"internationalCallingCode":"+246","smsPossible":true},"NO":{"internationalCallingCode":"+47","smsPossible":true},"HT":{"internationalCallingCode":"+509","smsPossible":true},"BD":{"internationalCallingCode":"+880","smsPossible":true},"VA":{"internationalCallingCode":"+39","smsPossible":false},"PA":{"internationalCallingCode":"+507","smsPossible":true},"VU":{"internationalCallingCode":"+678","smsPossible":false},"PG":{"internationalCallingCode":"+675","smsPossible":true},"PW":{"internationalCallingCode":"+680","smsPossible":true},"PY":{"internationalCallingCode":"+595","smsPossible":true},"PS":{"internationalCallingCode":"+970","smsPossible":true},"BR":{"internationalCallingCode":"+55","smsPossible":true},"HU":{"internationalCallingCode":"+36","smsPossible":true},"TL":{"internationalCallingCode":"+670","smsPossible":true},"BG":{"internationalCallingCode":"+359","smsPossible":true},"BF":{"internationalCallingCode":"+226","smsPossible":true},"BN":{"internationalCallingCode":"+673","smsPossible":true},"FJ":{"internationalCallingCode":"+679","smsPossible":true},"PH":{"internationalCallingCode":"+63","smsPossible":true},"FI":{"internationalCallingCode":"+358","smsPossible":true},"BT":{"internationalCallingCode":"+975","smsPossible":true},"BY":{"internationalCallingCode":"+375","smsPossible":true},"BZ":{"internationalCallingCode":"+501","smsPossible":false},"PR":{"internationalCallingCode":"+1","smsPossible":true},"BE":{"internationalCallingCode":"+32","smsPossible":true},"BA":{"internationalCallingCode":"+387","smsPossible":true},"FR":{"internationalCallingCode":"+33","smsPossible":true},"BO":{"internationalCallingCode":"+591","smsPossible":false},"PF":{"internationalCallingCode":"+689","smsPossible":true},"BI":{"internationalCallingCode":"+257","smsPossible":true},"VN":{"internationalCallingCode":"+84","smsPossible":true},"BJ":{"internationalCallingCode":"+229","smsPossible":true},"VE":{"internationalCallingCode":"+58","smsPossible":true},"PE":{"internationalCallingCode":"+51","smsPossible":true},"PL":{"internationalCallingCode":"+48","smsPossible":true},"BW":{"internationalCallingCode":"+267","smsPossible":true},"PT":{"internationalCallingCode":"+351","smsPossible":false},"HK":{"internationalCallingCode":"+852","smsPossible":true},"HN":{"internationalCallingCode":"+504","smsPossible":false},"MH":{"internationalCallingCode":"+692","smsPossible":true},"MO":{"internationalCallingCode":"+853","smsPossible":true},"MG":{"internationalCallingCode":"+261","smsPossible":true},"MW":{"internationalCallingCode":"+265","smsPossible":true},"ML":{"internationalCallingCode":"+223","smsPossible":true},"MT":{"internationalCallingCode":"+356","smsPossible":true},"MY":{"internationalCallingCode":"+60","smsPossible":true},"FM":{"internationalCallingCode":"+691","smsPossible":false},"MM":{"internationalCallingCode":"+95","smsPossible":true},"MX":{"internationalCallingCode":"+52","smsPossible":true},"MU":{"internationalCallingCode":"+230","smsPossible":true},"MR":{"internationalCallingCode":"+222","smsPossible":true},"MZ":{"internationalCallingCode":"+258","smsPossible":true},"MC":{"internationalCallingCode":"+377","smsPossible":true},"MV":{"internationalCallingCode":"+960","smsPossible":true},"MD":{"internationalCallingCode":"+373","smsPossible":false},"RO":{"internationalCallingCode":"+40","smsPossible":true},"MA":{"internationalCallingCode":"+212","smsPossible":true},"MN":{"internationalCallingCode":"+976","smsPossible":true},"ME":{"internationalCallingCode":"+382","smsPossible":true},"JO":{"internationalCallingCode":"+962","smsPossible":true},"LA":{"internationalCallingCode":"+856","smsPossible":true},"RU":{"internationalCallingCode":"+7","smsPossible":true},"LV":{"internationalCallingCode":"+371","smsPossible":true},"LT":{"internationalCallingCode":"+370","smsPossible":true},"LY":{"internationalCallingCode":"+218","smsPossible":true},"GQ":{"internationalCallingCode":"+240","smsPossible":false},"LI":{"internationalCallingCode":"+423","smsPossible":false},"LR":{"internationalCallingCode":"+231","smsPossible":true},"CN":{"internationalCallingCode":"+86","smsPossible":true},"LU":{"internationalCallingCode":"+352","smsPossible":true},"ZA":{"internationalCallingCode":"+27","smsPossible":true},"RW":{"internationalCallingCode":"+250","smsPossible":true},"LS":{"internationalCallingCode":"+266","smsPossible":false},"LB":{"internationalCallingCode":"+961","smsPossible":true}};

		$(function () {
			contactInfoInit();
		});
		
		function contactInfoInit() {
			
			
			Asw.get('representativeTelType').find('option[value="M1"]').attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_TYPE,
					Asw.ContactInput.Const.DATA_TYPE_VALUE_SMS);
			Asw.get('representativeTelCountry').children('option').each(function(i,option){
				var countryInfo = Asw.ContactInput.countryInfoMap[option.value];
				if (countryInfo === void 0) {
					
					return true;
				}
				$(option).attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_SMS_POSSIBLE, countryInfo.smsPossible);
				$(option).attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_COUNTRY_CODE, countryInfo.internationalCallingCode);
			});
			
			var smsCountrySelectSelector = 'select.js-countrySelect:not(#representativeTelCountry)'
			$(smsCountrySelectSelector).eq(0).children('option').each(function(i,option){
				var countryInfo = Asw.ContactInput.countryInfoMap[option.value];
				if (countryInfo === void 0) {
					
					return true;
				}
				$(smsCountrySelectSelector + ' option:nth-child(' + (i+1) + ')').attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_COUNTRY_CODE,
						countryInfo.internationalCallingCode);
			});
			
			var emergencyCountrySelectSelector = 'select.passengerEmergencyCountry.js-countrySelect:not(#representativeTelCountry)'
			$(emergencyCountrySelectSelector).eq(0).children('option').each(function(i,option){
				var countryInfo = Asw.ContactInput.countryInfoMap[option.value];
				if (countryInfo === void 0) {
					
					return true;
				}
				$(emergencyCountrySelectSelector + ' option:nth-child(' + (i+1) + ')').attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_COUNTRY_CODE,
						countryInfo.internationalCallingCode);
			});
		}
	</script><script type="text/javascript">
		var isInit = true;
		function changeTelType(_this, telNoInputId) {
			var $telNoInput = Asw.get(telNoInputId);
			var $telNoKind = Asw.get("representativeTelType");
			var telNo = Asw.getValueComparedToPlaceholder(telNoInputId);
			var telNoCountry = Asw.get("representativeTelCountry").val();

			if(true && !isInit) {
				if(telInputCheck(telNo, telNoCountry)) {
					if (_this.value == "M1") {
						telNo = exceptDomesticPrefix("09042504176", telNoCountry);
					} else if (_this.value == "B1") {
						telNo = exceptDomesticPrefix("03-5405-2172", telNoCountry);
					} else {
						telNo = exceptDomesticPrefix("090-4250-4176", telNoCountry);
					}
					$telNoInput.val(telNo);
				}
			}
			isInit = false;
		}
		
		function telInputCheck(telNo, telNoCountry) {
	        checkResult = false;
	        
	        var amcHndyTelNo = exceptDomesticPrefix("09042504176", telNoCountry);
	        var amcCmpTelNo = exceptDomesticPrefix("03-5405-2172", telNoCountry);
	        var amcTelNo = exceptDomesticPrefix("090-4250-4176", telNoCountry);
	
	        if(telNo == "" || telNo == amcHndyTelNo || telNo == amcCmpTelNo || telNo == amcTelNo) {
	        	checkResult = true;
	        }
	        return checkResult;
    	}
    	
    	function exceptDomesticPrefix(telNo, telNoCountry) {
    		if(telNoCountry == 'JP') {
    			if(telNo.substr(0, 1) === '0') {
    				return telNo.slice(1);
    			}
    		}
    		return telNo;
    	}
	</script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-customerinfomationform.js?6e91953"></script><script type="text/javascript">
		$(document).ready(function(){
				Asw.enableTextWhenCheckBox('.cascadedSwitch');
				Asw.notPasteInput('.jsNotPasteInput');
			$(function(){
				Asw.alignHeightToParentTdElement( '.passengerInfoInputArea' );
				
					Asw.alignHeightToParentTdElement( '.passengerInfoInputText' );
				
				
				$('select.js-countrySelect').trigger('change');
			});
		});
	</script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-session-keeper.js?6ec0730"></script><script type="text/javascript">
			$(document).ready(function() {
				Asw.SessionKeeper.initAndStart("sessionKeeperContainer", "520", "570", "PC");
			});
			</script><script type="text/javascript">
		Asw.addAjaxCallback(function(data) {
			// アコーディオンのメソッドと設定 
			if($('.jsAccordionSwitch .jsTriggerSwitch').length > 0){
				var events = $._data($('.jsAccordionSwitch .jsTriggerSwitch').get(0), "events");
				if(events === undefined || events.click === undefined) {
					Asw.AccordionInfo('.jsAccordionSwitch', {
						'triggerSwitch': '.jsAccordionSwitch .jsTriggerSwitch',
						'listClass': '.jsAccordionSwitchList',
						'visibilityHidden': 'toggleContents'
					});
				}
			}
		});
	</script><script type="text/javascript">
	Asw.Price = Asw.Price || {};
	// 整数の区切り文字
	Asw.Price.separateString = ',';
	
	// 整数の区切り桁数
	Asw.Price.separateDigitNumber = 3;
	
	// 小数点
	Asw.Price.decimalPointString = '.';
	
	// 小数点以降の有効桁数
	Asw.Price.significantDigit = 0;
	
	// 表示金額の単位
	Asw.Price.currencyUnit = '円';
	</script><script type="text/javascript">
			$(document).ready(function() {
				$('#preLoadingArea').css('visibility', 'hidden');
			});
		</script><noscript><img src="https://aswbe-i.ana.co.jp/akam/13/pixel_224265f6?a=dD1hNjNhNDRjMGUwOGEyZDkzYTFhMWYxODYwYmQ1ZGNjNzAxMGQ0MDZlJmpzPW9mZg==" style="visibility: hidden; position: absolute; left: -999px; top: -999px;" /></noscript><script type="text/javascript"  src="/Q1XS4o/Cn_ls/KfDO7/xA/t9OfhmEwSiGQaG/NG4dCQE/bw/QJBChQIQEB"></script></body>
</html>