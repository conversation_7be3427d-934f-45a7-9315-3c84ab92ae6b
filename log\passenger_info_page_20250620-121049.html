<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="ja-<PERSON>" xml:lang="ja-<PERSON>">

	
	<!-- skey = 2025/06/20 13:10:50 rei21e KzaLiAi_Zt dljdmx+f88  --><head id="j_idt53">
    <base href="about:blank">
    <style>
        /* Hide elements that might try to load external resources, as a fallback. */
        img, svg, video, iframe, link[rel="stylesheet"], script { display: none !important; }
    </style>

		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
			<meta http-equiv="X-UA-Compatible" content="IE=EmulateIE9; IE=EmulateIE10" />
			<meta http-equiv="X-UA-Compatible" content="IE=7" />
			<meta http-equiv="X-UA-Compatible" content="IE=edge" />
		<meta name="description" content="" />
		<meta name="keywords" content="" />
		<meta name="format-detection" content="telephone=no" />
		<script type="text/javascript" src="/international_asw/dynatrace/ruxitagentjs_ICANVfhqrux_10273230920145353.js" data-dtconfig="rid=RID_-*********|rpid=-1013638522|domain=aswbe-i.ana.co.jp|reportUrl=https://bf90419wvr.bf.dynatrace.com/bf|app=78bf0b58acf6ed13|cors=1|owasp=1|featureHash=ICANVfhqrux|xb=www^bs.google-analytics^bs.com^pana^bs.demdex^bs.net^pcollect^bs.tealiumiq^bs.com^pallnipponairways^bs.australia-3^bs.evergage^bs.com^pallnipponairwayscolt^bs.tt^bs.omtrdc^bs.net|rdnt=1|uxrgce=1|cuc=k8vpgh61|mel=100000|expw=1|dpvc=1|md=mdcc1=bAsw.PnrOutput.recordLocator,mdcc2=bAsw.PnrOutput.paymentDetailInfoList[0].fopCode,mdcc3=bAsw.PnrOutput.ticketedFlag,mdcc4=bAsw.PnrOutput.nhPnrFlag,mdcc5=bAsw.PnrOutput.itinerarySummary.departureDateYyyymmdd,mdcc6=bAsw.SearchCriteriaOutput.cffCodeInput,mdcc7=a#main ^rb div.section.sectionNoFlow ^rb table ^rb tbody ^rb tr ^rb td.flightNumber.lastCol ^rb span ^rb span:nth-child(2),mdcc8=cunique|lastModification=1750184416409|tp=500,50,0|agentUri=/international_asw/dynatrace/ruxitagentjs_ICANVfhqrux_10273230920145353.js"></script><link rel="shortcut icon" href="https://www.ana.co.jp/favicon.ico" />
		<title>お客様情報入力 | ANA</title>
		<noscript><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/css/asw_noscript_pc.css?99001fc" />
		</noscript>

		<!--  [ Adobe Target ] -->
		<script type="text/javascript" src="https://www.ana.co.jp/behavior/adobetarget/target_intbe.js"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/script/jquery-1.8.3.min.js?717d3c0"></script><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/ja/css/asw_initialize.css?99001fc" /><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/css/asw_screen_pc.css?99001fc" /><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/css/asw_common_pc.css?99001fc" /><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/css/asw_forminput_pc.css?99001fc" /><script type="text/javascript">
		var Asw = Asw || {};
		Asw.GlobalWord = Asw.GlobalWord || {};
		
		Asw.GlobalWord.toolTipCloseIconAltText = "閉じる";
		
		</script><script >bazadebezolkohpepadr="1003740716"</script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/akam/13/3bd3deee"  defer></script></head><body>
		
		<noscript>当サイトをご利用になるためには、JavaScript対応のブラウザが必要です。設定でJavaScriptを有効にしてください。有効にできない場合はANA電話窓口へお問い合わせください。
		</noscript>
		<div id="preLoadingArea"></div>
	
	<div id="cmnHeader" class="platinum">
		<!--===== cmnHeader =====-->
		<div class="headerWrapper">
			<div class="header">
				<p class="logo"><a href="https://www.ana.co.jp/asw/AMCTopServlet?type=a" tabindex="-1" aria-hidden="true"><img src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/image/ana_logo_navy.png?717d3c0" alt="ANA Inspiration of JAPAN。リンク先はマイレージクラブトップページです。" height="28" width="274" /></a>
				</p>
					<ul class="headerMenu">
						<li><a href="https://www.ana.co.jp/asw/AMCTopServlet?type=a">ANAマイレージクラブトップページ</a>
						</li>
						<li><a href="https://www.ana.co.jp/other/int/meta/0005.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">お問い合わせ<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
						</li>
							<li class="btnLogoutArea"><a href="https://aswbe-i.ana.co.jp/rei21e/international_asw/rest/logout" role="button" class="btnBase btnLogout">ログアウト</a>
							</li>
					</ul>
			</div>
		</div>
			<div class="amcUserInfoWrapper">
				<div class="amcUserInfo">
					<div class="userNameArea">
						<table>
							<tr>	
									<td class="statusIcon"><img src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/image/icon_status_05.png?717d3c0" alt="プラチナサービスメンバー" height="46" width="60" />
									</td>
								<td class="userName">佐藤 直樹 <span>様</span>
								</td>
							</tr>					
						</table>
					</div>
						<div class="balance">
								<dl class="upgradePointBalance">
									<dt>アップグレードポイント残高
									</dt>
									<dd>
											<dl>
												<dt>(2025年度)
												</dt>
												<dd>
													<em>20
													</em>ポイント
												</dd>
											</dl>
									</dd>
								</dl>
							<dl class="mileBalance">
								<dt>マイル口座残高
								</dt>
								<dd><em>75,663</em><span class="currencyCode">マイル</span>
								</dd>
							</dl>
						</div>
				</div>
					<p class="nowTime">6月20日13時10分現在
					</p>
			</div>
		<!--===== /cmnHeader =====-->
	</div>
		<div id="sessionKeeperContainer" class="sessionKeepingContainer"><img src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/image/bg_modalWindow.png?717d3c0" alt="" height="0" width="0" />
			<div id="extendSessionExpiredDialog" class="modalExpire modalSmall modalContainer" role="dialog" aria-labelledby="extendSessionExpiredDialogTitle" aria-hidden="true">
<form id="sessionKeeperContainer:j_idt183" name="sessionKeeperContainer:j_idt183" method="post" action="https://aswbe-i.ana.co.jp/rei21e/international_asw/pages/award/reservation/award_mandatory_passenger_information_input.xhtml?aswcid=1&amp;rand=20250620131050KzaLiAi_Zt" autocomplete="off" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="sessionKeeperContainer:j_idt183" value="sessionKeeperContainer:j_idt183" />
<input type="hidden" name="sessionKeeperContainer:j_idt183_operationTicket" value="dljdmx+f88dd8af736703b33a0798072239391d~ReUu1Xp0cf8NImlFqT9JjMwo4zDJZ13AOppkuebi!1750392640654.aere-xml-controller-67d4778877-x4hbs" /><input type="hidden" name="sessionKeeperContainer:j_idt183_cmnPageTicket" value="4" />
					<h1 id="extendSessionExpiredDialogTitle" class="dialogTitle">まもなくセッションが終了します</h1>
					<div id="extendSessionExpiredDialogMessage" class="dialogMessage" tabindex="-1">このままお手続きを進める場合は、［延長］ボタンを押してください。</div>
					<p class="modalButton"><input id="sessionKeeperContainer:cmnSessionKeepingButton" type="submit" name="sessionKeeperContainer:cmnSessionKeepingButton" value="延長" class="btnBase btnModal btnMainStream" onclick="mojarra.ab(this,event,'action',0,0,{'onevent':function(data) {Asw.doCommonAjaxCallback(data,false, event); Asw.SessionKeeper.doSessionKeep(data);},'onerror':function(data) {Asw.doCommonAjaxErrorCallback(data); }});return false" />
					</p><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:0" value="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" autocomplete="off" />
</form>
			</div>
			<div id="showSessionExpiredDialog" class="modalExpire modalSmall modalContainer" role="dialog" aria-labelledby="showSessionExpiredDialogTitle" aria-hidden="true">
				<h1 id="showSessionExpiredDialogTitle" class="dialogTitle">セッションが終了しました</h1>
				<div id="showSessionExpiredDialogMessage" class="dialogMessage" tabindex="-1">一定時間操作されなかったため、お手続きを進めることができません。お手数ですが、再度トップページからお手続きください。</div>
				<p class="modalButton">
					<input type="submit" value="閉じる" class="btnBase btnModal btnMainStream" onclick="Asw.SessionKeeper.closeTimeoutDialog(); return false;" />
				</p>
			</div>
		</div>
		<!--===== dialogMessages =====--><div id="cmnModalMessages"></div>
		<div id="prebookConfirmDialog" class="modalContainer modalConfirm modalSmall " aria-hidden="true" role="dialog">
<form id="prebookConfirmDialog:j_idt403" name="prebookConfirmDialog:j_idt403" method="post" action="https://aswbe-i.ana.co.jp/rei21e/international_asw/pages/award/reservation/award_mandatory_passenger_information_input.xhtml?aswcid=1&amp;rand=20250620131050KzaLiAi_Zt" autocomplete="off" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="prebookConfirmDialog:j_idt403" value="prebookConfirmDialog:j_idt403" />
<input type="hidden" name="prebookConfirmDialog:j_idt403_operationTicket" value="dljdmx+f88dd8af736703b33a0798072239391d~ReUu1Xp0cf8NImlFqT9JjMwo4zDJZ13AOppkuebi!1750392640654.aere-xml-controller-67d4778877-x4hbs" /><input type="hidden" name="prebookConfirmDialog:j_idt403_cmnPageTicket" value="4" /><div class="dialogMessage" tabindex="0"><p>この後、名・姓の修正や変更ができません。</p><p>「搭乗者選択」欄の氏名が「名」「姓」の順番で表示されていることを再確認ください。</p><p>例）<strong class="highlight">空野 太郎</strong> 様 → MR <strong class="highlight">TARO SORANO</strong></p><p>保安上の理由により、航空券とパスポートのお名前が同じ表記でない場合はご搭乗いただくことができませんので必ず再度ご確認ください。</p></div>
					
					<ul class="modalBtnHorizontal">
							
							<li><input type="submit" name="prebookConfirmDialog:j_idt419" value="戻る" aria-controls="prebookConfirmDialog" class="btnBase btnModal btnCancel" onclick="Asw.Dialog.getInstance('prebookConfirmDialog').close(event, Asw.DialogSequencer.isActiveSequencerNotDisplayedLastDialog());  return false;" />
							</li>
						
						<li class="btnArrowNext"><input type="submit" name="prebookConfirmDialog:j_idt422" value="OK" aria-controls="prebookConfirmDialog" class="btnBase btnModal btnMainStream " onclick="Asw.Dialog.getInstance('prebookConfirmDialog').callOpener('NORMAL', event); return false;" />
						</li>
					</ul><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:1" value="oNhLHSmn8/TNLPRX6/Cu/1pkZsuYKK0D4pPr8U8TesVmboWb9nnv+FMwM7xCUnaUmqG/iEispF8abMET8/9IDZ2FRAi/hyku+m4qn/4QcuI/TUIGaebnBZDdXKMJ18MwdjufWY050xFqk1lvB+Gje7KT8MR7x/UIJOZkVQnwrGujRZwjG0UQK+GCjux0u4zbywNf9lgTS/rYFnWSNzNwuYtCqBhM62GNllxpwUzvO4r+ux9lqbhzu9gJfBzmrSHAYTQcKXQaXoopPhxvo1MjH6XNoG5DJE4EKTBzmRGhu8vjhkt7WKT8oFeLMTk6gDVT6P8qVMJn5xRBjppy4KEB72MWqizRdWPe81cCovYJhrhxB8xXMflMrG27k2AD2ZArABTH2wxQwUQNiDaeHkIT1s+dhY4ci1XMQro6FDQE8uKvseri44bOdju6Lcfwj/SuNvRBkgG8UmGszbOBhau54H/GNe1SY/E+UT1jPgCzLtT0Lws98mLqsEpHR7I4ZX7pesD0fzU6lnIRVt7tj29GpXvX7ZXX+LNT6DEqhTjl26rESNu5yysT1QvGGfN7714CL8FmLb3HqxQJslsIxcRD+oU0H42km/aKkZ6PRkvA4fdqb4EvV6TkHkqv9ftw1Fz128aAyC7TW93paDuWXBiSZExMhEgSBXSnIl9ANwPSM2+xFA3e2xPEyo9y/ZVXz/faBAur3C5hTr/0mkHfV2MKBZiD84bGJkFmtSLJQ2C9/e1822aiD6XdXbU1fmG/KGDfl60pxO6qsMG9SzdaEBWULB1y+Q+FXRckWthWA+Ykk2qwK26+irnvT0d2uYYk/CQpmDCoVAZXcdR8BeHWF6mDklXTUbzXDGnfzJCn3mkFK3/33KUUA3oL8INQ3AaybuOvsScMn4xEJQzcruqqbsN2GkWZ3Xw4HFspH6JaWSgc78sqSblq/6y+CaoUhktifmvziO4db6kfwg9TLVciHy+m3MHN32Fe/ENBPbwtJHEyqa91ewHdKpem1FCGn7JVH9jj1CY0dGENcxeXy+YOq2Q+mTjR5fND1ClqjCFVsq04UXObuQag/Pv9DCNyKbDpLtwhBuG/I2skncBinxP05TUe2bE8MKKYXcjd9VKZRA/04V4X9Wx2ZGryaDP4D/R6PGNviFr2/CmnBa9rm+Wy+trPLDL7xdssK0JQnK+lByMTRQ95S8lkjpQYOeerLHQnH++ib13LojW56z+31ELT874cMWWPb/3b6rrLJwuxJFwPIz6/rUrm05iRw6keCRIKq69KC5w46/Mj/Z6lzcfBh4KtFoFFNvo3jZ7YKafuim0O1iJTY07BJybet3eG0ZgpJaFkHQATpzOb8+FuEi1IRQI0mpLCUU7F2iycmTbCyBnWCYgBrr4swjEKovc9G1kSqbi4fwTwGGlefyCL1C+N5Llsk7Sn/jO417/HmMcbiGl7bsOaHbM7w0/QUquOHmsOdzox36d8Il/SmUEkFOz8vad6FVIvTlFKoZUT9Sp1KKabZikJ4GXMMD0Bd++1Nz1QRgdpj1oKyKSF2VTIANGciYcNZjAM5xCX13TFIcyEnGCMFKjo/s87wlH1KTwH6Di8W5+AAwMjfNjImLs8WqvEEhLfkmDeVUOJNUTz2tqG+PuPd+/PrKB//zIoDxrqsH8QaL+DWjNpmqieUoVEz8UiLwdr3b7eFhwjgg7CoBWLhfQfJRWyPZzzj+m+ej//JQKUkaZz2o3m+Set3NS2EDGl/FCv8niGYHP8H0KnVLceg5LeQ7y3BM6LUgM+uq0pklua1xzTcWo6n0c+lvLmGAYC1thRrvN2VFmtx9k/g9D+GoRhcl9ZaA+hettFp6QyqxVxrk3DgWgLatJIgRD7mTpf1PhvarjMo4JhqlgG9PlKb8cQPDQ7aDKI8GO+e8DMWez9dE1u+w/Y1oX1kmcUF4Ji5iCAvinWH0x8IOkQVaS61hekqjGO3A4pvMZqlU9xAy5XRENCVWYsKWp9a551qIF7hDEuK+6Z7vdFDiybbnD5SbqHOcDhxLtrW0ZAiAhn6M+yJut1/WTJ0H8YAk1q1HiEczNsOduP510L8GCvxAHJTzatT5vtEYtedEfXX0i9KD1npFw8MYPRUJczH00mctE43c+tr3F8tV7nuSDUfw+ndFU8IxceFFPDEhxnIKQKwSzLkgprm/tPQ18WrmES2YhUH7fEg66fVtKTX9AuR4RxKIChWtE0bygsBILys7tlF6VN9MQVGWINpRVI46AdT4oMOC2ITwJC5MG6hWn20D7sj1EyhOLdUZ6r2MYqUrifmguDyN54GY9BzPyn14n9nX3h1ty1kEa6szpAi+Z4LnLnjR9Zhwyle08A/rFqfhTKbD7wQ19RAIQ+ybSnCeBVVRYSNKTt1jzfOkt0ZVZS9FKgErulwxpr0XE5/747yjNokyG0O6Pe/idZwRsdqaqERGwnEk2bdTGCvTivpiBaMRgt/hQ8DewMWBTg35gvPXbcwMt5hgaOc2bg+tcIJKReqdjbn87K9PTJMgeBkF7SIwAZrIcT+w85a/i6qu49Fb7GEHETzRtCaWBIvrmX6A6ia8IhIhiPBs5qM4N+XiXLVldf6IhyVWQNqEhsZcUMtAunMaopXcRU1HDzqGEO73dbBeVptFrKsqe15rPb8b+6JjvznjddaQRaC8emRlKzsGWnnnba/wMP9RIBEFLof7+Padm7GudSYOXJVmZrObQlEWZf0RPAbf8VUHJtxBL7Qd00nXyspe8hGNXT45489l5XWmX+QuVL3S3CpSIE5SLzrjY1Qhs0F7jNfEooG8GeUS/NqTZl83rPFBf4jMvGwcIs2KIeHiMMatQOPznGnRVOCOCJbc4eJagzffw2/+4xzqs9SCWEAmUD5/bb7kza6lVM9Jpxso7o0IkRIKn/N3GMEmDkSkh+eUoUEq2BUfixhoABLJ09PUB5zJtyrfLTvxzEc8q2UFDGRNHqi2DyNrLlZjYTRYmeZ41YK54bpSndPnjia7mjT1WJSx5R9RY/1O8xe4xhl5uBbUP4V35X7fXwtBrZkvQkFIZ2DVrYFkdoIX695BW8eiCH0iBJImFcIQMhOzvt7vNlaIJDIkFETL96EoXYnQPaeIxLRvcWHkbSh3PkTxrZBwZgrZrAKJBUKepRS+HLNlBU9U5P+H+q11phI6NPHMg6hSmvs7OlHMYauuZ8I40ooIUkKl0VgmEzDyg62bl5pVXFcA5IW6TTDNktDmAyLnSyKngJo4jeVcOBt8sI4dRte0O8pFvjmcmzze7qjEhg3s4AnMT6gjJj+Pib4Knj6L8VLe32QhsgWaRRcOOsMkImKw1aElub+1r2gwHUpoluUrkMq8h3zbYv7NMe5TO35WZc/AjgvyHZQG03ZZlruAwpoqyep+CFDTBAOdMoHtXfBnVld5I36Y1/8lgOmuZHkrj2G6oo1MO6jkj+pbvvYMUZovO83rKidmo9rtrNWoHjKRckAimmw+LDBJwieA969O3wmrcSyI8P7vpWnQXMAD6cIFfacybshbEvsJ72EcG2OAvJJ5Qpwbd51wYcZBd4aH707VYLu37hY+rqhJXagKvEr3TqFgevjPO05bcS1O8F9XPxhBKJZ0d7zwZMszIz7x906hal8ffYfL8BAoY7YYZlBIb8KGOVWUXR2LGU+4/QO8gtGYa6QVmhUZBUUCx3Bbjk2pjmPTX/ZWSK8hLa8U6sjkMjQRDGWm4oUeUHO5WSLORYBkNC7nh7H0W1ZNrm7MB6a+X5Qy5Xqt6MH7/68xEcCTGrUXxh/P6hfpbtjLBqI9uTnwX1NyKPuDaMRLKj/Bco4Pg1Jlc6qDtqgntZxPbjmW8tD2fWGvS0IXu2VLkl/uVCl84aC8b6q+G8+Lb0+GyssYpKIWQeo0H/ykJ9JxsRsi/eG+tdmSscWS8pyaontJqZhjcKROFAcpf94EzQx4ywHVSKTzwbSO2KJgqGdfV3UCSJHSClJkBwZ9QbrbhSDzYnlsIEwFINoHoR6Eq6D2/VbO+woBXwkioIRVHo2t+XPlkhO02sVjPlp+T8Tphsj+5BlY3PK5c2YAkjPVYsaZALL9lk3fB9CMZ0yipEepb0JDBcCKfUsLRuGVeRT+BDxqpaFuKg2sHtqT8I0ofLvk6nd7X/A7oevb8bfma5UXEcrafyvEIshIRk25n6Pu0eoorZJ5w48PLUAkouV7y5Yf9JgwO8jqbATfbLpSFD0+yNLii0FzaZKpuyXcAJfErJ6iImgZmyUhmD25q0Ep8fCCORZ9zw8OH8jf34HPmLdwqQWLfEAY2iUQ3bdoOJeQpz889tzKU9sobRMYp07JzeJ9dIk3iqYtLdmIC/8InV40qZtgSJQquPUhMDzL/Ct3V1A9MYtnvgfjQsvfVvlpm8sA/MCRuFEUaPYlZ0UI8vJHRg4085tCVzDiyMOa1An+Kqd8v24BL3NUl85eFKJMPc2aEdUTeHxusPPn5TDid2o6VUAIC9VPdsq1pF/JWhHue1dKACg8Iddrfb0EdOWOnhDc3uPyEMTdIVpQY22yb6yGAA7MVYYY3dIfykRBX0ckDjEYjGmhihr2rKUFt2d6XkmDLquMH//vSuimHNrH9ZC/RWKdGn5enSeaLjpBEFNmVTl0K1AM8/RGz7YIC8re/9vLkOwLd3C4zce8BjBFnU8spnXtlTIBHQv1AJjiuVXqdtktOiIxe0qnVpyX/Of883S4wNEuyg5MT3+xTUUfp0mE1xzpo30X/c333CN8HbOYCf8YtiBJS84btOhYPnKXqk6XHxfK28VhUevzavOvsMG3MaM8WFyLNspvyxQtz4IlHZcBKeVg7eNG7GUS50+9AvWATk7RzXzE3VOHaCVOrkDJH7XSyUldr7d+Z7wHLsvVNlzsl2M4imlGuoNpGatY4YWYAMURkilqMUE+6kYdhsJkCKxcD7RQPvJ0uxX+GLbBPRpNg8V+gWON9jikb6j4MalV9XEs64EPn7Tos4U7Hfrc4ELZT5HQkxrqyrUrd/hf5apBetoy55/rRAOT2+YUUUgST6oKu0Mq19NgXK62fpya0GPGgTy8ks+Kli8eSwpcRXisHOgLl8daIP0iccAbxbY5W94U0W45ktRwRMgLliAw/gsAdTAGt6kmQjJCI3Q322OOboJuZExKiC27FiobSdBjO/T4uuQcX9KNKD+VtxGllBFLwGEkEBoAz6u/DnZvsvaasl3WDpqp3xOjT7L5dfYdffI7J0OA0Y475UiRfKY7HV8kyknJqX4T6sFARZQ54Nl3rsEcFCt39rwdkiEd5DOvL6GitihuzxuRsrY3a/7UQ0mfCaAn8Xe1MNmrlzbgjWGmBlVJC2GphOCBYByPgaBxnlmnsteYFB8mqT3rlHrL8taqArVcWBoQ0OIXbSzDZrpHPh8lVLdCYW0gcRJDSR40GnDPgCgTvSfMpCo7uCEBacaswi4PMX3AM/w4S0j8l8z6r04U4C/t+OAOD8+sOKqocxRRXtqJ0qV+ON80lDpdrTKconO6C3tjkN/RhHTLJwXYkPyZ4sREjDIxs/lzDuSdCt8aEsHjdj3BliTX3NVXKtwOQc9BfnOygILiKTcQOw8rUCfC4x6eJwI+CH2qu6/x1zVX9b3lBM+UCVl9zf5j9/YxN5m+sMyLz8rmqsv3ERnHfMgaSFrV8KYsQvCO+F7lZKRKR+WxLMadT64IsnUJ4ABOpRGnBDzizc71mQMuCx1EW1BbDAuWNh5k+JpC0Vj593qg/XejL+duYctzSDn2EEVWj6/BnfrYbWdP5uiVJEFYTdCjvttn2kBjNfc74n/GXkXdd7Tr61oDXFXRVJeE2Hs16YkfdRT9qdBK6pYKKopE/mR/+OQd8BN+Lt/bcxeXFrzU1T4nf8eq86jRut9S6QuxOZR9pprip3QKS2vOAKpbo2fAJOZJkV8hyCpg5vACzE1KSKnq1QFZJScSWoZQzpCsZGR6Ptwb0SOeRcl0J8ExTMA/W+7QQodWYHuhA4c1c9h43QEGJD8w8m4iYYdGa6+12ryVAOYC3eg0/sBOkTGAvxT4B4l9jdzO8BTuXwD6JUDSG/l35uLV294DgiQHDNYgRHcSa4dmokYgqMJeUAi9rjXt5dyQNeED+JHomMfE9QMyCL3AoX2B4NPTuOMwUyWfVZ95QK0J7RWpv+jymlbZTEUIeEyRU5aFRU4Mc4KfUVEL93IjgY6L2ebwdXntcSYxu/hp9bz7rwn388eFdudrMoN8niQK0hJhCB+RJY5LaMfuhDGlRviIrLMJyfUJ7zQLiP1fOUaPwQPP3zfXAK0nJSR6FxR8T0I3e2899J9SmUOj3oj+q2GWZyrfy3V3mIVDylZDc9DM04TwC1l5tYy7l/Mss/LwYL+Dw4Jwov2HbF9KhNy4lt2VOV/pFTDGfUz9eU4nL3az/NbA3TI4GXWUlzUQ7ftUY/raIUSw/dnYy7A3H4Ot928/7wAAeQUKv2PyAlBMlUeApdTf5ge6cx87PpWmtQU9OyEoXD0jl+tAyxtxRZzNSOymL30K8HhIdPS4ps15MDgj72lDmMts53Aq71N6V2y4z7foblU6vHSqPaYW/6dIDfMZ/AAQ1Il/MbqsH0oT2xNv07H6TsiOVfL2g7gM9+MsasWCQa2sxNyiba97thSBiAQ3NcvHPbIG5IusygJNTdmjftUrIUM/X0Jk/bRT9RISpb5be0thu2Vj+ygip469Noq+mdGrWVN3ZFIRl83H+MI9MumrrgYIrpHgCnMguKStKtQBkOpXUiGY1Cq/yNlb6qbZ7nFATz4q2jwGtOuPdagErHVYus3/L2H0EfsvYnqnO0VXDtstRIGZV6yBuquvRVWd9agOWLOvY1RzCY0stHwrEGy+rKJ1i45QLSz8g73b380j/eVcyj/czIai+v0Q89dt93igs6u8rempLQzAzOYRZeoeDst0/cbtuFbLbdLO6cu5MgnaiudjWAyUcDhGBmcwGg7IRNQHl+RlQRYEyJlGJTK2S9BvYbsiE7A1ooqtnVmSVzV/phn/8NmjyLWLDGNTYhYenoMJOO3cK6szNao3MHvtpdU8fxBRxWUn+FV+rfdt58MnC0r+G12rh31eAzzeFGn3HFTMSWEGvj6bEmJQz/hlJmxat9RMNTnFAZTELJVyFkyqIOezN1fAVh87u8dPUOAI2vwHbL9/6zQBjABnYJsRErmN1RJx6fHJ6D8Q2ifwZuPuB5BSi0HHDV2robRy5phPbUOZs1WyUe0nWkGWZKyJ4cpLWnJOfyg2xTVKuqvUlYUmNDa6hr3Q0H7n62LePPGrtz4u0tT0bVkRZTmWnKoB/Me642L7XUfoOyp310/ZnQ/vVbcxqToz/7YkBM9bLhMy/2sSNEOJOmlyFSkwcuiH5R5qusTDVJ598r0zA6V0eS2jJxfMSU7m7pW66dl/BF00nReUGtVdlN1bb6NYvBAgwbmpPBjptD67Oa9a3g2YNBJiaHgEhZ8af2tbjCkdpdX3q2upSszKo9RHnNwFF1DOD0qRk/IfPDbwrsCN31s+AgmofPJaztNW9m/P1qsIgFYfQIgTSA0Q3QZQCOZH7admaZ1bL1wGvhs58MxwEYnAU0Fie5YnLM2/SwETuQEI9RgXIZj169SAWPADDCsMG41pT+ECR4to78DadIs871KSAYXBKxwLzobEzX+uiQKossbS3ksOmZOe5b/i+QKLfnzSrKq8Vt56XqSnKJ3o9D4vcIFhjTUHUI2FLIcqIL+1uJUeep9S8n522RFM9pTNxFfe9x+81QcefO0KFZoQNjDygv44eBWEfuEBA5vir9xNOoDytb1+5CtN0pCv096XpkJ7aX9b2aNP4LW+KNl/NopS+yi96n0KHGzb75JuKWiaMHsJQIxmQsGPgaFIYoPP2qQWx2/qv9D3tLkFOSvT41ZnN5UjdI3wf/AlHEpaBVwi+xB9JgcrRKRJy2wrfeVWGES79m34ngyoWV7mDlhCIbWlgMpvtSjWT8paL5sHnU1Z5yTjA2F21kEK9orVhcBcq/ticsWsmzHbcUOd0lBgefan8+ZalmonymHC5H9asqattZ1WCagBvlO2OiGrX6oXyp7wg3K3MOJJy9j6o2PUaUT7Us6unXvywIzP4JcHsKM41UryvxTkPBOThQaTk6BwWyBjal18dkQI0nHMn85A/2XDJta0q7wyOh17xDyziJtvEnWzPdaLmNsk/Oja3NFl0yyv3zVT3xw5/JohxEpRGs0RscGkgGrao+GmGVoh3sitay4eaHLylTqwOrwwCKrYSqisQIYKkp02NhU5wfTueboDHE4Hv2bbKqQjlcNs2quiH1Y4K927ij/P29" autocomplete="off" />
</form>
		</div>
		<!--===== /dialogMessages =====--><div id="cmnContainer">
		<!--=========== .cmnContainer ============--><div id="cmnEmergencyNotice"></div><div id="cmnTitle" class="hgroup ">
		<h1 class="visuallyHidden">お客様情報入力</h1></div>

		<div id="cmnWrapper">
		<!--===== cmnWrapper =====-->
		<ol class="mainFlow" aria-label="完了までの手順、">
			<li><img src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/image/icon_flow_01.png?717d3c0" alt="1" height="20" width="28" />フライト検索</li><!--
			--><li><img src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/image/icon_flow_02_on.png?717d3c0" alt="2" height="20" width="28" /><span class="visuallyHidden">現在のステップ、</span>お客様情報入力</li><!--
			--><li><img src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/image/icon_flow_03.png?717d3c0" alt="3" height="20" width="28" />お支払い情報</li><!--
			--><li class="flowLast"><img src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/image/icon_flow4_04.png?717d3c0" alt="4" height="20" width="28" />完了</li>
		</ol>

	<div id="main">
	<!--=========== main =============-->
		<ul class="iconExample">
				<li class="requiredIconExample">
					<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="" height="7" width="7" /></span>必須項目です	
				</li>
		</ul>
<form id="j_idt1529" name="j_idt1529" method="post" action="https://aswbe-i.ana.co.jp/rei21e/international_asw/pages/award/reservation/award_mandatory_passenger_information_input.xhtml?aswcid=1&amp;rand=20250620131050KzaLiAi_Zt" autocomplete="off" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="j_idt1529" value="j_idt1529" />
<input type="hidden" name="j_idt1529_operationTicket" value="dljdmx+f88dd8af736703b33a0798072239391d~ReUu1Xp0cf8NImlFqT9JjMwo4zDJZ13AOppkuebi!1750392640654.aere-xml-controller-67d4778877-x4hbs" /><input type="hidden" name="j_idt1529_cmnPageTicket" value="4" />

<div class="section">
	<h2>搭乗者選択<a href="https://www.ana.co.jp/other/int/meta/0086.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="特典のご利用に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a></h2>
	<table class="passengerInfo">
		<thead>
			<tr>
				
				<th class="name">氏名</th>
				
				<th class="dateOfBirthWide" id="dateOfBirthHeader">生年月日</th>
				
				<th class="gender lastCol" id="genderHeader">性別<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span></th>
				
			</tr>
		</thead>
		<tbody>
				
				<tr>
					<th class="parent_01" id="parentHeader01">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:0:passenger" type="checkbox" name="passengers:0:passenger" checked="checked" data-number="1" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:0:passenger">NAOKI SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader01">1963年06月05日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader01">				
							<span class="formWrapper">
							<label for="passengers:0:gender" class="visuallyHidden">NAOKI SATOの性別</label><select id="passengers:0:gender" name="passengers:0:gender" size="1">	<option value="">選択してください</option>
	<option value="M" selected="selected">男性</option>
	<option value="F">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_02" id="parentHeader02">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:1:passenger" type="checkbox" name="passengers:1:passenger" data-number="2" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:1:passenger">REO SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader02">1995年05月18日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader02">				
							<span class="formWrapper">
							<label for="passengers:1:gender" class="visuallyHidden">REO SATOの性別</label><select id="passengers:1:gender" name="passengers:1:gender" size="1">	<option value="">選択してください</option>
	<option value="M" selected="selected">男性</option>
	<option value="F">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_03" id="parentHeader03">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:2:passenger" type="checkbox" name="passengers:2:passenger" data-number="3" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:2:passenger">ERI SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader03">1963年08月14日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader03">				
							<span class="formWrapper">
							<label for="passengers:2:gender" class="visuallyHidden">ERI SATOの性別</label><select id="passengers:2:gender" name="passengers:2:gender" size="1">	<option value="">選択してください</option>
	<option value="M">男性</option>
	<option value="F" selected="selected">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_04" id="parentHeader04">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:3:passenger" type="checkbox" name="passengers:3:passenger" data-number="4" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:3:passenger">TAISHI SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader04">1992年12月03日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader04">				
							<span class="formWrapper">
							<label for="passengers:3:gender" class="visuallyHidden">TAISHI SATOの性別</label><select id="passengers:3:gender" name="passengers:3:gender" size="1">	<option value="">選択してください</option>
	<option value="M" selected="selected">男性</option>
	<option value="F">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_05" id="parentHeader05">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:4:passenger" type="checkbox" name="passengers:4:passenger" data-number="5" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:4:passenger">HIROMI FUJIMOTO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader05">1961年11月23日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader05">				
							<span class="formWrapper">
							<label for="passengers:4:gender" class="visuallyHidden">HIROMI FUJIMOTOの性別</label><select id="passengers:4:gender" name="passengers:4:gender" size="1">	<option value="">選択してください</option>
	<option value="M">男性</option>
	<option value="F" selected="selected">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
		</tbody>
	</table>
</div>
		<div class="section">
			<h2 class="midstream">連絡先情報
			</h2>
			<h3>代表者
			</h3>
	
	
	
	<div class="formInput">
		<fieldset>
			<legend><label>代表者</label></legend>
			<dl id="contactEmailAddressInfo">
				<dt class="hasRequiredIcon">メールアドレス<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span><a href="https://www.ana.co.jp/other/int/meta/0057.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="メールの宛先と内容に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a>
				</dt>
				<dd>
					<ul class="verticalList">
						<li class="nonMarginTop"><label for="representativeEmail" class="visuallyHidden">代表者メールアドレス</label><input id="representativeEmail" type="text" name="representativeEmail" value="<EMAIL>" class="widthLarge" placeholder="例：<EMAIL>" />
						</li>
						<li>
							<span class="supplementTxt" id="confirmMailAddressForRepresentative">確認のため再度ご入力ください。
							</span><label for="confirmrepresentativeEmail" class="visuallyHidden">確認用メールアドレス</label><input id="confirmrepresentativeEmail" type="text" name="confirmrepresentativeEmail" value="<EMAIL>" class="widthLarge jsNotPasteInput" placeholder="例：<EMAIL>" aria-describedby="confirmMailAddressForRepresentative" />
						</li>
					</ul>
				</dd>
					<dd style="display:none;"><input id="updateAlwaysInfo" type="checkbox" name="updateAlwaysInfo" /><label for="updateAlwaysInfo">ログイン中の会員情報へこのメールアドレスを登録</label>
					</dd>
			</dl>
			<dl>
				<dt>電話番号<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span>
				</dt>
				<dd class="js-countryWrap">
					<ul>
						<li><label for="representativeTelType" class="visuallyHidden">電話番号種別</label><select id="representativeTelType" name="representativeTelType" class="basicWidth js-typeSelect" size="1" onchange="changeTelType(this,'representativeTel')">	<option value="M1" selected="selected">携帯</option>
	<option value="B1">会社</option>
	<option value="H1">自宅</option>
</select>
						</li>
						<li><label for="representativeTelCountry" class="visuallyHidden">電話番号の国/地区選択</label><select id="representativeTelCountry" name="representativeTelCountry" class="js-countrySelect" size="1">	<option value="" data-sms-possible="true" data-country-code="---">選択してください</option>
	<option value="JP" selected="selected" data-sms-possible="true">日本</option>
	<option value="-" data-sms-possible="true" data-country-code="---">-----------</option>
	<option value="IS" data-sms-possible="true">アイスランド</option>
	<option value="IE" data-sms-possible="true">アイルランド</option>
	<option value="AZ" data-sms-possible="true">アゼルバイジャン</option>
	<option value="AF" data-sms-possible="true">アフガニスタン</option>
	<option value="US" data-sms-possible="true">アメリカ合衆国/カナダ</option>
	<option value="AE" data-sms-possible="true">アラブ首長国連邦</option>
	<option value="DZ" data-sms-possible="true">アルジェリア</option>
	<option value="AR" data-sms-possible="true">アルゼンチン</option>
	<option value="AW" data-sms-possible="true">アルバ</option>
	<option value="AL" data-sms-possible="true">アルバニア</option>
	<option value="AM" data-sms-possible="true">アルメニア</option>
	<option value="AO" data-sms-possible="true">アンゴラ</option>
	<option value="AG" data-sms-possible="true">アンティグア・バーブーダ</option>
	<option value="AD" data-sms-possible="true">アンドラ</option>
	<option value="YE" data-sms-possible="true">イエメン</option>
	<option value="GB" data-sms-possible="true">イギリス</option>
	<option value="IL" data-sms-possible="true">イスラエル</option>
	<option value="IT" data-sms-possible="true">イタリア</option>
	<option value="IQ" data-sms-possible="true">イラク</option>
	<option value="IR" data-sms-possible="true">イラン</option>
	<option value="IN" data-sms-possible="true">インド</option>
	<option value="ID" data-sms-possible="true">インドネシア</option>
	<option value="UG" data-sms-possible="true">ウガンダ</option>
	<option value="UA" data-sms-possible="true">ウクライナ</option>
	<option value="UZ" data-sms-possible="true">ウズベキスタン</option>
	<option value="UY" data-sms-possible="true">ウルグアイ</option>
	<option value="EC" data-sms-possible="true">エクアドル</option>
	<option value="EG" data-sms-possible="true">エジプト</option>
	<option value="EE" data-sms-possible="true">エストニア</option>
	<option value="SZ" data-sms-possible="true">エスワティニ</option>
	<option value="ET" data-sms-possible="true">エチオピア</option>
	<option value="ER" data-sms-possible="true">エリトリア</option>
	<option value="SV" data-sms-possible="true">エルサルバドル</option>
	<option value="AU" data-sms-possible="true">オーストラリア</option>
	<option value="AT" data-sms-possible="true">オーストリア</option>
	<option value="OM" data-sms-possible="true">オマーン</option>
	<option value="NL" data-sms-possible="true">オランダ</option>
	<option value="GH" data-sms-possible="true">ガーナ</option>
	<option value="CV" data-sms-possible="true">カーボベルデ</option>
	<option value="GY" data-sms-possible="true">ガイアナ</option>
	<option value="KZ" data-sms-possible="true">カザフスタン</option>
	<option value="QA" data-sms-possible="true">カタール</option>
	<option value="CA" data-sms-possible="true">カナダ/アメリカ合衆国</option>
	<option value="GA" data-sms-possible="true">ガボン</option>
	<option value="CM" data-sms-possible="true">カメルーン</option>
	<option value="KR" data-sms-possible="true">韓国</option>
	<option value="GM" data-sms-possible="true">ガンビア</option>
	<option value="KH" data-sms-possible="true">カンボジア</option>
	<option value="KP" data-sms-possible="true">北朝鮮</option>
	<option value="MK" data-sms-possible="true">北マケドニア</option>
	<option value="GN" data-sms-possible="true">ギニア</option>
	<option value="GW" data-sms-possible="true">ギニアビサウ</option>
	<option value="CY" data-sms-possible="true">キプロス</option>
	<option value="CU" data-sms-possible="true">キューバ</option>
	<option value="GR" data-sms-possible="true">ギリシャ</option>
	<option value="KI" data-sms-possible="true">キリバス</option>
	<option value="KG" data-sms-possible="true">キルギス</option>
	<option value="GT" data-sms-possible="true">グアテマラ</option>
	<option value="GU" data-sms-possible="true">グアム</option>
	<option value="KW" data-sms-possible="true">クウェート</option>
	<option value="GD" data-sms-possible="true">グレナダ</option>
	<option value="HR" data-sms-possible="true">クロアチア</option>
	<option value="KY" data-sms-possible="true">ケイマン諸島</option>
	<option value="KE" data-sms-possible="true">ケニア</option>
	<option value="CI" data-sms-possible="true">コートジボワール</option>
	<option value="CR" data-sms-possible="true">コスタリカ</option>
	<option value="KM" data-sms-possible="true">コモロ</option>
	<option value="CO" data-sms-possible="true">コロンビア</option>
	<option value="CG" data-sms-possible="true">コンゴ共和国</option>
	<option value="CD" data-sms-possible="true">コンゴ民主共和国</option>
	<option value="SA" data-sms-possible="true">サウジアラビア</option>
	<option value="ZM" data-sms-possible="true">ザンビア</option>
	<option value="SM" data-sms-possible="true">サンマリノ</option>
	<option value="SL" data-sms-possible="true">シエラレオネ</option>
	<option value="DJ" data-sms-possible="true">ジブチ</option>
	<option value="JM" data-sms-possible="true">ジャマイカ</option>
	<option value="GE" data-sms-possible="true">ジョージア</option>
	<option value="SY" data-sms-possible="true">シリア</option>
	<option value="SG" data-sms-possible="true">シンガポール</option>
	<option value="ZW" data-sms-possible="true">ジンバブエ</option>
	<option value="CH" data-sms-possible="true">スイス</option>
	<option value="SE" data-sms-possible="true">スウェーデン</option>
	<option value="SD" data-sms-possible="true">スーダン</option>
	<option value="ES" data-sms-possible="true">スペイン</option>
	<option value="SR" data-sms-possible="true">スリナム</option>
	<option value="LK" data-sms-possible="true">スリランカ</option>
	<option value="SK" data-sms-possible="true">スロバキア</option>
	<option value="SI" data-sms-possible="true">スロベニア</option>
	<option value="SC" data-sms-possible="true">セイシェル</option>
	<option value="GQ" data-sms-possible="true">赤道ギニア</option>
	<option value="SN" data-sms-possible="true">セネガル</option>
	<option value="RS" data-sms-possible="true">セルビア</option>
	<option value="KN" data-sms-possible="true">セントクリストファー・ネイビス</option>
	<option value="LC" data-sms-possible="true">セントルシア</option>
	<option value="SB" data-sms-possible="true">ソロモン諸島</option>
	<option value="TH" data-sms-possible="true">タイ</option>
	<option value="TW" data-sms-possible="true">台湾</option>
	<option value="TJ" data-sms-possible="true">タジキスタン</option>
	<option value="TZ" data-sms-possible="true">タンザニア</option>
	<option value="CZ" data-sms-possible="true">チェコ</option>
	<option value="TD" data-sms-possible="true">チャド</option>
	<option value="CF" data-sms-possible="true">中央アフリカ共和国</option>
	<option value="CN" data-sms-possible="true">中国</option>
	<option value="TN" data-sms-possible="true">チュニジア</option>
	<option value="CL" data-sms-possible="true">チリ</option>
	<option value="TV" data-sms-possible="true">ツバル</option>
	<option value="DK" data-sms-possible="true">デンマーク</option>
	<option value="DE" data-sms-possible="true">ドイツ</option>
	<option value="TG" data-sms-possible="true">トーゴ</option>
	<option value="DO" data-sms-possible="true">ドミニカ共和国</option>
	<option value="DM" data-sms-possible="true">ドミニカ国</option>
	<option value="TT" data-sms-possible="true">トリニダード・トバゴ</option>
	<option value="TM" data-sms-possible="true">トルクメニスタン</option>
	<option value="TR" data-sms-possible="true">トルコ</option>
	<option value="TO" data-sms-possible="true">トンガ</option>
	<option value="NG" data-sms-possible="true">ナイジェリア</option>
	<option value="NR" data-sms-possible="true">ナウル</option>
	<option value="NA" data-sms-possible="true">ナミビア</option>
	<option value="NI" data-sms-possible="true">ニカラグア</option>
	<option value="NE" data-sms-possible="true">ニジェール</option>
	<option value="NZ" data-sms-possible="true">ニュージーランド</option>
	<option value="NP" data-sms-possible="true">ネパール</option>
	<option value="NO" data-sms-possible="true">ノルウェー</option>
	<option value="BH" data-sms-possible="true">バーレーン</option>
	<option value="HT" data-sms-possible="true">ハイチ</option>
	<option value="PK" data-sms-possible="true">パキスタン</option>
	<option value="VA" data-sms-possible="true">バチカン</option>
	<option value="PA" data-sms-possible="true">パナマ</option>
	<option value="VU" data-sms-possible="true">バヌアツ</option>
	<option value="BS" data-sms-possible="true">バハマ</option>
	<option value="PG" data-sms-possible="true">パプアニューギニア</option>
	<option value="BM" data-sms-possible="true">バミューダ</option>
	<option value="PW" data-sms-possible="true">パラオ</option>
	<option value="PY" data-sms-possible="true">パラグアイ</option>
	<option value="BB" data-sms-possible="true">バルバドス</option>
	<option value="PS" data-sms-possible="true">パレスチナ</option>
	<option value="HU" data-sms-possible="true">ハンガリー</option>
	<option value="BD" data-sms-possible="true">バングラデシュ</option>
	<option value="TL" data-sms-possible="true">東ティモール</option>
	<option value="FJ" data-sms-possible="true">フィジー</option>
	<option value="PH" data-sms-possible="true">フィリピン</option>
	<option value="FI" data-sms-possible="true">フィンランド</option>
	<option value="BT" data-sms-possible="true">ブータン</option>
	<option value="PR" data-sms-possible="true">プエルトリコ</option>
	<option value="BR" data-sms-possible="true">ブラジル</option>
	<option value="FR" data-sms-possible="true">フランス</option>
	<option value="PF" data-sms-possible="true">フランス領ポリネシア</option>
	<option value="BG" data-sms-possible="true">ブルガリア</option>
	<option value="BF" data-sms-possible="true">ブルキナファソ</option>
	<option value="BN" data-sms-possible="true">ブルネイ</option>
	<option value="BI" data-sms-possible="true">ブルンジ</option>
	<option value="VN" data-sms-possible="true">ベトナム</option>
	<option value="BJ" data-sms-possible="true">ベナン</option>
	<option value="VE" data-sms-possible="true">ベネズエラ</option>
	<option value="BY" data-sms-possible="true">ベラルーシ</option>
	<option value="BZ" data-sms-possible="true">ベリーズ</option>
	<option value="PE" data-sms-possible="true">ペルー</option>
	<option value="BE" data-sms-possible="true">ベルギー</option>
	<option value="PL" data-sms-possible="true">ポーランド</option>
	<option value="BA" data-sms-possible="true">ボスニア・ヘルツェゴビナ</option>
	<option value="BW" data-sms-possible="true">ボツワナ</option>
	<option value="BO" data-sms-possible="true">ボリビア</option>
	<option value="PT" data-sms-possible="true">ポルトガル</option>
	<option value="HK" data-sms-possible="true">香港</option>
	<option value="HN" data-sms-possible="true">ホンジュラス</option>
	<option value="MH" data-sms-possible="true">マーシャル諸島</option>
	<option value="MO" data-sms-possible="true">マカオ</option>
	<option value="MG" data-sms-possible="true">マダガスカル</option>
	<option value="MW" data-sms-possible="true">マラウイ</option>
	<option value="ML" data-sms-possible="true">マリ</option>
	<option value="MT" data-sms-possible="true">マルタ</option>
	<option value="MY" data-sms-possible="true">マレーシア</option>
	<option value="FM" data-sms-possible="true">ミクロネシア連邦</option>
	<option value="ZA" data-sms-possible="true">南アフリカ</option>
	<option value="MM" data-sms-possible="true">ミャンマー</option>
	<option value="MX" data-sms-possible="true">メキシコ</option>
	<option value="MU" data-sms-possible="true">モーリシャス</option>
	<option value="MR" data-sms-possible="true">モーリタニア</option>
	<option value="MZ" data-sms-possible="true">モザンビーク</option>
	<option value="MC" data-sms-possible="true">モナコ</option>
	<option value="MV" data-sms-possible="true">モルディヴ</option>
	<option value="MD" data-sms-possible="true">モルドバ</option>
	<option value="MA" data-sms-possible="true">モロッコ</option>
	<option value="MN" data-sms-possible="true">モンゴル</option>
	<option value="ME" data-sms-possible="true">モンテネグロ</option>
	<option value="JO" data-sms-possible="true">ヨルダン</option>
	<option value="LA" data-sms-possible="true">ラオス人民民主共和国</option>
	<option value="LV" data-sms-possible="true">ラトビア</option>
	<option value="LT" data-sms-possible="true">リトアニア</option>
	<option value="LY" data-sms-possible="true">リビア</option>
	<option value="LI" data-sms-possible="true">リヒテンシュタイン</option>
	<option value="LR" data-sms-possible="true">リベリア</option>
	<option value="RO" data-sms-possible="true">ルーマニア</option>
	<option value="LU" data-sms-possible="true">ルクセンブルク</option>
	<option value="RW" data-sms-possible="true">ルワンダ</option>
	<option value="LS" data-sms-possible="true">レソト</option>
	<option value="LB" data-sms-possible="true">レバノン</option>
	<option value="RU" data-sms-possible="true">ロシア</option>
</select>
						</li>
						<li class="numberInputWrap">
							<span id="numberInputInfo1" class="supplementTxt marginTop">先頭に0(ゼロ)がある場合、0(ゼロ)を抜いてください(一部、国・地域除く)。携帯電話番号の場合は、携帯電話を契約された国を選択ください。</span>
							<div class="numberRowWrap">
								<div class="block">
									
									<span class="visuallyHidden">国番号</span>
									<span class="viewInput widthMini js-countryCode"></span>
								</div>
								<div class="block"><label for="representativeTel" class="visuallyHidden">電話番号</label><input id="representativeTel" type="text" name="representativeTel" value="9042504176" class="widthMiddle" placeholder="例：9012345678" maxlength="30" aria-describedby="numberInputInfo1" />
								</div>
							</div>
						</li>
					</ul>
				</dd>
			</dl>
		</fieldset>
	</div>
	<div class="titleWrapper">
		<h3>
			<span class="text">eメール・SMSに関するご案内<a href="https://www.ana.co.jp/other/int/meta/0057.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="eメール・SMSに関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a></span>
		</h3>
	</div>
	<div class="formInput">
	<h4 class="formAreaTitle">主に搭乗・運航に関わるeメールの送付先設定（米国へ通知いたします）<a href="https://www.ana.co.jp/other/int/meta/0629.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="米国入国用eメール・SMS・電話番号に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a>
	</h4>
	<div class="paxSettingArea js-mailFormWrap onePax is-open">
			<div class="settingArea">
					<dl>
						<dt class="breakWord">NAOKI SATO
						</dt>
						<dd>
							<fieldset>
								<legend>NAOKI SATOのeメール送付先および米国到着時に提供するメールアドレス</legend>代表者と同じ
							</fieldset>
						</dd>
					</dl>
			</div>
	</div>
	<h4 class="formAreaTitle hasTopLine">主に搭乗・運航に関わるSMSの送付先および米国で受信できる電話番号の設定（米国へ通知いたします）<a href="https://www.ana.co.jp/other/int/meta/0629.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="米国入国用eメール・SMS・電話番号に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a>
	</h4>
	<div class="paxSettingArea js-smsFormWrap onePax is-open">
			<div class="settingArea">
					<dl>
						<dt class="breakWord">NAOKI SATO<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span>
						</dt>
						<dd>
							<fieldset>
								<legend>NAOKI SATOの1つ目の電話番号</legend>
									<ul class="verticalList js-formItem">
										<li><input type="radio" id="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSameAsRepresentativeSms:radioItem" name="contactsSms:0:recipientSmsEachPersonSelection:radioGroup" value="0" checked="checked" /><label for="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSameAsRepresentativeSms:radioItem">代表者電話番号と同じ</label>
										</li>
										<li><input type="radio" id="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSmsPerPassenger:radioItem" name="contactsSms:0:recipientSmsEachPersonSelection:radioGroup" value="1" /><label for="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSmsPerPassenger:radioItem">電話番号を指定</label>
											<div class="indentArea js-formImputArea">
												<span id="confirmTelNumberForRepresentative:0" class="supplementTxt marginTop">先頭に0(ゼロ)がある場合、0(ゼロ)を抜いてください(一部、国・地域除く)。携帯電話番号の場合は、携帯電話を契約された国を選択ください。</span>
												<ul class="countryNumberInput js-countryWrap">
													<li><label for="contactsSms:0:recipientSmsEachPersonSelection:passengerSmsCountry" class="visuallyHidden">電話番号の国/地区選択</label><select id="contactsSms:0:recipientSmsEachPersonSelection:passengerSmsCountry" name="contactsSms:0:recipientSmsEachPersonSelection:passengerSmsCountry" class="countrySelect js-countrySelect" size="1">	<option value="" data-country-code="---">選択してください</option>
	<option value="JP">日本</option>
	<option value="-" data-country-code="---">-----------</option>
	<option value="IE">アイルランド</option>
	<option value="AZ">アゼルバイジャン</option>
	<option value="AF">アフガニスタン</option>
	<option value="US">アメリカ合衆国/カナダ</option>
	<option value="AE">アラブ首長国連邦</option>
	<option value="DZ">アルジェリア</option>
	<option value="AR">アルゼンチン</option>
	<option value="AW">アルバ</option>
	<option value="AL">アルバニア</option>
	<option value="AM">アルメニア</option>
	<option value="AG">アンティグア・バーブーダ</option>
	<option value="AD">アンドラ</option>
	<option value="YE">イエメン</option>
	<option value="GB">イギリス</option>
	<option value="IL">イスラエル</option>
	<option value="IT">イタリア</option>
	<option value="IQ">イラク</option>
	<option value="IR">イラン</option>
	<option value="IN">インド</option>
	<option value="ID">インドネシア</option>
	<option value="UG">ウガンダ</option>
	<option value="UA">ウクライナ</option>
	<option value="UZ">ウズベキスタン</option>
	<option value="UY">ウルグアイ</option>
	<option value="EC">エクアドル</option>
	<option value="EG">エジプト</option>
	<option value="EE">エストニア</option>
	<option value="ER">エリトリア</option>
	<option value="SV">エルサルバドル</option>
	<option value="AU">オーストラリア</option>
	<option value="AT">オーストリア</option>
	<option value="OM">オマーン</option>
	<option value="NL">オランダ</option>
	<option value="GH">ガーナ</option>
	<option value="GY">ガイアナ</option>
	<option value="KZ">カザフスタン</option>
	<option value="QA">カタール</option>
	<option value="CA">カナダ/アメリカ合衆国</option>
	<option value="GA">ガボン</option>
	<option value="CM">カメルーン</option>
	<option value="KR">韓国</option>
	<option value="GM">ガンビア</option>
	<option value="KH">カンボジア</option>
	<option value="GN">ギニア</option>
	<option value="GW">ギニアビサウ</option>
	<option value="CY">キプロス</option>
	<option value="CU">キューバ</option>
	<option value="GR">ギリシャ</option>
	<option value="KG">キルギス</option>
	<option value="GT">グアテマラ</option>
	<option value="GU">グアム</option>
	<option value="KW">クウェート</option>
	<option value="GD">グレナダ</option>
	<option value="HR">クロアチア</option>
	<option value="KY">ケイマン諸島</option>
	<option value="KE">ケニア</option>
	<option value="CI">コートジボワール</option>
	<option value="CR">コスタリカ</option>
	<option value="KM">コモロ</option>
	<option value="CO">コロンビア</option>
	<option value="CD">コンゴ民主共和国</option>
	<option value="SA">サウジアラビア</option>
	<option value="ZM">ザンビア</option>
	<option value="SL">シエラレオネ</option>
	<option value="JM">ジャマイカ</option>
	<option value="GE">ジョージア</option>
	<option value="SY">シリア</option>
	<option value="SG">シンガポール</option>
	<option value="ZW">ジンバブエ</option>
	<option value="CH">スイス</option>
	<option value="SE">スウェーデン</option>
	<option value="SD">スーダン</option>
	<option value="ES">スペイン</option>
	<option value="SR">スリナム</option>
	<option value="LK">スリランカ</option>
	<option value="SK">スロバキア</option>
	<option value="SI">スロベニア</option>
	<option value="SN">セネガル</option>
	<option value="RS">セルビア</option>
	<option value="KN">セントクリストファー・ネイビス</option>
	<option value="LC">セントルシア</option>
	<option value="TH">タイ</option>
	<option value="TW">台湾</option>
	<option value="TJ">タジキスタン</option>
	<option value="TZ">タンザニア</option>
	<option value="CZ">チェコ</option>
	<option value="TD">チャド</option>
	<option value="CF">中央アフリカ共和国</option>
	<option value="CN">中国</option>
	<option value="TN">チュニジア</option>
	<option value="CL">チリ</option>
	<option value="DK">デンマーク</option>
	<option value="DE">ドイツ</option>
	<option value="TG">トーゴ</option>
	<option value="DO">ドミニカ共和国</option>
	<option value="TT">トリニダード・トバゴ</option>
	<option value="TR">トルコ</option>
	<option value="NG">ナイジェリア</option>
	<option value="NI">ニカラグア</option>
	<option value="NE">ニジェール</option>
	<option value="NZ">ニュージーランド</option>
	<option value="NP">ネパール</option>
	<option value="NO">ノルウェー</option>
	<option value="HT">ハイチ</option>
	<option value="PK">パキスタン</option>
	<option value="PA">パナマ</option>
	<option value="BS">バハマ</option>
	<option value="PG">パプアニューギニア</option>
	<option value="BM">バミューダ</option>
	<option value="PW">パラオ</option>
	<option value="PY">パラグアイ</option>
	<option value="BB">バルバドス</option>
	<option value="PS">パレスチナ</option>
	<option value="HU">ハンガリー</option>
	<option value="BD">バングラデシュ</option>
	<option value="TL">東ティモール</option>
	<option value="FJ">フィジー</option>
	<option value="PH">フィリピン</option>
	<option value="FI">フィンランド</option>
	<option value="BT">ブータン</option>
	<option value="PR">プエルトリコ</option>
	<option value="BR">ブラジル</option>
	<option value="FR">フランス</option>
	<option value="PF">フランス領ポリネシア</option>
	<option value="BG">ブルガリア</option>
	<option value="BF">ブルキナファソ</option>
	<option value="BN">ブルネイ</option>
	<option value="BI">ブルンジ</option>
	<option value="VN">ベトナム</option>
	<option value="BJ">ベナン</option>
	<option value="VE">ベネズエラ</option>
	<option value="BY">ベラルーシ</option>
	<option value="PE">ペルー</option>
	<option value="BE">ベルギー</option>
	<option value="PL">ポーランド</option>
	<option value="BA">ボスニア・ヘルツェゴビナ</option>
	<option value="BW">ボツワナ</option>
	<option value="HK">香港</option>
	<option value="MH">マーシャル諸島</option>
	<option value="MO">マカオ</option>
	<option value="MG">マダガスカル</option>
	<option value="MW">マラウイ</option>
	<option value="ML">マリ</option>
	<option value="MT">マルタ</option>
	<option value="MY">マレーシア</option>
	<option value="ZA">南アフリカ</option>
	<option value="MM">ミャンマー</option>
	<option value="MX">メキシコ</option>
	<option value="MU">モーリシャス</option>
	<option value="MR">モーリタニア</option>
	<option value="MZ">モザンビーク</option>
	<option value="MC">モナコ</option>
	<option value="MV">モルディヴ</option>
	<option value="MA">モロッコ</option>
	<option value="MN">モンゴル</option>
	<option value="ME">モンテネグロ</option>
	<option value="JO">ヨルダン</option>
	<option value="LA">ラオス人民民主共和国</option>
	<option value="LV">ラトビア</option>
	<option value="LT">リトアニア</option>
	<option value="LY">リビア</option>
	<option value="LR">リベリア</option>
	<option value="RO">ルーマニア</option>
	<option value="LU">ルクセンブルク</option>
	<option value="RW">ルワンダ</option>
	<option value="LB">レバノン</option>
	<option value="RU">ロシア</option>
</select>
													</li>
													<li>
														
														<div class="numberRowWrap">
															<div class="block">
																<span class="visuallyHidden">国番号</span>
																<span class="countryCode viewInput js-countryCode"></span>
															</div>
															
															<div class="block"><label for="contactsSms:0:recipientSmsEachPersonSelection:flightStatusNotificationContactPointSmsDescription" class="visuallyHidden">電話番号</label><input id="contactsSms:0:recipientSmsEachPersonSelection:flightStatusNotificationContactPointSmsDescription" type="text" name="contactsSms:0:recipientSmsEachPersonSelection:flightStatusNotificationContactPointSmsDescription" class="telNumber" placeholder="例：9012345678" maxlength="30" aria-describedby="confirmTelNumberForRepresentative:0" />
															</div>
														</div>
													</li>
												</ul>
											</div>
										</li>
									</ul>
							</fieldset>
						</dd>
					</dl>
			</div>
	</div>
	</div>
	
	<div class="formInput articleLine">
		<h3>滞在中の連絡先(任意)
		</h3>
		<dl>
			
			<dt><label for="accommodationOne">現地連絡先1</label>
			</dt>
			<dd><input id="accommodationOne" type="text" name="accommodationOne" class="widthLarge" placeholder="例：09012345678 ABC HOTEL" maxlength="28" title="例：09012345678 ABC HOTEL" />
			</dd>
		</dl>
		<dl>
			
			<dt><label for="accommodationTwo">現地連絡先2</label>
			</dt>
			<dd><input id="accommodationTwo" type="text" name="accommodationTwo" class="widthLarge" placeholder="例：09012345678 ABC HOTEL" maxlength="28" title="例：09012345678 ABC HOTEL" />
			</dd>
		</dl>
	</div>
		</div>
		
		<p class="btnAreaSubmit btnArrowNext "><input id="next" type="submit" name="next" value="次へ" class="btnBase btnMainStream btnVerticalMain btnWidthVariable" onclick="return Asw.Dialog.getInstance('prebookConfirmDialog').toggle(event);return Asw.LoadingWindow.open(&quot;NORMAL&quot;, event)" />
		</p><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:2" value="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" autocomplete="off" />
</form><div id="cmnDynamicMessages" class="infoBoxSection">
				<dl class="infoBox importantInfo jsAccordionSwitch">
					<dt><strong>重要なご案内</strong></dt>
					<dd>
						<ul class="jsAccordionSwitchList" id="informationMessages">
								<li>個人情報の利用目的<a href="https://www.ana.co.jp/other/int/meta/0012.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="ANAプライバシーポリシー(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">ANAプライバシーポリシー<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>を必ずお読みいただき、内容に同意いただいた上で、お進みください。</li>
								<li><a href="https://www.ana.co.jp/other/int/meta/0058.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="Secure Flight Programについて(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">米国Secure Flightプログラム<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>に基づき、パスポート名、生年月日、性別などの情報は事前に米国へ通知いたします。</li>
								<li>入力したeメールアドレス、電話番号は入国に必要な情報として米国に通知される可能性があります。</li>
									<li class="jsHiddenFlg toggleContents">お名前のスペルがパスポート名と一致しているかご確認ください。異なる場合はインターネットではお取り扱いできません。<a href="https://www.ana.co.jp/other/int/meta/intbe0001.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="ANAお問い合わせ先(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">ANA<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>へ名前の変更をお申し出ください。</li>
									<li class="jsHiddenFlg toggleContents">二重国籍のお客様は、ご搭乗時に使用されるパスポートのお名前でご予約ください。<br />パスポートごとにお名前が異なる場合は、お客様ご自身で入国管理事務所・外務省・大使館などで各種条件をご確認のうえ、使用されるパスポートを決めてください。<br />なお、入国可否については国によって異なるため、お客様ご自身で各入国管理事務所などでご確認ください。</li>
									<li class="jsHiddenFlg toggleContents">旅行開始日時点で12歳以上の場合は大人、2歳以上12歳未満の場合は小児、2歳未満の場合は幼児の扱いとなります。なお、小児は大人と同マイル数が必要です。</li>
									<li class="jsHiddenFlg toggleContents">運航に関する情報をお客様へ通知するため、提携航空会社にもお客様のメールアドレスが提供されます。あらかじめご了承ください。</li>
									<li class="jsHiddenFlg toggleContents"><dl><dt>携帯電話へのメール送信をご希望のお客様へ</dt><dd>ドメイン指定受信をご利用の場合は、ana.co.jpとamadeus.comからのメールが受信できるようあらかじめ設定をお願いいたします。</dd></dl></li>
									<li class="jsHiddenFlg toggleContents">SMSはスマートフォンの番号に配信されます。一部の国ではSMSでのご案内をご利用いただけません。詳細は<a href="https://www.ana.co.jp/other/int/meta/0737.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="ショートメッセージ(SMS)でのご案内対象外の国について(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">ショートメッセージ(SMS)でのご案内対象外の国について<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>をご覧ください。</li>
									<li class="jsHiddenFlg toggleContents">ログインされている会員ご本人が搭乗しない場合は、再度フライト検索画面より「ログインされている会員ご本人は搭乗しない」のチェックボックスを選択してお手続きください。</li>
						</ul>
							<a href="#" class="toggleSwitch jsTriggerSwitch" role="button" aria-controls="informationMessages" aria-expanded="false"><img src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/image/toggle_open.png?7bec38a" alt="開閉ボタン" height="25" width="25" class="jsRollOver" />
							</a>
					</dd>
				</dl></div>
	<!--=========== /main =============-->
	</div>

	<!--=========== Script ============-->
	<script type="text/javascript">
	$(window).on('load', function() {
		// アコーディオンのメソッドと設定
		Asw.AccordionInfo('.jsAccordionSwitch', {
			'triggerSwitch': '.jsAccordionSwitch .jsTriggerSwitch',
			'listClass': '.jsAccordionSwitchList',
			'visibilityHidden': 'toggleContents'
		});
	});
	</script>
	<!--=========== Script ============-->
	<script type="text/javascript">
		//プラットフォーム判別（サマリーエリア追従用として）
		var Asw = Asw || {};
	
	  	Asw.ClientInfo = Asw.ClientInfo  || {};
	  	Asw.ClientInfo.DeviceType = Asw.ClientInfo.DeviceType || {};
	
	  	Asw.ClientInfo.DeviceType.PC = 'PC';
	  	Asw.ClientInfo.DeviceType.SP = 'SP';
	  	Asw.ClientInfo.DeviceType.TC = 'TC';
	
	  	Asw.ClientInfo.deviceType = Asw.ClientInfo.DeviceType.PC;
	
	 	if((navigator.userAgent.indexOf('Android') > 0 && navigator.userAgent.indexOf('Mobile') == -1) || navigator.userAgent.indexOf('iPad') > 0){
	    	Asw.ClientInfo.deviceType = Asw.ClientInfo.DeviceType.TC;
	  	}
	</script>
	<!--=========== /Script ============--><div id="summaryArea">
			<div class="flightSummaryArea">
						<div class="flightSummarySection">
							<h2>区間 1
							</h2>
							<p class="flightDate"><em>6</em>月<em class="hasLeft">28</em>日（土）
							</p>
							<div class="flightSummaryRoute">
									<div class="flightSummaryRouteSection">
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation1_1">
													<span>東京(成田)
													</span>
												</p>
												<p class="time">
													<span>17:00
													</span>
												</p>
											</div>
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation1_1">
													<span>シカゴ(ORD)
													</span>
												</p>
												<p class="time">
													<span>14:55
													</span>
												</p>
											</div>
										<p class="flightNumberClass">
												<span>NH012</span><span><img src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/image/flight_ana_summaryarea.png?717d3c0" alt="ANA運航便" height="11" width="41" /></span>
												<br />特典ビジネス
										</p>
									</div>
							</div>
						</div>
						<div class="flightSummarySection">
							<h2 class="midstream">区間 2
							</h2>
							<p class="flightDate"><em>7</em>月<em class="hasLeft">23</em>日（水）
							</p>
							<div class="flightSummaryRoute">
									<div class="flightSummaryRouteSection">
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation2_1">
													<span>ホノルル(オアフ島)
													</span>
												</p>
												<p class="time">
													<span>11:35
													</span>
												</p>
											</div>
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation2_1">
													<span>東京(成田)
													</span>
												</p>
												<p class="time">
													<span>14:50
																	<span>翌日
																	</span>
													</span>
												</p>
											</div>
										<p class="flightNumberClass">
												<span>NH183</span><span><img src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/image/flight_ana_summaryarea.png?717d3c0" alt="ANA運航便" height="11" width="41" /></span>
												<br />特典エコノミー
										</p>
									</div>
							</div>
						</div>
					<div class="total">
						<p class="passenger">大人 x1
						</p>
						<div class="totalArea">
					  		<dl class="mealAmountWrap">
									<dt>有料機内食サービス
									</dt>
									<dd><em class="mealAmount">0</em><span class="currencyCode">円</span>
									</dd>
							</dl>
									<div>
										<div class="mileageAndTotal">
												<dl class="requiredMileage">
													<dt>必要マイル
													</dt>
													<dd><em class="requiredMileage">74,000</em><span class="currencyCode">マイル</span>
													</dd>
												</dl>
												<dl>
													<dt>総額
													</dt>
													<dd><em class="awardTotalPayment">68,130</em>円
													</dd>
												</dl>
												<p class="includeFareAndFuelCost">※各種税金、燃油特別付加運賃等を含みます。
												</p>
										</div>
									</div>
						</div>
					</div>
				
			</div></div>
		<!--===== /cmnWrapper =====-->
		</div>
			
		<span id="message" class="visuallyHidden" role="alert" aria-live="assertive"></span>
		<span id="politeMessage" class="visuallyHidden" role="log" aria-live="polite"></span><script type="text/javascript">
		var Asw = Asw || {};
		Asw.BaseOutput = {"sessionID":"KzaLiAi_Zt","language":"ja","siteCatalystLanguageCode":"J","operationDate":"20250620","operationDateTime":"20250620131050","office":{"officeCode":"TYONH08ZZ","country2letterCode":"JP"},"bookingType":"A","pageID":"A04_P01","siteCatalystPageName":"INT_BE_AWARD_J_A04特典予約_P01旅客情報入力（必須情報）","device":{"deviceType":"PC"}};
	</script><div id="promotionArea">
		<!--===== promotionArea =====-->
		<!--===== /promotionArea =====--></div>
		<!--=========== /cmnContainer ============--></div><div id="cmnBaloonArea" aria-live="polite"></div><div id="cmnPopupArea" aria-live="polite"></div>

	

	<div id="cmnFooterWrapper">
		<!--=========== .cmnFooterWrapper ============-->
		<div id="cmnFooter">
			<!--===== cmnFooter =====-->
			<ul>
				<li><a href="https://www.ana.co.jp/other/int/meta/0012.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">プライバシーポリシー<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
					<li><a href="https://www.ana.co.jp/other/int/meta/0670.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">利用者情報の外部送信について<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
					</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/0052.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">サイト利用規約<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/be0238.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">推奨環境<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/0133.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">サイトマップ<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/0732.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">運送約款<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
					<li class="lastChild"><a href="https://www.ana.co.jp/other/int/meta/0556.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">ウェブアクセシビリティについて<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
					</li>
			</ul>
			<p class="blankText" aria-hidden="true"><img src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="" height="10" width="10" />外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。</p>
			<p class="copyright"><span lang="en" xml:lang="en">Copyright&#169;ANA</span>
			</p>
			<p class="starAllianceLogo"><a href="http://www.staralliance.com/ja/" target="_blank"><img src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/image/star_alliance_logo.png?0fe73b5" alt="A STAR ALLIANCE MEMBER" height="18" width="175" /><span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
			</p>
			<!--===== cmnFooter =====-->
		</div>
		<!--=========== /cmnFooterWrapper ============-->
	</div>

		
		<div id="cmnLoadingForAjax"><img src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/image/bg_modalWindow.png?717d3c0" alt="" height="0" width="0" />
			<div class="loadingArea">
				<p class="loadingImage"><img src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/image/loading.gif?717d3c0" alt="リクエストを処理しています...しばらくお待ちください。" height="33" width="33" />
				</p>
			</div>
		</div>
		
		
		<div id="popupAltSentence" style="display: none" aria-hidden="true">別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。</div>
	<div id="cmnSiteCatalyst" aria-hidden="true"><div id="cmnSiteCatalystParamArea"><script type="text/javascript">
				// ▼SiteCatalyst
				// SiteCatalyst code version: H.2.
				// Copyright 1997-2005 Omniture, Inc. More info available at http://www.omniture.com
				SiteCatalystReportSuites   = "INT";
				SiteCatalystCharSet        = "UTF-8";
				SiteCatalystChannel        = "BE_AWARD_J";
				SiteCatalystPageName       = "INT_BE_AWARD_J_A04\u7279\u5178\u4E88\u7D04_P01\u65C5\u5BA2\u60C5\u5831\u5165\u529B\uFF08\u5FC5\u9808\u60C5\u5831\uFF09";
				SiteCatalystERR            = "";
				SiteCatalystEventNum       = "";
				SiteCatalystPurchase       = "FALSE";
				SiteCatalystProductPrice   = "";
				SiteCatalystCurrencyCode   = "";
				SiteCatalystSeatAvailabilityINT   = "";
				SiteCatalystDepartDate     = "";
				SiteCatalystDepart         = "";
				SiteCatalystArrive         = "";
				SiteCatalystFlightNo       = "";
				SiteCatalystClass          = "";
				SiteCatalystFare           = "";
				SiteCatalystPurchaseID     = "";
				
				SiteCatalystLocater        = "";
				SiteCatalystPayment        = "";
				SiteCatalystIncome         = "";
				
				SiteCatalystPax            = "";
				SiteCatalystManipulateDay  = "";
				SiteCatalystCompartmentClass   = "";
				SiteCatalystOptionInfo     = "";
				SiteCatalystOutboundUpsellPrice   = "";
				SiteCatalystInboundUpsellPrice    = "";
				
				SiteCatalystSearchInput    = "";
				SiteCatalystSegment        = "";
				SiteCatalystOfficeCode     = "";
				SiteCatalystPassengerCount = "";
				SiteCatalystPassenger      = "";
				SiteCatalystAncillaryService = "";
				// End SiteCatalyst code version: H.2.
				// ▲SiteCatalyst
			</script></div><script type="text/javascript"><!-- if(navigator.appVersion.indexOf('MSIE')>=0)document.write(unescape('%3C')+'\!-'+'-') // --></script>
		<!--/DO NOT REMOVE/-->
		

		
		<!-- Google Tag Manager -->
		<script type="text/javascript" charset="Shift_JIS" src="https://www.ana.co.jp/common/js/GTMINTBE.js"></script>
		<!-- End Google Tag Manager -->
		<!-- Yahoo Tag Manager -->
		<script type="text/javascript" charset="Shift_JIS" src="https://www.ana.co.jp/common/js/YTMINTBE.js"></script>
		<!-- End Yahoo Tag Manager -->
		

	</div>

	

	<div class="mboxDefault"></div><script type="text/javascript">mboxCreate("ASW_common_A04_P01");</script>
			<script type="text/javascript" src="https://www.ana.co.jp/common/js/tealium/tealium.js"></script>
			<script type="text/javascript" src="//cdn.evgnet.com/beacon/allnipponairways/nonamcprd/scripts/evergage.min.js"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21e/international_asw/javax.faces.resource/jsf.js.xhtml?ln=javax.faces"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/script/jquery.mousewheel.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/script/jquery.jscrollpane.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/script/jquery-ui-1.10.4.custom.min.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/script/jquery.ui.touch-punch.min.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/script/asw-common.js?beb6a50"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/script/asw-common-pc.js?fd88d7d"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/script/asw-format.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/script/mask.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/script/loading.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/script/asw-loadingwindow-pc.js?9e233c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/script/asw-loadingwindow.js?51db600"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/script/dialog-pc.js?25d36a4"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/script/asw-dialog-sequencer.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/script/asw-event.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/script/asw-date-select.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/script/asw-calendar-pc.js?9b86b8a"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/script/asw-calendar.js?db624c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/script/heightLine.js?0e7a1ce"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/script/asw-summaryarea.js?b21c6d7"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/script/asw-fingerprints.js?99001fc"></script><script type="text/javascript">
			Asw.init("https://aswbe-i.ana.co.jp/rei21e/international_asw", "ja");
			Asw.getCalendarHolidayList = function() {return [20250721,20250811,20250915,20250923,20251013,20251103,20251123,20251124,20260101,20260112,20260211,20260223,20260320,20260429,20260503,20260504,20260505,20260506,20260720,20260811,20260921,20260922,20260923,20261012,20261103,20261123,20270101,20270111,20270211,20270223,20270321,20270322,20270429,20270503,20270504,20270505,20270719,20270811,20270920,20270923,20271011,20271103,20271123,20280101,20280110,20280211,20280223,20280320,20280429,20280503,20280504,20280505,20280717,20280811,20280918,20280922,20281009,20281103,20281123,20290101,20290108,20290211,20290212,20290223,20290320,20290429,20290430,20290503,20290504,20290505,20290716,20290811,20290917,20290923,20290924,20291008,20291103,20291123];}
			Asw.ClientInfo.deviceType = "PC";
			Asw.ClientInfo.mobileDeviceType = "UNKNOWN";
			Asw.ClientInfo.osVersion = "";
			Asw.Format.init("https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/{1}/{2}/{3}?{0}");
			Asw.overwriteTargetAttributeValue("false");
		</script><script type="text/javascript">
		
		var Asw = Asw || {};
		Asw.ContactInput = Asw.ContactInput || {};
		Asw.ContactInput.Const = {
			/** data-属性名「data-type」 @constant @type {String} @default 'data-type' */
			DATA_ATTRIBUTE_NAME_TYPE: 'data-type',
			/** data-属性名「data-sms-possible」 @constant @type {String} @default 'data-sms-possible' */
			DATA_ATTRIBUTE_NAME_SMS_POSSIBLE: 'data-sms-possible',
			/** data-属性名「data-country-code」 @constant @type {String} @default 'data-country-code' */
			DATA_ATTRIBUTE_NAME_COUNTRY_CODE: 'data-country-code',
			/** data-type属性値「sms」 @constant @type {String} @default 'sms' */
			DATA_TYPE_VALUE_SMS: 'sms'
		};

		
		Asw.ContactInput.countryInfoMap = {"IS":{"internationalCallingCode":"+354","smsPossible":false},"IE":{"internationalCallingCode":"+353","smsPossible":true},"AZ":{"internationalCallingCode":"+994","smsPossible":true},"AF":{"internationalCallingCode":"+93","smsPossible":true},"US":{"internationalCallingCode":"+1","smsPossible":true},"AE":{"internationalCallingCode":"+971","smsPossible":true},"DZ":{"internationalCallingCode":"+213","smsPossible":true},"AR":{"internationalCallingCode":"+54","smsPossible":true},"AW":{"internationalCallingCode":"+297","smsPossible":true},"AL":{"internationalCallingCode":"+355","smsPossible":true},"AM":{"internationalCallingCode":"+374","smsPossible":true},"AO":{"internationalCallingCode":"+244","smsPossible":false},"AG":{"internationalCallingCode":"+1","smsPossible":true},"AD":{"internationalCallingCode":"+376","smsPossible":true},"YE":{"internationalCallingCode":"+967","smsPossible":true},"GB":{"internationalCallingCode":"+44","smsPossible":true},"IL":{"internationalCallingCode":"+972","smsPossible":true},"IT":{"internationalCallingCode":"+39","smsPossible":true},"IQ":{"internationalCallingCode":"+964","smsPossible":true},"IR":{"internationalCallingCode":"+98","smsPossible":true},"IN":{"internationalCallingCode":"+91","smsPossible":true},"ID":{"internationalCallingCode":"+62","smsPossible":true},"UG":{"internationalCallingCode":"+256","smsPossible":true},"UA":{"internationalCallingCode":"+380","smsPossible":true},"UZ":{"internationalCallingCode":"+998","smsPossible":true},"UY":{"internationalCallingCode":"+598","smsPossible":true},"EC":{"internationalCallingCode":"+593","smsPossible":true},"EG":{"internationalCallingCode":"+20","smsPossible":true},"EE":{"internationalCallingCode":"+372","smsPossible":true},"SZ":{"internationalCallingCode":"+268","smsPossible":false},"ET":{"internationalCallingCode":"+251","smsPossible":false},"ER":{"internationalCallingCode":"+291","smsPossible":true},"SV":{"internationalCallingCode":"+503","smsPossible":true},"AU":{"internationalCallingCode":"+61","smsPossible":true},"AT":{"internationalCallingCode":"+43","smsPossible":true},"OM":{"internationalCallingCode":"+968","smsPossible":true},"NL":{"internationalCallingCode":"+31","smsPossible":true},"GH":{"internationalCallingCode":"+233","smsPossible":true},"CV":{"internationalCallingCode":"+238","smsPossible":false},"GY":{"internationalCallingCode":"+592","smsPossible":true},"KZ":{"internationalCallingCode":"+7","smsPossible":true},"QA":{"internationalCallingCode":"+974","smsPossible":true},"CA":{"internationalCallingCode":"+1","smsPossible":true},"GA":{"internationalCallingCode":"+241","smsPossible":true},"CM":{"internationalCallingCode":"+237","smsPossible":true},"KR":{"internationalCallingCode":"+82","smsPossible":true},"KH":{"internationalCallingCode":"+855","smsPossible":true},"GM":{"internationalCallingCode":"+220","smsPossible":true},"KP":{"internationalCallingCode":"+850","smsPossible":false},"MK":{"internationalCallingCode":"+389","smsPossible":false},"GN":{"internationalCallingCode":"+224","smsPossible":true},"GW":{"internationalCallingCode":"+245","smsPossible":true},"CY":{"internationalCallingCode":"+357","smsPossible":true},"CU":{"internationalCallingCode":"+53","smsPossible":true},"GR":{"internationalCallingCode":"+30","smsPossible":true},"KI":{"internationalCallingCode":"+686","smsPossible":false},"KG":{"internationalCallingCode":"+996","smsPossible":true},"GT":{"internationalCallingCode":"+502","smsPossible":true},"GU":{"internationalCallingCode":"+1","smsPossible":true},"KW":{"internationalCallingCode":"+965","smsPossible":true},"KY":{"internationalCallingCode":"+1","smsPossible":true},"GD":{"internationalCallingCode":"+1","smsPossible":true},"HR":{"internationalCallingCode":"+385","smsPossible":true},"KE":{"internationalCallingCode":"+254","smsPossible":true},"CI":{"internationalCallingCode":"+225","smsPossible":true},"CR":{"internationalCallingCode":"+506","smsPossible":true},"SA":{"internationalCallingCode":"+966","smsPossible":true},"KM":{"internationalCallingCode":"+269","smsPossible":true},"CO":{"internationalCallingCode":"+57","smsPossible":true},"CG":{"internationalCallingCode":"+242","smsPossible":false},"CD":{"internationalCallingCode":"+243","smsPossible":true},"ZM":{"internationalCallingCode":"+260","smsPossible":true},"SM":{"internationalCallingCode":"+378","smsPossible":false},"SL":{"internationalCallingCode":"+232","smsPossible":true},"DJ":{"internationalCallingCode":"+253","smsPossible":false},"JM":{"internationalCallingCode":"+1","smsPossible":true},"CH":{"internationalCallingCode":"+41","smsPossible":true},"GE":{"internationalCallingCode":"+995","smsPossible":true},"SE":{"internationalCallingCode":"+46","smsPossible":true},"SY":{"internationalCallingCode":"+963","smsPossible":true},"ES":{"internationalCallingCode":"+34","smsPossible":true},"SG":{"internationalCallingCode":"+65","smsPossible":true},"LK":{"internationalCallingCode":"+94","smsPossible":true},"SK":{"internationalCallingCode":"+421","smsPossible":true},"SI":{"internationalCallingCode":"+386","smsPossible":true},"ZW":{"internationalCallingCode":"+263","smsPossible":true},"SD":{"internationalCallingCode":"+249","smsPossible":true},"KN":{"internationalCallingCode":"+1","smsPossible":true},"SR":{"internationalCallingCode":"+597","smsPossible":true},"LC":{"internationalCallingCode":"+1","smsPossible":true},"SC":{"internationalCallingCode":"+248","smsPossible":false},"SN":{"internationalCallingCode":"+221","smsPossible":true},"SB":{"internationalCallingCode":"+677","smsPossible":false},"RS":{"internationalCallingCode":"+381","smsPossible":true},"TH":{"internationalCallingCode":"+66","smsPossible":true},"TW":{"internationalCallingCode":"+886","smsPossible":true},"TJ":{"internationalCallingCode":"+992","smsPossible":true},"TZ":{"internationalCallingCode":"+255","smsPossible":true},"CZ":{"internationalCallingCode":"+420","smsPossible":true},"TD":{"internationalCallingCode":"+235","smsPossible":true},"CF":{"internationalCallingCode":"+236","smsPossible":true},"TN":{"internationalCallingCode":"+216","smsPossible":true},"CL":{"internationalCallingCode":"+56","smsPossible":true},"TV":{"internationalCallingCode":"+688","smsPossible":false},"DK":{"internationalCallingCode":"+45","smsPossible":true},"DE":{"internationalCallingCode":"+49","smsPossible":true},"TG":{"internationalCallingCode":"+228","smsPossible":true},"DO":{"internationalCallingCode":"+1","smsPossible":true},"DM":{"internationalCallingCode":"+1","smsPossible":false},"TT":{"internationalCallingCode":"+1","smsPossible":true},"TM":{"internationalCallingCode":"+993","smsPossible":false},"TR":{"internationalCallingCode":"+90","smsPossible":true},"TO":{"internationalCallingCode":"+676","smsPossible":false},"NG":{"internationalCallingCode":"+234","smsPossible":true},"NR":{"internationalCallingCode":"+674","smsPossible":false},"NA":{"internationalCallingCode":"+264","smsPossible":false},"NI":{"internationalCallingCode":"+505","smsPossible":true},"BH":{"internationalCallingCode":"+973","smsPossible":false},"NE":{"internationalCallingCode":"+227","smsPossible":true},"PK":{"internationalCallingCode":"+92","smsPossible":true},"JP":{"internationalCallingCode":"+81","smsPossible":true},"BS":{"internationalCallingCode":"+1","smsPossible":true},"NZ":{"internationalCallingCode":"+64","smsPossible":true},"BM":{"internationalCallingCode":"+1","smsPossible":true},"NP":{"internationalCallingCode":"+977","smsPossible":true},"BB":{"internationalCallingCode":"+246","smsPossible":true},"NO":{"internationalCallingCode":"+47","smsPossible":true},"HT":{"internationalCallingCode":"+509","smsPossible":true},"BD":{"internationalCallingCode":"+880","smsPossible":true},"VA":{"internationalCallingCode":"+39","smsPossible":false},"PA":{"internationalCallingCode":"+507","smsPossible":true},"VU":{"internationalCallingCode":"+678","smsPossible":false},"PG":{"internationalCallingCode":"+675","smsPossible":true},"PW":{"internationalCallingCode":"+680","smsPossible":true},"PY":{"internationalCallingCode":"+595","smsPossible":true},"PS":{"internationalCallingCode":"+970","smsPossible":true},"BR":{"internationalCallingCode":"+55","smsPossible":true},"HU":{"internationalCallingCode":"+36","smsPossible":true},"TL":{"internationalCallingCode":"+670","smsPossible":true},"BG":{"internationalCallingCode":"+359","smsPossible":true},"BF":{"internationalCallingCode":"+226","smsPossible":true},"BN":{"internationalCallingCode":"+673","smsPossible":true},"FJ":{"internationalCallingCode":"+679","smsPossible":true},"PH":{"internationalCallingCode":"+63","smsPossible":true},"FI":{"internationalCallingCode":"+358","smsPossible":true},"BT":{"internationalCallingCode":"+975","smsPossible":true},"BY":{"internationalCallingCode":"+375","smsPossible":true},"BZ":{"internationalCallingCode":"+501","smsPossible":false},"PR":{"internationalCallingCode":"+1","smsPossible":true},"BE":{"internationalCallingCode":"+32","smsPossible":true},"BA":{"internationalCallingCode":"+387","smsPossible":true},"FR":{"internationalCallingCode":"+33","smsPossible":true},"BO":{"internationalCallingCode":"+591","smsPossible":false},"PF":{"internationalCallingCode":"+689","smsPossible":true},"BI":{"internationalCallingCode":"+257","smsPossible":true},"VN":{"internationalCallingCode":"+84","smsPossible":true},"BJ":{"internationalCallingCode":"+229","smsPossible":true},"VE":{"internationalCallingCode":"+58","smsPossible":true},"PE":{"internationalCallingCode":"+51","smsPossible":true},"PL":{"internationalCallingCode":"+48","smsPossible":true},"BW":{"internationalCallingCode":"+267","smsPossible":true},"PT":{"internationalCallingCode":"+351","smsPossible":false},"HK":{"internationalCallingCode":"+852","smsPossible":true},"HN":{"internationalCallingCode":"+504","smsPossible":false},"MH":{"internationalCallingCode":"+692","smsPossible":true},"MO":{"internationalCallingCode":"+853","smsPossible":true},"MG":{"internationalCallingCode":"+261","smsPossible":true},"MW":{"internationalCallingCode":"+265","smsPossible":true},"ML":{"internationalCallingCode":"+223","smsPossible":true},"MT":{"internationalCallingCode":"+356","smsPossible":true},"MY":{"internationalCallingCode":"+60","smsPossible":true},"FM":{"internationalCallingCode":"+691","smsPossible":false},"MM":{"internationalCallingCode":"+95","smsPossible":true},"MX":{"internationalCallingCode":"+52","smsPossible":true},"MU":{"internationalCallingCode":"+230","smsPossible":true},"MR":{"internationalCallingCode":"+222","smsPossible":true},"MZ":{"internationalCallingCode":"+258","smsPossible":true},"MC":{"internationalCallingCode":"+377","smsPossible":true},"MV":{"internationalCallingCode":"+960","smsPossible":true},"MD":{"internationalCallingCode":"+373","smsPossible":false},"RO":{"internationalCallingCode":"+40","smsPossible":true},"MA":{"internationalCallingCode":"+212","smsPossible":true},"MN":{"internationalCallingCode":"+976","smsPossible":true},"ME":{"internationalCallingCode":"+382","smsPossible":true},"JO":{"internationalCallingCode":"+962","smsPossible":true},"LA":{"internationalCallingCode":"+856","smsPossible":true},"RU":{"internationalCallingCode":"+7","smsPossible":true},"LV":{"internationalCallingCode":"+371","smsPossible":true},"LT":{"internationalCallingCode":"+370","smsPossible":true},"LY":{"internationalCallingCode":"+218","smsPossible":true},"GQ":{"internationalCallingCode":"+240","smsPossible":false},"LI":{"internationalCallingCode":"+423","smsPossible":false},"LR":{"internationalCallingCode":"+231","smsPossible":true},"CN":{"internationalCallingCode":"+86","smsPossible":true},"LU":{"internationalCallingCode":"+352","smsPossible":true},"ZA":{"internationalCallingCode":"+27","smsPossible":true},"RW":{"internationalCallingCode":"+250","smsPossible":true},"LS":{"internationalCallingCode":"+266","smsPossible":false},"LB":{"internationalCallingCode":"+961","smsPossible":true}};

		$(function () {
			contactInfoInit();
		});
		
		function contactInfoInit() {
			
			
			Asw.get('representativeTelType').find('option[value="M1"]').attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_TYPE,
					Asw.ContactInput.Const.DATA_TYPE_VALUE_SMS);
			Asw.get('representativeTelCountry').children('option').each(function(i,option){
				var countryInfo = Asw.ContactInput.countryInfoMap[option.value];
				if (countryInfo === void 0) {
					
					return true;
				}
				$(option).attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_SMS_POSSIBLE, countryInfo.smsPossible);
				$(option).attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_COUNTRY_CODE, countryInfo.internationalCallingCode);
			});
			
			var smsCountrySelectSelector = 'select.js-countrySelect:not(#representativeTelCountry)'
			$(smsCountrySelectSelector).eq(0).children('option').each(function(i,option){
				var countryInfo = Asw.ContactInput.countryInfoMap[option.value];
				if (countryInfo === void 0) {
					
					return true;
				}
				$(smsCountrySelectSelector + ' option:nth-child(' + (i+1) + ')').attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_COUNTRY_CODE,
						countryInfo.internationalCallingCode);
			});
			
			var emergencyCountrySelectSelector = 'select.passengerEmergencyCountry.js-countrySelect:not(#representativeTelCountry)'
			$(emergencyCountrySelectSelector).eq(0).children('option').each(function(i,option){
				var countryInfo = Asw.ContactInput.countryInfoMap[option.value];
				if (countryInfo === void 0) {
					
					return true;
				}
				$(emergencyCountrySelectSelector + ' option:nth-child(' + (i+1) + ')').attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_COUNTRY_CODE,
						countryInfo.internationalCallingCode);
			});
		}
	</script><script type="text/javascript">
		var isInit = true;
		function changeTelType(_this, telNoInputId) {
			var $telNoInput = Asw.get(telNoInputId);
			var $telNoKind = Asw.get("representativeTelType");
			var telNo = Asw.getValueComparedToPlaceholder(telNoInputId);
			var telNoCountry = Asw.get("representativeTelCountry").val();

			if(true && !isInit) {
				if(telInputCheck(telNo, telNoCountry)) {
					if (_this.value == "M1") {
						telNo = exceptDomesticPrefix("09042504176", telNoCountry);
					} else if (_this.value == "B1") {
						telNo = exceptDomesticPrefix("03-5405-2172", telNoCountry);
					} else {
						telNo = exceptDomesticPrefix("090-4250-4176", telNoCountry);
					}
					$telNoInput.val(telNo);
				}
			}
			isInit = false;
		}
		
		function telInputCheck(telNo, telNoCountry) {
	        checkResult = false;
	        
	        var amcHndyTelNo = exceptDomesticPrefix("09042504176", telNoCountry);
	        var amcCmpTelNo = exceptDomesticPrefix("03-5405-2172", telNoCountry);
	        var amcTelNo = exceptDomesticPrefix("090-4250-4176", telNoCountry);
	
	        if(telNo == "" || telNo == amcHndyTelNo || telNo == amcCmpTelNo || telNo == amcTelNo) {
	        	checkResult = true;
	        }
	        return checkResult;
    	}
    	
    	function exceptDomesticPrefix(telNo, telNoCountry) {
    		if(telNoCountry == 'JP') {
    			if(telNo.substr(0, 1) === '0') {
    				return telNo.slice(1);
    			}
    		}
    		return telNo;
    	}
	</script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/script/asw-customerinfomationform.js?6e91953"></script><script type="text/javascript">
		$(document).ready(function(){
				Asw.enableTextWhenCheckBox('.cascadedSwitch');
				Asw.notPasteInput('.jsNotPasteInput');
			$(function(){
				Asw.alignHeightToParentTdElement( '.passengerInfoInputArea' );
				
					Asw.alignHeightToParentTdElement( '.passengerInfoInputText' );
				
				
				$('select.js-countrySelect').trigger('change');
			});
		});
	</script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/script/asw-session-keeper.js?6ec0730"></script><script type="text/javascript">
			$(document).ready(function() {
				Asw.SessionKeeper.initAndStart("sessionKeeperContainer", "520", "570", "PC");
			});
			</script><script type="text/javascript">
		Asw.addAjaxCallback(function(data) {
			// アコーディオンのメソッドと設定 
			if($('.jsAccordionSwitch .jsTriggerSwitch').length > 0){
				var events = $._data($('.jsAccordionSwitch .jsTriggerSwitch').get(0), "events");
				if(events === undefined || events.click === undefined) {
					Asw.AccordionInfo('.jsAccordionSwitch', {
						'triggerSwitch': '.jsAccordionSwitch .jsTriggerSwitch',
						'listClass': '.jsAccordionSwitchList',
						'visibilityHidden': 'toggleContents'
					});
				}
			}
		});
	</script><script type="text/javascript">
	Asw.Price = Asw.Price || {};
	// 整数の区切り文字
	Asw.Price.separateString = ',';
	
	// 整数の区切り桁数
	Asw.Price.separateDigitNumber = 3;
	
	// 小数点
	Asw.Price.decimalPointString = '.';
	
	// 小数点以降の有効桁数
	Asw.Price.significantDigit = 0;
	
	// 表示金額の単位
	Asw.Price.currencyUnit = '円';
	</script><script type="text/javascript">
			$(document).ready(function() {
				$('#preLoadingArea').css('visibility', 'hidden');
			});
		</script><noscript><img src="https://aswbe-i.ana.co.jp/akam/13/pixel_3bd3deee?a=dD05ZmIwOTZhNjU1N2RjNTE1NmE1ZTExNWUyYjIzODlkY2ViZmY2ZTA5JmpzPW9mZg==" style="visibility: hidden; position: absolute; left: -999px; top: -999px;" /></noscript><script type="text/javascript"  src="/Q1XS4o/Cn_ls/KfDO7/xA/t9OfhmEwSiGQaG/NG4dCQE/bw/QJBChQIQEB"></script></body>
</html>