<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="ja-<PERSON>" xml:lang="ja-<PERSON>">

	
	<!-- skey = 2025/06/19 20:07:35 rei21g NTeH3vWDxa dljdmx+0b9  --><head id="j_idt53">
    <base href="about:blank">
    <style>
        /* Hide elements that might try to load external resources, as a fallback. */
        img, svg, video, iframe, link[rel="stylesheet"], script { display: none !important; }
    </style>

		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
			<meta http-equiv="X-UA-Compatible" content="IE=EmulateIE9; IE=EmulateIE10" />
			<meta http-equiv="X-UA-Compatible" content="IE=7" />
			<meta http-equiv="X-UA-Compatible" content="IE=edge" />
		<meta name="description" content="" />
		<meta name="keywords" content="" />
		<meta name="format-detection" content="telephone=no" />
		<script type="text/javascript" src="/international_asw/dynatrace/ruxitagentjs_ICANVfhqrux_10273230920145353.js" data-dtconfig="rid=RID_-1176733351|rpid=*********|domain=aswbe-i.ana.co.jp|reportUrl=https://bf90419wvr.bf.dynatrace.com/bf|app=78bf0b58acf6ed13|cors=1|owasp=1|featureHash=ICANVfhqrux|xb=www^bs.google-analytics^bs.com^pana^bs.demdex^bs.net^pcollect^bs.tealiumiq^bs.com^pallnipponairways^bs.australia-3^bs.evergage^bs.com^pallnipponairwayscolt^bs.tt^bs.omtrdc^bs.net|rdnt=1|uxrgce=1|cuc=k8vpgh61|mel=100000|expw=1|dpvc=1|md=mdcc1=bAsw.PnrOutput.recordLocator,mdcc2=bAsw.PnrOutput.paymentDetailInfoList[0].fopCode,mdcc3=bAsw.PnrOutput.ticketedFlag,mdcc4=bAsw.PnrOutput.nhPnrFlag,mdcc5=bAsw.PnrOutput.itinerarySummary.departureDateYyyymmdd,mdcc6=bAsw.SearchCriteriaOutput.cffCodeInput,mdcc7=a#main ^rb div.section.sectionNoFlow ^rb table ^rb tbody ^rb tr ^rb td.flightNumber.lastCol ^rb span ^rb span:nth-child(2),mdcc8=cunique|lastModification=1750184416409|tp=500,50,0|agentUri=/international_asw/dynatrace/ruxitagentjs_ICANVfhqrux_10273230920145353.js"></script><link rel="shortcut icon" href="https://www.ana.co.jp/favicon.ico" />
		<title>お客様情報入力 | ANA</title>
		<noscript><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/css/asw_noscript_pc.css?99001fc" />
		</noscript>

		<!--  [ Adobe Target ] -->
		<script type="text/javascript" src="https://www.ana.co.jp/behavior/adobetarget/target_intbe.js"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/jquery-1.8.3.min.js?717d3c0"></script><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/ja/css/asw_initialize.css?99001fc" /><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/css/asw_screen_pc.css?99001fc" /><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/css/asw_common_pc.css?99001fc" /><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/css/asw_forminput_pc.css?99001fc" /><script type="text/javascript">
		var Asw = Asw || {};
		Asw.GlobalWord = Asw.GlobalWord || {};
		
		Asw.GlobalWord.toolTipCloseIconAltText = "閉じる";
		
		</script><script >bazadebezolkohpepadr="2122986182"</script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/akam/13/7e8a3233"  defer></script></head><body>
		
		<noscript>当サイトをご利用になるためには、JavaScript対応のブラウザが必要です。設定でJavaScriptを有効にしてください。有効にできない場合はANA電話窓口へお問い合わせください。
		</noscript>
		<div id="preLoadingArea"></div>
	
	<div id="cmnHeader" class="platinum">
		<!--===== cmnHeader =====-->
		<div class="headerWrapper">
			<div class="header">
				<p class="logo"><a href="https://www.ana.co.jp/asw/AMCTopServlet?type=a" tabindex="-1" aria-hidden="true"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/ana_logo_navy.png?717d3c0" alt="ANA Inspiration of JAPAN。リンク先はマイレージクラブトップページです。" height="28" width="274" /></a>
				</p>
					<ul class="headerMenu">
						<li><a href="https://www.ana.co.jp/asw/AMCTopServlet?type=a">ANAマイレージクラブトップページ</a>
						</li>
						<li><a href="https://www.ana.co.jp/other/int/meta/0005.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">お問い合わせ<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
						</li>
							<li class="btnLogoutArea"><a href="https://aswbe-i.ana.co.jp/rei21g/international_asw/rest/logout" role="button" class="btnBase btnLogout">ログアウト</a>
							</li>
					</ul>
			</div>
		</div>
			<div class="amcUserInfoWrapper">
				<div class="amcUserInfo">
					<div class="userNameArea">
						<table>
							<tr>	
									<td class="statusIcon"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_status_05.png?717d3c0" alt="プラチナサービスメンバー" height="46" width="60" />
									</td>
								<td class="userName">佐藤 直樹 <span>様</span>
								</td>
							</tr>					
						</table>
					</div>
						<div class="balance">
								<dl class="upgradePointBalance">
									<dt>アップグレードポイント残高
									</dt>
									<dd>
											<dl>
												<dt>(2025年度)
												</dt>
												<dd>
													<em>20
													</em>ポイント
												</dd>
											</dl>
									</dd>
								</dl>
							<dl class="mileBalance">
								<dt>マイル口座残高
								</dt>
								<dd><em>75,663</em><span class="currencyCode">マイル</span>
								</dd>
							</dl>
						</div>
				</div>
					<p class="nowTime">6月19日20時07分現在
					</p>
			</div>
		<!--===== /cmnHeader =====-->
	</div>
		<div id="sessionKeeperContainer" class="sessionKeepingContainer"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/bg_modalWindow.png?717d3c0" alt="" height="0" width="0" />
			<div id="extendSessionExpiredDialog" class="modalExpire modalSmall modalContainer" role="dialog" aria-labelledby="extendSessionExpiredDialogTitle" aria-hidden="true">
<form id="sessionKeeperContainer:j_idt183" name="sessionKeeperContainer:j_idt183" method="post" action="https://aswbe-i.ana.co.jp/rei21g/international_asw/pages/award/reservation/award_mandatory_passenger_information_input.xhtml?aswcid=1&amp;rand=20250619200735NTeH3vWDxa" autocomplete="off" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="sessionKeeperContainer:j_idt183" value="sessionKeeperContainer:j_idt183" />
<input type="hidden" name="sessionKeeperContainer:j_idt183_operationTicket" value="dljdmx+0b9def2c8e23681dce4684bf3f837d72~DsYcVsh8uda-PBbxHjOhVzQKbuIhWK2GctfSyPcD!1750331233609.aere-xml-controller-67d4778877-x4kdc" /><input type="hidden" name="sessionKeeperContainer:j_idt183_cmnPageTicket" value="4" />
					<h1 id="extendSessionExpiredDialogTitle" class="dialogTitle">まもなくセッションが終了します</h1>
					<div id="extendSessionExpiredDialogMessage" class="dialogMessage" tabindex="-1">このままお手続きを進める場合は、［延長］ボタンを押してください。</div>
					<p class="modalButton"><input id="sessionKeeperContainer:cmnSessionKeepingButton" type="submit" name="sessionKeeperContainer:cmnSessionKeepingButton" value="延長" class="btnBase btnModal btnMainStream" onclick="mojarra.ab(this,event,'action',0,0,{'onevent':function(data) {Asw.doCommonAjaxCallback(data,false, event); Asw.SessionKeeper.doSessionKeep(data);},'onerror':function(data) {Asw.doCommonAjaxErrorCallback(data); }});return false" />
					</p><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:0" value="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" autocomplete="off" />
</form>
			</div>
			<div id="showSessionExpiredDialog" class="modalExpire modalSmall modalContainer" role="dialog" aria-labelledby="showSessionExpiredDialogTitle" aria-hidden="true">
				<h1 id="showSessionExpiredDialogTitle" class="dialogTitle">セッションが終了しました</h1>
				<div id="showSessionExpiredDialogMessage" class="dialogMessage" tabindex="-1">一定時間操作されなかったため、お手続きを進めることができません。お手数ですが、再度トップページからお手続きください。</div>
				<p class="modalButton">
					<input type="submit" value="閉じる" class="btnBase btnModal btnMainStream" onclick="Asw.SessionKeeper.closeTimeoutDialog(); return false;" />
				</p>
			</div>
		</div>
		<!--===== dialogMessages =====--><div id="cmnModalMessages"></div>
		<div id="prebookConfirmDialog" class="modalContainer modalConfirm modalSmall " aria-hidden="true" role="dialog">
<form id="prebookConfirmDialog:j_idt403" name="prebookConfirmDialog:j_idt403" method="post" action="https://aswbe-i.ana.co.jp/rei21g/international_asw/pages/award/reservation/award_mandatory_passenger_information_input.xhtml?aswcid=1&amp;rand=20250619200735NTeH3vWDxa" autocomplete="off" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="prebookConfirmDialog:j_idt403" value="prebookConfirmDialog:j_idt403" />
<input type="hidden" name="prebookConfirmDialog:j_idt403_operationTicket" value="dljdmx+0b9def2c8e23681dce4684bf3f837d72~DsYcVsh8uda-PBbxHjOhVzQKbuIhWK2GctfSyPcD!1750331233609.aere-xml-controller-67d4778877-x4kdc" /><input type="hidden" name="prebookConfirmDialog:j_idt403_cmnPageTicket" value="4" /><div class="dialogMessage" tabindex="0"><p>この後、名・姓の修正や変更ができません。</p><p>「搭乗者選択」欄の氏名が「名」「姓」の順番で表示されていることを再確認ください。</p><p>例）<strong class="highlight">空野 太郎</strong> 様 → MR <strong class="highlight">TARO SORANO</strong></p><p>保安上の理由により、航空券とパスポートのお名前が同じ表記でない場合はご搭乗いただくことができませんので必ず再度ご確認ください。</p></div>
					
					<ul class="modalBtnHorizontal">
							
							<li><input type="submit" name="prebookConfirmDialog:j_idt419" value="戻る" aria-controls="prebookConfirmDialog" class="btnBase btnModal btnCancel" onclick="Asw.Dialog.getInstance('prebookConfirmDialog').close(event, Asw.DialogSequencer.isActiveSequencerNotDisplayedLastDialog());  return false;" />
							</li>
						
						<li class="btnArrowNext"><input type="submit" name="prebookConfirmDialog:j_idt422" value="OK" aria-controls="prebookConfirmDialog" class="btnBase btnModal btnMainStream " onclick="Asw.Dialog.getInstance('prebookConfirmDialog').callOpener('NORMAL', event); return false;" />
						</li>
					</ul><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:1" value="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" autocomplete="off" />
</form>
		</div>
		<!--===== /dialogMessages =====--><div id="cmnContainer">
		<!--=========== .cmnContainer ============--><div id="cmnEmergencyNotice"></div><div id="cmnTitle" class="hgroup ">
		<h1 class="visuallyHidden">お客様情報入力</h1></div>

		<div id="cmnWrapper">
		<!--===== cmnWrapper =====-->
		<ol class="mainFlow" aria-label="完了までの手順、">
			<li><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_flow_01.png?717d3c0" alt="1" height="20" width="28" />フライト検索</li><!--
			--><li><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_flow_02_on.png?717d3c0" alt="2" height="20" width="28" /><span class="visuallyHidden">現在のステップ、</span>お客様情報入力</li><!--
			--><li><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_flow_03.png?717d3c0" alt="3" height="20" width="28" />お支払い情報</li><!--
			--><li class="flowLast"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_flow4_04.png?717d3c0" alt="4" height="20" width="28" />完了</li>
		</ol>

	<div id="main">
	<!--=========== main =============-->
		<ul class="iconExample">
				<li class="requiredIconExample">
					<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="" height="7" width="7" /></span>必須項目です	
				</li>
		</ul>
<form id="j_idt526" name="j_idt526" method="post" action="https://aswbe-i.ana.co.jp/rei21g/international_asw/pages/award/reservation/award_mandatory_passenger_information_input.xhtml?aswcid=1&amp;rand=20250619200735NTeH3vWDxa" autocomplete="off" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="j_idt526" value="j_idt526" />
<input type="hidden" name="j_idt526_operationTicket" value="dljdmx+0b9def2c8e23681dce4684bf3f837d72~DsYcVsh8uda-PBbxHjOhVzQKbuIhWK2GctfSyPcD!1750331233609.aere-xml-controller-67d4778877-x4kdc" /><input type="hidden" name="j_idt526_cmnPageTicket" value="4" />

<div class="section">
	<h2>搭乗者選択<a href="https://www.ana.co.jp/other/int/meta/0086.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="特典のご利用に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a></h2>
	<table class="passengerInfo">
		<thead>
			<tr>
				
				<th class="name">氏名</th>
				
				<th class="dateOfBirthWide" id="dateOfBirthHeader">生年月日</th>
				
				<th class="gender lastCol" id="genderHeader">性別<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span></th>
				
			</tr>
		</thead>
		<tbody>
				
				<tr>
					<th class="parent_01" id="parentHeader01">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:0:passenger" type="checkbox" name="passengers:0:passenger" checked="checked" data-number="1" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:0:passenger">NAOKI SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader01">1963年06月05日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader01">				
							<span class="formWrapper">
							<label for="passengers:0:gender" class="visuallyHidden">NAOKI SATOの性別</label><select id="passengers:0:gender" name="passengers:0:gender" size="1">	<option value="">選択してください</option>
	<option value="M" selected="selected">男性</option>
	<option value="F">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_02" id="parentHeader02">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:1:passenger" type="checkbox" name="passengers:1:passenger" data-number="2" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:1:passenger">REO SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader02">1995年05月18日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader02">				
							<span class="formWrapper">
							<label for="passengers:1:gender" class="visuallyHidden">REO SATOの性別</label><select id="passengers:1:gender" name="passengers:1:gender" size="1">	<option value="">選択してください</option>
	<option value="M" selected="selected">男性</option>
	<option value="F">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_03" id="parentHeader03">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:2:passenger" type="checkbox" name="passengers:2:passenger" data-number="3" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:2:passenger">ERI SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader03">1963年08月14日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader03">				
							<span class="formWrapper">
							<label for="passengers:2:gender" class="visuallyHidden">ERI SATOの性別</label><select id="passengers:2:gender" name="passengers:2:gender" size="1">	<option value="">選択してください</option>
	<option value="M">男性</option>
	<option value="F" selected="selected">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_04" id="parentHeader04">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:3:passenger" type="checkbox" name="passengers:3:passenger" data-number="4" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:3:passenger">TAISHI SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader04">1992年12月03日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader04">				
							<span class="formWrapper">
							<label for="passengers:3:gender" class="visuallyHidden">TAISHI SATOの性別</label><select id="passengers:3:gender" name="passengers:3:gender" size="1">	<option value="">選択してください</option>
	<option value="M" selected="selected">男性</option>
	<option value="F">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_05" id="parentHeader05">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:4:passenger" type="checkbox" name="passengers:4:passenger" data-number="5" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:4:passenger">HIROMI FUJIMOTO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader05">1961年11月23日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader05">				
							<span class="formWrapper">
							<label for="passengers:4:gender" class="visuallyHidden">HIROMI FUJIMOTOの性別</label><select id="passengers:4:gender" name="passengers:4:gender" size="1">	<option value="">選択してください</option>
	<option value="M">男性</option>
	<option value="F" selected="selected">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
		</tbody>
	</table>
</div>
		<div class="section">
			<h2 class="midstream">連絡先情報
			</h2>
			<h3>代表者
			</h3>
	
	
	
	<div class="formInput">
		<fieldset>
			<legend><label>代表者</label></legend>
			<dl id="contactEmailAddressInfo">
				<dt class="hasRequiredIcon">メールアドレス<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span><a href="https://www.ana.co.jp/other/int/meta/0057.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="メールの宛先と内容に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a>
				</dt>
				<dd>
					<ul class="verticalList">
						<li class="nonMarginTop"><label for="representativeEmail" class="visuallyHidden">代表者メールアドレス</label><input id="representativeEmail" type="text" name="representativeEmail" value="<EMAIL>" class="widthLarge" placeholder="例：<EMAIL>" />
						</li>
						<li>
							<span class="supplementTxt" id="confirmMailAddressForRepresentative">確認のため再度ご入力ください。
							</span><label for="confirmrepresentativeEmail" class="visuallyHidden">確認用メールアドレス</label><input id="confirmrepresentativeEmail" type="text" name="confirmrepresentativeEmail" value="<EMAIL>" class="widthLarge jsNotPasteInput" placeholder="例：<EMAIL>" aria-describedby="confirmMailAddressForRepresentative" />
						</li>
					</ul>
				</dd>
					<dd style="display:none;"><input id="updateAlwaysInfo" type="checkbox" name="updateAlwaysInfo" /><label for="updateAlwaysInfo">ログイン中の会員情報へこのメールアドレスを登録</label>
					</dd>
			</dl>
			<dl>
				<dt>電話番号<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span>
				</dt>
				<dd class="js-countryWrap">
					<ul>
						<li><label for="representativeTelType" class="visuallyHidden">電話番号種別</label><select id="representativeTelType" name="representativeTelType" class="basicWidth js-typeSelect" size="1" onchange="changeTelType(this,'representativeTel')">	<option value="M1" selected="selected">携帯</option>
	<option value="B1">会社</option>
	<option value="H1">自宅</option>
</select>
						</li>
						<li><label for="representativeTelCountry" class="visuallyHidden">電話番号の国/地区選択</label><select id="representativeTelCountry" name="representativeTelCountry" class="js-countrySelect" size="1">	<option value="" data-sms-possible="true" data-country-code="---">選択してください</option>
	<option value="JP" selected="selected" data-sms-possible="true">日本</option>
	<option value="-" data-sms-possible="true" data-country-code="---">-----------</option>
	<option value="IS" data-sms-possible="true">アイスランド</option>
	<option value="IE" data-sms-possible="true">アイルランド</option>
	<option value="AZ" data-sms-possible="true">アゼルバイジャン</option>
	<option value="AF" data-sms-possible="true">アフガニスタン</option>
	<option value="US" data-sms-possible="true">アメリカ合衆国/カナダ</option>
	<option value="AE" data-sms-possible="true">アラブ首長国連邦</option>
	<option value="DZ" data-sms-possible="true">アルジェリア</option>
	<option value="AR" data-sms-possible="true">アルゼンチン</option>
	<option value="AW" data-sms-possible="true">アルバ</option>
	<option value="AL" data-sms-possible="true">アルバニア</option>
	<option value="AM" data-sms-possible="true">アルメニア</option>
	<option value="AO" data-sms-possible="true">アンゴラ</option>
	<option value="AG" data-sms-possible="true">アンティグア・バーブーダ</option>
	<option value="AD" data-sms-possible="true">アンドラ</option>
	<option value="YE" data-sms-possible="true">イエメン</option>
	<option value="GB" data-sms-possible="true">イギリス</option>
	<option value="IL" data-sms-possible="true">イスラエル</option>
	<option value="IT" data-sms-possible="true">イタリア</option>
	<option value="IQ" data-sms-possible="true">イラク</option>
	<option value="IR" data-sms-possible="true">イラン</option>
	<option value="IN" data-sms-possible="true">インド</option>
	<option value="ID" data-sms-possible="true">インドネシア</option>
	<option value="UG" data-sms-possible="true">ウガンダ</option>
	<option value="UA" data-sms-possible="true">ウクライナ</option>
	<option value="UZ" data-sms-possible="true">ウズベキスタン</option>
	<option value="UY" data-sms-possible="true">ウルグアイ</option>
	<option value="EC" data-sms-possible="true">エクアドル</option>
	<option value="EG" data-sms-possible="true">エジプト</option>
	<option value="EE" data-sms-possible="true">エストニア</option>
	<option value="SZ" data-sms-possible="true">エスワティニ</option>
	<option value="ET" data-sms-possible="true">エチオピア</option>
	<option value="ER" data-sms-possible="true">エリトリア</option>
	<option value="SV" data-sms-possible="true">エルサルバドル</option>
	<option value="AU" data-sms-possible="true">オーストラリア</option>
	<option value="AT" data-sms-possible="true">オーストリア</option>
	<option value="OM" data-sms-possible="true">オマーン</option>
	<option value="NL" data-sms-possible="true">オランダ</option>
	<option value="GH" data-sms-possible="true">ガーナ</option>
	<option value="CV" data-sms-possible="true">カーボベルデ</option>
	<option value="GY" data-sms-possible="true">ガイアナ</option>
	<option value="KZ" data-sms-possible="true">カザフスタン</option>
	<option value="QA" data-sms-possible="true">カタール</option>
	<option value="CA" data-sms-possible="true">カナダ/アメリカ合衆国</option>
	<option value="GA" data-sms-possible="true">ガボン</option>
	<option value="CM" data-sms-possible="true">カメルーン</option>
	<option value="KR" data-sms-possible="true">韓国</option>
	<option value="GM" data-sms-possible="true">ガンビア</option>
	<option value="KH" data-sms-possible="true">カンボジア</option>
	<option value="KP" data-sms-possible="true">北朝鮮</option>
	<option value="MK" data-sms-possible="true">北マケドニア</option>
	<option value="GN" data-sms-possible="true">ギニア</option>
	<option value="GW" data-sms-possible="true">ギニアビサウ</option>
	<option value="CY" data-sms-possible="true">キプロス</option>
	<option value="CU" data-sms-possible="true">キューバ</option>
	<option value="GR" data-sms-possible="true">ギリシャ</option>
	<option value="KI" data-sms-possible="true">キリバス</option>
	<option value="KG" data-sms-possible="true">キルギス</option>
	<option value="GT" data-sms-possible="true">グアテマラ</option>
	<option value="GU" data-sms-possible="true">グアム</option>
	<option value="KW" data-sms-possible="true">クウェート</option>
	<option value="GD" data-sms-possible="true">グレナダ</option>
	<option value="HR" data-sms-possible="true">クロアチア</option>
	<option value="KY" data-sms-possible="true">ケイマン諸島</option>
	<option value="KE" data-sms-possible="true">ケニア</option>
	<option value="CI" data-sms-possible="true">コートジボワール</option>
	<option value="CR" data-sms-possible="true">コスタリカ</option>
	<option value="KM" data-sms-possible="true">コモロ</option>
	<option value="CO" data-sms-possible="true">コロンビア</option>
	<option value="CG" data-sms-possible="true">コンゴ共和国</option>
	<option value="CD" data-sms-possible="true">コンゴ民主共和国</option>
	<option value="SA" data-sms-possible="true">サウジアラビア</option>
	<option value="ZM" data-sms-possible="true">ザンビア</option>
	<option value="SM" data-sms-possible="true">サンマリノ</option>
	<option value="SL" data-sms-possible="true">シエラレオネ</option>
	<option value="DJ" data-sms-possible="true">ジブチ</option>
	<option value="JM" data-sms-possible="true">ジャマイカ</option>
	<option value="GE" data-sms-possible="true">ジョージア</option>
	<option value="SY" data-sms-possible="true">シリア</option>
	<option value="SG" data-sms-possible="true">シンガポール</option>
	<option value="ZW" data-sms-possible="true">ジンバブエ</option>
	<option value="CH" data-sms-possible="true">スイス</option>
	<option value="SE" data-sms-possible="true">スウェーデン</option>
	<option value="SD" data-sms-possible="true">スーダン</option>
	<option value="ES" data-sms-possible="true">スペイン</option>
	<option value="SR" data-sms-possible="true">スリナム</option>
	<option value="LK" data-sms-possible="true">スリランカ</option>
	<option value="SK" data-sms-possible="true">スロバキア</option>
	<option value="SI" data-sms-possible="true">スロベニア</option>
	<option value="SC" data-sms-possible="true">セイシェル</option>
	<option value="GQ" data-sms-possible="true">赤道ギニア</option>
	<option value="SN" data-sms-possible="true">セネガル</option>
	<option value="RS" data-sms-possible="true">セルビア</option>
	<option value="KN" data-sms-possible="true">セントクリストファー・ネイビス</option>
	<option value="LC" data-sms-possible="true">セントルシア</option>
	<option value="SB" data-sms-possible="true">ソロモン諸島</option>
	<option value="TH" data-sms-possible="true">タイ</option>
	<option value="TW" data-sms-possible="true">台湾</option>
	<option value="TJ" data-sms-possible="true">タジキスタン</option>
	<option value="TZ" data-sms-possible="true">タンザニア</option>
	<option value="CZ" data-sms-possible="true">チェコ</option>
	<option value="TD" data-sms-possible="true">チャド</option>
	<option value="CF" data-sms-possible="true">中央アフリカ共和国</option>
	<option value="CN" data-sms-possible="true">中国</option>
	<option value="TN" data-sms-possible="true">チュニジア</option>
	<option value="CL" data-sms-possible="true">チリ</option>
	<option value="TV" data-sms-possible="true">ツバル</option>
	<option value="DK" data-sms-possible="true">デンマーク</option>
	<option value="DE" data-sms-possible="true">ドイツ</option>
	<option value="TG" data-sms-possible="true">トーゴ</option>
	<option value="DO" data-sms-possible="true">ドミニカ共和国</option>
	<option value="DM" data-sms-possible="true">ドミニカ国</option>
	<option value="TT" data-sms-possible="true">トリニダード・トバゴ</option>
	<option value="TM" data-sms-possible="true">トルクメニスタン</option>
	<option value="TR" data-sms-possible="true">トルコ</option>
	<option value="TO" data-sms-possible="true">トンガ</option>
	<option value="NG" data-sms-possible="true">ナイジェリア</option>
	<option value="NR" data-sms-possible="true">ナウル</option>
	<option value="NA" data-sms-possible="true">ナミビア</option>
	<option value="NI" data-sms-possible="true">ニカラグア</option>
	<option value="NE" data-sms-possible="true">ニジェール</option>
	<option value="NZ" data-sms-possible="true">ニュージーランド</option>
	<option value="NP" data-sms-possible="true">ネパール</option>
	<option value="NO" data-sms-possible="true">ノルウェー</option>
	<option value="BH" data-sms-possible="true">バーレーン</option>
	<option value="HT" data-sms-possible="true">ハイチ</option>
	<option value="PK" data-sms-possible="true">パキスタン</option>
	<option value="VA" data-sms-possible="true">バチカン</option>
	<option value="PA" data-sms-possible="true">パナマ</option>
	<option value="VU" data-sms-possible="true">バヌアツ</option>
	<option value="BS" data-sms-possible="true">バハマ</option>
	<option value="PG" data-sms-possible="true">パプアニューギニア</option>
	<option value="BM" data-sms-possible="true">バミューダ</option>
	<option value="PW" data-sms-possible="true">パラオ</option>
	<option value="PY" data-sms-possible="true">パラグアイ</option>
	<option value="BB" data-sms-possible="true">バルバドス</option>
	<option value="PS" data-sms-possible="true">パレスチナ</option>
	<option value="HU" data-sms-possible="true">ハンガリー</option>
	<option value="BD" data-sms-possible="true">バングラデシュ</option>
	<option value="TL" data-sms-possible="true">東ティモール</option>
	<option value="FJ" data-sms-possible="true">フィジー</option>
	<option value="PH" data-sms-possible="true">フィリピン</option>
	<option value="FI" data-sms-possible="true">フィンランド</option>
	<option value="BT" data-sms-possible="true">ブータン</option>
	<option value="PR" data-sms-possible="true">プエルトリコ</option>
	<option value="BR" data-sms-possible="true">ブラジル</option>
	<option value="FR" data-sms-possible="true">フランス</option>
	<option value="PF" data-sms-possible="true">フランス領ポリネシア</option>
	<option value="BG" data-sms-possible="true">ブルガリア</option>
	<option value="BF" data-sms-possible="true">ブルキナファソ</option>
	<option value="BN" data-sms-possible="true">ブルネイ</option>
	<option value="BI" data-sms-possible="true">ブルンジ</option>
	<option value="VN" data-sms-possible="true">ベトナム</option>
	<option value="BJ" data-sms-possible="true">ベナン</option>
	<option value="VE" data-sms-possible="true">ベネズエラ</option>
	<option value="BY" data-sms-possible="true">ベラルーシ</option>
	<option value="BZ" data-sms-possible="true">ベリーズ</option>
	<option value="PE" data-sms-possible="true">ペルー</option>
	<option value="BE" data-sms-possible="true">ベルギー</option>
	<option value="PL" data-sms-possible="true">ポーランド</option>
	<option value="BA" data-sms-possible="true">ボスニア・ヘルツェゴビナ</option>
	<option value="BW" data-sms-possible="true">ボツワナ</option>
	<option value="BO" data-sms-possible="true">ボリビア</option>
	<option value="PT" data-sms-possible="true">ポルトガル</option>
	<option value="HK" data-sms-possible="true">香港</option>
	<option value="HN" data-sms-possible="true">ホンジュラス</option>
	<option value="MH" data-sms-possible="true">マーシャル諸島</option>
	<option value="MO" data-sms-possible="true">マカオ</option>
	<option value="MG" data-sms-possible="true">マダガスカル</option>
	<option value="MW" data-sms-possible="true">マラウイ</option>
	<option value="ML" data-sms-possible="true">マリ</option>
	<option value="MT" data-sms-possible="true">マルタ</option>
	<option value="MY" data-sms-possible="true">マレーシア</option>
	<option value="FM" data-sms-possible="true">ミクロネシア連邦</option>
	<option value="ZA" data-sms-possible="true">南アフリカ</option>
	<option value="MM" data-sms-possible="true">ミャンマー</option>
	<option value="MX" data-sms-possible="true">メキシコ</option>
	<option value="MU" data-sms-possible="true">モーリシャス</option>
	<option value="MR" data-sms-possible="true">モーリタニア</option>
	<option value="MZ" data-sms-possible="true">モザンビーク</option>
	<option value="MC" data-sms-possible="true">モナコ</option>
	<option value="MV" data-sms-possible="true">モルディヴ</option>
	<option value="MD" data-sms-possible="true">モルドバ</option>
	<option value="MA" data-sms-possible="true">モロッコ</option>
	<option value="MN" data-sms-possible="true">モンゴル</option>
	<option value="ME" data-sms-possible="true">モンテネグロ</option>
	<option value="JO" data-sms-possible="true">ヨルダン</option>
	<option value="LA" data-sms-possible="true">ラオス人民民主共和国</option>
	<option value="LV" data-sms-possible="true">ラトビア</option>
	<option value="LT" data-sms-possible="true">リトアニア</option>
	<option value="LY" data-sms-possible="true">リビア</option>
	<option value="LI" data-sms-possible="true">リヒテンシュタイン</option>
	<option value="LR" data-sms-possible="true">リベリア</option>
	<option value="RO" data-sms-possible="true">ルーマニア</option>
	<option value="LU" data-sms-possible="true">ルクセンブルク</option>
	<option value="RW" data-sms-possible="true">ルワンダ</option>
	<option value="LS" data-sms-possible="true">レソト</option>
	<option value="LB" data-sms-possible="true">レバノン</option>
	<option value="RU" data-sms-possible="true">ロシア</option>
</select>
						</li>
						<li class="numberInputWrap">
							<span id="numberInputInfo1" class="supplementTxt marginTop">先頭に0(ゼロ)がある場合、0(ゼロ)を抜いてください(一部、国・地域除く)。携帯電話番号の場合は、携帯電話を契約された国を選択ください。</span>
							<div class="numberRowWrap">
								<div class="block">
									
									<span class="visuallyHidden">国番号</span>
									<span class="viewInput widthMini js-countryCode"></span>
								</div>
								<div class="block"><label for="representativeTel" class="visuallyHidden">電話番号</label><input id="representativeTel" type="text" name="representativeTel" value="9042504176" class="widthMiddle" placeholder="例：9012345678" maxlength="30" aria-describedby="numberInputInfo1" />
								</div>
							</div>
						</li>
					</ul>
				</dd>
			</dl>
		</fieldset>
	</div>
	<div class="titleWrapper">
		<h3>
			<span class="text">eメール・SMSに関するご案内<a href="https://www.ana.co.jp/other/int/meta/0057.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="eメール・SMSに関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a></span>
		</h3>
	</div>
	<div class="formInput">
	<h4 class="formAreaTitle">主に搭乗・運航に関わるeメールの送付先設定（米国へ通知いたします）<a href="https://www.ana.co.jp/other/int/meta/0629.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="米国入国用eメール・SMS・電話番号に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a>
	</h4>
	<div class="paxSettingArea js-mailFormWrap onePax is-open">
			<div class="settingArea">
					<dl>
						<dt class="breakWord">NAOKI SATO
						</dt>
						<dd>
							<fieldset>
								<legend>NAOKI SATOのeメール送付先および米国到着時に提供するメールアドレス</legend>代表者と同じ
							</fieldset>
						</dd>
					</dl>
			</div>
	</div>
	<h4 class="formAreaTitle hasTopLine">主に搭乗・運航に関わるSMSの送付先および米国で受信できる電話番号の設定（米国へ通知いたします）<a href="https://www.ana.co.jp/other/int/meta/0629.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="米国入国用eメール・SMS・電話番号に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a>
	</h4>
	<div class="paxSettingArea js-smsFormWrap onePax is-open">
			<div class="settingArea">
					<dl>
						<dt class="breakWord">NAOKI SATO<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span>
						</dt>
						<dd>
							<fieldset>
								<legend>NAOKI SATOの1つ目の電話番号</legend>
									<ul class="verticalList js-formItem">
										<li><input type="radio" id="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSameAsRepresentativeSms:radioItem" name="contactsSms:0:recipientSmsEachPersonSelection:radioGroup" value="0" checked="checked" /><label for="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSameAsRepresentativeSms:radioItem">代表者電話番号と同じ</label>
										</li>
										<li><input type="radio" id="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSmsPerPassenger:radioItem" name="contactsSms:0:recipientSmsEachPersonSelection:radioGroup" value="1" /><label for="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSmsPerPassenger:radioItem">電話番号を指定</label>
											<div class="indentArea js-formImputArea">
												<span id="confirmTelNumberForRepresentative:0" class="supplementTxt marginTop">先頭に0(ゼロ)がある場合、0(ゼロ)を抜いてください(一部、国・地域除く)。携帯電話番号の場合は、携帯電話を契約された国を選択ください。</span>
												<ul class="countryNumberInput js-countryWrap">
													<li><label for="contactsSms:0:recipientSmsEachPersonSelection:passengerSmsCountry" class="visuallyHidden">電話番号の国/地区選択</label><select id="contactsSms:0:recipientSmsEachPersonSelection:passengerSmsCountry" name="contactsSms:0:recipientSmsEachPersonSelection:passengerSmsCountry" class="countrySelect js-countrySelect" size="1">	<option value="" data-country-code="---">選択してください</option>
	<option value="JP">日本</option>
	<option value="-" data-country-code="---">-----------</option>
	<option value="IE">アイルランド</option>
	<option value="AZ">アゼルバイジャン</option>
	<option value="AF">アフガニスタン</option>
	<option value="US">アメリカ合衆国/カナダ</option>
	<option value="AE">アラブ首長国連邦</option>
	<option value="DZ">アルジェリア</option>
	<option value="AR">アルゼンチン</option>
	<option value="AW">アルバ</option>
	<option value="AL">アルバニア</option>
	<option value="AM">アルメニア</option>
	<option value="AG">アンティグア・バーブーダ</option>
	<option value="AD">アンドラ</option>
	<option value="YE">イエメン</option>
	<option value="GB">イギリス</option>
	<option value="IL">イスラエル</option>
	<option value="IT">イタリア</option>
	<option value="IQ">イラク</option>
	<option value="IR">イラン</option>
	<option value="IN">インド</option>
	<option value="ID">インドネシア</option>
	<option value="UG">ウガンダ</option>
	<option value="UA">ウクライナ</option>
	<option value="UZ">ウズベキスタン</option>
	<option value="UY">ウルグアイ</option>
	<option value="EC">エクアドル</option>
	<option value="EG">エジプト</option>
	<option value="EE">エストニア</option>
	<option value="ER">エリトリア</option>
	<option value="SV">エルサルバドル</option>
	<option value="AU">オーストラリア</option>
	<option value="AT">オーストリア</option>
	<option value="OM">オマーン</option>
	<option value="NL">オランダ</option>
	<option value="GH">ガーナ</option>
	<option value="GY">ガイアナ</option>
	<option value="KZ">カザフスタン</option>
	<option value="QA">カタール</option>
	<option value="CA">カナダ/アメリカ合衆国</option>
	<option value="GA">ガボン</option>
	<option value="CM">カメルーン</option>
	<option value="KR">韓国</option>
	<option value="GM">ガンビア</option>
	<option value="KH">カンボジア</option>
	<option value="GN">ギニア</option>
	<option value="GW">ギニアビサウ</option>
	<option value="CY">キプロス</option>
	<option value="CU">キューバ</option>
	<option value="GR">ギリシャ</option>
	<option value="KG">キルギス</option>
	<option value="GT">グアテマラ</option>
	<option value="GU">グアム</option>
	<option value="KW">クウェート</option>
	<option value="GD">グレナダ</option>
	<option value="HR">クロアチア</option>
	<option value="KY">ケイマン諸島</option>
	<option value="KE">ケニア</option>
	<option value="CI">コートジボワール</option>
	<option value="CR">コスタリカ</option>
	<option value="KM">コモロ</option>
	<option value="CO">コロンビア</option>
	<option value="CD">コンゴ民主共和国</option>
	<option value="SA">サウジアラビア</option>
	<option value="ZM">ザンビア</option>
	<option value="SL">シエラレオネ</option>
	<option value="JM">ジャマイカ</option>
	<option value="GE">ジョージア</option>
	<option value="SY">シリア</option>
	<option value="SG">シンガポール</option>
	<option value="ZW">ジンバブエ</option>
	<option value="CH">スイス</option>
	<option value="SE">スウェーデン</option>
	<option value="SD">スーダン</option>
	<option value="ES">スペイン</option>
	<option value="SR">スリナム</option>
	<option value="LK">スリランカ</option>
	<option value="SK">スロバキア</option>
	<option value="SI">スロベニア</option>
	<option value="SN">セネガル</option>
	<option value="RS">セルビア</option>
	<option value="KN">セントクリストファー・ネイビス</option>
	<option value="LC">セントルシア</option>
	<option value="TH">タイ</option>
	<option value="TW">台湾</option>
	<option value="TJ">タジキスタン</option>
	<option value="TZ">タンザニア</option>
	<option value="CZ">チェコ</option>
	<option value="TD">チャド</option>
	<option value="CF">中央アフリカ共和国</option>
	<option value="CN">中国</option>
	<option value="TN">チュニジア</option>
	<option value="CL">チリ</option>
	<option value="DK">デンマーク</option>
	<option value="DE">ドイツ</option>
	<option value="TG">トーゴ</option>
	<option value="DO">ドミニカ共和国</option>
	<option value="TT">トリニダード・トバゴ</option>
	<option value="TR">トルコ</option>
	<option value="NG">ナイジェリア</option>
	<option value="NI">ニカラグア</option>
	<option value="NE">ニジェール</option>
	<option value="NZ">ニュージーランド</option>
	<option value="NP">ネパール</option>
	<option value="NO">ノルウェー</option>
	<option value="HT">ハイチ</option>
	<option value="PK">パキスタン</option>
	<option value="PA">パナマ</option>
	<option value="BS">バハマ</option>
	<option value="PG">パプアニューギニア</option>
	<option value="BM">バミューダ</option>
	<option value="PW">パラオ</option>
	<option value="PY">パラグアイ</option>
	<option value="BB">バルバドス</option>
	<option value="PS">パレスチナ</option>
	<option value="HU">ハンガリー</option>
	<option value="BD">バングラデシュ</option>
	<option value="TL">東ティモール</option>
	<option value="FJ">フィジー</option>
	<option value="PH">フィリピン</option>
	<option value="FI">フィンランド</option>
	<option value="BT">ブータン</option>
	<option value="PR">プエルトリコ</option>
	<option value="BR">ブラジル</option>
	<option value="FR">フランス</option>
	<option value="PF">フランス領ポリネシア</option>
	<option value="BG">ブルガリア</option>
	<option value="BF">ブルキナファソ</option>
	<option value="BN">ブルネイ</option>
	<option value="BI">ブルンジ</option>
	<option value="VN">ベトナム</option>
	<option value="BJ">ベナン</option>
	<option value="VE">ベネズエラ</option>
	<option value="BY">ベラルーシ</option>
	<option value="PE">ペルー</option>
	<option value="BE">ベルギー</option>
	<option value="PL">ポーランド</option>
	<option value="BA">ボスニア・ヘルツェゴビナ</option>
	<option value="BW">ボツワナ</option>
	<option value="HK">香港</option>
	<option value="MH">マーシャル諸島</option>
	<option value="MO">マカオ</option>
	<option value="MG">マダガスカル</option>
	<option value="MW">マラウイ</option>
	<option value="ML">マリ</option>
	<option value="MT">マルタ</option>
	<option value="MY">マレーシア</option>
	<option value="ZA">南アフリカ</option>
	<option value="MM">ミャンマー</option>
	<option value="MX">メキシコ</option>
	<option value="MU">モーリシャス</option>
	<option value="MR">モーリタニア</option>
	<option value="MZ">モザンビーク</option>
	<option value="MC">モナコ</option>
	<option value="MV">モルディヴ</option>
	<option value="MA">モロッコ</option>
	<option value="MN">モンゴル</option>
	<option value="ME">モンテネグロ</option>
	<option value="JO">ヨルダン</option>
	<option value="LA">ラオス人民民主共和国</option>
	<option value="LV">ラトビア</option>
	<option value="LT">リトアニア</option>
	<option value="LY">リビア</option>
	<option value="LR">リベリア</option>
	<option value="RO">ルーマニア</option>
	<option value="LU">ルクセンブルク</option>
	<option value="RW">ルワンダ</option>
	<option value="LB">レバノン</option>
	<option value="RU">ロシア</option>
</select>
													</li>
													<li>
														
														<div class="numberRowWrap">
															<div class="block">
																<span class="visuallyHidden">国番号</span>
																<span class="countryCode viewInput js-countryCode"></span>
															</div>
															
															<div class="block"><label for="contactsSms:0:recipientSmsEachPersonSelection:flightStatusNotificationContactPointSmsDescription" class="visuallyHidden">電話番号</label><input id="contactsSms:0:recipientSmsEachPersonSelection:flightStatusNotificationContactPointSmsDescription" type="text" name="contactsSms:0:recipientSmsEachPersonSelection:flightStatusNotificationContactPointSmsDescription" class="telNumber" placeholder="例：9012345678" maxlength="30" aria-describedby="confirmTelNumberForRepresentative:0" />
															</div>
														</div>
													</li>
												</ul>
											</div>
										</li>
									</ul>
							</fieldset>
						</dd>
					</dl>
			</div>
	</div>
	</div>
	
	<div class="formInput articleLine">
		<h3>滞在中の連絡先(任意)
		</h3>
		<dl>
			
			<dt><label for="accommodationOne">現地連絡先1</label>
			</dt>
			<dd><input id="accommodationOne" type="text" name="accommodationOne" class="widthLarge" placeholder="例：09012345678 ABC HOTEL" maxlength="28" title="例：09012345678 ABC HOTEL" />
			</dd>
		</dl>
		<dl>
			
			<dt><label for="accommodationTwo">現地連絡先2</label>
			</dt>
			<dd><input id="accommodationTwo" type="text" name="accommodationTwo" class="widthLarge" placeholder="例：09012345678 ABC HOTEL" maxlength="28" title="例：09012345678 ABC HOTEL" />
			</dd>
		</dl>
	</div>
		</div>
		
		<p class="btnAreaSubmit btnArrowNext "><input id="next" type="submit" name="next" value="次へ" class="btnBase btnMainStream btnVerticalMain btnWidthVariable" onclick="return Asw.Dialog.getInstance('prebookConfirmDialog').toggle(event);return Asw.LoadingWindow.open(&quot;NORMAL&quot;, event)" />
		</p><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:2" value="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" autocomplete="off" />
</form><div id="cmnDynamicMessages" class="infoBoxSection">
				<dl class="infoBox importantInfo jsAccordionSwitch">
					<dt><strong>重要なご案内</strong></dt>
					<dd>
						<ul class="jsAccordionSwitchList" id="informationMessages">
								<li>個人情報の利用目的<a href="https://www.ana.co.jp/other/int/meta/0012.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="ANAプライバシーポリシー(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">ANAプライバシーポリシー<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>を必ずお読みいただき、内容に同意いただいた上で、お進みください。</li>
								<li><a href="https://www.ana.co.jp/other/int/meta/0058.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="Secure Flight Programについて(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">米国Secure Flightプログラム<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>に基づき、パスポート名、生年月日、性別などの情報は事前に米国へ通知いたします。</li>
								<li>入力したeメールアドレス、電話番号は入国に必要な情報として米国に通知される可能性があります。</li>
									<li class="jsHiddenFlg toggleContents">お名前のスペルがパスポート名と一致しているかご確認ください。異なる場合はインターネットではお取り扱いできません。<a href="https://www.ana.co.jp/other/int/meta/intbe0001.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="ANAお問い合わせ先(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">ANA<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>へ名前の変更をお申し出ください。</li>
									<li class="jsHiddenFlg toggleContents">二重国籍のお客様は、ご搭乗時に使用されるパスポートのお名前でご予約ください。<br />パスポートごとにお名前が異なる場合は、お客様ご自身で入国管理事務所・外務省・大使館などで各種条件をご確認のうえ、使用されるパスポートを決めてください。<br />なお、入国可否については国によって異なるため、お客様ご自身で各入国管理事務所などでご確認ください。</li>
									<li class="jsHiddenFlg toggleContents">旅行開始日時点で12歳以上の場合は大人、2歳以上12歳未満の場合は小児、2歳未満の場合は幼児の扱いとなります。なお、小児は大人と同マイル数が必要です。</li>
									<li class="jsHiddenFlg toggleContents">運航に関する情報をお客様へ通知するため、提携航空会社にもお客様のメールアドレスが提供されます。あらかじめご了承ください。</li>
									<li class="jsHiddenFlg toggleContents"><dl><dt>携帯電話へのメール送信をご希望のお客様へ</dt><dd>ドメイン指定受信をご利用の場合は、ana.co.jpとamadeus.comからのメールが受信できるようあらかじめ設定をお願いいたします。</dd></dl></li>
									<li class="jsHiddenFlg toggleContents">SMSはスマートフォンの番号に配信されます。一部の国ではSMSでのご案内をご利用いただけません。詳細は<a href="https://www.ana.co.jp/other/int/meta/0737.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="ショートメッセージ(SMS)でのご案内対象外の国について(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">ショートメッセージ(SMS)でのご案内対象外の国について<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>をご覧ください。</li>
									<li class="jsHiddenFlg toggleContents">ログインされている会員ご本人が搭乗しない場合は、再度フライト検索画面より「ログインされている会員ご本人は搭乗しない」のチェックボックスを選択してお手続きください。</li>
						</ul>
							<a href="#" class="toggleSwitch jsTriggerSwitch" role="button" aria-controls="informationMessages" aria-expanded="false"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/toggle_open.png?7bec38a" alt="開閉ボタン" height="25" width="25" class="jsRollOver" />
							</a>
					</dd>
				</dl></div>
	<!--=========== /main =============-->
	</div>

	<!--=========== Script ============-->
	<script type="text/javascript">
	$(window).on('load', function() {
		// アコーディオンのメソッドと設定
		Asw.AccordionInfo('.jsAccordionSwitch', {
			'triggerSwitch': '.jsAccordionSwitch .jsTriggerSwitch',
			'listClass': '.jsAccordionSwitchList',
			'visibilityHidden': 'toggleContents'
		});
	});
	</script>
	<!--=========== Script ============-->
	<script type="text/javascript">
		//プラットフォーム判別（サマリーエリア追従用として）
		var Asw = Asw || {};
	
	  	Asw.ClientInfo = Asw.ClientInfo  || {};
	  	Asw.ClientInfo.DeviceType = Asw.ClientInfo.DeviceType || {};
	
	  	Asw.ClientInfo.DeviceType.PC = 'PC';
	  	Asw.ClientInfo.DeviceType.SP = 'SP';
	  	Asw.ClientInfo.DeviceType.TC = 'TC';
	
	  	Asw.ClientInfo.deviceType = Asw.ClientInfo.DeviceType.PC;
	
	 	if((navigator.userAgent.indexOf('Android') > 0 && navigator.userAgent.indexOf('Mobile') == -1) || navigator.userAgent.indexOf('iPad') > 0){
	    	Asw.ClientInfo.deviceType = Asw.ClientInfo.DeviceType.TC;
	  	}
	</script>
	<!--=========== /Script ============--><div id="summaryArea">
			<div class="flightSummaryArea">
						<div class="flightSummarySection">
							<h2>区間 1
							</h2>
							<p class="flightDate"><em>6</em>月<em class="hasLeft">24</em>日（火）
							</p>
							<div class="flightSummaryRoute">
									<div class="flightSummaryRouteSection">
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation1_1">
													<span>東京(成田)
													</span>
												</p>
												<p class="time">
													<span>17:00
													</span>
												</p>
											</div>
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation1_1">
													<span>シカゴ(ORD)
													</span>
												</p>
												<p class="time">
													<span>14:55
													</span>
												</p>
											</div>
										<p class="flightNumberClass">
												<span>NH012</span><span><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/flight_ana_summaryarea.png?717d3c0" alt="ANA運航便" height="11" width="41" /></span>
												<br />特典ビジネス
										</p>
									</div>
							</div>
						</div>
						<div class="flightSummarySection">
							<h2 class="midstream">区間 2
							</h2>
							<p class="flightDate"><em>7</em>月<em class="hasLeft">23</em>日（水）
							</p>
							<div class="flightSummaryRoute">
									<div class="flightSummaryRouteSection">
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation2_1">
													<span>ホノルル(オアフ島)
													</span>
												</p>
												<p class="time">
													<span>11:35
													</span>
												</p>
											</div>
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation2_1">
													<span>東京(成田)
													</span>
												</p>
												<p class="time">
													<span>14:50
																	<span>翌日
																	</span>
													</span>
												</p>
											</div>
										<p class="flightNumberClass">
												<span>NH183</span><span><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/flight_ana_summaryarea.png?717d3c0" alt="ANA運航便" height="11" width="41" /></span>
												<br />特典エコノミー
										</p>
									</div>
							</div>
						</div>
					<div class="total">
						<p class="passenger">大人 x1
						</p>
						<div class="totalArea">
					  		<dl class="mealAmountWrap">
									<dt>有料機内食サービス
									</dt>
									<dd><em class="mealAmount">0</em><span class="currencyCode">円</span>
									</dd>
							</dl>
									<div>
										<div class="mileageAndTotal">
												<dl class="requiredMileage">
													<dt>必要マイル
													</dt>
													<dd><em class="requiredMileage">74,000</em><span class="currencyCode">マイル</span>
													</dd>
												</dl>
												<dl>
													<dt>総額
													</dt>
													<dd><em class="awardTotalPayment">68,130</em>円
													</dd>
												</dl>
												<p class="includeFareAndFuelCost">※各種税金、燃油特別付加運賃等を含みます。
												</p>
										</div>
									</div>
						</div>
					</div>
				
			</div></div>
		<!--===== /cmnWrapper =====-->
		</div>
			
		<span id="message" class="visuallyHidden" role="alert" aria-live="assertive"></span>
		<span id="politeMessage" class="visuallyHidden" role="log" aria-live="polite"></span><script type="text/javascript">
		var Asw = Asw || {};
		Asw.BaseOutput = {"sessionID":"NTeH3vWDxa","language":"ja","siteCatalystLanguageCode":"J","operationDate":"20250619","operationDateTime":"20250619200735","office":{"officeCode":"TYONH08ZZ","country2letterCode":"JP"},"bookingType":"A","pageID":"A04_P01","siteCatalystPageName":"INT_BE_AWARD_J_A04特典予約_P01旅客情報入力（必須情報）","device":{"deviceType":"PC"}};
	</script><div id="promotionArea">
		<!--===== promotionArea =====-->
		<!--===== /promotionArea =====--></div>
		<!--=========== /cmnContainer ============--></div><div id="cmnBaloonArea" aria-live="polite"></div><div id="cmnPopupArea" aria-live="polite"></div>

	

	<div id="cmnFooterWrapper">
		<!--=========== .cmnFooterWrapper ============-->
		<div id="cmnFooter">
			<!--===== cmnFooter =====-->
			<ul>
				<li><a href="https://www.ana.co.jp/other/int/meta/0012.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">プライバシーポリシー<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
					<li><a href="https://www.ana.co.jp/other/int/meta/0670.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">利用者情報の外部送信について<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
					</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/0052.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">サイト利用規約<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/be0238.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">推奨環境<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/0133.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">サイトマップ<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/0732.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">運送約款<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
					<li class="lastChild"><a href="https://www.ana.co.jp/other/int/meta/0556.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">ウェブアクセシビリティについて<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
					</li>
			</ul>
			<p class="blankText" aria-hidden="true"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="" height="10" width="10" />外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。</p>
			<p class="copyright"><span lang="en" xml:lang="en">Copyright&#169;ANA</span>
			</p>
			<p class="starAllianceLogo"><a href="http://www.staralliance.com/ja/" target="_blank"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/star_alliance_logo.png?0fe73b5" alt="A STAR ALLIANCE MEMBER" height="18" width="175" /><span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
			</p>
			<!--===== cmnFooter =====-->
		</div>
		<!--=========== /cmnFooterWrapper ============-->
	</div>

		
		<div id="cmnLoadingForAjax"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/bg_modalWindow.png?717d3c0" alt="" height="0" width="0" />
			<div class="loadingArea">
				<p class="loadingImage"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/loading.gif?717d3c0" alt="リクエストを処理しています...しばらくお待ちください。" height="33" width="33" />
				</p>
			</div>
		</div>
		
		
		<div id="popupAltSentence" style="display: none" aria-hidden="true">別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。</div>
	<div id="cmnSiteCatalyst" aria-hidden="true"><div id="cmnSiteCatalystParamArea"><script type="text/javascript">
				// ▼SiteCatalyst
				// SiteCatalyst code version: H.2.
				// Copyright 1997-2005 Omniture, Inc. More info available at http://www.omniture.com
				SiteCatalystReportSuites   = "INT";
				SiteCatalystCharSet        = "UTF-8";
				SiteCatalystChannel        = "BE_AWARD_J";
				SiteCatalystPageName       = "INT_BE_AWARD_J_A04\u7279\u5178\u4E88\u7D04_P01\u65C5\u5BA2\u60C5\u5831\u5165\u529B\uFF08\u5FC5\u9808\u60C5\u5831\uFF09";
				SiteCatalystERR            = "";
				SiteCatalystEventNum       = "";
				SiteCatalystPurchase       = "FALSE";
				SiteCatalystProductPrice   = "";
				SiteCatalystCurrencyCode   = "";
				SiteCatalystSeatAvailabilityINT   = "";
				SiteCatalystDepartDate     = "";
				SiteCatalystDepart         = "";
				SiteCatalystArrive         = "";
				SiteCatalystFlightNo       = "";
				SiteCatalystClass          = "";
				SiteCatalystFare           = "";
				SiteCatalystPurchaseID     = "";
				
				SiteCatalystLocater        = "";
				SiteCatalystPayment        = "";
				SiteCatalystIncome         = "";
				
				SiteCatalystPax            = "";
				SiteCatalystManipulateDay  = "";
				SiteCatalystCompartmentClass   = "";
				SiteCatalystOptionInfo     = "";
				SiteCatalystOutboundUpsellPrice   = "";
				SiteCatalystInboundUpsellPrice    = "";
				
				SiteCatalystSearchInput    = "";
				SiteCatalystSegment        = "";
				SiteCatalystOfficeCode     = "";
				SiteCatalystPassengerCount = "";
				SiteCatalystPassenger      = "";
				SiteCatalystAncillaryService = "";
				// End SiteCatalyst code version: H.2.
				// ▲SiteCatalyst
			</script></div><script type="text/javascript"><!-- if(navigator.appVersion.indexOf('MSIE')>=0)document.write(unescape('%3C')+'\!-'+'-') // --></script>
		<!--/DO NOT REMOVE/-->
		

		
		<!-- Google Tag Manager -->
		<script type="text/javascript" charset="Shift_JIS" src="https://www.ana.co.jp/common/js/GTMINTBE.js"></script>
		<!-- End Google Tag Manager -->
		<!-- Yahoo Tag Manager -->
		<script type="text/javascript" charset="Shift_JIS" src="https://www.ana.co.jp/common/js/YTMINTBE.js"></script>
		<!-- End Yahoo Tag Manager -->
		

	</div>

	

	<div class="mboxDefault"></div><script type="text/javascript">mboxCreate("ASW_common_A04_P01");</script>
			<script type="text/javascript" src="https://www.ana.co.jp/common/js/tealium/tealium.js"></script>
			<script type="text/javascript" src="//cdn.evgnet.com/beacon/allnipponairways/nonamcprd/scripts/evergage.min.js"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/javax.faces.resource/jsf.js.xhtml?ln=javax.faces"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/jquery.mousewheel.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/jquery.jscrollpane.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/jquery-ui-1.10.4.custom.min.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/jquery.ui.touch-punch.min.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/asw-common.js?beb6a50"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/asw-common-pc.js?fd88d7d"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/asw-format.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/mask.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/loading.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/asw-loadingwindow-pc.js?9e233c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/asw-loadingwindow.js?51db600"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/dialog-pc.js?25d36a4"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/asw-dialog-sequencer.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/asw-event.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/asw-date-select.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/asw-calendar-pc.js?9b86b8a"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/asw-calendar.js?db624c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/heightLine.js?0e7a1ce"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/asw-summaryarea.js?b21c6d7"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/asw-fingerprints.js?99001fc"></script><script type="text/javascript">
			Asw.init("https://aswbe-i.ana.co.jp/rei21g/international_asw", "ja");
			Asw.getCalendarHolidayList = function() {return [20250721,20250811,20250915,20250923,20251013,20251103,20251123,20251124,20260101,20260112,20260211,20260223,20260320,20260429,20260503,20260504,20260505,20260506,20260720,20260811,20260921,20260922,20260923,20261012,20261103,20261123,20270101,20270111,20270211,20270223,20270321,20270322,20270429,20270503,20270504,20270505,20270719,20270811,20270920,20270923,20271011,20271103,20271123,20280101,20280110,20280211,20280223,20280320,20280429,20280503,20280504,20280505,20280717,20280811,20280918,20280922,20281009,20281103,20281123,20290101,20290108,20290211,20290212,20290223,20290320,20290429,20290430,20290503,20290504,20290505,20290716,20290811,20290917,20290923,20290924,20291008,20291103,20291123];}
			Asw.ClientInfo.deviceType = "PC";
			Asw.ClientInfo.mobileDeviceType = "UNKNOWN";
			Asw.ClientInfo.osVersion = "";
			Asw.Format.init("https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/{1}/{2}/{3}?{0}");
			Asw.overwriteTargetAttributeValue("false");
		</script><script type="text/javascript">
		
		var Asw = Asw || {};
		Asw.ContactInput = Asw.ContactInput || {};
		Asw.ContactInput.Const = {
			/** data-属性名「data-type」 @constant @type {String} @default 'data-type' */
			DATA_ATTRIBUTE_NAME_TYPE: 'data-type',
			/** data-属性名「data-sms-possible」 @constant @type {String} @default 'data-sms-possible' */
			DATA_ATTRIBUTE_NAME_SMS_POSSIBLE: 'data-sms-possible',
			/** data-属性名「data-country-code」 @constant @type {String} @default 'data-country-code' */
			DATA_ATTRIBUTE_NAME_COUNTRY_CODE: 'data-country-code',
			/** data-type属性値「sms」 @constant @type {String} @default 'sms' */
			DATA_TYPE_VALUE_SMS: 'sms'
		};

		
		Asw.ContactInput.countryInfoMap = {"IS":{"internationalCallingCode":"+354","smsPossible":false},"IE":{"internationalCallingCode":"+353","smsPossible":true},"AZ":{"internationalCallingCode":"+994","smsPossible":true},"AF":{"internationalCallingCode":"+93","smsPossible":true},"US":{"internationalCallingCode":"+1","smsPossible":true},"AE":{"internationalCallingCode":"+971","smsPossible":true},"DZ":{"internationalCallingCode":"+213","smsPossible":true},"AR":{"internationalCallingCode":"+54","smsPossible":true},"AW":{"internationalCallingCode":"+297","smsPossible":true},"AL":{"internationalCallingCode":"+355","smsPossible":true},"AM":{"internationalCallingCode":"+374","smsPossible":true},"AO":{"internationalCallingCode":"+244","smsPossible":false},"AG":{"internationalCallingCode":"+1","smsPossible":true},"AD":{"internationalCallingCode":"+376","smsPossible":true},"YE":{"internationalCallingCode":"+967","smsPossible":true},"GB":{"internationalCallingCode":"+44","smsPossible":true},"IL":{"internationalCallingCode":"+972","smsPossible":true},"IT":{"internationalCallingCode":"+39","smsPossible":true},"IQ":{"internationalCallingCode":"+964","smsPossible":true},"IR":{"internationalCallingCode":"+98","smsPossible":true},"IN":{"internationalCallingCode":"+91","smsPossible":true},"ID":{"internationalCallingCode":"+62","smsPossible":true},"UG":{"internationalCallingCode":"+256","smsPossible":true},"UA":{"internationalCallingCode":"+380","smsPossible":true},"UZ":{"internationalCallingCode":"+998","smsPossible":true},"UY":{"internationalCallingCode":"+598","smsPossible":true},"EC":{"internationalCallingCode":"+593","smsPossible":true},"EG":{"internationalCallingCode":"+20","smsPossible":true},"EE":{"internationalCallingCode":"+372","smsPossible":true},"SZ":{"internationalCallingCode":"+268","smsPossible":false},"ET":{"internationalCallingCode":"+251","smsPossible":false},"ER":{"internationalCallingCode":"+291","smsPossible":true},"SV":{"internationalCallingCode":"+503","smsPossible":true},"AU":{"internationalCallingCode":"+61","smsPossible":true},"AT":{"internationalCallingCode":"+43","smsPossible":true},"OM":{"internationalCallingCode":"+968","smsPossible":true},"NL":{"internationalCallingCode":"+31","smsPossible":true},"GH":{"internationalCallingCode":"+233","smsPossible":true},"CV":{"internationalCallingCode":"+238","smsPossible":false},"GY":{"internationalCallingCode":"+592","smsPossible":true},"KZ":{"internationalCallingCode":"+7","smsPossible":true},"QA":{"internationalCallingCode":"+974","smsPossible":true},"CA":{"internationalCallingCode":"+1","smsPossible":true},"GA":{"internationalCallingCode":"+241","smsPossible":true},"CM":{"internationalCallingCode":"+237","smsPossible":true},"KR":{"internationalCallingCode":"+82","smsPossible":true},"KH":{"internationalCallingCode":"+855","smsPossible":true},"GM":{"internationalCallingCode":"+220","smsPossible":true},"KP":{"internationalCallingCode":"+850","smsPossible":false},"MK":{"internationalCallingCode":"+389","smsPossible":false},"GN":{"internationalCallingCode":"+224","smsPossible":true},"GW":{"internationalCallingCode":"+245","smsPossible":true},"CY":{"internationalCallingCode":"+357","smsPossible":true},"CU":{"internationalCallingCode":"+53","smsPossible":true},"GR":{"internationalCallingCode":"+30","smsPossible":true},"KI":{"internationalCallingCode":"+686","smsPossible":false},"KG":{"internationalCallingCode":"+996","smsPossible":true},"GT":{"internationalCallingCode":"+502","smsPossible":true},"GU":{"internationalCallingCode":"+1","smsPossible":true},"KW":{"internationalCallingCode":"+965","smsPossible":true},"KY":{"internationalCallingCode":"+1","smsPossible":true},"GD":{"internationalCallingCode":"+1","smsPossible":true},"HR":{"internationalCallingCode":"+385","smsPossible":true},"KE":{"internationalCallingCode":"+254","smsPossible":true},"CI":{"internationalCallingCode":"+225","smsPossible":true},"CR":{"internationalCallingCode":"+506","smsPossible":true},"SA":{"internationalCallingCode":"+966","smsPossible":true},"KM":{"internationalCallingCode":"+269","smsPossible":true},"CO":{"internationalCallingCode":"+57","smsPossible":true},"CG":{"internationalCallingCode":"+242","smsPossible":false},"CD":{"internationalCallingCode":"+243","smsPossible":true},"ZM":{"internationalCallingCode":"+260","smsPossible":true},"SM":{"internationalCallingCode":"+378","smsPossible":false},"SL":{"internationalCallingCode":"+232","smsPossible":true},"DJ":{"internationalCallingCode":"+253","smsPossible":false},"JM":{"internationalCallingCode":"+1","smsPossible":true},"CH":{"internationalCallingCode":"+41","smsPossible":true},"GE":{"internationalCallingCode":"+995","smsPossible":true},"SE":{"internationalCallingCode":"+46","smsPossible":true},"SY":{"internationalCallingCode":"+963","smsPossible":true},"ES":{"internationalCallingCode":"+34","smsPossible":true},"SG":{"internationalCallingCode":"+65","smsPossible":true},"LK":{"internationalCallingCode":"+94","smsPossible":true},"SK":{"internationalCallingCode":"+421","smsPossible":true},"SI":{"internationalCallingCode":"+386","smsPossible":true},"ZW":{"internationalCallingCode":"+263","smsPossible":true},"SD":{"internationalCallingCode":"+249","smsPossible":true},"KN":{"internationalCallingCode":"+1","smsPossible":true},"SR":{"internationalCallingCode":"+597","smsPossible":true},"LC":{"internationalCallingCode":"+1","smsPossible":true},"SC":{"internationalCallingCode":"+248","smsPossible":false},"SN":{"internationalCallingCode":"+221","smsPossible":true},"SB":{"internationalCallingCode":"+677","smsPossible":false},"RS":{"internationalCallingCode":"+381","smsPossible":true},"TH":{"internationalCallingCode":"+66","smsPossible":true},"TW":{"internationalCallingCode":"+886","smsPossible":true},"TJ":{"internationalCallingCode":"+992","smsPossible":true},"TZ":{"internationalCallingCode":"+255","smsPossible":true},"CZ":{"internationalCallingCode":"+420","smsPossible":true},"TD":{"internationalCallingCode":"+235","smsPossible":true},"CF":{"internationalCallingCode":"+236","smsPossible":true},"TN":{"internationalCallingCode":"+216","smsPossible":true},"CL":{"internationalCallingCode":"+56","smsPossible":true},"TV":{"internationalCallingCode":"+688","smsPossible":false},"DK":{"internationalCallingCode":"+45","smsPossible":true},"DE":{"internationalCallingCode":"+49","smsPossible":true},"TG":{"internationalCallingCode":"+228","smsPossible":true},"DO":{"internationalCallingCode":"+1","smsPossible":true},"DM":{"internationalCallingCode":"+1","smsPossible":false},"TT":{"internationalCallingCode":"+1","smsPossible":true},"TM":{"internationalCallingCode":"+993","smsPossible":false},"TR":{"internationalCallingCode":"+90","smsPossible":true},"TO":{"internationalCallingCode":"+676","smsPossible":false},"NG":{"internationalCallingCode":"+234","smsPossible":true},"NR":{"internationalCallingCode":"+674","smsPossible":false},"NA":{"internationalCallingCode":"+264","smsPossible":false},"NI":{"internationalCallingCode":"+505","smsPossible":true},"BH":{"internationalCallingCode":"+973","smsPossible":false},"NE":{"internationalCallingCode":"+227","smsPossible":true},"PK":{"internationalCallingCode":"+92","smsPossible":true},"JP":{"internationalCallingCode":"+81","smsPossible":true},"BS":{"internationalCallingCode":"+1","smsPossible":true},"NZ":{"internationalCallingCode":"+64","smsPossible":true},"BM":{"internationalCallingCode":"+1","smsPossible":true},"NP":{"internationalCallingCode":"+977","smsPossible":true},"BB":{"internationalCallingCode":"+246","smsPossible":true},"NO":{"internationalCallingCode":"+47","smsPossible":true},"HT":{"internationalCallingCode":"+509","smsPossible":true},"BD":{"internationalCallingCode":"+880","smsPossible":true},"VA":{"internationalCallingCode":"+39","smsPossible":false},"PA":{"internationalCallingCode":"+507","smsPossible":true},"VU":{"internationalCallingCode":"+678","smsPossible":false},"PG":{"internationalCallingCode":"+675","smsPossible":true},"PW":{"internationalCallingCode":"+680","smsPossible":true},"PY":{"internationalCallingCode":"+595","smsPossible":true},"PS":{"internationalCallingCode":"+970","smsPossible":true},"BR":{"internationalCallingCode":"+55","smsPossible":true},"HU":{"internationalCallingCode":"+36","smsPossible":true},"TL":{"internationalCallingCode":"+670","smsPossible":true},"BG":{"internationalCallingCode":"+359","smsPossible":true},"BF":{"internationalCallingCode":"+226","smsPossible":true},"BN":{"internationalCallingCode":"+673","smsPossible":true},"FJ":{"internationalCallingCode":"+679","smsPossible":true},"PH":{"internationalCallingCode":"+63","smsPossible":true},"FI":{"internationalCallingCode":"+358","smsPossible":true},"BT":{"internationalCallingCode":"+975","smsPossible":true},"BY":{"internationalCallingCode":"+375","smsPossible":true},"BZ":{"internationalCallingCode":"+501","smsPossible":false},"PR":{"internationalCallingCode":"+1","smsPossible":true},"BE":{"internationalCallingCode":"+32","smsPossible":true},"BA":{"internationalCallingCode":"+387","smsPossible":true},"FR":{"internationalCallingCode":"+33","smsPossible":true},"BO":{"internationalCallingCode":"+591","smsPossible":false},"PF":{"internationalCallingCode":"+689","smsPossible":true},"BI":{"internationalCallingCode":"+257","smsPossible":true},"VN":{"internationalCallingCode":"+84","smsPossible":true},"BJ":{"internationalCallingCode":"+229","smsPossible":true},"VE":{"internationalCallingCode":"+58","smsPossible":true},"PE":{"internationalCallingCode":"+51","smsPossible":true},"PL":{"internationalCallingCode":"+48","smsPossible":true},"BW":{"internationalCallingCode":"+267","smsPossible":true},"PT":{"internationalCallingCode":"+351","smsPossible":false},"HK":{"internationalCallingCode":"+852","smsPossible":true},"HN":{"internationalCallingCode":"+504","smsPossible":false},"MH":{"internationalCallingCode":"+692","smsPossible":true},"MO":{"internationalCallingCode":"+853","smsPossible":true},"MG":{"internationalCallingCode":"+261","smsPossible":true},"MW":{"internationalCallingCode":"+265","smsPossible":true},"ML":{"internationalCallingCode":"+223","smsPossible":true},"MT":{"internationalCallingCode":"+356","smsPossible":true},"MY":{"internationalCallingCode":"+60","smsPossible":true},"FM":{"internationalCallingCode":"+691","smsPossible":false},"MM":{"internationalCallingCode":"+95","smsPossible":true},"MX":{"internationalCallingCode":"+52","smsPossible":true},"MU":{"internationalCallingCode":"+230","smsPossible":true},"MR":{"internationalCallingCode":"+222","smsPossible":true},"MZ":{"internationalCallingCode":"+258","smsPossible":true},"MC":{"internationalCallingCode":"+377","smsPossible":true},"MV":{"internationalCallingCode":"+960","smsPossible":true},"MD":{"internationalCallingCode":"+373","smsPossible":false},"RO":{"internationalCallingCode":"+40","smsPossible":true},"MA":{"internationalCallingCode":"+212","smsPossible":true},"MN":{"internationalCallingCode":"+976","smsPossible":true},"ME":{"internationalCallingCode":"+382","smsPossible":true},"JO":{"internationalCallingCode":"+962","smsPossible":true},"LA":{"internationalCallingCode":"+856","smsPossible":true},"RU":{"internationalCallingCode":"+7","smsPossible":true},"LV":{"internationalCallingCode":"+371","smsPossible":true},"LT":{"internationalCallingCode":"+370","smsPossible":true},"LY":{"internationalCallingCode":"+218","smsPossible":true},"GQ":{"internationalCallingCode":"+240","smsPossible":false},"LI":{"internationalCallingCode":"+423","smsPossible":false},"LR":{"internationalCallingCode":"+231","smsPossible":true},"CN":{"internationalCallingCode":"+86","smsPossible":true},"LU":{"internationalCallingCode":"+352","smsPossible":true},"ZA":{"internationalCallingCode":"+27","smsPossible":true},"RW":{"internationalCallingCode":"+250","smsPossible":true},"LS":{"internationalCallingCode":"+266","smsPossible":false},"LB":{"internationalCallingCode":"+961","smsPossible":true}};

		$(function () {
			contactInfoInit();
		});
		
		function contactInfoInit() {
			
			
			Asw.get('representativeTelType').find('option[value="M1"]').attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_TYPE,
					Asw.ContactInput.Const.DATA_TYPE_VALUE_SMS);
			Asw.get('representativeTelCountry').children('option').each(function(i,option){
				var countryInfo = Asw.ContactInput.countryInfoMap[option.value];
				if (countryInfo === void 0) {
					
					return true;
				}
				$(option).attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_SMS_POSSIBLE, countryInfo.smsPossible);
				$(option).attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_COUNTRY_CODE, countryInfo.internationalCallingCode);
			});
			
			var smsCountrySelectSelector = 'select.js-countrySelect:not(#representativeTelCountry)'
			$(smsCountrySelectSelector).eq(0).children('option').each(function(i,option){
				var countryInfo = Asw.ContactInput.countryInfoMap[option.value];
				if (countryInfo === void 0) {
					
					return true;
				}
				$(smsCountrySelectSelector + ' option:nth-child(' + (i+1) + ')').attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_COUNTRY_CODE,
						countryInfo.internationalCallingCode);
			});
			
			var emergencyCountrySelectSelector = 'select.passengerEmergencyCountry.js-countrySelect:not(#representativeTelCountry)'
			$(emergencyCountrySelectSelector).eq(0).children('option').each(function(i,option){
				var countryInfo = Asw.ContactInput.countryInfoMap[option.value];
				if (countryInfo === void 0) {
					
					return true;
				}
				$(emergencyCountrySelectSelector + ' option:nth-child(' + (i+1) + ')').attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_COUNTRY_CODE,
						countryInfo.internationalCallingCode);
			});
		}
	</script><script type="text/javascript">
		var isInit = true;
		function changeTelType(_this, telNoInputId) {
			var $telNoInput = Asw.get(telNoInputId);
			var $telNoKind = Asw.get("representativeTelType");
			var telNo = Asw.getValueComparedToPlaceholder(telNoInputId);
			var telNoCountry = Asw.get("representativeTelCountry").val();

			if(true && !isInit) {
				if(telInputCheck(telNo, telNoCountry)) {
					if (_this.value == "M1") {
						telNo = exceptDomesticPrefix("09042504176", telNoCountry);
					} else if (_this.value == "B1") {
						telNo = exceptDomesticPrefix("03-5405-2172", telNoCountry);
					} else {
						telNo = exceptDomesticPrefix("090-4250-4176", telNoCountry);
					}
					$telNoInput.val(telNo);
				}
			}
			isInit = false;
		}
		
		function telInputCheck(telNo, telNoCountry) {
	        checkResult = false;
	        
	        var amcHndyTelNo = exceptDomesticPrefix("09042504176", telNoCountry);
	        var amcCmpTelNo = exceptDomesticPrefix("03-5405-2172", telNoCountry);
	        var amcTelNo = exceptDomesticPrefix("090-4250-4176", telNoCountry);
	
	        if(telNo == "" || telNo == amcHndyTelNo || telNo == amcCmpTelNo || telNo == amcTelNo) {
	        	checkResult = true;
	        }
	        return checkResult;
    	}
    	
    	function exceptDomesticPrefix(telNo, telNoCountry) {
    		if(telNoCountry == 'JP') {
    			if(telNo.substr(0, 1) === '0') {
    				return telNo.slice(1);
    			}
    		}
    		return telNo;
    	}
	</script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/asw-customerinfomationform.js?6e91953"></script><script type="text/javascript">
		$(document).ready(function(){
				Asw.enableTextWhenCheckBox('.cascadedSwitch');
				Asw.notPasteInput('.jsNotPasteInput');
			$(function(){
				Asw.alignHeightToParentTdElement( '.passengerInfoInputArea' );
				
					Asw.alignHeightToParentTdElement( '.passengerInfoInputText' );
				
				
				$('select.js-countrySelect').trigger('change');
			});
		});
	</script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/asw-session-keeper.js?6ec0730"></script><script type="text/javascript">
			$(document).ready(function() {
				Asw.SessionKeeper.initAndStart("sessionKeeperContainer", "520", "570", "PC");
			});
			</script><script type="text/javascript">
		Asw.addAjaxCallback(function(data) {
			// アコーディオンのメソッドと設定 
			if($('.jsAccordionSwitch .jsTriggerSwitch').length > 0){
				var events = $._data($('.jsAccordionSwitch .jsTriggerSwitch').get(0), "events");
				if(events === undefined || events.click === undefined) {
					Asw.AccordionInfo('.jsAccordionSwitch', {
						'triggerSwitch': '.jsAccordionSwitch .jsTriggerSwitch',
						'listClass': '.jsAccordionSwitchList',
						'visibilityHidden': 'toggleContents'
					});
				}
			}
		});
	</script><script type="text/javascript">
	Asw.Price = Asw.Price || {};
	// 整数の区切り文字
	Asw.Price.separateString = ',';
	
	// 整数の区切り桁数
	Asw.Price.separateDigitNumber = 3;
	
	// 小数点
	Asw.Price.decimalPointString = '.';
	
	// 小数点以降の有効桁数
	Asw.Price.significantDigit = 0;
	
	// 表示金額の単位
	Asw.Price.currencyUnit = '円';
	</script><script type="text/javascript">
			$(document).ready(function() {
				$('#preLoadingArea').css('visibility', 'hidden');
			});
		</script><noscript><img src="https://aswbe-i.ana.co.jp/akam/13/pixel_7e8a3233?a=dD1kMGM3NDc5ZDFhMjhjYjk5OWQ2MDllMWYwMDQ1NjhjZDUxMmMzZWJkJmpzPW9mZg==" style="visibility: hidden; position: absolute; left: -999px; top: -999px;" /></noscript><script type="text/javascript"  src="/RcS9yzCRgrNnM/gY/v0Wf9fQwtpdQ/7NE3X2ruOE2SL9YE/HyNpQmYB/RWt/XOHcnHAU"></script></body>
</html>