# -*-coding: Utf-8 -*-
import re
import sys
import json
import time
import random
import os
import traceback
import threading
import concurrent.futures
from curl_cffi import requests # Use curl_cffi requests
import redis
import redis.asyncio as aredis # <-- 导入 redis.asyncio
import asyncio
import execjs

from loguru import logger
from urllib.parse import urlparse
from flask import Flask, request

# 动态添加父目录到 sys.path 以便找到 proxy_pool_manager
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.append(parent_dir)

# 添加项目根目录到Python路径
import sys
import os
# 当前文件在 web/reese84/main.py，需要回到项目根目录
current_file = os.path.abspath(__file__)  # /path/to/va-docker/web/reese84/main.py
web_dir = os.path.dirname(os.path.dirname(current_file))  # /path/to/va-docker/web
project_root = os.path.dirname(web_dir)  # /path/to/va-docker
sys.path.insert(0, project_root)

# 现在尝试导入
try:
    from proxy_pool_manager import ProxyManager, Proxy
except ImportError:
    print("ERROR: 无法导入 ProxyManager，请确保 proxy_pool_manager.py 文件存在且路径正确")
    print(f"项目根目录: {project_root}")
    print(f"当前Python路径: {sys.path}")
    sys.exit(1)

# 配置日志
def setup_logging():
    """配置日志系统"""
    try:
        logger.remove()
    except ValueError:
        pass 

    try:
        current_script = os.path.abspath(__file__)
        current_dir = os.path.dirname(current_script)
        log_dir = os.path.join(current_dir, "logs")
        os.makedirs(log_dir, exist_ok=True)
        log_file = os.path.join(log_dir, "reese84_main.log")
        
        logger.add(
            log_file, 
            rotation="10 MB", 
            retention="7 days", 
            level="DEBUG",
            backtrace=True,
            diagnose=True,
            enqueue=True
        )
        logger.add(
            sys.stderr,
            colorize=True, 
            level="INFO"
        )
        logger.info("reese84 服务 (令牌生成器) 日志系统初始化完成")
        
    except Exception as e:
        print(f"日志配置过程中发生错误: {e}")
        import traceback
        print(traceback.format_exc())

# Redis 配置 (使用环境变量以支持 Docker 环境)
REDIS_HOST = os.environ.get("REDIS_HOST", "localhost")
REDIS_PORT = int(os.environ.get("REDIS_PORT", 6379))
REDIS_DB = int(os.environ.get("REDIS_DB", 0))
REDIS_PASSWORD = os.environ.get("REDIS_PASSWORD", None)
REDIS_KEY_PREFIX = "reese84:"
REDIS_TOKEN_EXPIRY = 600 # 令牌有效期调整为 30 分钟 (增加到1800秒)

# 全局 Redis 客户端
redis_client = None

def initialize_redis():
    """初始化全局 Redis 连接"""
    global redis_client
    try:
        redis_client = redis.Redis(
            host=REDIS_HOST,
            port=REDIS_PORT,
            db=REDIS_DB,
            password=REDIS_PASSWORD,
            decode_responses=True,
            socket_timeout=5,
            socket_connect_timeout=5
        )
        redis_client.ping()
        logger.success(f"Redis 连接成功: {REDIS_HOST}:{REDIS_PORT}")
        return True
    except Exception as e:
        logger.error(f"Redis 连接失败: {e}")
        redis_client = None
        return False

def get_redis_key(proxy_str):
    """获取代理对应的Redis键名"""
    if not proxy_str:
        return f"{REDIS_KEY_PREFIX}direct"
    return f"{REDIS_KEY_PREFIX}{proxy_str}"

def save_token_to_cache(proxy_str, token, ua, expiry=REDIS_TOKEN_EXPIRY):
    """将令牌和UA保存到Redis缓存 (作为JSON)"""
    if not redis_client or not token or not ua:
        logger.warning(f"跳过保存到 Redis：redis_client 不可用或 token/ua 为空 (代理: {proxy_str}) ")
        return False
    
    key = get_redis_key(proxy_str)
    try:
        logger.debug(f"准备序列化并保存令牌到 Redis. Key: {key}, Expiry: {expiry}s")
        data_to_cache = {'token': token, 'ua': ua}
        data_json = json.dumps(data_to_cache)
        
        set_result = redis_client.setex(key, expiry, data_json)
        
        if set_result:
            logger.success(f"令牌和UA已成功保存到Redis: {key} (代理: {proxy_str}), 过期时间: {expiry}秒")
            return True
        else:
            logger.error(f"保存令牌和UA到Redis失败: setex 命令未返回成功状态. Key: {key}, 返回值: {set_result}")
            return False
            
    except json.JSONDecodeError as json_err:
        logger.error(f"序列化令牌数据到 JSON 时失败: {json_err}")
        return False
    except redis.exceptions.RedisError as redis_err:
        logger.error(f"保存令牌和UA到Redis时发生 Redis 错误: {redis_err}")
        logger.error(traceback.format_exc())
        return False
    except Exception as e:
        logger.error(f"保存令牌和UA到Redis时发生未知异常: {e}")
        logger.error(traceback.format_exc())
        return False

app = Flask(__name__)

class Reese84Resolve:
    def __init__(self, src, proxy_obj=None):
        self.src = src
        self.proxy_obj = proxy_obj
        self.proxy_str = proxy_obj.proxy_str if proxy_obj else None
        self.proxies = proxy_obj.proxies if proxy_obj else None
        
        script_dir = os.path.dirname(os.path.abspath(__file__))
        js_path = os.path.join(script_dir, 're84_Pro.js')
        with open(js_path, 'r', encoding='utf-8') as f:
            js_code = f.read()
        self.ctx = execjs.compile(js_code)
        
        self.parsed_url = urlparse(self.src)
        self.aih = "P3zzFvDlRg/eIxIRb+nwJEpB63XRusRCTISFW2aZ2Ds="

    def resolve(self):
        """使用 execjs 和 re84_Pro.js 生成 reese84 token 和 ua"""
        try:
            domain = self.parsed_url.netloc
            base_url = self.parsed_url.scheme + "://" + domain
            
            ua_for_js = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"

            logger.debug(f"开始为代理 {self.proxy_str or 'direct'} 执行 execjs...")
            js_start_time = time.perf_counter()

            reese84_data = self.ctx.call(
                "get_reese84",
                self.src,
                self.aih,
                ua_for_js
            )
            
            js_end_time = time.perf_counter()
            logger.debug(f"execjs 执行耗时 for {self.proxy_str or 'direct'} >> {js_end_time - js_start_time:.4f} 秒")

            ua = reese84_data.get('ua')
            if not ua:
                logger.error(f"execjs 未返回 'ua' for {self.proxy_str or 'direct'}")
                return None, None

            headers = {
                "accept": "application/json; charset=utf-8",
                "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
                "cache-control": "no-cache",
                "content-type": "text/plain; charset=utf-8",
                "origin": base_url,
                "pragma": "no-cache",
                "referer": f"{base_url}/dx/VADX/",
                "sec-ch-ua": '"Microsoft Edge";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
                "sec-ch-ua-mobile": "?0",
                "sec-ch-ua-platform": '"Windows"',
                "sec-fetch-dest": "empty",
                "sec-fetch-mode": "cors",
                "sec-fetch-site": "same-origin",
                "user-agent": ua,
            }
            params = {'d': domain}
            data = {
                "solution": {
                    "interrogation": {
                        "p": reese84_data["p"],
                        "st": reese84_data["st"],
                        "sr": reese84_data["sr"],
                        "cr": reese84_data["cr"],
                        "og": 2,
                    },
                    "version": "stable",
                },
                "old_token": None,
                "error": None,
                "performance": {"interrogation": random.randint(100, 999)},
            }
            
            impersonate_browser = "chrome101"  # 与demo程序保持一致

            logger.debug(f"准备发送 POST 请求到: {self.src} (代理: {self.proxy_str or 'direct'}, impersonate={impersonate_browser})")
            post_start_time = time.perf_counter()

            resp = requests.post(
                url=self.src,
                params=params,
                headers=headers,
                json=data,
                proxies=self.proxies,
                timeout=30,
                impersonate=impersonate_browser
            )
            post_end_time = time.perf_counter()
            logger.debug(f"POST 请求耗时 for {self.proxy_str or 'direct'} >> {post_end_time - post_start_time:.4f} 秒, 状态码: {resp.status_code}")

            if resp.status_code == 200:
                try:
                    resp_data = resp.json()
                    token = resp_data.get("token")
                    if token:
                        logger.success(f"成功获取 reese84 for {self.proxy_str or 'direct'}")
                        return token, ua
                    else:
                        logger.error(f"POST 响应成功但未找到 token for {self.proxy_str or 'direct'}: {resp.text[:500]}")
                        return None, None
                except json.JSONDecodeError as e:
                    logger.error(f"解析 POST 响应 JSON 失败 for {self.proxy_str or 'direct'}: {e}, 内容: {resp.text[:200]}")
                    return None, None
            else:
                logger.error(f"获取 reese84 POST 请求失败 for {self.proxy_str or 'direct'}, 状态码: {resp.status_code}, 内容: {resp.text[:200]}")
                return None, None
        
        except Exception as e:
            logger.error(f'获取 reese84 POST 请求异常 for {self.proxy_str or "direct"}: {e}')
            logger.error(traceback.format_exc())
            return None, None


# --- 主动生成逻辑 ---
proxy_manager = None
REESE_TARGET_URL = 'https://book.virginaustralia.com/side-you-ares-may-Exit-sition-Alaruern-Naugmen-G?d=book.virginaustralia.com'
GENERATOR_MAX_WORKERS = 100 # 限制并发数增加到 100
GENERATION_INTERVAL_SECONDS = 10 # 检查频率提高到 10 秒
PROXY_REFRESH_INTERVAL_SECONDS = 60 
processing_proxies = set()
processing_lock = threading.Lock()

def generate_token_for_proxy(proxy):
    """为单个代理生成令牌并缓存"""
    proxy_str = proxy.proxy_str if proxy else "direct"
    with processing_lock:
        if proxy_str in processing_proxies:
            return
        processing_proxies.add(proxy_str)

    try:
        logger.info(f"开始为代理 {proxy_str} 生成令牌...")
        resolver = Reese84Resolve(REESE_TARGET_URL, proxy)
        token, ua = resolver.resolve()

        if token and ua:
            save_token_to_cache(proxy_str, token, ua)
        else:
            logger.warning(f"为代理 {proxy_str} 生成令牌失败")
    except Exception as e:
        logger.error(f"为代理 {proxy_str} 生成令牌时发生异常: {e}")
        logger.error(traceback.format_exc())
    finally:
        with processing_lock:
            processing_proxies.remove(proxy_str)

def token_generator_worker():
    """持续运行的令牌生成器"""
    if not redis_client:
        logger.error("Redis 未连接，令牌生成器无法启动")
        return
    if not proxy_manager:
        logger.error("ProxyManager 未初始化，令牌生成器无法启动")
        return

    executor = concurrent.futures.ThreadPoolExecutor(max_workers=GENERATOR_MAX_WORKERS)
    futures = []
    last_proxy_refresh_time = 0
    active_proxies = []

    logger.info(f"令牌生成器启动，最大并发数: {GENERATOR_MAX_WORKERS}, 检查间隔: {GENERATION_INTERVAL_SECONDS}秒")

    while True:
        now = time.time()

        if now - last_proxy_refresh_time > PROXY_REFRESH_INTERVAL_SECONDS:
            try:
                active_proxies = [p for p in proxy_manager.proxies if not p.disabled]
                logger.info(f"刷新代理列表，当前可用代理数: {len(active_proxies)}")
                last_proxy_refresh_time = now
            except Exception as e:
                logger.error(f"刷新代理列表失败: {e}")
                time.sleep(5)
                continue

        futures = [f for f in futures if not f.done()]

        proxies_to_process = []
        keys_to_check = [get_redis_key(p.proxy_str if p else "direct") for p in active_proxies]

        try:
            if keys_to_check:
                pipe = redis_client.pipeline()
                for key in keys_to_check:
                    pipe.ttl(key)
                ttls = pipe.execute()

                for proxy, ttl in zip(active_proxies, ttls):
                    proxy_str = proxy.proxy_str if proxy else "direct"
                    if ttl is None or ttl < 300:  # 改为5分钟阈值，减少过度生成
                         if proxy_str not in processing_proxies:
                            proxies_to_process.append(proxy)

        except Exception as e:
            logger.error(f"检查 Redis TTL 失败: {e}")
            proxies_to_process = [p for p in active_proxies if (p.proxy_str if p else "direct") not in processing_proxies]

        submitted_count = 0
        for proxy in proxies_to_process:
             if len(futures) < GENERATOR_MAX_WORKERS:
                proxy_str_submit = proxy.proxy_str if proxy else "direct"
                with processing_lock:
                    if proxy_str_submit not in processing_proxies:
                         future = executor.submit(generate_token_for_proxy, proxy)
                         futures.append(future)
                         submitted_count += 1
             else:
                 logger.warning("令牌生成线程池已满，等待下一轮")
                 break

        if submitted_count > 0:
            logger.info(f"提交了 {submitted_count} 个令牌生成任务，当前活动任务: {len(futures)}")

        time.sleep(GENERATION_INTERVAL_SECONDS)

# AsyncTokenFetcher and related constants
TOKEN_TTL_THRESHOLD_SECONDS = 180  # 改为3分钟阈值，减少令牌浪费
MAX_RANDOM_PROXY_ATTEMPTS = 5

class AsyncTokenFetcher:
    """
    Asynchronously fetches valid tokens from Redis cache populated by the background generator.
    """
    def __init__(self, redis_client: aredis.Redis, proxy_manager_instance, logger_instance, loop):
        if not redis_client:
            raise ValueError("redis.asyncio client is required.")
        if not proxy_manager_instance:
             raise ValueError("ProxyManager instance is required.")
        if not loop:
            raise ValueError("Asyncio event loop is required.")
        self.redis_client = redis_client
        self.proxy_manager = proxy_manager_instance
        self.logger = logger_instance
        self.loop = loop

    async def _get_token_data_from_redis(self, proxy_str):
        """Internal helper to get token data and TTL for a specific proxy using aredis."""
        if not proxy_str:
            proxy_str = "direct"
        key = get_redis_key(proxy_str)
        try:
            async with self.redis_client.pipeline(transaction=False) as pipe:
                await pipe.ttl(key)
                await pipe.get(key)
                results = await pipe.execute()

            ttl = results[0]
            data_json = results[1]

            if data_json and ttl is not None and ttl >= TOKEN_TTL_THRESHOLD_SECONDS:
                try:
                    data = json.loads(data_json)
                    token = data.get('token')
                    ua = data.get('ua')
                    if token and ua:
                        self.logger.debug(f"Found valid token for {proxy_str} in Redis (TTL: {ttl})")
                        return token, ua, proxy_str
                    else:
                        self.logger.warning(f"Invalid data format in Redis for key {key}: {data_json}")
                        return None, None, None
                except (json.JSONDecodeError, TypeError) as e:
                    self.logger.error(f"Failed to decode JSON from Redis for key {key}: {e}. Data: {data_json}")
                    return None, None, None
            else:
                return None, None, None
        except redis.exceptions.RedisError as e:
            self.logger.error(f"Redis error when fetching token for {proxy_str}: {e}")
            return None, None, None
        except Exception as e:
             self.logger.error(f"Unexpected error fetching token for {proxy_str} from Redis: {e}")
             return None, None, None

    async def get_token(self, preferred_proxy_str=None):
        """Asynchronously fetches a valid token from Redis."""
        if preferred_proxy_str:
            token, ua, used_proxy = await self._get_token_data_from_redis(preferred_proxy_str)
            if token:
                return token, ua, used_proxy

        try:
            active_proxies = [p for p in self.proxy_manager.proxies if not p.disabled]
            if not active_proxies:
                 self.logger.warning("No active proxies available in ProxyManager.")
                 return None, None, None

            random.shuffle(active_proxies)
            attempts = 0
            for proxy_obj in active_proxies:
                if attempts >= MAX_RANDOM_PROXY_ATTEMPTS:
                    break
                proxy_str_to_try = proxy_obj.proxy_str
                if preferred_proxy_str and proxy_str_to_try == preferred_proxy_str:
                     continue

                token, ua, used_proxy = await self._get_token_data_from_redis(proxy_str_to_try)
                if token:
                    self.logger.info(f"Got token via random proxy: {used_proxy}")
                    return token, ua, used_proxy
                attempts += 1

            self.logger.warning(f"Failed to find a valid token after checking preferred and {attempts} random proxies.")
            return None, None, None
        except Exception as e:
             self.logger.error(f"Error getting active proxies or during random selection: {e}")
             return None, None, None

    async def invalidate_token(self, proxy_str_to_invalidate):
        """Removes the token for the specified proxy from Redis cache."""
        if not proxy_str_to_invalidate:
            proxy_str_to_invalidate = "direct"
        key = get_redis_key(proxy_str_to_invalidate)
        try:
            deleted_count = await self.redis_client.delete(key)
            if deleted_count > 0:
                self.logger.info(f"Invalidated token for proxy {proxy_str_to_invalidate} in Redis (deleted key: {key})")
            else:
                self.logger.warning(f"Attempted to invalidate token for {proxy_str_to_invalidate}, but key {key} was not found in Redis.")
            return True
        except redis.exceptions.RedisError as e:
            self.logger.error(f"Redis error when invalidating token for {proxy_str_to_invalidate}: {e}")
            return False
        except Exception as e:
            self.logger.error(f"Unexpected error invalidating token for {proxy_str_to_invalidate}: {e}")
            return False

if __name__ == '__main__':
    setup_logging()

    if not initialize_redis():
        logger.critical("无法连接到 Redis，程序退出")
        sys.exit(1)

    try:
        config_path = os.path.join(project_root, 'proxy_config.json')
        proxy_manager = ProxyManager(config_file=config_path)
        logger.info(f"ProxyManager 初始化完成，尝试从 {config_path} 加载配置")
        if not proxy_manager.proxies:
             logger.warning("ProxyManager 未能从配置文件加载任何代理，或者配置文件不存在/为空")
        else:
             logger.info(f"从配置文件加载了 {len(proxy_manager.proxies)} 个代理")
    except Exception as e:
        logger.critical(f"初始化 ProxyManager 失败: {e}")
        logger.critical(traceback.format_exc())
        sys.exit(1)

    generator_thread = threading.Thread(target=token_generator_worker, daemon=True)
    generator_thread.start()

    logger.info("令牌生成器已在后台启动。主线程将保持运行。按 Ctrl+C 退出。")
    try:
        while True:
            time.sleep(60)
            logger.debug(f"主线程运行中，生成器线程状态: {'运行中' if generator_thread.is_alive() else '已停止'}")
    except KeyboardInterrupt:
        logger.info("接收到退出信号，程序即将退出...")
    finally:
        logger.info("程序退出") 