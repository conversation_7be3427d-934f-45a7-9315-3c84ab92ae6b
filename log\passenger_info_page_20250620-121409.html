<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="ja-<PERSON>" xml:lang="ja-<PERSON>">

	
	<!-- skey = 2025/06/20 13:14:09 rei22a R6-LixTeIT dljdmx+ab0  --><head id="j_idt53">
    <base href="about:blank">
    <style>
        /* Hide elements that might try to load external resources, as a fallback. */
        img, svg, video, iframe, link[rel="stylesheet"], script { display: none !important; }
    </style>

		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
			<meta http-equiv="X-UA-Compatible" content="IE=EmulateIE9; IE=EmulateIE10" />
			<meta http-equiv="X-UA-Compatible" content="IE=7" />
			<meta http-equiv="X-UA-Compatible" content="IE=edge" />
		<meta name="description" content="" />
		<meta name="keywords" content="" />
		<meta name="format-detection" content="telephone=no" />
		<script type="text/javascript" src="/international_asw/dynatrace/ruxitagentjs_ICANVfhqrux_10273230920145353.js" data-dtconfig="rid=RID_-**********|rpid=-*********|domain=aswbe-i.ana.co.jp|reportUrl=https://bf90419wvr.bf.dynatrace.com/bf|app=78bf0b58acf6ed13|cors=1|owasp=1|featureHash=ICANVfhqrux|xb=www^bs.google-analytics^bs.com^pana^bs.demdex^bs.net^pcollect^bs.tealiumiq^bs.com^pallnipponairways^bs.australia-3^bs.evergage^bs.com^pallnipponairwayscolt^bs.tt^bs.omtrdc^bs.net|rdnt=1|uxrgce=1|cuc=k8vpgh61|mel=100000|expw=1|dpvc=1|md=mdcc1=bAsw.PnrOutput.recordLocator,mdcc2=bAsw.PnrOutput.paymentDetailInfoList[0].fopCode,mdcc3=bAsw.PnrOutput.ticketedFlag,mdcc4=bAsw.PnrOutput.nhPnrFlag,mdcc5=bAsw.PnrOutput.itinerarySummary.departureDateYyyymmdd,mdcc6=bAsw.SearchCriteriaOutput.cffCodeInput,mdcc7=a#main ^rb div.section.sectionNoFlow ^rb table ^rb tbody ^rb tr ^rb td.flightNumber.lastCol ^rb span ^rb span:nth-child(2),mdcc8=cunique|lastModification=1750184416409|tp=500,50,0|agentUri=/international_asw/dynatrace/ruxitagentjs_ICANVfhqrux_10273230920145353.js"></script><link rel="shortcut icon" href="https://www.ana.co.jp/favicon.ico" />
		<title>お客様情報入力 | ANA</title>
		<noscript><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/css/asw_noscript_pc.css?99001fc" />
		</noscript>

		<!--  [ Adobe Target ] -->
		<script type="text/javascript" src="https://www.ana.co.jp/behavior/adobetarget/target_intbe.js"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/script/jquery-1.8.3.min.js?717d3c0"></script><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/ja/css/asw_initialize.css?99001fc" /><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/css/asw_screen_pc.css?99001fc" /><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/css/asw_common_pc.css?99001fc" /><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/css/asw_forminput_pc.css?99001fc" /><script type="text/javascript">
		var Asw = Asw || {};
		Asw.GlobalWord = Asw.GlobalWord || {};
		
		Asw.GlobalWord.toolTipCloseIconAltText = "閉じる";
		
		</script><script >bazadebezolkohpepadr="1916649765"</script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/akam/13/723dc229"  defer></script></head><body>
		
		<noscript>当サイトをご利用になるためには、JavaScript対応のブラウザが必要です。設定でJavaScriptを有効にしてください。有効にできない場合はANA電話窓口へお問い合わせください。
		</noscript>
		<div id="preLoadingArea"></div>
	
	<div id="cmnHeader" class="platinum">
		<!--===== cmnHeader =====-->
		<div class="headerWrapper">
			<div class="header">
				<p class="logo"><a href="https://www.ana.co.jp/asw/AMCTopServlet?type=a" tabindex="-1" aria-hidden="true"><img src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/image/ana_logo_navy.png?717d3c0" alt="ANA Inspiration of JAPAN。リンク先はマイレージクラブトップページです。" height="28" width="274" /></a>
				</p>
					<ul class="headerMenu">
						<li><a href="https://www.ana.co.jp/asw/AMCTopServlet?type=a">ANAマイレージクラブトップページ</a>
						</li>
						<li><a href="https://www.ana.co.jp/other/int/meta/0005.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">お問い合わせ<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
						</li>
							<li class="btnLogoutArea"><a href="https://aswbe-i.ana.co.jp/rei22a/international_asw/rest/logout" role="button" class="btnBase btnLogout">ログアウト</a>
							</li>
					</ul>
			</div>
		</div>
			<div class="amcUserInfoWrapper">
				<div class="amcUserInfo">
					<div class="userNameArea">
						<table>
							<tr>	
									<td class="statusIcon"><img src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/image/icon_status_05.png?717d3c0" alt="プラチナサービスメンバー" height="46" width="60" />
									</td>
								<td class="userName">佐藤 直樹 <span>様</span>
								</td>
							</tr>					
						</table>
					</div>
						<div class="balance">
								<dl class="upgradePointBalance">
									<dt>アップグレードポイント残高
									</dt>
									<dd>
											<dl>
												<dt>(2025年度)
												</dt>
												<dd>
													<em>20
													</em>ポイント
												</dd>
											</dl>
									</dd>
								</dl>
							<dl class="mileBalance">
								<dt>マイル口座残高
								</dt>
								<dd><em>75,663</em><span class="currencyCode">マイル</span>
								</dd>
							</dl>
						</div>
				</div>
					<p class="nowTime">6月20日13時14分現在
					</p>
			</div>
		<!--===== /cmnHeader =====-->
	</div>
		<div id="sessionKeeperContainer" class="sessionKeepingContainer"><img src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/image/bg_modalWindow.png?717d3c0" alt="" height="0" width="0" />
			<div id="extendSessionExpiredDialog" class="modalExpire modalSmall modalContainer" role="dialog" aria-labelledby="extendSessionExpiredDialogTitle" aria-hidden="true">
<form id="sessionKeeperContainer:j_idt183" name="sessionKeeperContainer:j_idt183" method="post" action="https://aswbe-i.ana.co.jp/rei22a/international_asw/pages/award/reservation/award_mandatory_passenger_information_input.xhtml?aswcid=1&amp;rand=20250620131409R6-LixTeIT" autocomplete="off" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="sessionKeeperContainer:j_idt183" value="sessionKeeperContainer:j_idt183" />
<input type="hidden" name="sessionKeeperContainer:j_idt183_operationTicket" value="dljdmx+ab0f014be734f4f8c1a5e1eea3f40fdc~2XCU3qzOsmnLEH5wN4SS1EPVrje152nqB4c5AvOY!1750392840562.aere-xml-controller-67d4778877-cpqbk" /><input type="hidden" name="sessionKeeperContainer:j_idt183_cmnPageTicket" value="4" />
					<h1 id="extendSessionExpiredDialogTitle" class="dialogTitle">まもなくセッションが終了します</h1>
					<div id="extendSessionExpiredDialogMessage" class="dialogMessage" tabindex="-1">このままお手続きを進める場合は、［延長］ボタンを押してください。</div>
					<p class="modalButton"><input id="sessionKeeperContainer:cmnSessionKeepingButton" type="submit" name="sessionKeeperContainer:cmnSessionKeepingButton" value="延長" class="btnBase btnModal btnMainStream" onclick="mojarra.ab(this,event,'action',0,0,{'onevent':function(data) {Asw.doCommonAjaxCallback(data,false, event); Asw.SessionKeeper.doSessionKeep(data);},'onerror':function(data) {Asw.doCommonAjaxErrorCallback(data); }});return false" />
					</p><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:0" value="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***********************************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" autocomplete="off" />
</form>
			</div>
			<div id="showSessionExpiredDialog" class="modalExpire modalSmall modalContainer" role="dialog" aria-labelledby="showSessionExpiredDialogTitle" aria-hidden="true">
				<h1 id="showSessionExpiredDialogTitle" class="dialogTitle">セッションが終了しました</h1>
				<div id="showSessionExpiredDialogMessage" class="dialogMessage" tabindex="-1">一定時間操作されなかったため、お手続きを進めることができません。お手数ですが、再度トップページからお手続きください。</div>
				<p class="modalButton">
					<input type="submit" value="閉じる" class="btnBase btnModal btnMainStream" onclick="Asw.SessionKeeper.closeTimeoutDialog(); return false;" />
				</p>
			</div>
		</div>
		<!--===== dialogMessages =====--><div id="cmnModalMessages"></div>
		<div id="prebookConfirmDialog" class="modalContainer modalConfirm modalSmall " aria-hidden="true" role="dialog">
<form id="prebookConfirmDialog:j_idt403" name="prebookConfirmDialog:j_idt403" method="post" action="https://aswbe-i.ana.co.jp/rei22a/international_asw/pages/award/reservation/award_mandatory_passenger_information_input.xhtml?aswcid=1&amp;rand=20250620131409R6-LixTeIT" autocomplete="off" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="prebookConfirmDialog:j_idt403" value="prebookConfirmDialog:j_idt403" />
<input type="hidden" name="prebookConfirmDialog:j_idt403_operationTicket" value="dljdmx+ab0f014be734f4f8c1a5e1eea3f40fdc~2XCU3qzOsmnLEH5wN4SS1EPVrje152nqB4c5AvOY!1750392840562.aere-xml-controller-67d4778877-cpqbk" /><input type="hidden" name="prebookConfirmDialog:j_idt403_cmnPageTicket" value="4" /><div class="dialogMessage" tabindex="0"><p>この後、名・姓の修正や変更ができません。</p><p>「搭乗者選択」欄の氏名が「名」「姓」の順番で表示されていることを再確認ください。</p><p>例）<strong class="highlight">空野 太郎</strong> 様 → MR <strong class="highlight">TARO SORANO</strong></p><p>保安上の理由により、航空券とパスポートのお名前が同じ表記でない場合はご搭乗いただくことができませんので必ず再度ご確認ください。</p></div>
					
					<ul class="modalBtnHorizontal">
							
							<li><input type="submit" name="prebookConfirmDialog:j_idt419" value="戻る" aria-controls="prebookConfirmDialog" class="btnBase btnModal btnCancel" onclick="Asw.Dialog.getInstance('prebookConfirmDialog').close(event, Asw.DialogSequencer.isActiveSequencerNotDisplayedLastDialog());  return false;" />
							</li>
						
						<li class="btnArrowNext"><input type="submit" name="prebookConfirmDialog:j_idt422" value="OK" aria-controls="prebookConfirmDialog" class="btnBase btnModal btnMainStream " onclick="Asw.Dialog.getInstance('prebookConfirmDialog').callOpener('NORMAL', event); return false;" />
						</li>
					</ul><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:1" value="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" autocomplete="off" />
</form>
		</div>
		<!--===== /dialogMessages =====--><div id="cmnContainer">
		<!--=========== .cmnContainer ============--><div id="cmnEmergencyNotice"></div><div id="cmnTitle" class="hgroup ">
		<h1 class="visuallyHidden">お客様情報入力</h1></div>

		<div id="cmnWrapper">
		<!--===== cmnWrapper =====-->
		<ol class="mainFlow" aria-label="完了までの手順、">
			<li><img src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/image/icon_flow_01.png?717d3c0" alt="1" height="20" width="28" />フライト検索</li><!--
			--><li><img src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/image/icon_flow_02_on.png?717d3c0" alt="2" height="20" width="28" /><span class="visuallyHidden">現在のステップ、</span>お客様情報入力</li><!--
			--><li><img src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/image/icon_flow_03.png?717d3c0" alt="3" height="20" width="28" />お支払い情報</li><!--
			--><li class="flowLast"><img src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/image/icon_flow4_04.png?717d3c0" alt="4" height="20" width="28" />完了</li>
		</ol>

	<div id="main">
	<!--=========== main =============-->
		<ul class="iconExample">
				<li class="requiredIconExample">
					<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="" height="7" width="7" /></span>必須項目です	
				</li>
		</ul>
<form id="j_idt1529" name="j_idt1529" method="post" action="https://aswbe-i.ana.co.jp/rei22a/international_asw/pages/award/reservation/award_mandatory_passenger_information_input.xhtml?aswcid=1&amp;rand=20250620131409R6-LixTeIT" autocomplete="off" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="j_idt1529" value="j_idt1529" />
<input type="hidden" name="j_idt1529_operationTicket" value="dljdmx+ab0f014be734f4f8c1a5e1eea3f40fdc~2XCU3qzOsmnLEH5wN4SS1EPVrje152nqB4c5AvOY!1750392840562.aere-xml-controller-67d4778877-cpqbk" /><input type="hidden" name="j_idt1529_cmnPageTicket" value="4" />

<div class="section">
	<h2>搭乗者選択<a href="https://www.ana.co.jp/other/int/meta/0086.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="特典のご利用に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a></h2>
	<table class="passengerInfo">
		<thead>
			<tr>
				
				<th class="name">氏名</th>
				
				<th class="dateOfBirthWide" id="dateOfBirthHeader">生年月日</th>
				
				<th class="gender lastCol" id="genderHeader">性別<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span></th>
				
			</tr>
		</thead>
		<tbody>
				
				<tr>
					<th class="parent_01" id="parentHeader01">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:0:passenger" type="checkbox" name="passengers:0:passenger" checked="checked" data-number="1" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:0:passenger">NAOKI SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader01">1963年06月05日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader01">				
							<span class="formWrapper">
							<label for="passengers:0:gender" class="visuallyHidden">NAOKI SATOの性別</label><select id="passengers:0:gender" name="passengers:0:gender" size="1">	<option value="">選択してください</option>
	<option value="M" selected="selected">男性</option>
	<option value="F">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_02" id="parentHeader02">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:1:passenger" type="checkbox" name="passengers:1:passenger" data-number="2" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:1:passenger">REO SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader02">1995年05月18日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader02">				
							<span class="formWrapper">
							<label for="passengers:1:gender" class="visuallyHidden">REO SATOの性別</label><select id="passengers:1:gender" name="passengers:1:gender" size="1">	<option value="">選択してください</option>
	<option value="M" selected="selected">男性</option>
	<option value="F">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_03" id="parentHeader03">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:2:passenger" type="checkbox" name="passengers:2:passenger" data-number="3" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:2:passenger">ERI SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader03">1963年08月14日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader03">				
							<span class="formWrapper">
							<label for="passengers:2:gender" class="visuallyHidden">ERI SATOの性別</label><select id="passengers:2:gender" name="passengers:2:gender" size="1">	<option value="">選択してください</option>
	<option value="M">男性</option>
	<option value="F" selected="selected">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_04" id="parentHeader04">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:3:passenger" type="checkbox" name="passengers:3:passenger" data-number="4" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:3:passenger">TAISHI SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader04">1992年12月03日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader04">				
							<span class="formWrapper">
							<label for="passengers:3:gender" class="visuallyHidden">TAISHI SATOの性別</label><select id="passengers:3:gender" name="passengers:3:gender" size="1">	<option value="">選択してください</option>
	<option value="M" selected="selected">男性</option>
	<option value="F">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_05" id="parentHeader05">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:4:passenger" type="checkbox" name="passengers:4:passenger" data-number="5" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:4:passenger">HIROMI FUJIMOTO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader05">1961年11月23日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader05">				
							<span class="formWrapper">
							<label for="passengers:4:gender" class="visuallyHidden">HIROMI FUJIMOTOの性別</label><select id="passengers:4:gender" name="passengers:4:gender" size="1">	<option value="">選択してください</option>
	<option value="M">男性</option>
	<option value="F" selected="selected">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
		</tbody>
	</table>
</div>
		<div class="section">
			<h2 class="midstream">連絡先情報
			</h2>
			<h3>代表者
			</h3>
	
	
	
	<div class="formInput">
		<fieldset>
			<legend><label>代表者</label></legend>
			<dl id="contactEmailAddressInfo">
				<dt class="hasRequiredIcon">メールアドレス<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span><a href="https://www.ana.co.jp/other/int/meta/0057.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="メールの宛先と内容に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a>
				</dt>
				<dd>
					<ul class="verticalList">
						<li class="nonMarginTop"><label for="representativeEmail" class="visuallyHidden">代表者メールアドレス</label><input id="representativeEmail" type="text" name="representativeEmail" value="<EMAIL>" class="widthLarge" placeholder="例：<EMAIL>" />
						</li>
						<li>
							<span class="supplementTxt" id="confirmMailAddressForRepresentative">確認のため再度ご入力ください。
							</span><label for="confirmrepresentativeEmail" class="visuallyHidden">確認用メールアドレス</label><input id="confirmrepresentativeEmail" type="text" name="confirmrepresentativeEmail" value="<EMAIL>" class="widthLarge jsNotPasteInput" placeholder="例：<EMAIL>" aria-describedby="confirmMailAddressForRepresentative" />
						</li>
					</ul>
				</dd>
					<dd style="display:none;"><input id="updateAlwaysInfo" type="checkbox" name="updateAlwaysInfo" /><label for="updateAlwaysInfo">ログイン中の会員情報へこのメールアドレスを登録</label>
					</dd>
			</dl>
			<dl>
				<dt>電話番号<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span>
				</dt>
				<dd class="js-countryWrap">
					<ul>
						<li><label for="representativeTelType" class="visuallyHidden">電話番号種別</label><select id="representativeTelType" name="representativeTelType" class="basicWidth js-typeSelect" size="1" onchange="changeTelType(this,'representativeTel')">	<option value="M1" selected="selected">携帯</option>
	<option value="B1">会社</option>
	<option value="H1">自宅</option>
</select>
						</li>
						<li><label for="representativeTelCountry" class="visuallyHidden">電話番号の国/地区選択</label><select id="representativeTelCountry" name="representativeTelCountry" class="js-countrySelect" size="1">	<option value="" data-sms-possible="true" data-country-code="---">選択してください</option>
	<option value="JP" selected="selected" data-sms-possible="true">日本</option>
	<option value="-" data-sms-possible="true" data-country-code="---">-----------</option>
	<option value="IS" data-sms-possible="true">アイスランド</option>
	<option value="IE" data-sms-possible="true">アイルランド</option>
	<option value="AZ" data-sms-possible="true">アゼルバイジャン</option>
	<option value="AF" data-sms-possible="true">アフガニスタン</option>
	<option value="US" data-sms-possible="true">アメリカ合衆国/カナダ</option>
	<option value="AE" data-sms-possible="true">アラブ首長国連邦</option>
	<option value="DZ" data-sms-possible="true">アルジェリア</option>
	<option value="AR" data-sms-possible="true">アルゼンチン</option>
	<option value="AW" data-sms-possible="true">アルバ</option>
	<option value="AL" data-sms-possible="true">アルバニア</option>
	<option value="AM" data-sms-possible="true">アルメニア</option>
	<option value="AO" data-sms-possible="true">アンゴラ</option>
	<option value="AG" data-sms-possible="true">アンティグア・バーブーダ</option>
	<option value="AD" data-sms-possible="true">アンドラ</option>
	<option value="YE" data-sms-possible="true">イエメン</option>
	<option value="GB" data-sms-possible="true">イギリス</option>
	<option value="IL" data-sms-possible="true">イスラエル</option>
	<option value="IT" data-sms-possible="true">イタリア</option>
	<option value="IQ" data-sms-possible="true">イラク</option>
	<option value="IR" data-sms-possible="true">イラン</option>
	<option value="IN" data-sms-possible="true">インド</option>
	<option value="ID" data-sms-possible="true">インドネシア</option>
	<option value="UG" data-sms-possible="true">ウガンダ</option>
	<option value="UA" data-sms-possible="true">ウクライナ</option>
	<option value="UZ" data-sms-possible="true">ウズベキスタン</option>
	<option value="UY" data-sms-possible="true">ウルグアイ</option>
	<option value="EC" data-sms-possible="true">エクアドル</option>
	<option value="EG" data-sms-possible="true">エジプト</option>
	<option value="EE" data-sms-possible="true">エストニア</option>
	<option value="SZ" data-sms-possible="true">エスワティニ</option>
	<option value="ET" data-sms-possible="true">エチオピア</option>
	<option value="ER" data-sms-possible="true">エリトリア</option>
	<option value="SV" data-sms-possible="true">エルサルバドル</option>
	<option value="AU" data-sms-possible="true">オーストラリア</option>
	<option value="AT" data-sms-possible="true">オーストリア</option>
	<option value="OM" data-sms-possible="true">オマーン</option>
	<option value="NL" data-sms-possible="true">オランダ</option>
	<option value="GH" data-sms-possible="true">ガーナ</option>
	<option value="CV" data-sms-possible="true">カーボベルデ</option>
	<option value="GY" data-sms-possible="true">ガイアナ</option>
	<option value="KZ" data-sms-possible="true">カザフスタン</option>
	<option value="QA" data-sms-possible="true">カタール</option>
	<option value="CA" data-sms-possible="true">カナダ/アメリカ合衆国</option>
	<option value="GA" data-sms-possible="true">ガボン</option>
	<option value="CM" data-sms-possible="true">カメルーン</option>
	<option value="KR" data-sms-possible="true">韓国</option>
	<option value="GM" data-sms-possible="true">ガンビア</option>
	<option value="KH" data-sms-possible="true">カンボジア</option>
	<option value="KP" data-sms-possible="true">北朝鮮</option>
	<option value="MK" data-sms-possible="true">北マケドニア</option>
	<option value="GN" data-sms-possible="true">ギニア</option>
	<option value="GW" data-sms-possible="true">ギニアビサウ</option>
	<option value="CY" data-sms-possible="true">キプロス</option>
	<option value="CU" data-sms-possible="true">キューバ</option>
	<option value="GR" data-sms-possible="true">ギリシャ</option>
	<option value="KI" data-sms-possible="true">キリバス</option>
	<option value="KG" data-sms-possible="true">キルギス</option>
	<option value="GT" data-sms-possible="true">グアテマラ</option>
	<option value="GU" data-sms-possible="true">グアム</option>
	<option value="KW" data-sms-possible="true">クウェート</option>
	<option value="GD" data-sms-possible="true">グレナダ</option>
	<option value="HR" data-sms-possible="true">クロアチア</option>
	<option value="KY" data-sms-possible="true">ケイマン諸島</option>
	<option value="KE" data-sms-possible="true">ケニア</option>
	<option value="CI" data-sms-possible="true">コートジボワール</option>
	<option value="CR" data-sms-possible="true">コスタリカ</option>
	<option value="KM" data-sms-possible="true">コモロ</option>
	<option value="CO" data-sms-possible="true">コロンビア</option>
	<option value="CG" data-sms-possible="true">コンゴ共和国</option>
	<option value="CD" data-sms-possible="true">コンゴ民主共和国</option>
	<option value="SA" data-sms-possible="true">サウジアラビア</option>
	<option value="ZM" data-sms-possible="true">ザンビア</option>
	<option value="SM" data-sms-possible="true">サンマリノ</option>
	<option value="SL" data-sms-possible="true">シエラレオネ</option>
	<option value="DJ" data-sms-possible="true">ジブチ</option>
	<option value="JM" data-sms-possible="true">ジャマイカ</option>
	<option value="GE" data-sms-possible="true">ジョージア</option>
	<option value="SY" data-sms-possible="true">シリア</option>
	<option value="SG" data-sms-possible="true">シンガポール</option>
	<option value="ZW" data-sms-possible="true">ジンバブエ</option>
	<option value="CH" data-sms-possible="true">スイス</option>
	<option value="SE" data-sms-possible="true">スウェーデン</option>
	<option value="SD" data-sms-possible="true">スーダン</option>
	<option value="ES" data-sms-possible="true">スペイン</option>
	<option value="SR" data-sms-possible="true">スリナム</option>
	<option value="LK" data-sms-possible="true">スリランカ</option>
	<option value="SK" data-sms-possible="true">スロバキア</option>
	<option value="SI" data-sms-possible="true">スロベニア</option>
	<option value="SC" data-sms-possible="true">セイシェル</option>
	<option value="GQ" data-sms-possible="true">赤道ギニア</option>
	<option value="SN" data-sms-possible="true">セネガル</option>
	<option value="RS" data-sms-possible="true">セルビア</option>
	<option value="KN" data-sms-possible="true">セントクリストファー・ネイビス</option>
	<option value="LC" data-sms-possible="true">セントルシア</option>
	<option value="SB" data-sms-possible="true">ソロモン諸島</option>
	<option value="TH" data-sms-possible="true">タイ</option>
	<option value="TW" data-sms-possible="true">台湾</option>
	<option value="TJ" data-sms-possible="true">タジキスタン</option>
	<option value="TZ" data-sms-possible="true">タンザニア</option>
	<option value="CZ" data-sms-possible="true">チェコ</option>
	<option value="TD" data-sms-possible="true">チャド</option>
	<option value="CF" data-sms-possible="true">中央アフリカ共和国</option>
	<option value="CN" data-sms-possible="true">中国</option>
	<option value="TN" data-sms-possible="true">チュニジア</option>
	<option value="CL" data-sms-possible="true">チリ</option>
	<option value="TV" data-sms-possible="true">ツバル</option>
	<option value="DK" data-sms-possible="true">デンマーク</option>
	<option value="DE" data-sms-possible="true">ドイツ</option>
	<option value="TG" data-sms-possible="true">トーゴ</option>
	<option value="DO" data-sms-possible="true">ドミニカ共和国</option>
	<option value="DM" data-sms-possible="true">ドミニカ国</option>
	<option value="TT" data-sms-possible="true">トリニダード・トバゴ</option>
	<option value="TM" data-sms-possible="true">トルクメニスタン</option>
	<option value="TR" data-sms-possible="true">トルコ</option>
	<option value="TO" data-sms-possible="true">トンガ</option>
	<option value="NG" data-sms-possible="true">ナイジェリア</option>
	<option value="NR" data-sms-possible="true">ナウル</option>
	<option value="NA" data-sms-possible="true">ナミビア</option>
	<option value="NI" data-sms-possible="true">ニカラグア</option>
	<option value="NE" data-sms-possible="true">ニジェール</option>
	<option value="NZ" data-sms-possible="true">ニュージーランド</option>
	<option value="NP" data-sms-possible="true">ネパール</option>
	<option value="NO" data-sms-possible="true">ノルウェー</option>
	<option value="BH" data-sms-possible="true">バーレーン</option>
	<option value="HT" data-sms-possible="true">ハイチ</option>
	<option value="PK" data-sms-possible="true">パキスタン</option>
	<option value="VA" data-sms-possible="true">バチカン</option>
	<option value="PA" data-sms-possible="true">パナマ</option>
	<option value="VU" data-sms-possible="true">バヌアツ</option>
	<option value="BS" data-sms-possible="true">バハマ</option>
	<option value="PG" data-sms-possible="true">パプアニューギニア</option>
	<option value="BM" data-sms-possible="true">バミューダ</option>
	<option value="PW" data-sms-possible="true">パラオ</option>
	<option value="PY" data-sms-possible="true">パラグアイ</option>
	<option value="BB" data-sms-possible="true">バルバドス</option>
	<option value="PS" data-sms-possible="true">パレスチナ</option>
	<option value="HU" data-sms-possible="true">ハンガリー</option>
	<option value="BD" data-sms-possible="true">バングラデシュ</option>
	<option value="TL" data-sms-possible="true">東ティモール</option>
	<option value="FJ" data-sms-possible="true">フィジー</option>
	<option value="PH" data-sms-possible="true">フィリピン</option>
	<option value="FI" data-sms-possible="true">フィンランド</option>
	<option value="BT" data-sms-possible="true">ブータン</option>
	<option value="PR" data-sms-possible="true">プエルトリコ</option>
	<option value="BR" data-sms-possible="true">ブラジル</option>
	<option value="FR" data-sms-possible="true">フランス</option>
	<option value="PF" data-sms-possible="true">フランス領ポリネシア</option>
	<option value="BG" data-sms-possible="true">ブルガリア</option>
	<option value="BF" data-sms-possible="true">ブルキナファソ</option>
	<option value="BN" data-sms-possible="true">ブルネイ</option>
	<option value="BI" data-sms-possible="true">ブルンジ</option>
	<option value="VN" data-sms-possible="true">ベトナム</option>
	<option value="BJ" data-sms-possible="true">ベナン</option>
	<option value="VE" data-sms-possible="true">ベネズエラ</option>
	<option value="BY" data-sms-possible="true">ベラルーシ</option>
	<option value="BZ" data-sms-possible="true">ベリーズ</option>
	<option value="PE" data-sms-possible="true">ペルー</option>
	<option value="BE" data-sms-possible="true">ベルギー</option>
	<option value="PL" data-sms-possible="true">ポーランド</option>
	<option value="BA" data-sms-possible="true">ボスニア・ヘルツェゴビナ</option>
	<option value="BW" data-sms-possible="true">ボツワナ</option>
	<option value="BO" data-sms-possible="true">ボリビア</option>
	<option value="PT" data-sms-possible="true">ポルトガル</option>
	<option value="HK" data-sms-possible="true">香港</option>
	<option value="HN" data-sms-possible="true">ホンジュラス</option>
	<option value="MH" data-sms-possible="true">マーシャル諸島</option>
	<option value="MO" data-sms-possible="true">マカオ</option>
	<option value="MG" data-sms-possible="true">マダガスカル</option>
	<option value="MW" data-sms-possible="true">マラウイ</option>
	<option value="ML" data-sms-possible="true">マリ</option>
	<option value="MT" data-sms-possible="true">マルタ</option>
	<option value="MY" data-sms-possible="true">マレーシア</option>
	<option value="FM" data-sms-possible="true">ミクロネシア連邦</option>
	<option value="ZA" data-sms-possible="true">南アフリカ</option>
	<option value="MM" data-sms-possible="true">ミャンマー</option>
	<option value="MX" data-sms-possible="true">メキシコ</option>
	<option value="MU" data-sms-possible="true">モーリシャス</option>
	<option value="MR" data-sms-possible="true">モーリタニア</option>
	<option value="MZ" data-sms-possible="true">モザンビーク</option>
	<option value="MC" data-sms-possible="true">モナコ</option>
	<option value="MV" data-sms-possible="true">モルディヴ</option>
	<option value="MD" data-sms-possible="true">モルドバ</option>
	<option value="MA" data-sms-possible="true">モロッコ</option>
	<option value="MN" data-sms-possible="true">モンゴル</option>
	<option value="ME" data-sms-possible="true">モンテネグロ</option>
	<option value="JO" data-sms-possible="true">ヨルダン</option>
	<option value="LA" data-sms-possible="true">ラオス人民民主共和国</option>
	<option value="LV" data-sms-possible="true">ラトビア</option>
	<option value="LT" data-sms-possible="true">リトアニア</option>
	<option value="LY" data-sms-possible="true">リビア</option>
	<option value="LI" data-sms-possible="true">リヒテンシュタイン</option>
	<option value="LR" data-sms-possible="true">リベリア</option>
	<option value="RO" data-sms-possible="true">ルーマニア</option>
	<option value="LU" data-sms-possible="true">ルクセンブルク</option>
	<option value="RW" data-sms-possible="true">ルワンダ</option>
	<option value="LS" data-sms-possible="true">レソト</option>
	<option value="LB" data-sms-possible="true">レバノン</option>
	<option value="RU" data-sms-possible="true">ロシア</option>
</select>
						</li>
						<li class="numberInputWrap">
							<span id="numberInputInfo1" class="supplementTxt marginTop">先頭に0(ゼロ)がある場合、0(ゼロ)を抜いてください(一部、国・地域除く)。携帯電話番号の場合は、携帯電話を契約された国を選択ください。</span>
							<div class="numberRowWrap">
								<div class="block">
									
									<span class="visuallyHidden">国番号</span>
									<span class="viewInput widthMini js-countryCode"></span>
								</div>
								<div class="block"><label for="representativeTel" class="visuallyHidden">電話番号</label><input id="representativeTel" type="text" name="representativeTel" value="9042504176" class="widthMiddle" placeholder="例：9012345678" maxlength="30" aria-describedby="numberInputInfo1" />
								</div>
							</div>
						</li>
					</ul>
				</dd>
			</dl>
		</fieldset>
	</div>
	<div class="titleWrapper">
		<h3>
			<span class="text">eメール・SMSに関するご案内<a href="https://www.ana.co.jp/other/int/meta/0057.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="eメール・SMSに関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a></span>
		</h3>
	</div>
	<div class="formInput">
	<h4 class="formAreaTitle">主に搭乗・運航に関わるeメールの送付先設定（米国へ通知いたします）<a href="https://www.ana.co.jp/other/int/meta/0629.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="米国入国用eメール・SMS・電話番号に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a>
	</h4>
	<div class="paxSettingArea js-mailFormWrap onePax is-open">
			<div class="settingArea">
					<dl>
						<dt class="breakWord">NAOKI SATO
						</dt>
						<dd>
							<fieldset>
								<legend>NAOKI SATOのeメール送付先および米国到着時に提供するメールアドレス</legend>代表者と同じ
							</fieldset>
						</dd>
					</dl>
			</div>
	</div>
	<h4 class="formAreaTitle hasTopLine">主に搭乗・運航に関わるSMSの送付先および米国で受信できる電話番号の設定（米国へ通知いたします）<a href="https://www.ana.co.jp/other/int/meta/0629.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="米国入国用eメール・SMS・電話番号に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a>
	</h4>
	<div class="paxSettingArea js-smsFormWrap onePax is-open">
			<div class="settingArea">
					<dl>
						<dt class="breakWord">NAOKI SATO<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span>
						</dt>
						<dd>
							<fieldset>
								<legend>NAOKI SATOの1つ目の電話番号</legend>
									<ul class="verticalList js-formItem">
										<li><input type="radio" id="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSameAsRepresentativeSms:radioItem" name="contactsSms:0:recipientSmsEachPersonSelection:radioGroup" value="0" checked="checked" /><label for="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSameAsRepresentativeSms:radioItem">代表者電話番号と同じ</label>
										</li>
										<li><input type="radio" id="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSmsPerPassenger:radioItem" name="contactsSms:0:recipientSmsEachPersonSelection:radioGroup" value="1" /><label for="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSmsPerPassenger:radioItem">電話番号を指定</label>
											<div class="indentArea js-formImputArea">
												<span id="confirmTelNumberForRepresentative:0" class="supplementTxt marginTop">先頭に0(ゼロ)がある場合、0(ゼロ)を抜いてください(一部、国・地域除く)。携帯電話番号の場合は、携帯電話を契約された国を選択ください。</span>
												<ul class="countryNumberInput js-countryWrap">
													<li><label for="contactsSms:0:recipientSmsEachPersonSelection:passengerSmsCountry" class="visuallyHidden">電話番号の国/地区選択</label><select id="contactsSms:0:recipientSmsEachPersonSelection:passengerSmsCountry" name="contactsSms:0:recipientSmsEachPersonSelection:passengerSmsCountry" class="countrySelect js-countrySelect" size="1">	<option value="" data-country-code="---">選択してください</option>
	<option value="JP">日本</option>
	<option value="-" data-country-code="---">-----------</option>
	<option value="IE">アイルランド</option>
	<option value="AZ">アゼルバイジャン</option>
	<option value="AF">アフガニスタン</option>
	<option value="US">アメリカ合衆国/カナダ</option>
	<option value="AE">アラブ首長国連邦</option>
	<option value="DZ">アルジェリア</option>
	<option value="AR">アルゼンチン</option>
	<option value="AW">アルバ</option>
	<option value="AL">アルバニア</option>
	<option value="AM">アルメニア</option>
	<option value="AG">アンティグア・バーブーダ</option>
	<option value="AD">アンドラ</option>
	<option value="YE">イエメン</option>
	<option value="GB">イギリス</option>
	<option value="IL">イスラエル</option>
	<option value="IT">イタリア</option>
	<option value="IQ">イラク</option>
	<option value="IR">イラン</option>
	<option value="IN">インド</option>
	<option value="ID">インドネシア</option>
	<option value="UG">ウガンダ</option>
	<option value="UA">ウクライナ</option>
	<option value="UZ">ウズベキスタン</option>
	<option value="UY">ウルグアイ</option>
	<option value="EC">エクアドル</option>
	<option value="EG">エジプト</option>
	<option value="EE">エストニア</option>
	<option value="ER">エリトリア</option>
	<option value="SV">エルサルバドル</option>
	<option value="AU">オーストラリア</option>
	<option value="AT">オーストリア</option>
	<option value="OM">オマーン</option>
	<option value="NL">オランダ</option>
	<option value="GH">ガーナ</option>
	<option value="GY">ガイアナ</option>
	<option value="KZ">カザフスタン</option>
	<option value="QA">カタール</option>
	<option value="CA">カナダ/アメリカ合衆国</option>
	<option value="GA">ガボン</option>
	<option value="CM">カメルーン</option>
	<option value="KR">韓国</option>
	<option value="GM">ガンビア</option>
	<option value="KH">カンボジア</option>
	<option value="GN">ギニア</option>
	<option value="GW">ギニアビサウ</option>
	<option value="CY">キプロス</option>
	<option value="CU">キューバ</option>
	<option value="GR">ギリシャ</option>
	<option value="KG">キルギス</option>
	<option value="GT">グアテマラ</option>
	<option value="GU">グアム</option>
	<option value="KW">クウェート</option>
	<option value="GD">グレナダ</option>
	<option value="HR">クロアチア</option>
	<option value="KY">ケイマン諸島</option>
	<option value="KE">ケニア</option>
	<option value="CI">コートジボワール</option>
	<option value="CR">コスタリカ</option>
	<option value="KM">コモロ</option>
	<option value="CO">コロンビア</option>
	<option value="CD">コンゴ民主共和国</option>
	<option value="SA">サウジアラビア</option>
	<option value="ZM">ザンビア</option>
	<option value="SL">シエラレオネ</option>
	<option value="JM">ジャマイカ</option>
	<option value="GE">ジョージア</option>
	<option value="SY">シリア</option>
	<option value="SG">シンガポール</option>
	<option value="ZW">ジンバブエ</option>
	<option value="CH">スイス</option>
	<option value="SE">スウェーデン</option>
	<option value="SD">スーダン</option>
	<option value="ES">スペイン</option>
	<option value="SR">スリナム</option>
	<option value="LK">スリランカ</option>
	<option value="SK">スロバキア</option>
	<option value="SI">スロベニア</option>
	<option value="SN">セネガル</option>
	<option value="RS">セルビア</option>
	<option value="KN">セントクリストファー・ネイビス</option>
	<option value="LC">セントルシア</option>
	<option value="TH">タイ</option>
	<option value="TW">台湾</option>
	<option value="TJ">タジキスタン</option>
	<option value="TZ">タンザニア</option>
	<option value="CZ">チェコ</option>
	<option value="TD">チャド</option>
	<option value="CF">中央アフリカ共和国</option>
	<option value="CN">中国</option>
	<option value="TN">チュニジア</option>
	<option value="CL">チリ</option>
	<option value="DK">デンマーク</option>
	<option value="DE">ドイツ</option>
	<option value="TG">トーゴ</option>
	<option value="DO">ドミニカ共和国</option>
	<option value="TT">トリニダード・トバゴ</option>
	<option value="TR">トルコ</option>
	<option value="NG">ナイジェリア</option>
	<option value="NI">ニカラグア</option>
	<option value="NE">ニジェール</option>
	<option value="NZ">ニュージーランド</option>
	<option value="NP">ネパール</option>
	<option value="NO">ノルウェー</option>
	<option value="HT">ハイチ</option>
	<option value="PK">パキスタン</option>
	<option value="PA">パナマ</option>
	<option value="BS">バハマ</option>
	<option value="PG">パプアニューギニア</option>
	<option value="BM">バミューダ</option>
	<option value="PW">パラオ</option>
	<option value="PY">パラグアイ</option>
	<option value="BB">バルバドス</option>
	<option value="PS">パレスチナ</option>
	<option value="HU">ハンガリー</option>
	<option value="BD">バングラデシュ</option>
	<option value="TL">東ティモール</option>
	<option value="FJ">フィジー</option>
	<option value="PH">フィリピン</option>
	<option value="FI">フィンランド</option>
	<option value="BT">ブータン</option>
	<option value="PR">プエルトリコ</option>
	<option value="BR">ブラジル</option>
	<option value="FR">フランス</option>
	<option value="PF">フランス領ポリネシア</option>
	<option value="BG">ブルガリア</option>
	<option value="BF">ブルキナファソ</option>
	<option value="BN">ブルネイ</option>
	<option value="BI">ブルンジ</option>
	<option value="VN">ベトナム</option>
	<option value="BJ">ベナン</option>
	<option value="VE">ベネズエラ</option>
	<option value="BY">ベラルーシ</option>
	<option value="PE">ペルー</option>
	<option value="BE">ベルギー</option>
	<option value="PL">ポーランド</option>
	<option value="BA">ボスニア・ヘルツェゴビナ</option>
	<option value="BW">ボツワナ</option>
	<option value="HK">香港</option>
	<option value="MH">マーシャル諸島</option>
	<option value="MO">マカオ</option>
	<option value="MG">マダガスカル</option>
	<option value="MW">マラウイ</option>
	<option value="ML">マリ</option>
	<option value="MT">マルタ</option>
	<option value="MY">マレーシア</option>
	<option value="ZA">南アフリカ</option>
	<option value="MM">ミャンマー</option>
	<option value="MX">メキシコ</option>
	<option value="MU">モーリシャス</option>
	<option value="MR">モーリタニア</option>
	<option value="MZ">モザンビーク</option>
	<option value="MC">モナコ</option>
	<option value="MV">モルディヴ</option>
	<option value="MA">モロッコ</option>
	<option value="MN">モンゴル</option>
	<option value="ME">モンテネグロ</option>
	<option value="JO">ヨルダン</option>
	<option value="LA">ラオス人民民主共和国</option>
	<option value="LV">ラトビア</option>
	<option value="LT">リトアニア</option>
	<option value="LY">リビア</option>
	<option value="LR">リベリア</option>
	<option value="RO">ルーマニア</option>
	<option value="LU">ルクセンブルク</option>
	<option value="RW">ルワンダ</option>
	<option value="LB">レバノン</option>
	<option value="RU">ロシア</option>
</select>
													</li>
													<li>
														
														<div class="numberRowWrap">
															<div class="block">
																<span class="visuallyHidden">国番号</span>
																<span class="countryCode viewInput js-countryCode"></span>
															</div>
															
															<div class="block"><label for="contactsSms:0:recipientSmsEachPersonSelection:flightStatusNotificationContactPointSmsDescription" class="visuallyHidden">電話番号</label><input id="contactsSms:0:recipientSmsEachPersonSelection:flightStatusNotificationContactPointSmsDescription" type="text" name="contactsSms:0:recipientSmsEachPersonSelection:flightStatusNotificationContactPointSmsDescription" class="telNumber" placeholder="例：9012345678" maxlength="30" aria-describedby="confirmTelNumberForRepresentative:0" />
															</div>
														</div>
													</li>
												</ul>
											</div>
										</li>
									</ul>
							</fieldset>
						</dd>
					</dl>
			</div>
	</div>
	</div>
	
	<div class="formInput articleLine">
		<h3>滞在中の連絡先(任意)
		</h3>
		<dl>
			
			<dt><label for="accommodationOne">現地連絡先1</label>
			</dt>
			<dd><input id="accommodationOne" type="text" name="accommodationOne" class="widthLarge" placeholder="例：09012345678 ABC HOTEL" maxlength="28" title="例：09012345678 ABC HOTEL" />
			</dd>
		</dl>
		<dl>
			
			<dt><label for="accommodationTwo">現地連絡先2</label>
			</dt>
			<dd><input id="accommodationTwo" type="text" name="accommodationTwo" class="widthLarge" placeholder="例：09012345678 ABC HOTEL" maxlength="28" title="例：09012345678 ABC HOTEL" />
			</dd>
		</dl>
	</div>
		</div>
		
		<p class="btnAreaSubmit btnArrowNext "><input id="next" type="submit" name="next" value="次へ" class="btnBase btnMainStream btnVerticalMain btnWidthVariable" onclick="return Asw.Dialog.getInstance('prebookConfirmDialog').toggle(event);return Asw.LoadingWindow.open(&quot;NORMAL&quot;, event)" />
		</p><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:2" value="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" autocomplete="off" />
</form><div id="cmnDynamicMessages" class="infoBoxSection">
				<dl class="infoBox importantInfo jsAccordionSwitch">
					<dt><strong>重要なご案内</strong></dt>
					<dd>
						<ul class="jsAccordionSwitchList" id="informationMessages">
								<li>個人情報の利用目的<a href="https://www.ana.co.jp/other/int/meta/0012.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="ANAプライバシーポリシー(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">ANAプライバシーポリシー<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>を必ずお読みいただき、内容に同意いただいた上で、お進みください。</li>
								<li><a href="https://www.ana.co.jp/other/int/meta/0058.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="Secure Flight Programについて(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">米国Secure Flightプログラム<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>に基づき、パスポート名、生年月日、性別などの情報は事前に米国へ通知いたします。</li>
								<li>入力したeメールアドレス、電話番号は入国に必要な情報として米国に通知される可能性があります。</li>
									<li class="jsHiddenFlg toggleContents">お名前のスペルがパスポート名と一致しているかご確認ください。異なる場合はインターネットではお取り扱いできません。<a href="https://www.ana.co.jp/other/int/meta/intbe0001.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="ANAお問い合わせ先(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">ANA<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>へ名前の変更をお申し出ください。</li>
									<li class="jsHiddenFlg toggleContents">二重国籍のお客様は、ご搭乗時に使用されるパスポートのお名前でご予約ください。<br />パスポートごとにお名前が異なる場合は、お客様ご自身で入国管理事務所・外務省・大使館などで各種条件をご確認のうえ、使用されるパスポートを決めてください。<br />なお、入国可否については国によって異なるため、お客様ご自身で各入国管理事務所などでご確認ください。</li>
									<li class="jsHiddenFlg toggleContents">旅行開始日時点で12歳以上の場合は大人、2歳以上12歳未満の場合は小児、2歳未満の場合は幼児の扱いとなります。なお、小児は大人と同マイル数が必要です。</li>
									<li class="jsHiddenFlg toggleContents">運航に関する情報をお客様へ通知するため、提携航空会社にもお客様のメールアドレスが提供されます。あらかじめご了承ください。</li>
									<li class="jsHiddenFlg toggleContents"><dl><dt>携帯電話へのメール送信をご希望のお客様へ</dt><dd>ドメイン指定受信をご利用の場合は、ana.co.jpとamadeus.comからのメールが受信できるようあらかじめ設定をお願いいたします。</dd></dl></li>
									<li class="jsHiddenFlg toggleContents">SMSはスマートフォンの番号に配信されます。一部の国ではSMSでのご案内をご利用いただけません。詳細は<a href="https://www.ana.co.jp/other/int/meta/0737.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="ショートメッセージ(SMS)でのご案内対象外の国について(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">ショートメッセージ(SMS)でのご案内対象外の国について<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>をご覧ください。</li>
									<li class="jsHiddenFlg toggleContents">ログインされている会員ご本人が搭乗しない場合は、再度フライト検索画面より「ログインされている会員ご本人は搭乗しない」のチェックボックスを選択してお手続きください。</li>
						</ul>
							<a href="#" class="toggleSwitch jsTriggerSwitch" role="button" aria-controls="informationMessages" aria-expanded="false"><img src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/image/toggle_open.png?7bec38a" alt="開閉ボタン" height="25" width="25" class="jsRollOver" />
							</a>
					</dd>
				</dl></div>
	<!--=========== /main =============-->
	</div>

	<!--=========== Script ============-->
	<script type="text/javascript">
	$(window).on('load', function() {
		// アコーディオンのメソッドと設定
		Asw.AccordionInfo('.jsAccordionSwitch', {
			'triggerSwitch': '.jsAccordionSwitch .jsTriggerSwitch',
			'listClass': '.jsAccordionSwitchList',
			'visibilityHidden': 'toggleContents'
		});
	});
	</script>
	<!--=========== Script ============-->
	<script type="text/javascript">
		//プラットフォーム判別（サマリーエリア追従用として）
		var Asw = Asw || {};
	
	  	Asw.ClientInfo = Asw.ClientInfo  || {};
	  	Asw.ClientInfo.DeviceType = Asw.ClientInfo.DeviceType || {};
	
	  	Asw.ClientInfo.DeviceType.PC = 'PC';
	  	Asw.ClientInfo.DeviceType.SP = 'SP';
	  	Asw.ClientInfo.DeviceType.TC = 'TC';
	
	  	Asw.ClientInfo.deviceType = Asw.ClientInfo.DeviceType.PC;
	
	 	if((navigator.userAgent.indexOf('Android') > 0 && navigator.userAgent.indexOf('Mobile') == -1) || navigator.userAgent.indexOf('iPad') > 0){
	    	Asw.ClientInfo.deviceType = Asw.ClientInfo.DeviceType.TC;
	  	}
	</script>
	<!--=========== /Script ============--><div id="summaryArea">
			<div class="flightSummaryArea">
						<div class="flightSummarySection">
							<h2>区間 1
							</h2>
							<p class="flightDate"><em>6</em>月<em class="hasLeft">28</em>日（土）
							</p>
							<div class="flightSummaryRoute">
									<div class="flightSummaryRouteSection">
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation1_1">
													<span>東京(成田)
													</span>
												</p>
												<p class="time">
													<span>17:00
													</span>
												</p>
											</div>
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation1_1">
													<span>シカゴ(ORD)
													</span>
												</p>
												<p class="time">
													<span>14:55
													</span>
												</p>
											</div>
										<p class="flightNumberClass">
												<span>NH012</span><span><img src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/image/flight_ana_summaryarea.png?717d3c0" alt="ANA運航便" height="11" width="41" /></span>
												<br />特典ビジネス
										</p>
									</div>
							</div>
						</div>
						<div class="flightSummarySection">
							<h2 class="midstream">区間 2
							</h2>
							<p class="flightDate"><em>7</em>月<em class="hasLeft">23</em>日（水）
							</p>
							<div class="flightSummaryRoute">
									<div class="flightSummaryRouteSection">
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation2_1">
													<span>ホノルル(オアフ島)
													</span>
												</p>
												<p class="time">
													<span>11:35
													</span>
												</p>
											</div>
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation2_1">
													<span>東京(成田)
													</span>
												</p>
												<p class="time">
													<span>14:50
																	<span>翌日
																	</span>
													</span>
												</p>
											</div>
										<p class="flightNumberClass">
												<span>NH183</span><span><img src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/image/flight_ana_summaryarea.png?717d3c0" alt="ANA運航便" height="11" width="41" /></span>
												<br />特典エコノミー
										</p>
									</div>
							</div>
						</div>
					<div class="total">
						<p class="passenger">大人 x1
						</p>
						<div class="totalArea">
					  		<dl class="mealAmountWrap">
									<dt>有料機内食サービス
									</dt>
									<dd><em class="mealAmount">0</em><span class="currencyCode">円</span>
									</dd>
							</dl>
									<div>
										<div class="mileageAndTotal">
												<dl class="requiredMileage">
													<dt>必要マイル
													</dt>
													<dd><em class="requiredMileage">74,000</em><span class="currencyCode">マイル</span>
													</dd>
												</dl>
												<dl>
													<dt>総額
													</dt>
													<dd><em class="awardTotalPayment">68,130</em>円
													</dd>
												</dl>
												<p class="includeFareAndFuelCost">※各種税金、燃油特別付加運賃等を含みます。
												</p>
										</div>
									</div>
						</div>
					</div>
				
			</div></div>
		<!--===== /cmnWrapper =====-->
		</div>
			
		<span id="message" class="visuallyHidden" role="alert" aria-live="assertive"></span>
		<span id="politeMessage" class="visuallyHidden" role="log" aria-live="polite"></span><script type="text/javascript">
		var Asw = Asw || {};
		Asw.BaseOutput = {"sessionID":"R6-LixTeIT","language":"ja","siteCatalystLanguageCode":"J","operationDate":"20250620","operationDateTime":"20250620131409","office":{"officeCode":"TYONH08ZZ","country2letterCode":"JP"},"bookingType":"A","pageID":"A04_P01","siteCatalystPageName":"INT_BE_AWARD_J_A04特典予約_P01旅客情報入力（必須情報）","device":{"deviceType":"PC"}};
	</script><div id="promotionArea">
		<!--===== promotionArea =====-->
		<!--===== /promotionArea =====--></div>
		<!--=========== /cmnContainer ============--></div><div id="cmnBaloonArea" aria-live="polite"></div><div id="cmnPopupArea" aria-live="polite"></div>

	

	<div id="cmnFooterWrapper">
		<!--=========== .cmnFooterWrapper ============-->
		<div id="cmnFooter">
			<!--===== cmnFooter =====-->
			<ul>
				<li><a href="https://www.ana.co.jp/other/int/meta/0012.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">プライバシーポリシー<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
					<li><a href="https://www.ana.co.jp/other/int/meta/0670.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">利用者情報の外部送信について<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
					</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/0052.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">サイト利用規約<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/be0238.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">推奨環境<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/0133.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">サイトマップ<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/0732.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">運送約款<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
					<li class="lastChild"><a href="https://www.ana.co.jp/other/int/meta/0556.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">ウェブアクセシビリティについて<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
					</li>
			</ul>
			<p class="blankText" aria-hidden="true"><img src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="" height="10" width="10" />外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。</p>
			<p class="copyright"><span lang="en" xml:lang="en">Copyright&#169;ANA</span>
			</p>
			<p class="starAllianceLogo"><a href="http://www.staralliance.com/ja/" target="_blank"><img src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/image/star_alliance_logo.png?0fe73b5" alt="A STAR ALLIANCE MEMBER" height="18" width="175" /><span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
			</p>
			<!--===== cmnFooter =====-->
		</div>
		<!--=========== /cmnFooterWrapper ============-->
	</div>

		
		<div id="cmnLoadingForAjax"><img src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/image/bg_modalWindow.png?717d3c0" alt="" height="0" width="0" />
			<div class="loadingArea">
				<p class="loadingImage"><img src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/image/loading.gif?717d3c0" alt="リクエストを処理しています...しばらくお待ちください。" height="33" width="33" />
				</p>
			</div>
		</div>
		
		
		<div id="popupAltSentence" style="display: none" aria-hidden="true">別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。</div>
	<div id="cmnSiteCatalyst" aria-hidden="true"><div id="cmnSiteCatalystParamArea"><script type="text/javascript">
				// ▼SiteCatalyst
				// SiteCatalyst code version: H.2.
				// Copyright 1997-2005 Omniture, Inc. More info available at http://www.omniture.com
				SiteCatalystReportSuites   = "INT";
				SiteCatalystCharSet        = "UTF-8";
				SiteCatalystChannel        = "BE_AWARD_J";
				SiteCatalystPageName       = "INT_BE_AWARD_J_A04\u7279\u5178\u4E88\u7D04_P01\u65C5\u5BA2\u60C5\u5831\u5165\u529B\uFF08\u5FC5\u9808\u60C5\u5831\uFF09";
				SiteCatalystERR            = "";
				SiteCatalystEventNum       = "";
				SiteCatalystPurchase       = "FALSE";
				SiteCatalystProductPrice   = "";
				SiteCatalystCurrencyCode   = "";
				SiteCatalystSeatAvailabilityINT   = "";
				SiteCatalystDepartDate     = "";
				SiteCatalystDepart         = "";
				SiteCatalystArrive         = "";
				SiteCatalystFlightNo       = "";
				SiteCatalystClass          = "";
				SiteCatalystFare           = "";
				SiteCatalystPurchaseID     = "";
				
				SiteCatalystLocater        = "";
				SiteCatalystPayment        = "";
				SiteCatalystIncome         = "";
				
				SiteCatalystPax            = "";
				SiteCatalystManipulateDay  = "";
				SiteCatalystCompartmentClass   = "";
				SiteCatalystOptionInfo     = "";
				SiteCatalystOutboundUpsellPrice   = "";
				SiteCatalystInboundUpsellPrice    = "";
				
				SiteCatalystSearchInput    = "";
				SiteCatalystSegment        = "";
				SiteCatalystOfficeCode     = "";
				SiteCatalystPassengerCount = "";
				SiteCatalystPassenger      = "";
				SiteCatalystAncillaryService = "";
				// End SiteCatalyst code version: H.2.
				// ▲SiteCatalyst
			</script></div><script type="text/javascript"><!-- if(navigator.appVersion.indexOf('MSIE')>=0)document.write(unescape('%3C')+'\!-'+'-') // --></script>
		<!--/DO NOT REMOVE/-->
		

		
		<!-- Google Tag Manager -->
		<script type="text/javascript" charset="Shift_JIS" src="https://www.ana.co.jp/common/js/GTMINTBE.js"></script>
		<!-- End Google Tag Manager -->
		<!-- Yahoo Tag Manager -->
		<script type="text/javascript" charset="Shift_JIS" src="https://www.ana.co.jp/common/js/YTMINTBE.js"></script>
		<!-- End Yahoo Tag Manager -->
		

	</div>

	

	<div class="mboxDefault"></div><script type="text/javascript">mboxCreate("ASW_common_A04_P01");</script>
			<script type="text/javascript" src="https://www.ana.co.jp/common/js/tealium/tealium.js"></script>
			<script type="text/javascript" src="//cdn.evgnet.com/beacon/allnipponairways/nonamcprd/scripts/evergage.min.js"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22a/international_asw/javax.faces.resource/jsf.js.xhtml?ln=javax.faces"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/script/jquery.mousewheel.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/script/jquery.jscrollpane.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/script/jquery-ui-1.10.4.custom.min.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/script/jquery.ui.touch-punch.min.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/script/asw-common.js?beb6a50"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/script/asw-common-pc.js?fd88d7d"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/script/asw-format.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/script/mask.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/script/loading.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/script/asw-loadingwindow-pc.js?9e233c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/script/asw-loadingwindow.js?51db600"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/script/dialog-pc.js?25d36a4"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/script/asw-dialog-sequencer.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/script/asw-event.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/script/asw-date-select.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/script/asw-calendar-pc.js?9b86b8a"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/script/asw-calendar.js?db624c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/script/heightLine.js?0e7a1ce"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/script/asw-summaryarea.js?b21c6d7"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/script/asw-fingerprints.js?99001fc"></script><script type="text/javascript">
			Asw.init("https://aswbe-i.ana.co.jp/rei22a/international_asw", "ja");
			Asw.getCalendarHolidayList = function() {return [20250721,20250811,20250915,20250923,20251013,20251103,20251123,20251124,20260101,20260112,20260211,20260223,20260320,20260429,20260503,20260504,20260505,20260506,20260720,20260811,20260921,20260922,20260923,20261012,20261103,20261123,20270101,20270111,20270211,20270223,20270321,20270322,20270429,20270503,20270504,20270505,20270719,20270811,20270920,20270923,20271011,20271103,20271123,20280101,20280110,20280211,20280223,20280320,20280429,20280503,20280504,20280505,20280717,20280811,20280918,20280922,20281009,20281103,20281123,20290101,20290108,20290211,20290212,20290223,20290320,20290429,20290430,20290503,20290504,20290505,20290716,20290811,20290917,20290923,20290924,20291008,20291103,20291123];}
			Asw.ClientInfo.deviceType = "PC";
			Asw.ClientInfo.mobileDeviceType = "UNKNOWN";
			Asw.ClientInfo.osVersion = "";
			Asw.Format.init("https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/{1}/{2}/{3}?{0}");
			Asw.overwriteTargetAttributeValue("false");
		</script><script type="text/javascript">
		
		var Asw = Asw || {};
		Asw.ContactInput = Asw.ContactInput || {};
		Asw.ContactInput.Const = {
			/** data-属性名「data-type」 @constant @type {String} @default 'data-type' */
			DATA_ATTRIBUTE_NAME_TYPE: 'data-type',
			/** data-属性名「data-sms-possible」 @constant @type {String} @default 'data-sms-possible' */
			DATA_ATTRIBUTE_NAME_SMS_POSSIBLE: 'data-sms-possible',
			/** data-属性名「data-country-code」 @constant @type {String} @default 'data-country-code' */
			DATA_ATTRIBUTE_NAME_COUNTRY_CODE: 'data-country-code',
			/** data-type属性値「sms」 @constant @type {String} @default 'sms' */
			DATA_TYPE_VALUE_SMS: 'sms'
		};

		
		Asw.ContactInput.countryInfoMap = {"IS":{"internationalCallingCode":"+354","smsPossible":false},"IE":{"internationalCallingCode":"+353","smsPossible":true},"AZ":{"internationalCallingCode":"+994","smsPossible":true},"AF":{"internationalCallingCode":"+93","smsPossible":true},"US":{"internationalCallingCode":"+1","smsPossible":true},"AE":{"internationalCallingCode":"+971","smsPossible":true},"DZ":{"internationalCallingCode":"+213","smsPossible":true},"AR":{"internationalCallingCode":"+54","smsPossible":true},"AW":{"internationalCallingCode":"+297","smsPossible":true},"AL":{"internationalCallingCode":"+355","smsPossible":true},"AM":{"internationalCallingCode":"+374","smsPossible":true},"AO":{"internationalCallingCode":"+244","smsPossible":false},"AG":{"internationalCallingCode":"+1","smsPossible":true},"AD":{"internationalCallingCode":"+376","smsPossible":true},"YE":{"internationalCallingCode":"+967","smsPossible":true},"GB":{"internationalCallingCode":"+44","smsPossible":true},"IL":{"internationalCallingCode":"+972","smsPossible":true},"IT":{"internationalCallingCode":"+39","smsPossible":true},"IQ":{"internationalCallingCode":"+964","smsPossible":true},"IR":{"internationalCallingCode":"+98","smsPossible":true},"IN":{"internationalCallingCode":"+91","smsPossible":true},"ID":{"internationalCallingCode":"+62","smsPossible":true},"UG":{"internationalCallingCode":"+256","smsPossible":true},"UA":{"internationalCallingCode":"+380","smsPossible":true},"UZ":{"internationalCallingCode":"+998","smsPossible":true},"UY":{"internationalCallingCode":"+598","smsPossible":true},"EC":{"internationalCallingCode":"+593","smsPossible":true},"EG":{"internationalCallingCode":"+20","smsPossible":true},"EE":{"internationalCallingCode":"+372","smsPossible":true},"SZ":{"internationalCallingCode":"+268","smsPossible":false},"ET":{"internationalCallingCode":"+251","smsPossible":false},"ER":{"internationalCallingCode":"+291","smsPossible":true},"SV":{"internationalCallingCode":"+503","smsPossible":true},"AU":{"internationalCallingCode":"+61","smsPossible":true},"AT":{"internationalCallingCode":"+43","smsPossible":true},"OM":{"internationalCallingCode":"+968","smsPossible":true},"NL":{"internationalCallingCode":"+31","smsPossible":true},"GH":{"internationalCallingCode":"+233","smsPossible":true},"CV":{"internationalCallingCode":"+238","smsPossible":false},"GY":{"internationalCallingCode":"+592","smsPossible":true},"KZ":{"internationalCallingCode":"+7","smsPossible":true},"QA":{"internationalCallingCode":"+974","smsPossible":true},"CA":{"internationalCallingCode":"+1","smsPossible":true},"GA":{"internationalCallingCode":"+241","smsPossible":true},"CM":{"internationalCallingCode":"+237","smsPossible":true},"KR":{"internationalCallingCode":"+82","smsPossible":true},"KH":{"internationalCallingCode":"+855","smsPossible":true},"GM":{"internationalCallingCode":"+220","smsPossible":true},"KP":{"internationalCallingCode":"+850","smsPossible":false},"MK":{"internationalCallingCode":"+389","smsPossible":false},"GN":{"internationalCallingCode":"+224","smsPossible":true},"GW":{"internationalCallingCode":"+245","smsPossible":true},"CY":{"internationalCallingCode":"+357","smsPossible":true},"CU":{"internationalCallingCode":"+53","smsPossible":true},"GR":{"internationalCallingCode":"+30","smsPossible":true},"KI":{"internationalCallingCode":"+686","smsPossible":false},"KG":{"internationalCallingCode":"+996","smsPossible":true},"GT":{"internationalCallingCode":"+502","smsPossible":true},"GU":{"internationalCallingCode":"+1","smsPossible":true},"KW":{"internationalCallingCode":"+965","smsPossible":true},"KY":{"internationalCallingCode":"+1","smsPossible":true},"GD":{"internationalCallingCode":"+1","smsPossible":true},"HR":{"internationalCallingCode":"+385","smsPossible":true},"KE":{"internationalCallingCode":"+254","smsPossible":true},"CI":{"internationalCallingCode":"+225","smsPossible":true},"CR":{"internationalCallingCode":"+506","smsPossible":true},"SA":{"internationalCallingCode":"+966","smsPossible":true},"KM":{"internationalCallingCode":"+269","smsPossible":true},"CO":{"internationalCallingCode":"+57","smsPossible":true},"CG":{"internationalCallingCode":"+242","smsPossible":false},"CD":{"internationalCallingCode":"+243","smsPossible":true},"ZM":{"internationalCallingCode":"+260","smsPossible":true},"SM":{"internationalCallingCode":"+378","smsPossible":false},"SL":{"internationalCallingCode":"+232","smsPossible":true},"DJ":{"internationalCallingCode":"+253","smsPossible":false},"JM":{"internationalCallingCode":"+1","smsPossible":true},"CH":{"internationalCallingCode":"+41","smsPossible":true},"GE":{"internationalCallingCode":"+995","smsPossible":true},"SE":{"internationalCallingCode":"+46","smsPossible":true},"SY":{"internationalCallingCode":"+963","smsPossible":true},"ES":{"internationalCallingCode":"+34","smsPossible":true},"SG":{"internationalCallingCode":"+65","smsPossible":true},"LK":{"internationalCallingCode":"+94","smsPossible":true},"SK":{"internationalCallingCode":"+421","smsPossible":true},"SI":{"internationalCallingCode":"+386","smsPossible":true},"ZW":{"internationalCallingCode":"+263","smsPossible":true},"SD":{"internationalCallingCode":"+249","smsPossible":true},"KN":{"internationalCallingCode":"+1","smsPossible":true},"SR":{"internationalCallingCode":"+597","smsPossible":true},"LC":{"internationalCallingCode":"+1","smsPossible":true},"SC":{"internationalCallingCode":"+248","smsPossible":false},"SN":{"internationalCallingCode":"+221","smsPossible":true},"SB":{"internationalCallingCode":"+677","smsPossible":false},"RS":{"internationalCallingCode":"+381","smsPossible":true},"TH":{"internationalCallingCode":"+66","smsPossible":true},"TW":{"internationalCallingCode":"+886","smsPossible":true},"TJ":{"internationalCallingCode":"+992","smsPossible":true},"TZ":{"internationalCallingCode":"+255","smsPossible":true},"CZ":{"internationalCallingCode":"+420","smsPossible":true},"TD":{"internationalCallingCode":"+235","smsPossible":true},"CF":{"internationalCallingCode":"+236","smsPossible":true},"TN":{"internationalCallingCode":"+216","smsPossible":true},"CL":{"internationalCallingCode":"+56","smsPossible":true},"TV":{"internationalCallingCode":"+688","smsPossible":false},"DK":{"internationalCallingCode":"+45","smsPossible":true},"DE":{"internationalCallingCode":"+49","smsPossible":true},"TG":{"internationalCallingCode":"+228","smsPossible":true},"DO":{"internationalCallingCode":"+1","smsPossible":true},"DM":{"internationalCallingCode":"+1","smsPossible":false},"TT":{"internationalCallingCode":"+1","smsPossible":true},"TM":{"internationalCallingCode":"+993","smsPossible":false},"TR":{"internationalCallingCode":"+90","smsPossible":true},"TO":{"internationalCallingCode":"+676","smsPossible":false},"NG":{"internationalCallingCode":"+234","smsPossible":true},"NR":{"internationalCallingCode":"+674","smsPossible":false},"NA":{"internationalCallingCode":"+264","smsPossible":false},"NI":{"internationalCallingCode":"+505","smsPossible":true},"BH":{"internationalCallingCode":"+973","smsPossible":false},"NE":{"internationalCallingCode":"+227","smsPossible":true},"PK":{"internationalCallingCode":"+92","smsPossible":true},"JP":{"internationalCallingCode":"+81","smsPossible":true},"BS":{"internationalCallingCode":"+1","smsPossible":true},"NZ":{"internationalCallingCode":"+64","smsPossible":true},"BM":{"internationalCallingCode":"+1","smsPossible":true},"NP":{"internationalCallingCode":"+977","smsPossible":true},"BB":{"internationalCallingCode":"+246","smsPossible":true},"NO":{"internationalCallingCode":"+47","smsPossible":true},"HT":{"internationalCallingCode":"+509","smsPossible":true},"BD":{"internationalCallingCode":"+880","smsPossible":true},"VA":{"internationalCallingCode":"+39","smsPossible":false},"PA":{"internationalCallingCode":"+507","smsPossible":true},"VU":{"internationalCallingCode":"+678","smsPossible":false},"PG":{"internationalCallingCode":"+675","smsPossible":true},"PW":{"internationalCallingCode":"+680","smsPossible":true},"PY":{"internationalCallingCode":"+595","smsPossible":true},"PS":{"internationalCallingCode":"+970","smsPossible":true},"BR":{"internationalCallingCode":"+55","smsPossible":true},"HU":{"internationalCallingCode":"+36","smsPossible":true},"TL":{"internationalCallingCode":"+670","smsPossible":true},"BG":{"internationalCallingCode":"+359","smsPossible":true},"BF":{"internationalCallingCode":"+226","smsPossible":true},"BN":{"internationalCallingCode":"+673","smsPossible":true},"FJ":{"internationalCallingCode":"+679","smsPossible":true},"PH":{"internationalCallingCode":"+63","smsPossible":true},"FI":{"internationalCallingCode":"+358","smsPossible":true},"BT":{"internationalCallingCode":"+975","smsPossible":true},"BY":{"internationalCallingCode":"+375","smsPossible":true},"BZ":{"internationalCallingCode":"+501","smsPossible":false},"PR":{"internationalCallingCode":"+1","smsPossible":true},"BE":{"internationalCallingCode":"+32","smsPossible":true},"BA":{"internationalCallingCode":"+387","smsPossible":true},"FR":{"internationalCallingCode":"+33","smsPossible":true},"BO":{"internationalCallingCode":"+591","smsPossible":false},"PF":{"internationalCallingCode":"+689","smsPossible":true},"BI":{"internationalCallingCode":"+257","smsPossible":true},"VN":{"internationalCallingCode":"+84","smsPossible":true},"BJ":{"internationalCallingCode":"+229","smsPossible":true},"VE":{"internationalCallingCode":"+58","smsPossible":true},"PE":{"internationalCallingCode":"+51","smsPossible":true},"PL":{"internationalCallingCode":"+48","smsPossible":true},"BW":{"internationalCallingCode":"+267","smsPossible":true},"PT":{"internationalCallingCode":"+351","smsPossible":false},"HK":{"internationalCallingCode":"+852","smsPossible":true},"HN":{"internationalCallingCode":"+504","smsPossible":false},"MH":{"internationalCallingCode":"+692","smsPossible":true},"MO":{"internationalCallingCode":"+853","smsPossible":true},"MG":{"internationalCallingCode":"+261","smsPossible":true},"MW":{"internationalCallingCode":"+265","smsPossible":true},"ML":{"internationalCallingCode":"+223","smsPossible":true},"MT":{"internationalCallingCode":"+356","smsPossible":true},"MY":{"internationalCallingCode":"+60","smsPossible":true},"FM":{"internationalCallingCode":"+691","smsPossible":false},"MM":{"internationalCallingCode":"+95","smsPossible":true},"MX":{"internationalCallingCode":"+52","smsPossible":true},"MU":{"internationalCallingCode":"+230","smsPossible":true},"MR":{"internationalCallingCode":"+222","smsPossible":true},"MZ":{"internationalCallingCode":"+258","smsPossible":true},"MC":{"internationalCallingCode":"+377","smsPossible":true},"MV":{"internationalCallingCode":"+960","smsPossible":true},"MD":{"internationalCallingCode":"+373","smsPossible":false},"RO":{"internationalCallingCode":"+40","smsPossible":true},"MA":{"internationalCallingCode":"+212","smsPossible":true},"MN":{"internationalCallingCode":"+976","smsPossible":true},"ME":{"internationalCallingCode":"+382","smsPossible":true},"JO":{"internationalCallingCode":"+962","smsPossible":true},"LA":{"internationalCallingCode":"+856","smsPossible":true},"RU":{"internationalCallingCode":"+7","smsPossible":true},"LV":{"internationalCallingCode":"+371","smsPossible":true},"LT":{"internationalCallingCode":"+370","smsPossible":true},"LY":{"internationalCallingCode":"+218","smsPossible":true},"GQ":{"internationalCallingCode":"+240","smsPossible":false},"LI":{"internationalCallingCode":"+423","smsPossible":false},"LR":{"internationalCallingCode":"+231","smsPossible":true},"CN":{"internationalCallingCode":"+86","smsPossible":true},"LU":{"internationalCallingCode":"+352","smsPossible":true},"ZA":{"internationalCallingCode":"+27","smsPossible":true},"RW":{"internationalCallingCode":"+250","smsPossible":true},"LS":{"internationalCallingCode":"+266","smsPossible":false},"LB":{"internationalCallingCode":"+961","smsPossible":true}};

		$(function () {
			contactInfoInit();
		});
		
		function contactInfoInit() {
			
			
			Asw.get('representativeTelType').find('option[value="M1"]').attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_TYPE,
					Asw.ContactInput.Const.DATA_TYPE_VALUE_SMS);
			Asw.get('representativeTelCountry').children('option').each(function(i,option){
				var countryInfo = Asw.ContactInput.countryInfoMap[option.value];
				if (countryInfo === void 0) {
					
					return true;
				}
				$(option).attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_SMS_POSSIBLE, countryInfo.smsPossible);
				$(option).attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_COUNTRY_CODE, countryInfo.internationalCallingCode);
			});
			
			var smsCountrySelectSelector = 'select.js-countrySelect:not(#representativeTelCountry)'
			$(smsCountrySelectSelector).eq(0).children('option').each(function(i,option){
				var countryInfo = Asw.ContactInput.countryInfoMap[option.value];
				if (countryInfo === void 0) {
					
					return true;
				}
				$(smsCountrySelectSelector + ' option:nth-child(' + (i+1) + ')').attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_COUNTRY_CODE,
						countryInfo.internationalCallingCode);
			});
			
			var emergencyCountrySelectSelector = 'select.passengerEmergencyCountry.js-countrySelect:not(#representativeTelCountry)'
			$(emergencyCountrySelectSelector).eq(0).children('option').each(function(i,option){
				var countryInfo = Asw.ContactInput.countryInfoMap[option.value];
				if (countryInfo === void 0) {
					
					return true;
				}
				$(emergencyCountrySelectSelector + ' option:nth-child(' + (i+1) + ')').attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_COUNTRY_CODE,
						countryInfo.internationalCallingCode);
			});
		}
	</script><script type="text/javascript">
		var isInit = true;
		function changeTelType(_this, telNoInputId) {
			var $telNoInput = Asw.get(telNoInputId);
			var $telNoKind = Asw.get("representativeTelType");
			var telNo = Asw.getValueComparedToPlaceholder(telNoInputId);
			var telNoCountry = Asw.get("representativeTelCountry").val();

			if(true && !isInit) {
				if(telInputCheck(telNo, telNoCountry)) {
					if (_this.value == "M1") {
						telNo = exceptDomesticPrefix("09042504176", telNoCountry);
					} else if (_this.value == "B1") {
						telNo = exceptDomesticPrefix("03-5405-2172", telNoCountry);
					} else {
						telNo = exceptDomesticPrefix("090-4250-4176", telNoCountry);
					}
					$telNoInput.val(telNo);
				}
			}
			isInit = false;
		}
		
		function telInputCheck(telNo, telNoCountry) {
	        checkResult = false;
	        
	        var amcHndyTelNo = exceptDomesticPrefix("09042504176", telNoCountry);
	        var amcCmpTelNo = exceptDomesticPrefix("03-5405-2172", telNoCountry);
	        var amcTelNo = exceptDomesticPrefix("090-4250-4176", telNoCountry);
	
	        if(telNo == "" || telNo == amcHndyTelNo || telNo == amcCmpTelNo || telNo == amcTelNo) {
	        	checkResult = true;
	        }
	        return checkResult;
    	}
    	
    	function exceptDomesticPrefix(telNo, telNoCountry) {
    		if(telNoCountry == 'JP') {
    			if(telNo.substr(0, 1) === '0') {
    				return telNo.slice(1);
    			}
    		}
    		return telNo;
    	}
	</script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/script/asw-customerinfomationform.js?6e91953"></script><script type="text/javascript">
		$(document).ready(function(){
				Asw.enableTextWhenCheckBox('.cascadedSwitch');
				Asw.notPasteInput('.jsNotPasteInput');
			$(function(){
				Asw.alignHeightToParentTdElement( '.passengerInfoInputArea' );
				
					Asw.alignHeightToParentTdElement( '.passengerInfoInputText' );
				
				
				$('select.js-countrySelect').trigger('change');
			});
		});
	</script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22a/international_asw/contents/cmn/script/asw-session-keeper.js?6ec0730"></script><script type="text/javascript">
			$(document).ready(function() {
				Asw.SessionKeeper.initAndStart("sessionKeeperContainer", "520", "570", "PC");
			});
			</script><script type="text/javascript">
		Asw.addAjaxCallback(function(data) {
			// アコーディオンのメソッドと設定 
			if($('.jsAccordionSwitch .jsTriggerSwitch').length > 0){
				var events = $._data($('.jsAccordionSwitch .jsTriggerSwitch').get(0), "events");
				if(events === undefined || events.click === undefined) {
					Asw.AccordionInfo('.jsAccordionSwitch', {
						'triggerSwitch': '.jsAccordionSwitch .jsTriggerSwitch',
						'listClass': '.jsAccordionSwitchList',
						'visibilityHidden': 'toggleContents'
					});
				}
			}
		});
	</script><script type="text/javascript">
	Asw.Price = Asw.Price || {};
	// 整数の区切り文字
	Asw.Price.separateString = ',';
	
	// 整数の区切り桁数
	Asw.Price.separateDigitNumber = 3;
	
	// 小数点
	Asw.Price.decimalPointString = '.';
	
	// 小数点以降の有効桁数
	Asw.Price.significantDigit = 0;
	
	// 表示金額の単位
	Asw.Price.currencyUnit = '円';
	</script><script type="text/javascript">
			$(document).ready(function() {
				$('#preLoadingArea').css('visibility', 'hidden');
			});
		</script><noscript><img src="https://aswbe-i.ana.co.jp/akam/13/pixel_723dc229?a=dD1iM2ZmZjU5MmM3MzhlOGM4YjgzODllMWEzZGE2YjFhNmE4NGIzYzQyJmpzPW9mZg==" style="visibility: hidden; position: absolute; left: -999px; top: -999px;" /></noscript><script type="text/javascript"  src="/Q1XS4o/Cn_ls/KfDO7/xA/t9OfhmEwSiGQaG/NG4dCQE/bw/QJBChQIQEB"></script></body>
</html>