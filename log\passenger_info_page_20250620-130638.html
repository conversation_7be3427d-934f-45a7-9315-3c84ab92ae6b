<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="ja-<PERSON>" xml:lang="ja-<PERSON>">

	
	<!-- skey = 2025/06/20 14:06:38 rei21g 8yOLuw4Jlg dljdmx+9ea  --><head id="j_idt53">
    <base href="about:blank">
    <style>
        /* Hide elements that might try to load external resources, as a fallback. */
        img, svg, video, iframe, link[rel="stylesheet"], script { display: none !important; }
    </style>

		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
			<meta http-equiv="X-UA-Compatible" content="IE=EmulateIE9; IE=EmulateIE10" />
			<meta http-equiv="X-UA-Compatible" content="IE=7" />
			<meta http-equiv="X-UA-Compatible" content="IE=edge" />
		<meta name="description" content="" />
		<meta name="keywords" content="" />
		<meta name="format-detection" content="telephone=no" />
		<script type="text/javascript" src="/international_asw/dynatrace/ruxitagentjs_ICANVfhqrux_10273230920145353.js" data-dtconfig="rid=RID_-1474955565|rpid=-**********|domain=aswbe-i.ana.co.jp|reportUrl=https://bf90419wvr.bf.dynatrace.com/bf|app=78bf0b58acf6ed13|cors=1|owasp=1|featureHash=ICANVfhqrux|xb=www^bs.google-analytics^bs.com^pana^bs.demdex^bs.net^pcollect^bs.tealiumiq^bs.com^pallnipponairways^bs.australia-3^bs.evergage^bs.com^pallnipponairwayscolt^bs.tt^bs.omtrdc^bs.net|rdnt=1|uxrgce=1|cuc=k8vpgh61|mel=100000|expw=1|dpvc=1|md=mdcc1=bAsw.PnrOutput.recordLocator,mdcc2=bAsw.PnrOutput.paymentDetailInfoList[0].fopCode,mdcc3=bAsw.PnrOutput.ticketedFlag,mdcc4=bAsw.PnrOutput.nhPnrFlag,mdcc5=bAsw.PnrOutput.itinerarySummary.departureDateYyyymmdd,mdcc6=bAsw.SearchCriteriaOutput.cffCodeInput,mdcc7=a#main ^rb div.section.sectionNoFlow ^rb table ^rb tbody ^rb tr ^rb td.flightNumber.lastCol ^rb span ^rb span:nth-child(2),mdcc8=cunique|lastModification=1750184416409|tp=500,50,0|agentUri=/international_asw/dynatrace/ruxitagentjs_ICANVfhqrux_10273230920145353.js"></script><link rel="shortcut icon" href="https://www.ana.co.jp/favicon.ico" />
		<title>お客様情報入力 | ANA</title>
		<noscript><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/css/asw_noscript_pc.css?99001fc" />
		</noscript>

		<!--  [ Adobe Target ] -->
		<script type="text/javascript" src="https://www.ana.co.jp/behavior/adobetarget/target_intbe.js"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/jquery-1.8.3.min.js?717d3c0"></script><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/ja/css/asw_initialize.css?99001fc" /><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/css/asw_screen_pc.css?99001fc" /><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/css/asw_common_pc.css?99001fc" /><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/css/asw_forminput_pc.css?99001fc" /><script type="text/javascript">
		var Asw = Asw || {};
		Asw.GlobalWord = Asw.GlobalWord || {};
		
		Asw.GlobalWord.toolTipCloseIconAltText = "閉じる";
		
		</script><script >bazadebezolkohpepadr="1207773006"</script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/akam/13/47fd25bc"  defer></script></head><body>
		
		<noscript>当サイトをご利用になるためには、JavaScript対応のブラウザが必要です。設定でJavaScriptを有効にしてください。有効にできない場合はANA電話窓口へお問い合わせください。
		</noscript>
		<div id="preLoadingArea"></div>
	
	<div id="cmnHeader" class="platinum">
		<!--===== cmnHeader =====-->
		<div class="headerWrapper">
			<div class="header">
				<p class="logo"><a href="https://www.ana.co.jp/asw/AMCTopServlet?type=a" tabindex="-1" aria-hidden="true"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/ana_logo_navy.png?717d3c0" alt="ANA Inspiration of JAPAN。リンク先はマイレージクラブトップページです。" height="28" width="274" /></a>
				</p>
					<ul class="headerMenu">
						<li><a href="https://www.ana.co.jp/asw/AMCTopServlet?type=a">ANAマイレージクラブトップページ</a>
						</li>
						<li><a href="https://www.ana.co.jp/other/int/meta/0005.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">お問い合わせ<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
						</li>
							<li class="btnLogoutArea"><a href="https://aswbe-i.ana.co.jp/rei21g/international_asw/rest/logout" role="button" class="btnBase btnLogout">ログアウト</a>
							</li>
					</ul>
			</div>
		</div>
			<div class="amcUserInfoWrapper">
				<div class="amcUserInfo">
					<div class="userNameArea">
						<table>
							<tr>	
									<td class="statusIcon"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_status_05.png?717d3c0" alt="プラチナサービスメンバー" height="46" width="60" />
									</td>
								<td class="userName">佐藤 直樹 <span>様</span>
								</td>
							</tr>					
						</table>
					</div>
						<div class="balance">
								<dl class="upgradePointBalance">
									<dt>アップグレードポイント残高
									</dt>
									<dd>
											<dl>
												<dt>(2025年度)
												</dt>
												<dd>
													<em>20
													</em>ポイント
												</dd>
											</dl>
									</dd>
								</dl>
							<dl class="mileBalance">
								<dt>マイル口座残高
								</dt>
								<dd><em>75,663</em><span class="currencyCode">マイル</span>
								</dd>
							</dl>
						</div>
				</div>
					<p class="nowTime">6月20日14時06分現在
					</p>
			</div>
		<!--===== /cmnHeader =====-->
	</div>
		<div id="sessionKeeperContainer" class="sessionKeepingContainer"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/bg_modalWindow.png?717d3c0" alt="" height="0" width="0" />
			<div id="extendSessionExpiredDialog" class="modalExpire modalSmall modalContainer" role="dialog" aria-labelledby="extendSessionExpiredDialogTitle" aria-hidden="true">
<form id="sessionKeeperContainer:j_idt183" name="sessionKeeperContainer:j_idt183" method="post" action="https://aswbe-i.ana.co.jp/rei21g/international_asw/pages/award/reservation/award_mandatory_passenger_information_input.xhtml?aswcid=1&amp;rand=202506201406388yOLuw4Jlg" autocomplete="off" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="sessionKeeperContainer:j_idt183" value="sessionKeeperContainer:j_idt183" />
<input type="hidden" name="sessionKeeperContainer:j_idt183_operationTicket" value="dljdmx+9eaeb034f0406f4210b8250fa9d4780c~xxoJKwTQLOUr4IUwCzBXaBC4h5vkdyRz-dzK9Vr6!1750395986578.aere-xml-controller-67d4778877-6gwmg" /><input type="hidden" name="sessionKeeperContainer:j_idt183_cmnPageTicket" value="4" />
					<h1 id="extendSessionExpiredDialogTitle" class="dialogTitle">まもなくセッションが終了します</h1>
					<div id="extendSessionExpiredDialogMessage" class="dialogMessage" tabindex="-1">このままお手続きを進める場合は、［延長］ボタンを押してください。</div>
					<p class="modalButton"><input id="sessionKeeperContainer:cmnSessionKeepingButton" type="submit" name="sessionKeeperContainer:cmnSessionKeepingButton" value="延長" class="btnBase btnModal btnMainStream" onclick="mojarra.ab(this,event,'action',0,0,{'onevent':function(data) {Asw.doCommonAjaxCallback(data,false, event); Asw.SessionKeeper.doSessionKeep(data);},'onerror':function(data) {Asw.doCommonAjaxErrorCallback(data); }});return false" />
					</p><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:0" value="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" autocomplete="off" />
</form>
			</div>
			<div id="showSessionExpiredDialog" class="modalExpire modalSmall modalContainer" role="dialog" aria-labelledby="showSessionExpiredDialogTitle" aria-hidden="true">
				<h1 id="showSessionExpiredDialogTitle" class="dialogTitle">セッションが終了しました</h1>
				<div id="showSessionExpiredDialogMessage" class="dialogMessage" tabindex="-1">一定時間操作されなかったため、お手続きを進めることができません。お手数ですが、再度トップページからお手続きください。</div>
				<p class="modalButton">
					<input type="submit" value="閉じる" class="btnBase btnModal btnMainStream" onclick="Asw.SessionKeeper.closeTimeoutDialog(); return false;" />
				</p>
			</div>
		</div>
		<!--===== dialogMessages =====--><div id="cmnModalMessages"></div>
		<div id="prebookConfirmDialog" class="modalContainer modalConfirm modalSmall " aria-hidden="true" role="dialog">
<form id="prebookConfirmDialog:j_idt403" name="prebookConfirmDialog:j_idt403" method="post" action="https://aswbe-i.ana.co.jp/rei21g/international_asw/pages/award/reservation/award_mandatory_passenger_information_input.xhtml?aswcid=1&amp;rand=202506201406388yOLuw4Jlg" autocomplete="off" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="prebookConfirmDialog:j_idt403" value="prebookConfirmDialog:j_idt403" />
<input type="hidden" name="prebookConfirmDialog:j_idt403_operationTicket" value="dljdmx+9eaeb034f0406f4210b8250fa9d4780c~xxoJKwTQLOUr4IUwCzBXaBC4h5vkdyRz-dzK9Vr6!1750395986578.aere-xml-controller-67d4778877-6gwmg" /><input type="hidden" name="prebookConfirmDialog:j_idt403_cmnPageTicket" value="4" /><div class="dialogMessage" tabindex="0"><p>この後、名・姓の修正や変更ができません。</p><p>「搭乗者選択」欄の氏名が「名」「姓」の順番で表示されていることを再確認ください。</p><p>例）<strong class="highlight">空野 太郎</strong> 様 → MR <strong class="highlight">TARO SORANO</strong></p><p>保安上の理由により、航空券とパスポートのお名前が同じ表記でない場合はご搭乗いただくことができませんので必ず再度ご確認ください。</p></div>
					
					<ul class="modalBtnHorizontal">
							
							<li><input type="submit" name="prebookConfirmDialog:j_idt419" value="戻る" aria-controls="prebookConfirmDialog" class="btnBase btnModal btnCancel" onclick="Asw.Dialog.getInstance('prebookConfirmDialog').close(event, Asw.DialogSequencer.isActiveSequencerNotDisplayedLastDialog());  return false;" />
							</li>
						
						<li class="btnArrowNext"><input type="submit" name="prebookConfirmDialog:j_idt422" value="OK" aria-controls="prebookConfirmDialog" class="btnBase btnModal btnMainStream " onclick="Asw.Dialog.getInstance('prebookConfirmDialog').callOpener('NORMAL', event); return false;" />
						</li>
					</ul><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:1" value="ncnGtyZTyz51zMh/63bk2owy+tQkN5uanVEHrcR27ZgIRoDKeuXAUEaHX4lhw1lyJHNT+84wK2OK2tb7WrWhlv/sHCqTj8J7z9asmnLq8CR6jHj79TCYYmxiuqC2/CGp+DyQEpOr62adCq3su02pIGizMpmA00k6ggcIgur9ETgRbDFsyikGHspkY54mMgICG6cCGes6IgyftnOFCvOBeSjI530W+4o0WIc0mBoJhSCyubffTEYWL0k9eANYFB8lyJ0QdRaza6MyFrpeX2Y/mMn5eD/nlK/H5Gbd/c05TcruBDv+tAt3ANUKZgK0QWYFLWCKcMCDD2438kZ+PEbMna2Ec7HTM3PX11wGoVN+0yy6RjEQzVV3JlYNERZIP4awFZBgIvw6X6llFqivL1hIkUD9v/4G0v+CNZxaF+73UnO/VtV6PA7jP680u0u11EUp64ovXUmewMrR4Tu2C7AF5AFy62mSanyvQCl0EBDDgO8wxWFKPPAuIvmG2QrDZ3uSzaENl3Op0XfEZ3BJUOzj9rwXulLJ3xEr6+cTV1wa6su6QTbSnO3GcIbhL3BDnoefCrOxA3//DUufmd7GiyWgtPZxU5tPM1umyH73kAMOHKpPHM0gr2ILkv2yjcZCmCNpl7cO2sQGFmLA6v/pJfzAp5rFVVJ4Ka6XxWPMGcXWyow6AUQNQOIRGAX/bkMK+QWC9VGQPQIqW6flu7DDn6BEQ2P9v+D8oaISUHI84gQ5KCsCLurGC2B4Vg5nYx+vbl9OheHu2crXv6cQLL4dvKIiq6tnL1IAhb6C6/0aGyTSkD7VIlxwry0R4jx1QuSiu1KofNp5aClyXbVyWbdAubhJQU5lxCKjJR6HhhTWS+EF6zytr3UPzvZsSILFkY9gMsJz5JI4f882steAm4zdlupjQqAJ/IeDuIKp5R3Mqqg/8+XW1mCbPIlvh0PU4T2EV8tzaFB/l3SsMP8EWE4wE34W667y1+vJ5jwv7fdfdPaVADzZZ5py856A0Foi4jSRbKfDMRhKB1Uy0JbGux90xtiJpm81nzni+ItVVED0Fpgqq0Xqp7SDMWggkLmjs2c41He16qZN6LeMRL7C9m+Z+qz2t4q3hBNgFS1C6KrC1yyNq372F7fYdjzmPAWvK6MjNQbK0SXNnV5WevQqiqBnX24PvwAPeUHA2ZeTQiGaMPDTaGPOSZjkjSGbs5/q4HFZ53dc9gof3GPpzagT5znJsqTd2z0Gt6z1cUOEdxANa7gq1iZlgLSl2T9AgBUiuGm94o4dXZQWgfo3JyrUTwkTnrxq8g26SCGQGS0zR62D2mvi2grU25XV/Ta+ZtWUrDqeyGwmasyjxipvqPn2rrAwJ7g5HLpFFjFwhmHtQtJ6EtNwo4rkgd2+e84JOq5kgU8/nWCcWcYLQ/CReJM3Liqq6BOCy922s5BZ8go/+Ri2nfoKg9jqcVqfVz7YCNC1V2ZNywRTDeGyX352UxAiFDIcS8a4Dp6BilcO0A8bAWMeYzczWF23iEQJXCQuN4RWWQ1vEVIMbXJORG0nb41WcQzw8M2Z6U0JBOe28SKWLXuej7O16QKqxIEpB22cmQC71GCIxGaxN5L9gXjPvBTjjUy5lbc8pc/lxL762sADHnrsjVa8NnIk/73oq5jf4annrDUFDw9GoPRkTZ0ozKBELZrsGd9dYX6dJ4Wra4omg4a6OC2iFyUVOAnb694EPF54deQwKBAvcUXBqKxBwJg0QCN0UJ9/kDyJcyv6wBdkg+UDj6vZcC8imfacVhNS/nVyA0RiRCEwnozMhTDXbp+pVis4nlInYssCh78rJO2zjTTatSHVB0aL7UaAF5GWgUO22UI0LjJswFVMrxzfQ7+lg5Sj2fUPZAleaRxMdqP7OUhGBYOIzMQ2KAdFu+jHQfO/4qrfUpq4QxMxWS4I5s2OLFaG7J4XH2hAB5JSewVAmJ3Rk+ctKu4gX6IJXZYaL7IfU9EYroT1bMJs730F9fe71ON8Pi98mEiZ1cxtXdLal5/nuHw1/nGFQowARFF8vxgePhHsNyHOgYtGP8r8KyKoUMGrXljl4GYPv0DBknDv4I4xSg204zqnycMApQdtZcm5oecm32Y93NrojuM8xqy4nFVQJH90TSrf2YiZrQr8zMjXjlx+/FQSwkra+tPjsFOg3vme2fbQZMtO7Yc2Fb4VTLMmotakQBFLhMIZduVWpgL55yEsSuUGB/IzjB+nNXTO3g/tc/CBZRr7JCmZGlxpglMwvfqW3YDv7c09PaERAtgPD/e8i6c/zlN+p6h6wkNltlsb9tq2ZH1SwSjkooM96s/K68Cgv+aPBEaj5VUjZTm9PVHcLLN7hpXsNE6qABqzjJpUAtNzeEyplGzNmUq0SDH0lLVrhh0zPfvCu0QE/+AyPJW3NNaFfTZCn0ItValn9AOsQftnoh26FiEzfJsUvz+dTzz6qGdSF5TohB1lWL9KVgJDz4X7ENEdB0ayIWRcYe8asB0obHmsxzG6b/hM6A73hQ7pV/pGJBb3/Q89y4jiYrnpageVjA/HMAaFXDOH6Pr+A1LySX68EHDKZkWcTyJi9BaPMZDmFp+YfCX1ZJGcspvGWueGNqXnXLMbr1JJdyDMVe+BmnhPrBehyNpdVw8muE3Cw144sYtqAfc0o/enbfTXX/GWyC5OE+WyaHYQBc6zw4lFQu3NmOAvSn7MFzr5teiTXTK65iK0l92IlB4xoPXt87r8Sq2rxIlQeC8z4Jz4Plc167dZ9JLolV+yzX3fU9ucXnb7izhSY0kprdjUSiB0fL5Pe/2ogtYzp8tBsUOy0idDeVK9T1dML7JTxgqA80udzuy4vyV9PVyDL7z+g/K5/o14wyB+xJD8ldhdIQ4oGgghUmDWs589jGdt0/Lkst87u8Z9fLaj+m2Y47qMOFqHTcHT6IEnwRSCX9IhihGjMC2MNEz/3Lx41S5uYUvBsS8fNu5QbkdI9dCsuDp93wAZ1IpMqWmq4shmxm1pjTyZ7ALdhOOtpX67pHSQ3zL5I+tiZ9VemGINehFii494RyCgQ/+bXG7V10XXjK0sv4sUsuUZcJJ49GRCQn2HboTVJjnjIDnxPzBajPxH07YGKmEhU34H+SzlgpcE4YJya9nN1zDaC4SmA+ub0IYcQPTb6K70J2quii6dx5HKd9kTlGM52hYZ8K0udJvtQg1TqCU2bC2CKX1UEVHT9EytWr5vEABb/nCXS0sW0gafU0TzbnxkdV4vQWHe/OAo98Gd0MKG7VT0JWpseDrjIlaK4zpmYJAfTL1bUS+GsmdfUR1VIh1uFZv5MAJGdbv29Jl8mtsJgzRFXVoG5YtVImCXGqcUCnC9cdW14RfRS+SgF20gGy6h1psTmaCsN+FBDArZ9NArSIiEedDp8v6QizVQ1t831iSZrfV6TsX/Y/wRic6zmVR2/Ece/SlsyfiYy5EZu2v4M8f+PBJJNWcpHJxhKFesr24j5LGe3P62OtGO1Ke3QRW55qYHPYo8or3Z6WGFgppe4g//upJGur3jLSqzNdJOb3syEUpvYvPXpGyPSSXP8m39GoNwQLLjAMjuXkj5CQSc4QY939WykUgqtBIvzaX2uEJYjCB7U0Wxx2cs3E9fdp7CGmhRFvOeMNkL62pxzfHOiQ5zRoX1k0pUrnqXssVWoyYJKcD8sHD6ZqfkghKOM5/x+XHcHgI1xAeGlUNtAIY/uridA2C72iABhTc+3z5ivJUkAMAw9cqW45ga7o+bz0SOaTSXCY+kSHHwWXQ7uA54Y+CVkYB4HsQ5CEko/FJVMybOqqZNb0S0hQEC3+LwwnQ6R3zzxpgDQ2Zoiamgz0pHG09Gust+phOOl5djdnHAXh7m7B5QagcGh3ODnMnww43V/5HJFVKsM4B7HespjCC+f8gt6rdsiMqv6PEMMjlms7dIbnWHQdPA5hVOnuX2L5FGNQktusNZ6e1Wd1FvRVCkiImtW1GMtGiB9wXDP3aZVALAAlNL5E+yMVBg1qIpB/hL8IrP0KKVsKC89XwxqriIysEFPNraiMogAV/1sHssKB5epGkFOku/LvaspS5guQT9lCITBMb6VEndcjlHpy2a2pzLJvclsLc5aV7mqnHzp8WWCbdRtB1e6popUw5YRTuOkssVCRE9Gw1yvHc5ZDR8VlgqmungfhZaaveG5AwsGO7hGRkhKv88A3gFJacpzVsLkX0wrCZHcL4wVZb5Z8Q7FYlN4U6FxL+JCibIdO3Z2/BcPggZHbsNVdHpikhdHxGaS1AqA86C2DKGdILvCAd76N+ZBW/sEH3fnL7yZT1vgoRXrbi/U5zXnrW2xcAWKD9pu4gtVyjM2gSfQSXwcUNSCgwCt4HOMGsON4q0ki6Lcu5j9dlrnJfLW3oGlfsEB29J7KBfTDRTZR2hX2Q3H32l8WUV3DfhhH/oGEUE9hgXE2EHgESLSQ+mUXhhcga7JQT2Ic4qqq1FpraueSrf7I0nylp8KPl3hOiDWeGy1fvpp0zjhqE16EfVBDSBwwgavNrBOwJQNVr5dNoCjYN8zur2ivXp2mFw/psz4Ixao2SvFuhaaD8ccFcGkAgrZEv2TftxkRG6r0vLIzLnIcarMYyoYGFsVMUQariY/KGJx/jCnNMJ9m2rb5zYOV82RafqnXUh46JMlAR0N5e0QMeiuKB/28jTki9Htp8/4e2bUlhZGSRe0Kn7fQMrWdeXAmcoWGv2MPwEPL3PTNgd4oEht2LYuLVULidr1B8E13huFrO1wTrrgWXaMqzGZoE0TQSTDRCiO447FPgdrBUN4e2ojx3kBNF822qkXw+SWyXSjZw4DHhHUH/dAw9ET+YZxFIWUpoRDzFuxTz+VsGDDMUCN2gBJ5OIIB1ZKvaLzfcJ5o3/fOInImwg4Upe2XbrLByO2ykiQ9g+HwGfE7FqlSoKKVeCDUo5HVwFNZ8Gl7ShAJ16sx9umLDxt8eKlGwc9eKRz18iz3QDJKXVwsrkQrzf4HyisXRzHQmgV4J4NpfynIS2H9ELyJFUyMREJbO99KBk7uSFKFblCXdq4ueU9BSMN66CIlYselT4BOJflDsg4yazz7IQXLE6ee263RW/qylwMaD9zFxY6ss6ySVx9cfQyCHK2rz6ML6KLcciAmK8YzeiCYCBZctSg+4rPiGSxgi51qIkVdfMY9on6bx2jK3IaXPnfevIFaJwaUlvJtsdeUdlcsjVkKbHO3Xdsk9HzdOQdbXVX4KzjyKvg1D8pZgAQtdKRos9b9bLg1FFBMBzBxi0hxa1qzriCx1IdE9rTIwXzBRFLqjXfzwq5G5yABMfNRItcqumUVouKSyZWON4/+fkZGC2w9VWr+rtEvNge/uUTnDCTn05Kn+Mr5X1+qY9Trq+1yVU+N6Q42ex9EIhnNGPacy2GqwLUgEPp9OKvJ5902SW74EpfPcyE895rWnXdiW4C8FtVKgaBo4B1CsRpbw6HEjpInyG/vza0mXlGHbggcvNPaYbk6sa1nOHi3szAXXVV3rJQaQLcnmlVstR4WiaKCcmQpmH5xP+5t6fSqoGPZxncXILYgN2jriJGZJ4rV+GEKjFosMMHHUcV3NBHVZCHBEIbZD/6vmKKZFPfgZfbwJYX+3O13kOzSbleA0Ald1g+HGuUW5SpEDTLjZqA7UmnhIqQKAov7rSAs8WVQwdB+hipYUNYOYnAwVOYxFV3iB8uZBj6zMe7HRRl8nJV9MfMwm8J0OJH4QnMiya7NTfGwOkiDyJZXLpkz9YKFRKrb+z9//A3RqGhQEOML7E+vfqqeWjSVnWASyhyPxhJ0e+q546hF9uXyz++q3iBPQNPh8eEB2LjN/Om1IljohAwfW5Ja2de2vFtI2rQURxBQVybEoaZLr6b5vfyUnmmJ9ZPnCGtMb71OCBHhr8vW21zxxw5cXAWaSxbtPCx6uvp7o6BV2NIhZHPx5l5gTH6r6YtUhG+MHCzFrSB4hX8Isi8LDoDX818cKyBHmA8yAQB2adQHxp2tchnhKvFaKm18b7Vq+C6ra6VK1TMaDGoiovnIycDF0zd5jEPSd/AuYzY/8Gq6rE81r3BRhSt2PzGQ2Uw3zinNBjdflQjeaG5jQT4UHbjLrdrnEaCfLo2dVSEfQmGZgRQwUeXQNK8QMC+3MT2TIIyQxDE22O8/2KNW4lKjjTXFREDkN+3GTNCvNg1XCn6NjUTEwMKm4PfO88EsHoyF8eUAYrEU79XMVGP+qED8ZlbIkToPUctw7BrQoEfGG79QdRJzWREt28y8uJ5Dp6C/JhQfi6S9YpCWJjtCRKeeMCuc1Sd2LGvie0wizKvnyidbB/87zkGOat58GqsXB2Fui5jxWAHipPfGkXBzgv06TjZSsJbi2LiSisnQ5ch7xVQJoQzsrnHITlCbqnZ0M6MBjT2lOjW6w3K/Mf0qLMCG4TuxgAOOz5kkqW5sVidnnR5Mtnqc7QRHdmg+Uf9zwAv7GlfadUnsUHUuSL3CdgMqpcKdxP2IYUfWLGyaeXYsfZ+guJ8mrRlKk9J4sEhTCw8/KXLYjY4AR8Zbwv1KVopcb6bjBjsCq1HyYPhqpsAsYz9z/yBbGueBXQJwW6XZ5hmn94i1qPexYL31T3iG0A6E4HxRX07DnTia1110jW+oPLgS5xNS/TKiFS2Lb46osyc2N4CD/slmtPmSN2/9OFmM6prGN8ww6XXsndm9xco+eFsCPT8vta1K+1VArijaDeHkFDfVGVIliXhPG6ggn7BEjV4MaulKHa0aP73R4nRRtifH17ADTEY/k1p9HTuCPPEfIaDU5QPgh17WeCMMrrHklQLifsIlbLPB3AgVj3xWKwJ5Cx6wJDWXjIMlPOJqc+nN/ohlygmtNdLTePNRuG3/iO7VYzNY2fBp8fuMsI/dYnUaMfPAWW355oyThkZCymOFSz7ngR/Q8LM/VwmUhkA+uyLgFUwfhxPQMSDAL0225PrD7vJCSlwOW14+dWxU61KiamKsSC6cE7ils0R5k4o8k29tWKxJ0nD1SA2uOrkrgPRXeLFoaq7KKq5Sq+P1yeEFmYR+1+zGQ9kLkJM0waJwOEMhkKTtaPn5/DYDTJa3i+KYNToHHIo3M85kOB9mvTo+PJbNeTQs5zvKkXwZR9/4BZW2mYBSTplk9XhZMUkpMA9LneSv02Fc956RbT9bj8HPaikE/zUHvl80EOgfPaWS+huMKl1BR2sK1jyKB/XWenAN6NTVkKw+L6wkaTBKkrEe8suZonqSM1ABK0LFgG6kXy87NoXRUBchw8soeZ3IgvQjgBB7SGI+mE3qJu/TfuI9giwxPA6iu0mBk4EqEBPT71BfbZ5a2FJy6z/33Oh2HMzgVagxvpjHfb3f2hs+Z84Qi4hbYl/Mi2XngN88vG5tGeTMgemD1fUz5/4FsxmR0LH4BTlXHBot7TPW/djwcr0g9EDiVmnb+BAfP4R3GJ25hrCr+mqKlN9Y5oISGUdy0g+kX+v3CIMaAvM0I8elX4mqgCnQiZusFjMNxSlgdZQmBK8aFhauYavOkewNXCQsKKmvbMkY51BeS30UTw9HAe/ilpWNIFFOpFbzpkwKv3SOh0rWTUKnDEO0pvB4rRm356u+cLxcCzW1ZclNQPM5LmiLXMFK41i9heBSkoQRZ5evBPCkC1x5XVhoCy8e27zranqDgGyf7HkmkKFEQjW7tGdAOqfGhqLGSgq1n9kKrl77+KKGRwe+h2Cv0oJI1L6/zZVJuRzivLc8IhH85S+cj8MWVn3PLO50cxCtbiue33ASI7HyqdlnAF3G2pSjEUpQ5fLNHuWHMX5N5Z8pvZLXHBopdq5aqR21I3AQ275wCSmnKupULG/8sU0RtR0gJeBCShwj7ftj8sO/a0m8Xj6XvvMgXELP5V/WyZis+EGL5W70OyzZ0ykaWZjqqj6IhN6VdSH/IAeHE0lpi5pmVW6TzMUdutPU6hfH0nn0s88UYKC4PN5x9Hx58R19vLhIgKqXAToDCJDK3JF8xMwwKAhT/Ihu/K797Y+3Fe1SDcALtZlTA3/D6fMpeNBDfqy5hfAtLTaPytFik2p6/g0+eJOtiK0VIKpQyoIlu6qLghpY+D8eL7Z3YuHsCcc8rcva5ZatCu3XKyEADO3fOZsaZKnQcvAxrByVVHf4Hd8UD9pB9o82PDg2KCWUIkr5odET8PtuTJ8cCDNsVhBtYYH2mYiR1gqi2jS+wPgU8DQ3OhK8Y7+fQof58LwdvPUYW2rujReuPNs5+gvoD2pezTIvP7ZtqtZzEvQWNB79Hw+XTHyNKCYQt7bkoHkitsCMRTb/EHBQ3XDwwjMOVjcBDCFrv6Y90+jmGz55wgepHlkCk8LRaD9J8D3ZQ6oltrKokyFWC5qabpMldyh8U=" autocomplete="off" />
</form>
		</div>
		<!--===== /dialogMessages =====--><div id="cmnContainer">
		<!--=========== .cmnContainer ============--><div id="cmnEmergencyNotice"></div><div id="cmnTitle" class="hgroup ">
		<h1 class="visuallyHidden">お客様情報入力</h1></div>

		<div id="cmnWrapper">
		<!--===== cmnWrapper =====-->
		<ol class="mainFlow" aria-label="完了までの手順、">
			<li><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_flow_01.png?717d3c0" alt="1" height="20" width="28" />フライト検索</li><!--
			--><li><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_flow_02_on.png?717d3c0" alt="2" height="20" width="28" /><span class="visuallyHidden">現在のステップ、</span>お客様情報入力</li><!--
			--><li><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_flow_03.png?717d3c0" alt="3" height="20" width="28" />お支払い情報</li><!--
			--><li class="flowLast"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_flow4_04.png?717d3c0" alt="4" height="20" width="28" />完了</li>
		</ol>

	<div id="main">
	<!--=========== main =============-->
		<ul class="iconExample">
				<li class="requiredIconExample">
					<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="" height="7" width="7" /></span>必須項目です	
				</li>
		</ul>
<form id="j_idt526" name="j_idt526" method="post" action="https://aswbe-i.ana.co.jp/rei21g/international_asw/pages/award/reservation/award_mandatory_passenger_information_input.xhtml?aswcid=1&amp;rand=202506201406388yOLuw4Jlg" autocomplete="off" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="j_idt526" value="j_idt526" />
<input type="hidden" name="j_idt526_operationTicket" value="dljdmx+9eaeb034f0406f4210b8250fa9d4780c~xxoJKwTQLOUr4IUwCzBXaBC4h5vkdyRz-dzK9Vr6!1750395986578.aere-xml-controller-67d4778877-6gwmg" /><input type="hidden" name="j_idt526_cmnPageTicket" value="4" />

<div class="section">
	<h2>搭乗者選択<a href="https://www.ana.co.jp/other/int/meta/0086.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="特典のご利用に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a></h2>
	<table class="passengerInfo">
		<thead>
			<tr>
				
				<th class="name">氏名</th>
				
				<th class="dateOfBirthWide" id="dateOfBirthHeader">生年月日</th>
				
				<th class="gender lastCol" id="genderHeader">性別<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span></th>
				
			</tr>
		</thead>
		<tbody>
				
				<tr>
					<th class="parent_01" id="parentHeader01">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:0:passenger" type="checkbox" name="passengers:0:passenger" checked="checked" data-number="1" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:0:passenger">NAOKI SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader01">1963年06月05日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader01">				
							<span class="formWrapper">
							<label for="passengers:0:gender" class="visuallyHidden">NAOKI SATOの性別</label><select id="passengers:0:gender" name="passengers:0:gender" size="1">	<option value="">選択してください</option>
	<option value="M" selected="selected">男性</option>
	<option value="F">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_02" id="parentHeader02">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:1:passenger" type="checkbox" name="passengers:1:passenger" data-number="2" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:1:passenger">REO SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader02">1995年05月18日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader02">				
							<span class="formWrapper">
							<label for="passengers:1:gender" class="visuallyHidden">REO SATOの性別</label><select id="passengers:1:gender" name="passengers:1:gender" size="1">	<option value="">選択してください</option>
	<option value="M" selected="selected">男性</option>
	<option value="F">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_03" id="parentHeader03">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:2:passenger" type="checkbox" name="passengers:2:passenger" data-number="3" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:2:passenger">ERI SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader03">1963年08月14日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader03">				
							<span class="formWrapper">
							<label for="passengers:2:gender" class="visuallyHidden">ERI SATOの性別</label><select id="passengers:2:gender" name="passengers:2:gender" size="1">	<option value="">選択してください</option>
	<option value="M">男性</option>
	<option value="F" selected="selected">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_04" id="parentHeader04">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:3:passenger" type="checkbox" name="passengers:3:passenger" data-number="4" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:3:passenger">TAISHI SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader04">1992年12月03日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader04">				
							<span class="formWrapper">
							<label for="passengers:3:gender" class="visuallyHidden">TAISHI SATOの性別</label><select id="passengers:3:gender" name="passengers:3:gender" size="1">	<option value="">選択してください</option>
	<option value="M" selected="selected">男性</option>
	<option value="F">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_05" id="parentHeader05">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:4:passenger" type="checkbox" name="passengers:4:passenger" data-number="5" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:4:passenger">HIROMI FUJIMOTO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader05">1961年11月23日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader05">				
							<span class="formWrapper">
							<label for="passengers:4:gender" class="visuallyHidden">HIROMI FUJIMOTOの性別</label><select id="passengers:4:gender" name="passengers:4:gender" size="1">	<option value="">選択してください</option>
	<option value="M">男性</option>
	<option value="F" selected="selected">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
		</tbody>
	</table>
</div>
		<div class="section">
			<h2 class="midstream">連絡先情報
			</h2>
			<h3>代表者
			</h3>
	
	
	
	<div class="formInput">
		<fieldset>
			<legend><label>代表者</label></legend>
			<dl id="contactEmailAddressInfo">
				<dt class="hasRequiredIcon">メールアドレス<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span><a href="https://www.ana.co.jp/other/int/meta/0057.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="メールの宛先と内容に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a>
				</dt>
				<dd>
					<ul class="verticalList">
						<li class="nonMarginTop"><label for="representativeEmail" class="visuallyHidden">代表者メールアドレス</label><input id="representativeEmail" type="text" name="representativeEmail" value="<EMAIL>" class="widthLarge" placeholder="例：<EMAIL>" />
						</li>
						<li>
							<span class="supplementTxt" id="confirmMailAddressForRepresentative">確認のため再度ご入力ください。
							</span><label for="confirmrepresentativeEmail" class="visuallyHidden">確認用メールアドレス</label><input id="confirmrepresentativeEmail" type="text" name="confirmrepresentativeEmail" value="<EMAIL>" class="widthLarge jsNotPasteInput" placeholder="例：<EMAIL>" aria-describedby="confirmMailAddressForRepresentative" />
						</li>
					</ul>
				</dd>
					<dd style="display:none;"><input id="updateAlwaysInfo" type="checkbox" name="updateAlwaysInfo" /><label for="updateAlwaysInfo">ログイン中の会員情報へこのメールアドレスを登録</label>
					</dd>
			</dl>
			<dl>
				<dt>電話番号<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span>
				</dt>
				<dd class="js-countryWrap">
					<ul>
						<li><label for="representativeTelType" class="visuallyHidden">電話番号種別</label><select id="representativeTelType" name="representativeTelType" class="basicWidth js-typeSelect" size="1" onchange="changeTelType(this,'representativeTel')">	<option value="M1" selected="selected">携帯</option>
	<option value="B1">会社</option>
	<option value="H1">自宅</option>
</select>
						</li>
						<li><label for="representativeTelCountry" class="visuallyHidden">電話番号の国/地区選択</label><select id="representativeTelCountry" name="representativeTelCountry" class="js-countrySelect" size="1">	<option value="" data-sms-possible="true" data-country-code="---">選択してください</option>
	<option value="JP" selected="selected" data-sms-possible="true">日本</option>
	<option value="-" data-sms-possible="true" data-country-code="---">-----------</option>
	<option value="IS" data-sms-possible="true">アイスランド</option>
	<option value="IE" data-sms-possible="true">アイルランド</option>
	<option value="AZ" data-sms-possible="true">アゼルバイジャン</option>
	<option value="AF" data-sms-possible="true">アフガニスタン</option>
	<option value="US" data-sms-possible="true">アメリカ合衆国/カナダ</option>
	<option value="AE" data-sms-possible="true">アラブ首長国連邦</option>
	<option value="DZ" data-sms-possible="true">アルジェリア</option>
	<option value="AR" data-sms-possible="true">アルゼンチン</option>
	<option value="AW" data-sms-possible="true">アルバ</option>
	<option value="AL" data-sms-possible="true">アルバニア</option>
	<option value="AM" data-sms-possible="true">アルメニア</option>
	<option value="AO" data-sms-possible="true">アンゴラ</option>
	<option value="AG" data-sms-possible="true">アンティグア・バーブーダ</option>
	<option value="AD" data-sms-possible="true">アンドラ</option>
	<option value="YE" data-sms-possible="true">イエメン</option>
	<option value="GB" data-sms-possible="true">イギリス</option>
	<option value="IL" data-sms-possible="true">イスラエル</option>
	<option value="IT" data-sms-possible="true">イタリア</option>
	<option value="IQ" data-sms-possible="true">イラク</option>
	<option value="IR" data-sms-possible="true">イラン</option>
	<option value="IN" data-sms-possible="true">インド</option>
	<option value="ID" data-sms-possible="true">インドネシア</option>
	<option value="UG" data-sms-possible="true">ウガンダ</option>
	<option value="UA" data-sms-possible="true">ウクライナ</option>
	<option value="UZ" data-sms-possible="true">ウズベキスタン</option>
	<option value="UY" data-sms-possible="true">ウルグアイ</option>
	<option value="EC" data-sms-possible="true">エクアドル</option>
	<option value="EG" data-sms-possible="true">エジプト</option>
	<option value="EE" data-sms-possible="true">エストニア</option>
	<option value="SZ" data-sms-possible="true">エスワティニ</option>
	<option value="ET" data-sms-possible="true">エチオピア</option>
	<option value="ER" data-sms-possible="true">エリトリア</option>
	<option value="SV" data-sms-possible="true">エルサルバドル</option>
	<option value="AU" data-sms-possible="true">オーストラリア</option>
	<option value="AT" data-sms-possible="true">オーストリア</option>
	<option value="OM" data-sms-possible="true">オマーン</option>
	<option value="NL" data-sms-possible="true">オランダ</option>
	<option value="GH" data-sms-possible="true">ガーナ</option>
	<option value="CV" data-sms-possible="true">カーボベルデ</option>
	<option value="GY" data-sms-possible="true">ガイアナ</option>
	<option value="KZ" data-sms-possible="true">カザフスタン</option>
	<option value="QA" data-sms-possible="true">カタール</option>
	<option value="CA" data-sms-possible="true">カナダ/アメリカ合衆国</option>
	<option value="GA" data-sms-possible="true">ガボン</option>
	<option value="CM" data-sms-possible="true">カメルーン</option>
	<option value="KR" data-sms-possible="true">韓国</option>
	<option value="GM" data-sms-possible="true">ガンビア</option>
	<option value="KH" data-sms-possible="true">カンボジア</option>
	<option value="KP" data-sms-possible="true">北朝鮮</option>
	<option value="MK" data-sms-possible="true">北マケドニア</option>
	<option value="GN" data-sms-possible="true">ギニア</option>
	<option value="GW" data-sms-possible="true">ギニアビサウ</option>
	<option value="CY" data-sms-possible="true">キプロス</option>
	<option value="CU" data-sms-possible="true">キューバ</option>
	<option value="GR" data-sms-possible="true">ギリシャ</option>
	<option value="KI" data-sms-possible="true">キリバス</option>
	<option value="KG" data-sms-possible="true">キルギス</option>
	<option value="GT" data-sms-possible="true">グアテマラ</option>
	<option value="GU" data-sms-possible="true">グアム</option>
	<option value="KW" data-sms-possible="true">クウェート</option>
	<option value="GD" data-sms-possible="true">グレナダ</option>
	<option value="HR" data-sms-possible="true">クロアチア</option>
	<option value="KY" data-sms-possible="true">ケイマン諸島</option>
	<option value="KE" data-sms-possible="true">ケニア</option>
	<option value="CI" data-sms-possible="true">コートジボワール</option>
	<option value="CR" data-sms-possible="true">コスタリカ</option>
	<option value="KM" data-sms-possible="true">コモロ</option>
	<option value="CO" data-sms-possible="true">コロンビア</option>
	<option value="CG" data-sms-possible="true">コンゴ共和国</option>
	<option value="CD" data-sms-possible="true">コンゴ民主共和国</option>
	<option value="SA" data-sms-possible="true">サウジアラビア</option>
	<option value="ZM" data-sms-possible="true">ザンビア</option>
	<option value="SM" data-sms-possible="true">サンマリノ</option>
	<option value="SL" data-sms-possible="true">シエラレオネ</option>
	<option value="DJ" data-sms-possible="true">ジブチ</option>
	<option value="JM" data-sms-possible="true">ジャマイカ</option>
	<option value="GE" data-sms-possible="true">ジョージア</option>
	<option value="SY" data-sms-possible="true">シリア</option>
	<option value="SG" data-sms-possible="true">シンガポール</option>
	<option value="ZW" data-sms-possible="true">ジンバブエ</option>
	<option value="CH" data-sms-possible="true">スイス</option>
	<option value="SE" data-sms-possible="true">スウェーデン</option>
	<option value="SD" data-sms-possible="true">スーダン</option>
	<option value="ES" data-sms-possible="true">スペイン</option>
	<option value="SR" data-sms-possible="true">スリナム</option>
	<option value="LK" data-sms-possible="true">スリランカ</option>
	<option value="SK" data-sms-possible="true">スロバキア</option>
	<option value="SI" data-sms-possible="true">スロベニア</option>
	<option value="SC" data-sms-possible="true">セイシェル</option>
	<option value="GQ" data-sms-possible="true">赤道ギニア</option>
	<option value="SN" data-sms-possible="true">セネガル</option>
	<option value="RS" data-sms-possible="true">セルビア</option>
	<option value="KN" data-sms-possible="true">セントクリストファー・ネイビス</option>
	<option value="LC" data-sms-possible="true">セントルシア</option>
	<option value="SB" data-sms-possible="true">ソロモン諸島</option>
	<option value="TH" data-sms-possible="true">タイ</option>
	<option value="TW" data-sms-possible="true">台湾</option>
	<option value="TJ" data-sms-possible="true">タジキスタン</option>
	<option value="TZ" data-sms-possible="true">タンザニア</option>
	<option value="CZ" data-sms-possible="true">チェコ</option>
	<option value="TD" data-sms-possible="true">チャド</option>
	<option value="CF" data-sms-possible="true">中央アフリカ共和国</option>
	<option value="CN" data-sms-possible="true">中国</option>
	<option value="TN" data-sms-possible="true">チュニジア</option>
	<option value="CL" data-sms-possible="true">チリ</option>
	<option value="TV" data-sms-possible="true">ツバル</option>
	<option value="DK" data-sms-possible="true">デンマーク</option>
	<option value="DE" data-sms-possible="true">ドイツ</option>
	<option value="TG" data-sms-possible="true">トーゴ</option>
	<option value="DO" data-sms-possible="true">ドミニカ共和国</option>
	<option value="DM" data-sms-possible="true">ドミニカ国</option>
	<option value="TT" data-sms-possible="true">トリニダード・トバゴ</option>
	<option value="TM" data-sms-possible="true">トルクメニスタン</option>
	<option value="TR" data-sms-possible="true">トルコ</option>
	<option value="TO" data-sms-possible="true">トンガ</option>
	<option value="NG" data-sms-possible="true">ナイジェリア</option>
	<option value="NR" data-sms-possible="true">ナウル</option>
	<option value="NA" data-sms-possible="true">ナミビア</option>
	<option value="NI" data-sms-possible="true">ニカラグア</option>
	<option value="NE" data-sms-possible="true">ニジェール</option>
	<option value="NZ" data-sms-possible="true">ニュージーランド</option>
	<option value="NP" data-sms-possible="true">ネパール</option>
	<option value="NO" data-sms-possible="true">ノルウェー</option>
	<option value="BH" data-sms-possible="true">バーレーン</option>
	<option value="HT" data-sms-possible="true">ハイチ</option>
	<option value="PK" data-sms-possible="true">パキスタン</option>
	<option value="VA" data-sms-possible="true">バチカン</option>
	<option value="PA" data-sms-possible="true">パナマ</option>
	<option value="VU" data-sms-possible="true">バヌアツ</option>
	<option value="BS" data-sms-possible="true">バハマ</option>
	<option value="PG" data-sms-possible="true">パプアニューギニア</option>
	<option value="BM" data-sms-possible="true">バミューダ</option>
	<option value="PW" data-sms-possible="true">パラオ</option>
	<option value="PY" data-sms-possible="true">パラグアイ</option>
	<option value="BB" data-sms-possible="true">バルバドス</option>
	<option value="PS" data-sms-possible="true">パレスチナ</option>
	<option value="HU" data-sms-possible="true">ハンガリー</option>
	<option value="BD" data-sms-possible="true">バングラデシュ</option>
	<option value="TL" data-sms-possible="true">東ティモール</option>
	<option value="FJ" data-sms-possible="true">フィジー</option>
	<option value="PH" data-sms-possible="true">フィリピン</option>
	<option value="FI" data-sms-possible="true">フィンランド</option>
	<option value="BT" data-sms-possible="true">ブータン</option>
	<option value="PR" data-sms-possible="true">プエルトリコ</option>
	<option value="BR" data-sms-possible="true">ブラジル</option>
	<option value="FR" data-sms-possible="true">フランス</option>
	<option value="PF" data-sms-possible="true">フランス領ポリネシア</option>
	<option value="BG" data-sms-possible="true">ブルガリア</option>
	<option value="BF" data-sms-possible="true">ブルキナファソ</option>
	<option value="BN" data-sms-possible="true">ブルネイ</option>
	<option value="BI" data-sms-possible="true">ブルンジ</option>
	<option value="VN" data-sms-possible="true">ベトナム</option>
	<option value="BJ" data-sms-possible="true">ベナン</option>
	<option value="VE" data-sms-possible="true">ベネズエラ</option>
	<option value="BY" data-sms-possible="true">ベラルーシ</option>
	<option value="BZ" data-sms-possible="true">ベリーズ</option>
	<option value="PE" data-sms-possible="true">ペルー</option>
	<option value="BE" data-sms-possible="true">ベルギー</option>
	<option value="PL" data-sms-possible="true">ポーランド</option>
	<option value="BA" data-sms-possible="true">ボスニア・ヘルツェゴビナ</option>
	<option value="BW" data-sms-possible="true">ボツワナ</option>
	<option value="BO" data-sms-possible="true">ボリビア</option>
	<option value="PT" data-sms-possible="true">ポルトガル</option>
	<option value="HK" data-sms-possible="true">香港</option>
	<option value="HN" data-sms-possible="true">ホンジュラス</option>
	<option value="MH" data-sms-possible="true">マーシャル諸島</option>
	<option value="MO" data-sms-possible="true">マカオ</option>
	<option value="MG" data-sms-possible="true">マダガスカル</option>
	<option value="MW" data-sms-possible="true">マラウイ</option>
	<option value="ML" data-sms-possible="true">マリ</option>
	<option value="MT" data-sms-possible="true">マルタ</option>
	<option value="MY" data-sms-possible="true">マレーシア</option>
	<option value="FM" data-sms-possible="true">ミクロネシア連邦</option>
	<option value="ZA" data-sms-possible="true">南アフリカ</option>
	<option value="MM" data-sms-possible="true">ミャンマー</option>
	<option value="MX" data-sms-possible="true">メキシコ</option>
	<option value="MU" data-sms-possible="true">モーリシャス</option>
	<option value="MR" data-sms-possible="true">モーリタニア</option>
	<option value="MZ" data-sms-possible="true">モザンビーク</option>
	<option value="MC" data-sms-possible="true">モナコ</option>
	<option value="MV" data-sms-possible="true">モルディヴ</option>
	<option value="MD" data-sms-possible="true">モルドバ</option>
	<option value="MA" data-sms-possible="true">モロッコ</option>
	<option value="MN" data-sms-possible="true">モンゴル</option>
	<option value="ME" data-sms-possible="true">モンテネグロ</option>
	<option value="JO" data-sms-possible="true">ヨルダン</option>
	<option value="LA" data-sms-possible="true">ラオス人民民主共和国</option>
	<option value="LV" data-sms-possible="true">ラトビア</option>
	<option value="LT" data-sms-possible="true">リトアニア</option>
	<option value="LY" data-sms-possible="true">リビア</option>
	<option value="LI" data-sms-possible="true">リヒテンシュタイン</option>
	<option value="LR" data-sms-possible="true">リベリア</option>
	<option value="RO" data-sms-possible="true">ルーマニア</option>
	<option value="LU" data-sms-possible="true">ルクセンブルク</option>
	<option value="RW" data-sms-possible="true">ルワンダ</option>
	<option value="LS" data-sms-possible="true">レソト</option>
	<option value="LB" data-sms-possible="true">レバノン</option>
	<option value="RU" data-sms-possible="true">ロシア</option>
</select>
						</li>
						<li class="numberInputWrap">
							<span id="numberInputInfo1" class="supplementTxt marginTop">先頭に0(ゼロ)がある場合、0(ゼロ)を抜いてください(一部、国・地域除く)。携帯電話番号の場合は、携帯電話を契約された国を選択ください。</span>
							<div class="numberRowWrap">
								<div class="block">
									
									<span class="visuallyHidden">国番号</span>
									<span class="viewInput widthMini js-countryCode"></span>
								</div>
								<div class="block"><label for="representativeTel" class="visuallyHidden">電話番号</label><input id="representativeTel" type="text" name="representativeTel" value="9042504176" class="widthMiddle" placeholder="例：9012345678" maxlength="30" aria-describedby="numberInputInfo1" />
								</div>
							</div>
						</li>
					</ul>
				</dd>
			</dl>
		</fieldset>
	</div>
	<div class="titleWrapper">
		<h3>
			<span class="text">eメール・SMSに関するご案内<a href="https://www.ana.co.jp/other/int/meta/0057.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="eメール・SMSに関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a></span>
		</h3>
	</div>
	<div class="formInput">
	<h4 class="formAreaTitle">主に搭乗・運航に関わるeメールの送付先設定（米国へ通知いたします）<a href="https://www.ana.co.jp/other/int/meta/0629.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="米国入国用eメール・SMS・電話番号に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a>
	</h4>
	<div class="paxSettingArea js-mailFormWrap onePax is-open">
			<div class="settingArea">
					<dl>
						<dt class="breakWord">NAOKI SATO
						</dt>
						<dd>
							<fieldset>
								<legend>NAOKI SATOのeメール送付先および米国到着時に提供するメールアドレス</legend>代表者と同じ
							</fieldset>
						</dd>
					</dl>
			</div>
	</div>
	<h4 class="formAreaTitle hasTopLine">主に搭乗・運航に関わるSMSの送付先および米国で受信できる電話番号の設定（米国へ通知いたします）<a href="https://www.ana.co.jp/other/int/meta/0629.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="米国入国用eメール・SMS・電話番号に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a>
	</h4>
	<div class="paxSettingArea js-smsFormWrap onePax is-open">
			<div class="settingArea">
					<dl>
						<dt class="breakWord">NAOKI SATO<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span>
						</dt>
						<dd>
							<fieldset>
								<legend>NAOKI SATOの1つ目の電話番号</legend>
									<ul class="verticalList js-formItem">
										<li><input type="radio" id="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSameAsRepresentativeSms:radioItem" name="contactsSms:0:recipientSmsEachPersonSelection:radioGroup" value="0" checked="checked" /><label for="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSameAsRepresentativeSms:radioItem">代表者電話番号と同じ</label>
										</li>
										<li><input type="radio" id="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSmsPerPassenger:radioItem" name="contactsSms:0:recipientSmsEachPersonSelection:radioGroup" value="1" /><label for="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSmsPerPassenger:radioItem">電話番号を指定</label>
											<div class="indentArea js-formImputArea">
												<span id="confirmTelNumberForRepresentative:0" class="supplementTxt marginTop">先頭に0(ゼロ)がある場合、0(ゼロ)を抜いてください(一部、国・地域除く)。携帯電話番号の場合は、携帯電話を契約された国を選択ください。</span>
												<ul class="countryNumberInput js-countryWrap">
													<li><label for="contactsSms:0:recipientSmsEachPersonSelection:passengerSmsCountry" class="visuallyHidden">電話番号の国/地区選択</label><select id="contactsSms:0:recipientSmsEachPersonSelection:passengerSmsCountry" name="contactsSms:0:recipientSmsEachPersonSelection:passengerSmsCountry" class="countrySelect js-countrySelect" size="1">	<option value="" data-country-code="---">選択してください</option>
	<option value="JP">日本</option>
	<option value="-" data-country-code="---">-----------</option>
	<option value="IE">アイルランド</option>
	<option value="AZ">アゼルバイジャン</option>
	<option value="AF">アフガニスタン</option>
	<option value="US">アメリカ合衆国/カナダ</option>
	<option value="AE">アラブ首長国連邦</option>
	<option value="DZ">アルジェリア</option>
	<option value="AR">アルゼンチン</option>
	<option value="AW">アルバ</option>
	<option value="AL">アルバニア</option>
	<option value="AM">アルメニア</option>
	<option value="AG">アンティグア・バーブーダ</option>
	<option value="AD">アンドラ</option>
	<option value="YE">イエメン</option>
	<option value="GB">イギリス</option>
	<option value="IL">イスラエル</option>
	<option value="IT">イタリア</option>
	<option value="IQ">イラク</option>
	<option value="IR">イラン</option>
	<option value="IN">インド</option>
	<option value="ID">インドネシア</option>
	<option value="UG">ウガンダ</option>
	<option value="UA">ウクライナ</option>
	<option value="UZ">ウズベキスタン</option>
	<option value="UY">ウルグアイ</option>
	<option value="EC">エクアドル</option>
	<option value="EG">エジプト</option>
	<option value="EE">エストニア</option>
	<option value="ER">エリトリア</option>
	<option value="SV">エルサルバドル</option>
	<option value="AU">オーストラリア</option>
	<option value="AT">オーストリア</option>
	<option value="OM">オマーン</option>
	<option value="NL">オランダ</option>
	<option value="GH">ガーナ</option>
	<option value="GY">ガイアナ</option>
	<option value="KZ">カザフスタン</option>
	<option value="QA">カタール</option>
	<option value="CA">カナダ/アメリカ合衆国</option>
	<option value="GA">ガボン</option>
	<option value="CM">カメルーン</option>
	<option value="KR">韓国</option>
	<option value="GM">ガンビア</option>
	<option value="KH">カンボジア</option>
	<option value="GN">ギニア</option>
	<option value="GW">ギニアビサウ</option>
	<option value="CY">キプロス</option>
	<option value="CU">キューバ</option>
	<option value="GR">ギリシャ</option>
	<option value="KG">キルギス</option>
	<option value="GT">グアテマラ</option>
	<option value="GU">グアム</option>
	<option value="KW">クウェート</option>
	<option value="GD">グレナダ</option>
	<option value="HR">クロアチア</option>
	<option value="KY">ケイマン諸島</option>
	<option value="KE">ケニア</option>
	<option value="CI">コートジボワール</option>
	<option value="CR">コスタリカ</option>
	<option value="KM">コモロ</option>
	<option value="CO">コロンビア</option>
	<option value="CD">コンゴ民主共和国</option>
	<option value="SA">サウジアラビア</option>
	<option value="ZM">ザンビア</option>
	<option value="SL">シエラレオネ</option>
	<option value="JM">ジャマイカ</option>
	<option value="GE">ジョージア</option>
	<option value="SY">シリア</option>
	<option value="SG">シンガポール</option>
	<option value="ZW">ジンバブエ</option>
	<option value="CH">スイス</option>
	<option value="SE">スウェーデン</option>
	<option value="SD">スーダン</option>
	<option value="ES">スペイン</option>
	<option value="SR">スリナム</option>
	<option value="LK">スリランカ</option>
	<option value="SK">スロバキア</option>
	<option value="SI">スロベニア</option>
	<option value="SN">セネガル</option>
	<option value="RS">セルビア</option>
	<option value="KN">セントクリストファー・ネイビス</option>
	<option value="LC">セントルシア</option>
	<option value="TH">タイ</option>
	<option value="TW">台湾</option>
	<option value="TJ">タジキスタン</option>
	<option value="TZ">タンザニア</option>
	<option value="CZ">チェコ</option>
	<option value="TD">チャド</option>
	<option value="CF">中央アフリカ共和国</option>
	<option value="CN">中国</option>
	<option value="TN">チュニジア</option>
	<option value="CL">チリ</option>
	<option value="DK">デンマーク</option>
	<option value="DE">ドイツ</option>
	<option value="TG">トーゴ</option>
	<option value="DO">ドミニカ共和国</option>
	<option value="TT">トリニダード・トバゴ</option>
	<option value="TR">トルコ</option>
	<option value="NG">ナイジェリア</option>
	<option value="NI">ニカラグア</option>
	<option value="NE">ニジェール</option>
	<option value="NZ">ニュージーランド</option>
	<option value="NP">ネパール</option>
	<option value="NO">ノルウェー</option>
	<option value="HT">ハイチ</option>
	<option value="PK">パキスタン</option>
	<option value="PA">パナマ</option>
	<option value="BS">バハマ</option>
	<option value="PG">パプアニューギニア</option>
	<option value="BM">バミューダ</option>
	<option value="PW">パラオ</option>
	<option value="PY">パラグアイ</option>
	<option value="BB">バルバドス</option>
	<option value="PS">パレスチナ</option>
	<option value="HU">ハンガリー</option>
	<option value="BD">バングラデシュ</option>
	<option value="TL">東ティモール</option>
	<option value="FJ">フィジー</option>
	<option value="PH">フィリピン</option>
	<option value="FI">フィンランド</option>
	<option value="BT">ブータン</option>
	<option value="PR">プエルトリコ</option>
	<option value="BR">ブラジル</option>
	<option value="FR">フランス</option>
	<option value="PF">フランス領ポリネシア</option>
	<option value="BG">ブルガリア</option>
	<option value="BF">ブルキナファソ</option>
	<option value="BN">ブルネイ</option>
	<option value="BI">ブルンジ</option>
	<option value="VN">ベトナム</option>
	<option value="BJ">ベナン</option>
	<option value="VE">ベネズエラ</option>
	<option value="BY">ベラルーシ</option>
	<option value="PE">ペルー</option>
	<option value="BE">ベルギー</option>
	<option value="PL">ポーランド</option>
	<option value="BA">ボスニア・ヘルツェゴビナ</option>
	<option value="BW">ボツワナ</option>
	<option value="HK">香港</option>
	<option value="MH">マーシャル諸島</option>
	<option value="MO">マカオ</option>
	<option value="MG">マダガスカル</option>
	<option value="MW">マラウイ</option>
	<option value="ML">マリ</option>
	<option value="MT">マルタ</option>
	<option value="MY">マレーシア</option>
	<option value="ZA">南アフリカ</option>
	<option value="MM">ミャンマー</option>
	<option value="MX">メキシコ</option>
	<option value="MU">モーリシャス</option>
	<option value="MR">モーリタニア</option>
	<option value="MZ">モザンビーク</option>
	<option value="MC">モナコ</option>
	<option value="MV">モルディヴ</option>
	<option value="MA">モロッコ</option>
	<option value="MN">モンゴル</option>
	<option value="ME">モンテネグロ</option>
	<option value="JO">ヨルダン</option>
	<option value="LA">ラオス人民民主共和国</option>
	<option value="LV">ラトビア</option>
	<option value="LT">リトアニア</option>
	<option value="LY">リビア</option>
	<option value="LR">リベリア</option>
	<option value="RO">ルーマニア</option>
	<option value="LU">ルクセンブルク</option>
	<option value="RW">ルワンダ</option>
	<option value="LB">レバノン</option>
	<option value="RU">ロシア</option>
</select>
													</li>
													<li>
														
														<div class="numberRowWrap">
															<div class="block">
																<span class="visuallyHidden">国番号</span>
																<span class="countryCode viewInput js-countryCode"></span>
															</div>
															
															<div class="block"><label for="contactsSms:0:recipientSmsEachPersonSelection:flightStatusNotificationContactPointSmsDescription" class="visuallyHidden">電話番号</label><input id="contactsSms:0:recipientSmsEachPersonSelection:flightStatusNotificationContactPointSmsDescription" type="text" name="contactsSms:0:recipientSmsEachPersonSelection:flightStatusNotificationContactPointSmsDescription" class="telNumber" placeholder="例：9012345678" maxlength="30" aria-describedby="confirmTelNumberForRepresentative:0" />
															</div>
														</div>
													</li>
												</ul>
											</div>
										</li>
									</ul>
							</fieldset>
						</dd>
					</dl>
			</div>
	</div>
	</div>
	
	<div class="formInput articleLine">
		<h3>滞在中の連絡先(任意)
		</h3>
		<dl>
			
			<dt><label for="accommodationOne">現地連絡先1</label>
			</dt>
			<dd><input id="accommodationOne" type="text" name="accommodationOne" class="widthLarge" placeholder="例：09012345678 ABC HOTEL" maxlength="28" title="例：09012345678 ABC HOTEL" />
			</dd>
		</dl>
		<dl>
			
			<dt><label for="accommodationTwo">現地連絡先2</label>
			</dt>
			<dd><input id="accommodationTwo" type="text" name="accommodationTwo" class="widthLarge" placeholder="例：09012345678 ABC HOTEL" maxlength="28" title="例：09012345678 ABC HOTEL" />
			</dd>
		</dl>
	</div>
		</div>
		
		<p class="btnAreaSubmit btnArrowNext "><input id="next" type="submit" name="next" value="次へ" class="btnBase btnMainStream btnVerticalMain btnWidthVariable" onclick="return Asw.Dialog.getInstance('prebookConfirmDialog').toggle(event);return Asw.LoadingWindow.open(&quot;NORMAL&quot;, event)" />
		</p><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:2" value="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" autocomplete="off" />
</form><div id="cmnDynamicMessages" class="infoBoxSection">
				<dl class="infoBox importantInfo jsAccordionSwitch">
					<dt><strong>重要なご案内</strong></dt>
					<dd>
						<ul class="jsAccordionSwitchList" id="informationMessages">
								<li>個人情報の利用目的<a href="https://www.ana.co.jp/other/int/meta/0012.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="ANAプライバシーポリシー(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">ANAプライバシーポリシー<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>を必ずお読みいただき、内容に同意いただいた上で、お進みください。</li>
								<li><a href="https://www.ana.co.jp/other/int/meta/0058.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="Secure Flight Programについて(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">米国Secure Flightプログラム<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>に基づき、パスポート名、生年月日、性別などの情報は事前に米国へ通知いたします。</li>
								<li>入力したeメールアドレス、電話番号は入国に必要な情報として米国に通知される可能性があります。</li>
									<li class="jsHiddenFlg toggleContents">お名前のスペルがパスポート名と一致しているかご確認ください。異なる場合はインターネットではお取り扱いできません。<a href="https://www.ana.co.jp/other/int/meta/intbe0001.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="ANAお問い合わせ先(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">ANA<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>へ名前の変更をお申し出ください。</li>
									<li class="jsHiddenFlg toggleContents">二重国籍のお客様は、ご搭乗時に使用されるパスポートのお名前でご予約ください。<br />パスポートごとにお名前が異なる場合は、お客様ご自身で入国管理事務所・外務省・大使館などで各種条件をご確認のうえ、使用されるパスポートを決めてください。<br />なお、入国可否については国によって異なるため、お客様ご自身で各入国管理事務所などでご確認ください。</li>
									<li class="jsHiddenFlg toggleContents">旅行開始日時点で12歳以上の場合は大人、2歳以上12歳未満の場合は小児、2歳未満の場合は幼児の扱いとなります。なお、小児は大人と同マイル数が必要です。</li>
									<li class="jsHiddenFlg toggleContents">運航に関する情報をお客様へ通知するため、提携航空会社にもお客様のメールアドレスが提供されます。あらかじめご了承ください。</li>
									<li class="jsHiddenFlg toggleContents"><dl><dt>携帯電話へのメール送信をご希望のお客様へ</dt><dd>ドメイン指定受信をご利用の場合は、ana.co.jpとamadeus.comからのメールが受信できるようあらかじめ設定をお願いいたします。</dd></dl></li>
									<li class="jsHiddenFlg toggleContents">SMSはスマートフォンの番号に配信されます。一部の国ではSMSでのご案内をご利用いただけません。詳細は<a href="https://www.ana.co.jp/other/int/meta/0737.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="ショートメッセージ(SMS)でのご案内対象外の国について(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">ショートメッセージ(SMS)でのご案内対象外の国について<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>をご覧ください。</li>
									<li class="jsHiddenFlg toggleContents">ログインされている会員ご本人が搭乗しない場合は、再度フライト検索画面より「ログインされている会員ご本人は搭乗しない」のチェックボックスを選択してお手続きください。</li>
						</ul>
							<a href="#" class="toggleSwitch jsTriggerSwitch" role="button" aria-controls="informationMessages" aria-expanded="false"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/toggle_open.png?7bec38a" alt="開閉ボタン" height="25" width="25" class="jsRollOver" />
							</a>
					</dd>
				</dl></div>
	<!--=========== /main =============-->
	</div>

	<!--=========== Script ============-->
	<script type="text/javascript">
	$(window).on('load', function() {
		// アコーディオンのメソッドと設定
		Asw.AccordionInfo('.jsAccordionSwitch', {
			'triggerSwitch': '.jsAccordionSwitch .jsTriggerSwitch',
			'listClass': '.jsAccordionSwitchList',
			'visibilityHidden': 'toggleContents'
		});
	});
	</script>
	<!--=========== Script ============-->
	<script type="text/javascript">
		//プラットフォーム判別（サマリーエリア追従用として）
		var Asw = Asw || {};
	
	  	Asw.ClientInfo = Asw.ClientInfo  || {};
	  	Asw.ClientInfo.DeviceType = Asw.ClientInfo.DeviceType || {};
	
	  	Asw.ClientInfo.DeviceType.PC = 'PC';
	  	Asw.ClientInfo.DeviceType.SP = 'SP';
	  	Asw.ClientInfo.DeviceType.TC = 'TC';
	
	  	Asw.ClientInfo.deviceType = Asw.ClientInfo.DeviceType.PC;
	
	 	if((navigator.userAgent.indexOf('Android') > 0 && navigator.userAgent.indexOf('Mobile') == -1) || navigator.userAgent.indexOf('iPad') > 0){
	    	Asw.ClientInfo.deviceType = Asw.ClientInfo.DeviceType.TC;
	  	}
	</script>
	<!--=========== /Script ============--><div id="summaryArea">
			<div class="flightSummaryArea">
						<div class="flightSummarySection">
							<h2>区間 1
							</h2>
							<p class="flightDate"><em>6</em>月<em class="hasLeft">28</em>日（土）
							</p>
							<div class="flightSummaryRoute">
									<div class="flightSummaryRouteSection">
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation1_1">
													<span>東京(成田)
													</span>
												</p>
												<p class="time">
													<span>17:00
													</span>
												</p>
											</div>
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation1_1">
													<span>シカゴ(ORD)
													</span>
												</p>
												<p class="time">
													<span>14:55
													</span>
												</p>
											</div>
										<p class="flightNumberClass">
												<span>NH012</span><span><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/flight_ana_summaryarea.png?717d3c0" alt="ANA運航便" height="11" width="41" /></span>
												<br />特典ビジネス
										</p>
									</div>
							</div>
						</div>
						<div class="flightSummarySection">
							<h2 class="midstream">区間 2
							</h2>
							<p class="flightDate"><em>7</em>月<em class="hasLeft">23</em>日（水）
							</p>
							<div class="flightSummaryRoute">
									<div class="flightSummaryRouteSection">
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation2_1">
													<span>ホノルル(オアフ島)
													</span>
												</p>
												<p class="time">
													<span>11:35
													</span>
												</p>
											</div>
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation2_1">
													<span>東京(成田)
													</span>
												</p>
												<p class="time">
													<span>14:50
																	<span>翌日
																	</span>
													</span>
												</p>
											</div>
										<p class="flightNumberClass">
												<span>NH183</span><span><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/flight_ana_summaryarea.png?717d3c0" alt="ANA運航便" height="11" width="41" /></span>
												<br />特典エコノミー
										</p>
									</div>
							</div>
						</div>
					<div class="total">
						<p class="passenger">大人 x1
						</p>
						<div class="totalArea">
					  		<dl class="mealAmountWrap">
									<dt>有料機内食サービス
									</dt>
									<dd><em class="mealAmount">0</em><span class="currencyCode">円</span>
									</dd>
							</dl>
									<div>
										<div class="mileageAndTotal">
												<dl class="requiredMileage">
													<dt>必要マイル
													</dt>
													<dd><em class="requiredMileage">74,000</em><span class="currencyCode">マイル</span>
													</dd>
												</dl>
												<dl>
													<dt>総額
													</dt>
													<dd><em class="awardTotalPayment">68,130</em>円
													</dd>
												</dl>
												<p class="includeFareAndFuelCost">※各種税金、燃油特別付加運賃等を含みます。
												</p>
										</div>
									</div>
						</div>
					</div>
				
			</div></div>
		<!--===== /cmnWrapper =====-->
		</div>
			
		<span id="message" class="visuallyHidden" role="alert" aria-live="assertive"></span>
		<span id="politeMessage" class="visuallyHidden" role="log" aria-live="polite"></span><script type="text/javascript">
		var Asw = Asw || {};
		Asw.BaseOutput = {"sessionID":"8yOLuw4Jlg","language":"ja","siteCatalystLanguageCode":"J","operationDate":"20250620","operationDateTime":"20250620140638","office":{"officeCode":"TYONH08ZZ","country2letterCode":"JP"},"bookingType":"A","pageID":"A04_P01","siteCatalystPageName":"INT_BE_AWARD_J_A04特典予約_P01旅客情報入力（必須情報）","device":{"deviceType":"PC"}};
	</script><div id="promotionArea">
		<!--===== promotionArea =====-->
		<!--===== /promotionArea =====--></div>
		<!--=========== /cmnContainer ============--></div><div id="cmnBaloonArea" aria-live="polite"></div><div id="cmnPopupArea" aria-live="polite"></div>

	

	<div id="cmnFooterWrapper">
		<!--=========== .cmnFooterWrapper ============-->
		<div id="cmnFooter">
			<!--===== cmnFooter =====-->
			<ul>
				<li><a href="https://www.ana.co.jp/other/int/meta/0012.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">プライバシーポリシー<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
					<li><a href="https://www.ana.co.jp/other/int/meta/0670.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">利用者情報の外部送信について<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
					</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/0052.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">サイト利用規約<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/be0238.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">推奨環境<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/0133.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">サイトマップ<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/0732.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">運送約款<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
					<li class="lastChild"><a href="https://www.ana.co.jp/other/int/meta/0556.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">ウェブアクセシビリティについて<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
					</li>
			</ul>
			<p class="blankText" aria-hidden="true"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="" height="10" width="10" />外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。</p>
			<p class="copyright"><span lang="en" xml:lang="en">Copyright&#169;ANA</span>
			</p>
			<p class="starAllianceLogo"><a href="http://www.staralliance.com/ja/" target="_blank"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/star_alliance_logo.png?0fe73b5" alt="A STAR ALLIANCE MEMBER" height="18" width="175" /><span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
			</p>
			<!--===== cmnFooter =====-->
		</div>
		<!--=========== /cmnFooterWrapper ============-->
	</div>

		
		<div id="cmnLoadingForAjax"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/bg_modalWindow.png?717d3c0" alt="" height="0" width="0" />
			<div class="loadingArea">
				<p class="loadingImage"><img src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/image/loading.gif?717d3c0" alt="リクエストを処理しています...しばらくお待ちください。" height="33" width="33" />
				</p>
			</div>
		</div>
		
		
		<div id="popupAltSentence" style="display: none" aria-hidden="true">別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。</div>
	<div id="cmnSiteCatalyst" aria-hidden="true"><div id="cmnSiteCatalystParamArea"><script type="text/javascript">
				// ▼SiteCatalyst
				// SiteCatalyst code version: H.2.
				// Copyright 1997-2005 Omniture, Inc. More info available at http://www.omniture.com
				SiteCatalystReportSuites   = "INT";
				SiteCatalystCharSet        = "UTF-8";
				SiteCatalystChannel        = "BE_AWARD_J";
				SiteCatalystPageName       = "INT_BE_AWARD_J_A04\u7279\u5178\u4E88\u7D04_P01\u65C5\u5BA2\u60C5\u5831\u5165\u529B\uFF08\u5FC5\u9808\u60C5\u5831\uFF09";
				SiteCatalystERR            = "";
				SiteCatalystEventNum       = "";
				SiteCatalystPurchase       = "FALSE";
				SiteCatalystProductPrice   = "";
				SiteCatalystCurrencyCode   = "";
				SiteCatalystSeatAvailabilityINT   = "";
				SiteCatalystDepartDate     = "";
				SiteCatalystDepart         = "";
				SiteCatalystArrive         = "";
				SiteCatalystFlightNo       = "";
				SiteCatalystClass          = "";
				SiteCatalystFare           = "";
				SiteCatalystPurchaseID     = "";
				
				SiteCatalystLocater        = "";
				SiteCatalystPayment        = "";
				SiteCatalystIncome         = "";
				
				SiteCatalystPax            = "";
				SiteCatalystManipulateDay  = "";
				SiteCatalystCompartmentClass   = "";
				SiteCatalystOptionInfo     = "";
				SiteCatalystOutboundUpsellPrice   = "";
				SiteCatalystInboundUpsellPrice    = "";
				
				SiteCatalystSearchInput    = "";
				SiteCatalystSegment        = "";
				SiteCatalystOfficeCode     = "";
				SiteCatalystPassengerCount = "";
				SiteCatalystPassenger      = "";
				SiteCatalystAncillaryService = "";
				// End SiteCatalyst code version: H.2.
				// ▲SiteCatalyst
			</script></div><script type="text/javascript"><!-- if(navigator.appVersion.indexOf('MSIE')>=0)document.write(unescape('%3C')+'\!-'+'-') // --></script>
		<!--/DO NOT REMOVE/-->
		

		
		<!-- Google Tag Manager -->
		<script type="text/javascript" charset="Shift_JIS" src="https://www.ana.co.jp/common/js/GTMINTBE.js"></script>
		<!-- End Google Tag Manager -->
		<!-- Yahoo Tag Manager -->
		<script type="text/javascript" charset="Shift_JIS" src="https://www.ana.co.jp/common/js/YTMINTBE.js"></script>
		<!-- End Yahoo Tag Manager -->
		

	</div>

	

	<div class="mboxDefault"></div><script type="text/javascript">mboxCreate("ASW_common_A04_P01");</script>
			<script type="text/javascript" src="https://www.ana.co.jp/common/js/tealium/tealium.js"></script>
			<script type="text/javascript" src="//cdn.evgnet.com/beacon/allnipponairways/nonamcprd/scripts/evergage.min.js"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/javax.faces.resource/jsf.js.xhtml?ln=javax.faces"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/jquery.mousewheel.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/jquery.jscrollpane.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/jquery-ui-1.10.4.custom.min.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/jquery.ui.touch-punch.min.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/asw-common.js?beb6a50"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/asw-common-pc.js?fd88d7d"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/asw-format.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/mask.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/loading.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/asw-loadingwindow-pc.js?9e233c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/asw-loadingwindow.js?51db600"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/dialog-pc.js?25d36a4"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/asw-dialog-sequencer.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/asw-event.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/asw-date-select.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/asw-calendar-pc.js?9b86b8a"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/asw-calendar.js?db624c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/heightLine.js?0e7a1ce"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/asw-summaryarea.js?b21c6d7"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/asw-fingerprints.js?99001fc"></script><script type="text/javascript">
			Asw.init("https://aswbe-i.ana.co.jp/rei21g/international_asw", "ja");
			Asw.getCalendarHolidayList = function() {return [20250721,20250811,20250915,20250923,20251013,20251103,20251123,20251124,20260101,20260112,20260211,20260223,20260320,20260429,20260503,20260504,20260505,20260506,20260720,20260811,20260921,20260922,20260923,20261012,20261103,20261123,20270101,20270111,20270211,20270223,20270321,20270322,20270429,20270503,20270504,20270505,20270719,20270811,20270920,20270923,20271011,20271103,20271123,20280101,20280110,20280211,20280223,20280320,20280429,20280503,20280504,20280505,20280717,20280811,20280918,20280922,20281009,20281103,20281123,20290101,20290108,20290211,20290212,20290223,20290320,20290429,20290430,20290503,20290504,20290505,20290716,20290811,20290917,20290923,20290924,20291008,20291103,20291123];}
			Asw.ClientInfo.deviceType = "PC";
			Asw.ClientInfo.mobileDeviceType = "UNKNOWN";
			Asw.ClientInfo.osVersion = "";
			Asw.Format.init("https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/{1}/{2}/{3}?{0}");
			Asw.overwriteTargetAttributeValue("false");
		</script><script type="text/javascript">
		
		var Asw = Asw || {};
		Asw.ContactInput = Asw.ContactInput || {};
		Asw.ContactInput.Const = {
			/** data-属性名「data-type」 @constant @type {String} @default 'data-type' */
			DATA_ATTRIBUTE_NAME_TYPE: 'data-type',
			/** data-属性名「data-sms-possible」 @constant @type {String} @default 'data-sms-possible' */
			DATA_ATTRIBUTE_NAME_SMS_POSSIBLE: 'data-sms-possible',
			/** data-属性名「data-country-code」 @constant @type {String} @default 'data-country-code' */
			DATA_ATTRIBUTE_NAME_COUNTRY_CODE: 'data-country-code',
			/** data-type属性値「sms」 @constant @type {String} @default 'sms' */
			DATA_TYPE_VALUE_SMS: 'sms'
		};

		
		Asw.ContactInput.countryInfoMap = {"IS":{"internationalCallingCode":"+354","smsPossible":false},"IE":{"internationalCallingCode":"+353","smsPossible":true},"AZ":{"internationalCallingCode":"+994","smsPossible":true},"AF":{"internationalCallingCode":"+93","smsPossible":true},"US":{"internationalCallingCode":"+1","smsPossible":true},"AE":{"internationalCallingCode":"+971","smsPossible":true},"DZ":{"internationalCallingCode":"+213","smsPossible":true},"AR":{"internationalCallingCode":"+54","smsPossible":true},"AW":{"internationalCallingCode":"+297","smsPossible":true},"AL":{"internationalCallingCode":"+355","smsPossible":true},"AM":{"internationalCallingCode":"+374","smsPossible":true},"AO":{"internationalCallingCode":"+244","smsPossible":false},"AG":{"internationalCallingCode":"+1","smsPossible":true},"AD":{"internationalCallingCode":"+376","smsPossible":true},"YE":{"internationalCallingCode":"+967","smsPossible":true},"GB":{"internationalCallingCode":"+44","smsPossible":true},"IL":{"internationalCallingCode":"+972","smsPossible":true},"IT":{"internationalCallingCode":"+39","smsPossible":true},"IQ":{"internationalCallingCode":"+964","smsPossible":true},"IR":{"internationalCallingCode":"+98","smsPossible":true},"IN":{"internationalCallingCode":"+91","smsPossible":true},"ID":{"internationalCallingCode":"+62","smsPossible":true},"UG":{"internationalCallingCode":"+256","smsPossible":true},"UA":{"internationalCallingCode":"+380","smsPossible":true},"UZ":{"internationalCallingCode":"+998","smsPossible":true},"UY":{"internationalCallingCode":"+598","smsPossible":true},"EC":{"internationalCallingCode":"+593","smsPossible":true},"EG":{"internationalCallingCode":"+20","smsPossible":true},"EE":{"internationalCallingCode":"+372","smsPossible":true},"SZ":{"internationalCallingCode":"+268","smsPossible":false},"ET":{"internationalCallingCode":"+251","smsPossible":false},"ER":{"internationalCallingCode":"+291","smsPossible":true},"SV":{"internationalCallingCode":"+503","smsPossible":true},"AU":{"internationalCallingCode":"+61","smsPossible":true},"AT":{"internationalCallingCode":"+43","smsPossible":true},"OM":{"internationalCallingCode":"+968","smsPossible":true},"NL":{"internationalCallingCode":"+31","smsPossible":true},"GH":{"internationalCallingCode":"+233","smsPossible":true},"CV":{"internationalCallingCode":"+238","smsPossible":false},"GY":{"internationalCallingCode":"+592","smsPossible":true},"KZ":{"internationalCallingCode":"+7","smsPossible":true},"QA":{"internationalCallingCode":"+974","smsPossible":true},"CA":{"internationalCallingCode":"+1","smsPossible":true},"GA":{"internationalCallingCode":"+241","smsPossible":true},"CM":{"internationalCallingCode":"+237","smsPossible":true},"KR":{"internationalCallingCode":"+82","smsPossible":true},"KH":{"internationalCallingCode":"+855","smsPossible":true},"GM":{"internationalCallingCode":"+220","smsPossible":true},"KP":{"internationalCallingCode":"+850","smsPossible":false},"MK":{"internationalCallingCode":"+389","smsPossible":false},"GN":{"internationalCallingCode":"+224","smsPossible":true},"GW":{"internationalCallingCode":"+245","smsPossible":true},"CY":{"internationalCallingCode":"+357","smsPossible":true},"CU":{"internationalCallingCode":"+53","smsPossible":true},"GR":{"internationalCallingCode":"+30","smsPossible":true},"KI":{"internationalCallingCode":"+686","smsPossible":false},"KG":{"internationalCallingCode":"+996","smsPossible":true},"GT":{"internationalCallingCode":"+502","smsPossible":true},"GU":{"internationalCallingCode":"+1","smsPossible":true},"KW":{"internationalCallingCode":"+965","smsPossible":true},"KY":{"internationalCallingCode":"+1","smsPossible":true},"GD":{"internationalCallingCode":"+1","smsPossible":true},"HR":{"internationalCallingCode":"+385","smsPossible":true},"KE":{"internationalCallingCode":"+254","smsPossible":true},"CI":{"internationalCallingCode":"+225","smsPossible":true},"CR":{"internationalCallingCode":"+506","smsPossible":true},"SA":{"internationalCallingCode":"+966","smsPossible":true},"KM":{"internationalCallingCode":"+269","smsPossible":true},"CO":{"internationalCallingCode":"+57","smsPossible":true},"CG":{"internationalCallingCode":"+242","smsPossible":false},"CD":{"internationalCallingCode":"+243","smsPossible":true},"ZM":{"internationalCallingCode":"+260","smsPossible":true},"SM":{"internationalCallingCode":"+378","smsPossible":false},"SL":{"internationalCallingCode":"+232","smsPossible":true},"DJ":{"internationalCallingCode":"+253","smsPossible":false},"JM":{"internationalCallingCode":"+1","smsPossible":true},"CH":{"internationalCallingCode":"+41","smsPossible":true},"GE":{"internationalCallingCode":"+995","smsPossible":true},"SE":{"internationalCallingCode":"+46","smsPossible":true},"SY":{"internationalCallingCode":"+963","smsPossible":true},"ES":{"internationalCallingCode":"+34","smsPossible":true},"SG":{"internationalCallingCode":"+65","smsPossible":true},"LK":{"internationalCallingCode":"+94","smsPossible":true},"SK":{"internationalCallingCode":"+421","smsPossible":true},"SI":{"internationalCallingCode":"+386","smsPossible":true},"ZW":{"internationalCallingCode":"+263","smsPossible":true},"SD":{"internationalCallingCode":"+249","smsPossible":true},"KN":{"internationalCallingCode":"+1","smsPossible":true},"SR":{"internationalCallingCode":"+597","smsPossible":true},"LC":{"internationalCallingCode":"+1","smsPossible":true},"SC":{"internationalCallingCode":"+248","smsPossible":false},"SN":{"internationalCallingCode":"+221","smsPossible":true},"SB":{"internationalCallingCode":"+677","smsPossible":false},"RS":{"internationalCallingCode":"+381","smsPossible":true},"TH":{"internationalCallingCode":"+66","smsPossible":true},"TW":{"internationalCallingCode":"+886","smsPossible":true},"TJ":{"internationalCallingCode":"+992","smsPossible":true},"TZ":{"internationalCallingCode":"+255","smsPossible":true},"CZ":{"internationalCallingCode":"+420","smsPossible":true},"TD":{"internationalCallingCode":"+235","smsPossible":true},"CF":{"internationalCallingCode":"+236","smsPossible":true},"TN":{"internationalCallingCode":"+216","smsPossible":true},"CL":{"internationalCallingCode":"+56","smsPossible":true},"TV":{"internationalCallingCode":"+688","smsPossible":false},"DK":{"internationalCallingCode":"+45","smsPossible":true},"DE":{"internationalCallingCode":"+49","smsPossible":true},"TG":{"internationalCallingCode":"+228","smsPossible":true},"DO":{"internationalCallingCode":"+1","smsPossible":true},"DM":{"internationalCallingCode":"+1","smsPossible":false},"TT":{"internationalCallingCode":"+1","smsPossible":true},"TM":{"internationalCallingCode":"+993","smsPossible":false},"TR":{"internationalCallingCode":"+90","smsPossible":true},"TO":{"internationalCallingCode":"+676","smsPossible":false},"NG":{"internationalCallingCode":"+234","smsPossible":true},"NR":{"internationalCallingCode":"+674","smsPossible":false},"NA":{"internationalCallingCode":"+264","smsPossible":false},"NI":{"internationalCallingCode":"+505","smsPossible":true},"BH":{"internationalCallingCode":"+973","smsPossible":false},"NE":{"internationalCallingCode":"+227","smsPossible":true},"PK":{"internationalCallingCode":"+92","smsPossible":true},"JP":{"internationalCallingCode":"+81","smsPossible":true},"BS":{"internationalCallingCode":"+1","smsPossible":true},"NZ":{"internationalCallingCode":"+64","smsPossible":true},"BM":{"internationalCallingCode":"+1","smsPossible":true},"NP":{"internationalCallingCode":"+977","smsPossible":true},"BB":{"internationalCallingCode":"+246","smsPossible":true},"NO":{"internationalCallingCode":"+47","smsPossible":true},"HT":{"internationalCallingCode":"+509","smsPossible":true},"BD":{"internationalCallingCode":"+880","smsPossible":true},"VA":{"internationalCallingCode":"+39","smsPossible":false},"PA":{"internationalCallingCode":"+507","smsPossible":true},"VU":{"internationalCallingCode":"+678","smsPossible":false},"PG":{"internationalCallingCode":"+675","smsPossible":true},"PW":{"internationalCallingCode":"+680","smsPossible":true},"PY":{"internationalCallingCode":"+595","smsPossible":true},"PS":{"internationalCallingCode":"+970","smsPossible":true},"BR":{"internationalCallingCode":"+55","smsPossible":true},"HU":{"internationalCallingCode":"+36","smsPossible":true},"TL":{"internationalCallingCode":"+670","smsPossible":true},"BG":{"internationalCallingCode":"+359","smsPossible":true},"BF":{"internationalCallingCode":"+226","smsPossible":true},"BN":{"internationalCallingCode":"+673","smsPossible":true},"FJ":{"internationalCallingCode":"+679","smsPossible":true},"PH":{"internationalCallingCode":"+63","smsPossible":true},"FI":{"internationalCallingCode":"+358","smsPossible":true},"BT":{"internationalCallingCode":"+975","smsPossible":true},"BY":{"internationalCallingCode":"+375","smsPossible":true},"BZ":{"internationalCallingCode":"+501","smsPossible":false},"PR":{"internationalCallingCode":"+1","smsPossible":true},"BE":{"internationalCallingCode":"+32","smsPossible":true},"BA":{"internationalCallingCode":"+387","smsPossible":true},"FR":{"internationalCallingCode":"+33","smsPossible":true},"BO":{"internationalCallingCode":"+591","smsPossible":false},"PF":{"internationalCallingCode":"+689","smsPossible":true},"BI":{"internationalCallingCode":"+257","smsPossible":true},"VN":{"internationalCallingCode":"+84","smsPossible":true},"BJ":{"internationalCallingCode":"+229","smsPossible":true},"VE":{"internationalCallingCode":"+58","smsPossible":true},"PE":{"internationalCallingCode":"+51","smsPossible":true},"PL":{"internationalCallingCode":"+48","smsPossible":true},"BW":{"internationalCallingCode":"+267","smsPossible":true},"PT":{"internationalCallingCode":"+351","smsPossible":false},"HK":{"internationalCallingCode":"+852","smsPossible":true},"HN":{"internationalCallingCode":"+504","smsPossible":false},"MH":{"internationalCallingCode":"+692","smsPossible":true},"MO":{"internationalCallingCode":"+853","smsPossible":true},"MG":{"internationalCallingCode":"+261","smsPossible":true},"MW":{"internationalCallingCode":"+265","smsPossible":true},"ML":{"internationalCallingCode":"+223","smsPossible":true},"MT":{"internationalCallingCode":"+356","smsPossible":true},"MY":{"internationalCallingCode":"+60","smsPossible":true},"FM":{"internationalCallingCode":"+691","smsPossible":false},"MM":{"internationalCallingCode":"+95","smsPossible":true},"MX":{"internationalCallingCode":"+52","smsPossible":true},"MU":{"internationalCallingCode":"+230","smsPossible":true},"MR":{"internationalCallingCode":"+222","smsPossible":true},"MZ":{"internationalCallingCode":"+258","smsPossible":true},"MC":{"internationalCallingCode":"+377","smsPossible":true},"MV":{"internationalCallingCode":"+960","smsPossible":true},"MD":{"internationalCallingCode":"+373","smsPossible":false},"RO":{"internationalCallingCode":"+40","smsPossible":true},"MA":{"internationalCallingCode":"+212","smsPossible":true},"MN":{"internationalCallingCode":"+976","smsPossible":true},"ME":{"internationalCallingCode":"+382","smsPossible":true},"JO":{"internationalCallingCode":"+962","smsPossible":true},"LA":{"internationalCallingCode":"+856","smsPossible":true},"RU":{"internationalCallingCode":"+7","smsPossible":true},"LV":{"internationalCallingCode":"+371","smsPossible":true},"LT":{"internationalCallingCode":"+370","smsPossible":true},"LY":{"internationalCallingCode":"+218","smsPossible":true},"GQ":{"internationalCallingCode":"+240","smsPossible":false},"LI":{"internationalCallingCode":"+423","smsPossible":false},"LR":{"internationalCallingCode":"+231","smsPossible":true},"CN":{"internationalCallingCode":"+86","smsPossible":true},"LU":{"internationalCallingCode":"+352","smsPossible":true},"ZA":{"internationalCallingCode":"+27","smsPossible":true},"RW":{"internationalCallingCode":"+250","smsPossible":true},"LS":{"internationalCallingCode":"+266","smsPossible":false},"LB":{"internationalCallingCode":"+961","smsPossible":true}};

		$(function () {
			contactInfoInit();
		});
		
		function contactInfoInit() {
			
			
			Asw.get('representativeTelType').find('option[value="M1"]').attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_TYPE,
					Asw.ContactInput.Const.DATA_TYPE_VALUE_SMS);
			Asw.get('representativeTelCountry').children('option').each(function(i,option){
				var countryInfo = Asw.ContactInput.countryInfoMap[option.value];
				if (countryInfo === void 0) {
					
					return true;
				}
				$(option).attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_SMS_POSSIBLE, countryInfo.smsPossible);
				$(option).attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_COUNTRY_CODE, countryInfo.internationalCallingCode);
			});
			
			var smsCountrySelectSelector = 'select.js-countrySelect:not(#representativeTelCountry)'
			$(smsCountrySelectSelector).eq(0).children('option').each(function(i,option){
				var countryInfo = Asw.ContactInput.countryInfoMap[option.value];
				if (countryInfo === void 0) {
					
					return true;
				}
				$(smsCountrySelectSelector + ' option:nth-child(' + (i+1) + ')').attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_COUNTRY_CODE,
						countryInfo.internationalCallingCode);
			});
			
			var emergencyCountrySelectSelector = 'select.passengerEmergencyCountry.js-countrySelect:not(#representativeTelCountry)'
			$(emergencyCountrySelectSelector).eq(0).children('option').each(function(i,option){
				var countryInfo = Asw.ContactInput.countryInfoMap[option.value];
				if (countryInfo === void 0) {
					
					return true;
				}
				$(emergencyCountrySelectSelector + ' option:nth-child(' + (i+1) + ')').attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_COUNTRY_CODE,
						countryInfo.internationalCallingCode);
			});
		}
	</script><script type="text/javascript">
		var isInit = true;
		function changeTelType(_this, telNoInputId) {
			var $telNoInput = Asw.get(telNoInputId);
			var $telNoKind = Asw.get("representativeTelType");
			var telNo = Asw.getValueComparedToPlaceholder(telNoInputId);
			var telNoCountry = Asw.get("representativeTelCountry").val();

			if(true && !isInit) {
				if(telInputCheck(telNo, telNoCountry)) {
					if (_this.value == "M1") {
						telNo = exceptDomesticPrefix("09042504176", telNoCountry);
					} else if (_this.value == "B1") {
						telNo = exceptDomesticPrefix("03-5405-2172", telNoCountry);
					} else {
						telNo = exceptDomesticPrefix("090-4250-4176", telNoCountry);
					}
					$telNoInput.val(telNo);
				}
			}
			isInit = false;
		}
		
		function telInputCheck(telNo, telNoCountry) {
	        checkResult = false;
	        
	        var amcHndyTelNo = exceptDomesticPrefix("09042504176", telNoCountry);
	        var amcCmpTelNo = exceptDomesticPrefix("03-5405-2172", telNoCountry);
	        var amcTelNo = exceptDomesticPrefix("090-4250-4176", telNoCountry);
	
	        if(telNo == "" || telNo == amcHndyTelNo || telNo == amcCmpTelNo || telNo == amcTelNo) {
	        	checkResult = true;
	        }
	        return checkResult;
    	}
    	
    	function exceptDomesticPrefix(telNo, telNoCountry) {
    		if(telNoCountry == 'JP') {
    			if(telNo.substr(0, 1) === '0') {
    				return telNo.slice(1);
    			}
    		}
    		return telNo;
    	}
	</script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/asw-customerinfomationform.js?6e91953"></script><script type="text/javascript">
		$(document).ready(function(){
				Asw.enableTextWhenCheckBox('.cascadedSwitch');
				Asw.notPasteInput('.jsNotPasteInput');
			$(function(){
				Asw.alignHeightToParentTdElement( '.passengerInfoInputArea' );
				
					Asw.alignHeightToParentTdElement( '.passengerInfoInputText' );
				
				
				$('select.js-countrySelect').trigger('change');
			});
		});
	</script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21g/international_asw/contents/cmn/script/asw-session-keeper.js?6ec0730"></script><script type="text/javascript">
			$(document).ready(function() {
				Asw.SessionKeeper.initAndStart("sessionKeeperContainer", "520", "570", "PC");
			});
			</script><script type="text/javascript">
		Asw.addAjaxCallback(function(data) {
			// アコーディオンのメソッドと設定 
			if($('.jsAccordionSwitch .jsTriggerSwitch').length > 0){
				var events = $._data($('.jsAccordionSwitch .jsTriggerSwitch').get(0), "events");
				if(events === undefined || events.click === undefined) {
					Asw.AccordionInfo('.jsAccordionSwitch', {
						'triggerSwitch': '.jsAccordionSwitch .jsTriggerSwitch',
						'listClass': '.jsAccordionSwitchList',
						'visibilityHidden': 'toggleContents'
					});
				}
			}
		});
	</script><script type="text/javascript">
	Asw.Price = Asw.Price || {};
	// 整数の区切り文字
	Asw.Price.separateString = ',';
	
	// 整数の区切り桁数
	Asw.Price.separateDigitNumber = 3;
	
	// 小数点
	Asw.Price.decimalPointString = '.';
	
	// 小数点以降の有効桁数
	Asw.Price.significantDigit = 0;
	
	// 表示金額の単位
	Asw.Price.currencyUnit = '円';
	</script><script type="text/javascript">
			$(document).ready(function() {
				$('#preLoadingArea').css('visibility', 'hidden');
			});
		</script><noscript><img src="https://aswbe-i.ana.co.jp/akam/13/pixel_47fd25bc?a=dD1hOGU1NzVkY2QyNDMxYTAyNmJkMzVjNzBlN2EwYjUxOGUwNTU3Mzc0JmpzPW9mZg==" style="visibility: hidden; position: absolute; left: -999px; top: -999px;" /></noscript><script type="text/javascript"  src="/Q1XS4o/Cn_ls/KfDO7/xA/t9OfhmEwSiGQaG/NG4dCQE/bw/QJBChQIQEB"></script></body>
</html>