#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试ANA会话延长URL修复
基于抓包分析的关键发现
"""

from loguru import logger

# 配置日志
logger.add("test_url_fix.log", rotation="10 MB", level="DEBUG")

def analyze_url_issue():
    """分析URL使用问题的关键发现"""
    
    logger.info("=== ANA会话延长URL问题分析 ===")
    
    logger.info("🔍 关键发现:")
    logger.info("通过分析抓包文件 ana-network-log-2025-06-19T02_59_33.479Z.har")
    logger.info("发现成功的会话延长请求使用的URL包含rand参数！")
    
    logger.info("📋 抓包文件中的成功请求URL:")
    success_url = "https://aswbe-i.ana.co.jp/rei22f/international_asw/pages/common/password_input.xhtml?rand=20250619113246liiGBxMOF4"
    logger.info(f"成功URL: {success_url}")
    
    logger.info("❌ 原代码的问题:")
    logger.info("1. 使用表单的action URL: urljoin(current_page_url, form.get('action'))")
    logger.info("2. 这可能丢失了重要的rand参数")
    logger.info("3. 服务器可能需要rand参数来验证请求的有效性")
    
    logger.info("✅ 修复方案:")
    logger.info("1. 直接使用当前页面的完整URL作为请求URL")
    logger.info("2. 保留所有查询参数，特别是rand参数")
    logger.info("3. 确保与浏览器行为完全一致")
    
    logger.info("🔧 代码修改:")
    logger.info("修改前:")
    logger.info("  action_url = urljoin(current_page_url, form.get('action'))")
    logger.info("修改后:")
    logger.info("  action_url = current_page_url  # 使用当前页面的完整URL")
    
    logger.info("💡 为什么这个修复很重要:")
    logger.info("1. rand参数可能是时间戳或随机数，用于防止重放攻击")
    logger.info("2. 服务器可能验证请求URL的完整性")
    logger.info("3. JSF框架可能需要特定的URL格式")
    logger.info("4. 这是浏览器实际使用的URL格式")
    
    logger.info("🎯 预期效果:")
    logger.info("1. ✅ 不再出现60秒超时错误")
    logger.info("2. ✅ 不再出现连接重置错误")
    logger.info("3. ✅ 服务器接受并处理会话延长请求")
    logger.info("4. ✅ 收到正确的JSF AJAX XML响应")
    logger.info("5. ✅ 成功延长会话9次")

def show_comparison():
    """显示修复前后的对比"""
    
    logger.info("=== 修复前后对比 ===")
    
    logger.info("❌ 修复前:")
    logger.info("请求URL: 可能缺少rand参数的URL")
    logger.info("结果: 60秒超时 + 连接重置")
    logger.info("原因: 服务器拒绝不完整的URL请求")
    
    logger.info("✅ 修复后:")
    logger.info("请求URL: 包含完整rand参数的当前页面URL")
    logger.info("结果: 应该成功处理会话延长请求")
    logger.info("原因: 与浏览器行为完全一致")
    
    logger.info("📊 技术细节:")
    logger.info("1. rand参数格式: rand=20250619113246liiGBxMOF4")
    logger.info("2. 参数含义: 可能包含日期时间和随机字符串")
    logger.info("3. 验证机制: 服务器可能验证rand参数的有效性")
    logger.info("4. 安全考虑: 防止会话劫持和重放攻击")

def show_testing_plan():
    """显示测试计划"""
    
    logger.info("=== 测试计划 ===")
    
    logger.info("🚀 测试步骤:")
    logger.info("1. 运行修复后的ana_search.py")
    logger.info("2. 观察会话延长请求的日志")
    logger.info("3. 检查是否还有60秒超时错误")
    logger.info("4. 验证是否收到成功的响应")
    
    logger.info("✅ 成功标志:")
    logger.info("- 日志显示: '使用当前页面URL作为请求URL'")
    logger.info("- 不再有: 'Operation timed out after 60 seconds'")
    logger.info("- 不再有: 'Connection was reset'")
    logger.info("- 看到: '会话延长成功' 消息")
    logger.info("- 完成: 9次会话延长循环")
    
    logger.info("🔍 如果仍然失败:")
    logger.info("1. 检查URL是否包含rand参数")
    logger.info("2. 验证其他请求头是否正确")
    logger.info("3. 确认payload格式是否完整")
    logger.info("4. 检查dtPC值是否正确")

if __name__ == "__main__":
    logger.info("开始ANA会话延长URL问题分析...")
    analyze_url_issue()
    show_comparison()
    show_testing_plan()
    logger.info("分析完成！关键URL修复已准备就绪。")
