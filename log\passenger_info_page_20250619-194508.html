<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="ja-<PERSON>" xml:lang="ja-<PERSON>">

	
	<!-- skey = 2025/06/19 20:45:08 rei21c pLeIAYEgoD dljdmx+495  --><head id="j_idt53">
    <base href="about:blank">
    <style>
        /* Hide elements that might try to load external resources, as a fallback. */
        img, svg, video, iframe, link[rel="stylesheet"], script { display: none !important; }
    </style>

		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
			<meta http-equiv="X-UA-Compatible" content="IE=EmulateIE9; IE=EmulateIE10" />
			<meta http-equiv="X-UA-Compatible" content="IE=7" />
			<meta http-equiv="X-UA-Compatible" content="IE=edge" />
		<meta name="description" content="" />
		<meta name="keywords" content="" />
		<meta name="format-detection" content="telephone=no" />
		<script type="text/javascript" src="/international_asw/dynatrace/ruxitagentjs_ICANVfhqrux_10273230920145353.js" data-dtconfig="rid=RID_401508431|rpid=*********|domain=aswbe-i.ana.co.jp|reportUrl=https://bf90419wvr.bf.dynatrace.com/bf|app=78bf0b58acf6ed13|cors=1|owasp=1|featureHash=ICANVfhqrux|xb=www^bs.google-analytics^bs.com^pana^bs.demdex^bs.net^pcollect^bs.tealiumiq^bs.com^pallnipponairways^bs.australia-3^bs.evergage^bs.com^pallnipponairwayscolt^bs.tt^bs.omtrdc^bs.net|rdnt=1|uxrgce=1|cuc=k8vpgh61|mel=100000|expw=1|dpvc=1|md=mdcc1=bAsw.PnrOutput.recordLocator,mdcc2=bAsw.PnrOutput.paymentDetailInfoList[0].fopCode,mdcc3=bAsw.PnrOutput.ticketedFlag,mdcc4=bAsw.PnrOutput.nhPnrFlag,mdcc5=bAsw.PnrOutput.itinerarySummary.departureDateYyyymmdd,mdcc6=bAsw.SearchCriteriaOutput.cffCodeInput,mdcc7=a#main ^rb div.section.sectionNoFlow ^rb table ^rb tbody ^rb tr ^rb td.flightNumber.lastCol ^rb span ^rb span:nth-child(2),mdcc8=cunique|lastModification=1750184416409|tp=500,50,0|agentUri=/international_asw/dynatrace/ruxitagentjs_ICANVfhqrux_10273230920145353.js"></script><link rel="shortcut icon" href="https://www.ana.co.jp/favicon.ico" />
		<title>お客様情報入力 | ANA</title>
		<noscript><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/css/asw_noscript_pc.css?99001fc" />
		</noscript>

		<!--  [ Adobe Target ] -->
		<script type="text/javascript" src="https://www.ana.co.jp/behavior/adobetarget/target_intbe.js"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/jquery-1.8.3.min.js?717d3c0"></script><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/ja/css/asw_initialize.css?99001fc" /><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/css/asw_screen_pc.css?99001fc" /><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/css/asw_common_pc.css?99001fc" /><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/css/asw_forminput_pc.css?99001fc" /><script type="text/javascript">
		var Asw = Asw || {};
		Asw.GlobalWord = Asw.GlobalWord || {};
		
		Asw.GlobalWord.toolTipCloseIconAltText = "閉じる";
		
		</script><script >bazadebezolkohpepadr="1317077365"</script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/akam/13/4e8103ad"  defer></script></head><body>
		
		<noscript>当サイトをご利用になるためには、JavaScript対応のブラウザが必要です。設定でJavaScriptを有効にしてください。有効にできない場合はANA電話窓口へお問い合わせください。
		</noscript>
		<div id="preLoadingArea"></div>
	
	<div id="cmnHeader" class="platinum">
		<!--===== cmnHeader =====-->
		<div class="headerWrapper">
			<div class="header">
				<p class="logo"><a href="https://www.ana.co.jp/asw/AMCTopServlet?type=a" tabindex="-1" aria-hidden="true"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/ana_logo_navy.png?717d3c0" alt="ANA Inspiration of JAPAN。リンク先はマイレージクラブトップページです。" height="28" width="274" /></a>
				</p>
					<ul class="headerMenu">
						<li><a href="https://www.ana.co.jp/asw/AMCTopServlet?type=a">ANAマイレージクラブトップページ</a>
						</li>
						<li><a href="https://www.ana.co.jp/other/int/meta/0005.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">お問い合わせ<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
						</li>
							<li class="btnLogoutArea"><a href="https://aswbe-i.ana.co.jp/rei21c/international_asw/rest/logout" role="button" class="btnBase btnLogout">ログアウト</a>
							</li>
					</ul>
			</div>
		</div>
			<div class="amcUserInfoWrapper">
				<div class="amcUserInfo">
					<div class="userNameArea">
						<table>
							<tr>	
									<td class="statusIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_status_05.png?717d3c0" alt="プラチナサービスメンバー" height="46" width="60" />
									</td>
								<td class="userName">佐藤 直樹 <span>様</span>
								</td>
							</tr>					
						</table>
					</div>
						<div class="balance">
								<dl class="upgradePointBalance">
									<dt>アップグレードポイント残高
									</dt>
									<dd>
											<dl>
												<dt>(2025年度)
												</dt>
												<dd>
													<em>20
													</em>ポイント
												</dd>
											</dl>
									</dd>
								</dl>
							<dl class="mileBalance">
								<dt>マイル口座残高
								</dt>
								<dd><em>75,663</em><span class="currencyCode">マイル</span>
								</dd>
							</dl>
						</div>
				</div>
					<p class="nowTime">6月19日20時45分現在
					</p>
			</div>
		<!--===== /cmnHeader =====-->
	</div>
		<div id="sessionKeeperContainer" class="sessionKeepingContainer"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/bg_modalWindow.png?717d3c0" alt="" height="0" width="0" />
			<div id="extendSessionExpiredDialog" class="modalExpire modalSmall modalContainer" role="dialog" aria-labelledby="extendSessionExpiredDialogTitle" aria-hidden="true">
<form id="sessionKeeperContainer:j_idt183" name="sessionKeeperContainer:j_idt183" method="post" action="https://aswbe-i.ana.co.jp/rei21c/international_asw/pages/award/reservation/award_mandatory_passenger_information_input.xhtml?aswcid=1&amp;rand=20250619204508pLeIAYEgoD" autocomplete="off" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="sessionKeeperContainer:j_idt183" value="sessionKeeperContainer:j_idt183" />
<input type="hidden" name="sessionKeeperContainer:j_idt183_operationTicket" value="dljdmx+495a2f379a1025a7eac474deadbcd5db~nsM_pQhdUeXEcKPD-FFpJPFxmi3DCQZFO3FY8cqJ!1750333496030.aere-xml-controller-67d4778877-wn7sm" /><input type="hidden" name="sessionKeeperContainer:j_idt183_cmnPageTicket" value="4" />
					<h1 id="extendSessionExpiredDialogTitle" class="dialogTitle">まもなくセッションが終了します</h1>
					<div id="extendSessionExpiredDialogMessage" class="dialogMessage" tabindex="-1">このままお手続きを進める場合は、［延長］ボタンを押してください。</div>
					<p class="modalButton"><input id="sessionKeeperContainer:cmnSessionKeepingButton" type="submit" name="sessionKeeperContainer:cmnSessionKeepingButton" value="延長" class="btnBase btnModal btnMainStream" onclick="mojarra.ab(this,event,'action',0,0,{'onevent':function(data) {Asw.doCommonAjaxCallback(data,false, event); Asw.SessionKeeper.doSessionKeep(data);},'onerror':function(data) {Asw.doCommonAjaxErrorCallback(data); }});return false" />
					</p><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:0" value="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" autocomplete="off" />
</form>
			</div>
			<div id="showSessionExpiredDialog" class="modalExpire modalSmall modalContainer" role="dialog" aria-labelledby="showSessionExpiredDialogTitle" aria-hidden="true">
				<h1 id="showSessionExpiredDialogTitle" class="dialogTitle">セッションが終了しました</h1>
				<div id="showSessionExpiredDialogMessage" class="dialogMessage" tabindex="-1">一定時間操作されなかったため、お手続きを進めることができません。お手数ですが、再度トップページからお手続きください。</div>
				<p class="modalButton">
					<input type="submit" value="閉じる" class="btnBase btnModal btnMainStream" onclick="Asw.SessionKeeper.closeTimeoutDialog(); return false;" />
				</p>
			</div>
		</div>
		<!--===== dialogMessages =====--><div id="cmnModalMessages"></div>
		<div id="prebookConfirmDialog" class="modalContainer modalConfirm modalSmall " aria-hidden="true" role="dialog">
<form id="prebookConfirmDialog:j_idt403" name="prebookConfirmDialog:j_idt403" method="post" action="https://aswbe-i.ana.co.jp/rei21c/international_asw/pages/award/reservation/award_mandatory_passenger_information_input.xhtml?aswcid=1&amp;rand=20250619204508pLeIAYEgoD" autocomplete="off" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="prebookConfirmDialog:j_idt403" value="prebookConfirmDialog:j_idt403" />
<input type="hidden" name="prebookConfirmDialog:j_idt403_operationTicket" value="dljdmx+495a2f379a1025a7eac474deadbcd5db~nsM_pQhdUeXEcKPD-FFpJPFxmi3DCQZFO3FY8cqJ!1750333496030.aere-xml-controller-67d4778877-wn7sm" /><input type="hidden" name="prebookConfirmDialog:j_idt403_cmnPageTicket" value="4" /><div class="dialogMessage" tabindex="0"><p>この後、名・姓の修正や変更ができません。</p><p>「搭乗者選択」欄の氏名が「名」「姓」の順番で表示されていることを再確認ください。</p><p>例）<strong class="highlight">空野 太郎</strong> 様 → MR <strong class="highlight">TARO SORANO</strong></p><p>保安上の理由により、航空券とパスポートのお名前が同じ表記でない場合はご搭乗いただくことができませんので必ず再度ご確認ください。</p></div>
					
					<ul class="modalBtnHorizontal">
							
							<li><input type="submit" name="prebookConfirmDialog:j_idt419" value="戻る" aria-controls="prebookConfirmDialog" class="btnBase btnModal btnCancel" onclick="Asw.Dialog.getInstance('prebookConfirmDialog').close(event, Asw.DialogSequencer.isActiveSequencerNotDisplayedLastDialog());  return false;" />
							</li>
						
						<li class="btnArrowNext"><input type="submit" name="prebookConfirmDialog:j_idt422" value="OK" aria-controls="prebookConfirmDialog" class="btnBase btnModal btnMainStream " onclick="Asw.Dialog.getInstance('prebookConfirmDialog').callOpener('NORMAL', event); return false;" />
						</li>
					</ul><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:1" value="3AVh3BAKDu1FbY7VEb+kPoY5eDMhdB27HJTDfl3pPmBWi0BjYWk0WaiNIMHdUVhEmmszvhSjNXoq9KmyWSn117Q8auKzye1aPbOBsN5EAlHC90DHJA6W0fOm/5yhN0ZGRwc9GcqpCOnGZYD69WvgwPflZll+BBxBcMeCrioSLTyKGCK4gubs5P3FWrgfpvpaPtPX7FtOESXkSU8kbpkiqNqvKGjqS4mzhER+0rbgkAEF3fgk7Xc2Jh4efTrVndKzWQ9gJ0S2hL2hXFtemrrCZxxryWsFUlp3jyKUxg1y1zx+M852mwMh2dSPQU0rI7vuBXBQh/9g37qG3CmPcwQNthl26voNlaOUoiOrUG35C+tgMcxVVrhQzrCUHEBfiseie+uofuh+pjw06I5+N66w+QyKMbVwn0Xg9mijkukR3TSqBxDlCLd3ZuvURkbs1OIWiHjhegfEzzL6qrI4N+kWZ2QWC/6EVZeZ3/bp9bUWzDUfhAPVbAgMwr4OFaSG25bruKJrt+OZBLbiikpd/JCo/CSRq1MbSfpMhbWL9Zc3XLiRkUcuWJ5dW1cA8AH7xZMldDc9JBSugQF6yFe/FUJc05UEzfvsUaDvkiMzDiyy2bxxPgn40vN1WzU8Wf1Ezq2uZCIO4yHcp3s1WUsMMZ3hi3aUQ9UvbZOrRnpg7J/Zt46RrAwR66JoGcUgKt+xIa7z5iq3QwERTx3jRRRylDi7LuxAslJfSLqsKWcDXfNghOTDhiSkyNGws5IBbew3GAtzUhbjOEmfubbIxBdNuu63Z4xyWwWqF8KvjB9IDrrMF343KGTmeQ+PIyJ9PlFcRA8gKiAeBo8PH4usKWgl8G0gL2wmHx+gEqbnVjz51WyTJzIOtBdxc38EDwK7e3T0MdtcGcNN3nvSIf4ggg99r7lgshIsvXKskNQ7XS2QQze3m+us748bzAQpTd+Plo6kUD8Td3si8e5XHtMtrU+ipNbR6SPV2lj10HEk3rzplRoIJ52l56OT6KO9mMSQRev1MvfjNtD3J2HWe+H9JOpho0+GK7iyDzTi/ivZYsKL2yjxCwH/vzAVZ35Vd8rhTGWpTjsDmGBEK6qnlOxZ9aypOVLcrX1ENt+xuThL+0gSxeO4nk5DFM8OvQtnmBgPrJ94OIzj04WUlBe4d6HAjOHQ/vezicvr2+F9KR4YnFcPwzF0mQH7yDCaMsdDQ/SW7Cfw7rypbaG0Kp1TNYj5WnYy3Ajv0W77HFSyh1aHXcGs4VTSSP4XaP8lDqXkEK7GNPODOcwsYJwQSsWo9Nwv3/bJ1Pm7FS7GWmZ/mTNvDUrCD9x8PRHKUhCljt4grAsbnY9zFMoG3rZW6+Dyzc+99UWKBNBkv2sK2YvWCa/b5g/RMsOm4U+J1sFsIQFYbsCH+V7nUTrQZd8o9064IrKtQvyXtKaJK2q9padyzjfnL8kMHdcihPg7p1/eb/Sm91njllgDSoa+hElFpATyImHOlUzr6J9uvwAQbu/Ildcc/JGDfRh58cYBzTcCAz5eIfYFztxu0eEghhq8WY9rJQt9hxMubTkdaY3SVSDt1V1Q1sDLMTqthiqMgt/JGNnGyW5ibBmmT/5ND+cJJklQJXbn9jzUi7Z882cXYvssl/BGFNRgFP2uSIrJiuJdeZUbYhWCzI4kEj+IKPGJ6t5euq/2gV5vrUfNkuyldkPrmh9Kc3/f2JwD+qnWKoOdGLqcL77UyEDZcbNx95p7pe4EwnJOdnNYlHKyvUEkVtzQOMAaIbZC2Q87etofrlNP6NrQXv5CaTHwGJnA+f9wvHTufTOPoTO8wC65vHfZ2eeUspRBgkUn/Iqv9JIe8h64qn+uHam6AtA1MpaMVDT6+AyGdYbWR62LbKK4H4GID5BonRIdbKmd+eHxnZ81h0K4LlzOGzaUTgUaryK84yFwgDMRXCunsbUtVNtwOqwdvhaeXZrAe3JggoQDOza9kWQmlxyyMz6HoleOStVDGJ0MrL75K0ovOpCW7aY65QXslHlMaVJ1YkkrqhbccwsXnurAN3U8vU92R9AZ9kk9w/LoAck6VH5tcpjmwQ1OY9iP8971U8sjaqCOjV5bq+HCsLsO5+6fiYljIaQW30XJOIzZABS/axWhx21HCP8IOCX1DnBFKVtQ2eHAPoY1iHAu/ULB9PatMMnx+c7eXjwmiGfe97+El2XWLjsuUybCZiX4st5NpfIydqRpngrySPmpew2uiwOAS4vI/AaqoGPXl4fJj5coJxqO8gYJobrTsUr3FT8pHVCiVrWQMHYbKsPnW2wv6JOhdiZCamCpZd3X0KFJUg+fWg5x2Es5R4TCvagAPngK5zRZK2I4DTzeJNKqY2c+GHYZ1fw51+one1wC/9JgYftZeuPbgBdyB2dc79SjUsCTZ53yk61pDUtJtG14f75JjSAERjgMdWzQgkWo6gbmMsvcKlqIR3mhoWqhIjPLhyq+VWA0j+E9fYsc/1Zx+tWhFWaGvoE/8g+hRKgBjQ0WGVcQBAmSHhoFywYYBshSjchXkwbOenRbtHKxcKx/rJh7cGZEtEXuFWMZdjLnu/U0i/+YvN7SJUnlsO1FubGNEXxMea4Qmj2TiBAtYmL91DllcHlgt6FmSxhTu1FbRD/G/iu3W8/KfPBURhYK3AGPHJxxMcBH82ncQx9OEYALzO2SnQbv+0RPb1ugtKRkN2YKMO9UB+6gHajZmDd3vju9/ELgnX+tWUNmSSgEJUieyjZoUTu2Gecu20ZJrVxKdWPhuFZnIDHXOk87RXNMjXCTs9R/fX066jSgqH8pGybW14AyK19jgDjod3ekfxdf/5cjGJxUpXyUaO2y1u5RSoakGlvoo90KQ7vlk9MSQkrvVgIx5W/OtOHEZtWgrdl936htaOnmOKZWv94HQcl5sQxopu9yO0p3FGkcViXgNEfXxwH2CwS6xsrdzrkvnEAtJfXNIYxtbGuOwkBZgT1hGB+J0qMClX72rCdahnLoqXPAZeZ/ydqjNDoIy3nEk9BxGoDanyHiFw5n2Fn1wRbbx47ZqV4Nwk7IKxU/0Sx5Cp2rqVq2PPk5ge3x5t7nRXU0Bgdl4M4WEQHWBtAMVX3DZ4LUXrkit4RBgCtCUBJJHM6jDRj/sAtadaMkFUFDxoHVODjXv1WBZFeU+nLySmpyLOvVXlSo7IcDw3MTiJZ5qu2hnXBQafr3ujjxSItqBZni+onTbptJmjKp05mS4/QEWQt2/IshL/knrkzEAQRDoUiVfleWymxieV/g/DqLppDMLqpMJtEecE/6sC3Q9poF+Nh0xGNJWrCeYKFyLA6isnZ5GITXoCrkIdt/QLmVsaEFzpklUBvk7nq77wAKYlrOQDbC6cRvxWGTdwGJ1E6qTKbFciVOn4DDETCd/vBYO0orVt8jiF/mKJ9ks2Sq/6rdP4vJTqbySeUyr6ngnB+7/mUhXOVi/b9/0TualO3cZGJyqQAHSfBDd4ntbjzfqwVGK82Mm4XICBRu2HeU9sELEcX0W6tHSCHXvvZLhgHMstbTeq6TYNpHUkBZkH9v0vAW1i8yNjhgV54md+eTG2g7VVSzeaGfVuCkrogRRxyI3Fsr164SEyZLHWEjtN62J1DH0rCuEjb/t2I2Mk3luh7AEO5VcBOrzaFzXiBF9YbuXsSoInB+Usfz4/JJMBbbMwpnIokCxrDFDL18ZA3oqqJShRgzUDG5+dEPa21RN7VQwN/VS3/z9sfVW1mpsSZawI83tgwPIj2sHLi0k4wnmvW3zTNYmd1d8/c0iX7Kaw6LwVr55D/H2lUdBX8AWC18pMVBmOAbGdlu7/McI834n3D1DrdJvBUpsLGcX23PiHFICH8pTasFgdVL4ai3S5dTIksc8XwBqFiQKnsS1mSw7EvcZOvHX9It5p+t0fZZ92ki7VTpUTBxN4U9LhQvr5c3wVZYc2psn0xb7bLKNomndi0ZBjulennLCziM9J0irEEIFrfTNVXu8Px+L1DpsEW47mNbN2g3wGCamDC8wWlm9FSX8rZUzANqncfCEfQzHp4f739WVajKlR/HhtZZUX8fcAS5KwiPoi7BfNEKkg2xZ6FlvEQTtZDd6LyIQA7op2oLUzQBWf0DqcnHpcp6hE5yEdYYXOfgA4AzExSfuADqx3dbEbPrzNDwPY9Ljf+B8BBHLDjCtUlvvjG52nmC+VrWJ+1B1r9b8jjLZGYopWCashzjJRwLFL5vk8xFIaT1FvzWPxyMUZB1Uc8NPHhcYgQo1KtsMwAHJUeRlR6Nyk91kxjDaZZFzydo/FnvZYHAieS4K2hwLgtcqVT2GN3Zm8r1uNXsZ30DMwbpqCIDAjo3zN885zYbD2zCK7/g3YEyoEKfT9/as+kHKcTUpRFeUNssX1Zkesa1iXp2JKTHq8Iu18gQksiGmXwFy5qyIVeoZVAGoZte7aDoYMpjBmMGMRke5vCPwfpCkje0QN/T6WitYbuAoPma8OQORHpAjM1gfO7tAAvJ+xqLzB3k8U/wN3gH4WEtMW2LvAqFbR351yAG7RlRew1gydhSNAxRtpYcx65ofwTE4IPbv2+Ya+i/s67H7d44EoS6chCIcrAk5ef4Wt1pgeeRMM0Jx8tdAFjJyoSs4SEAKrCc9tygpbje4N4Stq+hBq2Pif48aHsRDMrm/gFm3+xFCMGu1lWnedKfvAJDVQYK1nHnQ0u0zXcnTkqP8Dv2R7f8xa3XLEzXN+FLYEHpW64R02Bgg20VgfhPdMBhcgiHeyqjGo8W9NaN+BlsxNfl1rW/eHb8BGhABERnsOAEtm3Wm4BU4OP8HjOoa0h9AMoa6m6miJSDePuAxSJvU6WgNN/WPADVxWSqIvx7mNO+VVvYK8PFT9YK6hLIV63FKOHuXumErgSCgSks9jdbxw/jzZbAzXjf715e/uTgpySHE9glPg86PVvuq0/MhFg3nuYTH/7nTa88ftrnNLY0OnUEAWSDE8yPq3gFZej09ffEMhPcxBbE//P/AOCw7KTYBmn8bQyz/oG+VY5G597WkfVS/LaJnssZADJl7RSKRqMvWPMRGe54c+M3tfWju78B4BW8bmTU7ronaxwyPJ8+hBl5G0sjhzzpWPOVLJJhFi4myjSuM6rtyD0hbO+9N44178NLrY0P8ltPSIVcR6hUuhkLRYGK41B2BSqt71gX1wUjl9uoWiYb/SdLGhq+snCrkXd2zY1xabaXgI/iyqK8APgXBE4ycXcLht6pKYz6xUGyev0iZYs4O1L3EsCkuEjCfULvUPVkpp8Twzhs9o0aU5V7dslSCnFzFJNgoA/P2D0bKNwkHqt3mi/WDRTi3NKSwMR+MWf3NfTTzYUdlDrkdsNB7lJWyLASXmSIBk1kE+2SqrgCoWBYJS429kYMwKRVsl03L6IrRpspBNsXcgklfVzofS5eWsVUP8IETGg0a4A6OuJvBzQo56d9rgjZm+/B33QQYD1rpX4r1pyvqg0YYk2ZgnUWFUFARyR5kgQ/Bl/SWOIG99tRLIx8kElPtmGYs5Ox0wDg52jZqqPjopSJ9Fs2zCeQJXWmNLr3lhesXBY2FkaxL5sGlGEEBiZfpe/CQpNMMg6YF7xKRxxikCo8SP4qHXz+C2gDA8ZZla8Rq7L4rxSp0KIxyRY2qfUIiOXqTRuYvoeeG/Jq8s1o5kFAl6c8CSdGwWjkzDuvb2eDveWJHaoe1hL1C5sHq5UHM2VdqytQXObsLJIhpzSA+HDwSZt1H8SW6gIKvnMzvBFCutsowtNGfJh8F93dNzaFPDc306H64zeCuP1hMQclNadU2hD3LV6elUvnaQbv0r6FLJ6QxMa3WfMIuTeDgERW6o//rs6dmDaECc5hZfxJ/C/YNs6j8NB0FXqHfWe8lpDKjrM1aQwKIEyYxczWxJWrcJVlQovR1LmfIfrsTlR0ddCSfvt4QJSmpH6TZKE1U5ZBz2ITAHJpxYxhDJplo+cdO9mLUSJtX0Kw2hKucaxUVLug2DrqWCdpmuTMrR6pdpWeb5sL6wL5ia+lep/Fit9Lnl6djfVFxOexC/NUsEMoPPRzl71GT+MOUZJS40xzs5Xn8XSALL2L8bue5ltAorFhv1Uu/xrxVqwS/iZJe5hgiRYAHoJWmtW5g1bRnGL6E6jRANsJLRSmqkFdsClaQzS7yghFvaA0jJlgddH1B3O1RbtsaPsuhftEqHhMCsMTSKjYiqLcKIdQgKtek883YdUhHGbQHliCG4EDOnX+wkXM9O/0tjhGCHpeNtE9OUOUyL3Uos0v9/8/k8TizOmM8+WYgBNnB4ZS8n4P4ide/Jw/9y2grFGfZGoAXeIsVfp+S4KpDPr0YTqQMOezNCLN/CyEtg+CYxdbhNVvniMIDRKINqMwSHJSkTJGLtRhHc7mNHPXUv+QsTgbBfRQek+KTrHpuiB9qYpdP6wBDXgDjUIYsmlXs5gUAkUVaQ+mJiTsfluf+BGywF0IuIpf26nKEZC+lhbLJoxBoZ8Hk76Jwf9R7UKOlgAAr9DTUcYY8hejHBmOMsBIhdXX9rhiOpuZ/oiK91V+mC2+xzR6EPJs9D4y3hFzO4cBiqzewcFCGvvHWixcZNj+C3NX4gCsg5NicFIcrTcEIW4a/FO0snbN6xoPjN0U8zgERFQIjOgVrngRfd24oV0pYBHhORAE1290Cyp2JyNf5Kf0j3EEKnDe1tVsgg9lJEhTTF7b/xOMobyvkPqXlPbhCy9gGe02IB7Yz/IEUCXjlIJuVt/1B4amlyepO0QNzz3u7wToaCz0Met5FrLOdiURGItzXyOwTHvw3Oa6eGOQC7Cj4RKnU7LdhuAhOWy0ZdASm7rIBwZUqHcdjriT6s8pL2GOTiNqr5qyyG6rOU3LJ4aC+fwVc1dCriuCTRBStBjBorq5swsS/hsl+Zo4Uj1o4HJj9O7RFV2U3hLbH8bmtOIgRFvlJ2IlYftnTfKgT6MT5FEEzxyzY4FHfM2kzepbfFIoxckIH5eHxQh+8OXVwHQLb1m0SeiF8om4zLw0l9lqs76oVv1+PFZqgjTn0oAdv9Kk2lGbe/8Rs4fSzqPnWNsuOAN41JUel+0cizSjSzTNZaK7E1OzxiJv/rUTpfc9aWTIrDp7rSRfqWJ/ufpyrBicdcESLvUb242a1hl+mzQA/Kcsc6dp47cC6O7yfy93JT0UQO7s2c54j2bux+4LOrKDmX2nsNK0+etcJdvS/FkgxVSLj7h2aSK7u+OcR8rgpZhi8ZgOriOamNYDAcTQILoupeU5glRJbTgsjvwSH7W4vBBN5GyuEFydvVVqw4A1YJfvs78WRfuBxwDI9KQzqpcEn5uyu0ypk3cwdy3qA7S9O/mcHji6TEliBP7IlFL1xOWTYQjOvdR2JHOkK32+HkxDB4EqTgNYT1b7vN49EyTWozHg/1x7rjFKKWG+rXSvh2gQsDWuHmrQ0mDgNPdpwJh7E+uPES17hGF5qOFfAWP+40NYx6LWlTCVLeY3VHRTzHJYLWLx2JVrJNTP0I3EFmiXmJ75OPAfzDMPH+Li3vVBHY2I1Noi8YFNphMwdfDIS8MW5JQ2QNFdusFitIxEDL1sdk0+eDDNj6l5SlunoBHEHzbLU3EiW+53srUUs2HqToSR/Wpu8xu3Al3FOmjpFZiYaNJpIQ6551K90D1FxGV6WcPAZo2iiWgXiexE6obmi18cLZojXAukPthsrlXUMZtITOv/AtiZGFEugIKNK3t+Sj1Wb0fN0HE7exo4F5JQUxJ/o2FxAwblw+hZNFP21PKdvT4vcQU4rzXW3ZtuUOsq0wi3eR3M11tETk1G0il5weMY8xEBGXeMyQpyqfwVDMhin4dHx5N+vkFcbegd4QytZ8H4K/eE/5DIKu+FV/gn+Y+5c5z8IRh2rpSZ1ExcHknSbb7GKX0S7ix5ZvmQoji0yiwMCZN0+eJ7A0y3Kd7rGRR6N1D1d82oPF0e6AsQEcd2djybITRyoKgZaGffQWCJeHTZlEM1p6OJiigdBo/xnqZZSkThSCpiUIAnFk0hS7m4xVnr25n5FKtdl8ILER5TWrgTeJSuxb5A50AAJ2DW94Uravr/mHdqVsfoJmFDOHMyfftuGT3m9DV2wNpSna54oQxs2Ys2l5uvnh0H1/HaWFEqk/Cjmw5MCbqRR/NbSneSj/g0Lz6hQS4B5W4M0X5IHwyrvlH23uX3KZ7/lZhgppQJRMBrI8YYsT99J5XVNzk4x5yAE8ZlVCEKIPaPnloA8M1/hVJnd5WvX6d4O/apPr/QNtR65BfXnapAXmfTLi1SgZi7/krFit9KTVxn4lP1rIdIET+pxlOcpgodGTz0TQTVpymXZlH55aKP7N021S7DtE5r7B9o6vpBgjkKli4f3eCWQ3lEPRgNUCtT5WSbdoASrAe36XJyRAjR++Ur4PI1" autocomplete="off" />
</form>
		</div>
		<!--===== /dialogMessages =====--><div id="cmnContainer">
		<!--=========== .cmnContainer ============--><div id="cmnEmergencyNotice"></div><div id="cmnTitle" class="hgroup ">
		<h1 class="visuallyHidden">お客様情報入力</h1></div>

		<div id="cmnWrapper">
		<!--===== cmnWrapper =====-->
		<ol class="mainFlow" aria-label="完了までの手順、">
			<li><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_flow_01.png?717d3c0" alt="1" height="20" width="28" />フライト検索</li><!--
			--><li><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_flow_02_on.png?717d3c0" alt="2" height="20" width="28" /><span class="visuallyHidden">現在のステップ、</span>お客様情報入力</li><!--
			--><li><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_flow_03.png?717d3c0" alt="3" height="20" width="28" />お支払い情報</li><!--
			--><li class="flowLast"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_flow4_04.png?717d3c0" alt="4" height="20" width="28" />完了</li>
		</ol>

	<div id="main">
	<!--=========== main =============-->
		<ul class="iconExample">
				<li class="requiredIconExample">
					<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="" height="7" width="7" /></span>必須項目です	
				</li>
		</ul>
<form id="j_idt526" name="j_idt526" method="post" action="https://aswbe-i.ana.co.jp/rei21c/international_asw/pages/award/reservation/award_mandatory_passenger_information_input.xhtml?aswcid=1&amp;rand=20250619204508pLeIAYEgoD" autocomplete="off" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="j_idt526" value="j_idt526" />
<input type="hidden" name="j_idt526_operationTicket" value="dljdmx+495a2f379a1025a7eac474deadbcd5db~nsM_pQhdUeXEcKPD-FFpJPFxmi3DCQZFO3FY8cqJ!1750333496030.aere-xml-controller-67d4778877-wn7sm" /><input type="hidden" name="j_idt526_cmnPageTicket" value="4" />

<div class="section">
	<h2>搭乗者選択<a href="https://www.ana.co.jp/other/int/meta/0086.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="特典のご利用に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a></h2>
	<table class="passengerInfo">
		<thead>
			<tr>
				
				<th class="name">氏名</th>
				
				<th class="dateOfBirthWide" id="dateOfBirthHeader">生年月日</th>
				
				<th class="gender lastCol" id="genderHeader">性別<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span></th>
				
			</tr>
		</thead>
		<tbody>
				
				<tr>
					<th class="parent_01" id="parentHeader01">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:0:passenger" type="checkbox" name="passengers:0:passenger" checked="checked" data-number="1" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:0:passenger">NAOKI SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader01">1963年06月05日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader01">				
							<span class="formWrapper">
							<label for="passengers:0:gender" class="visuallyHidden">NAOKI SATOの性別</label><select id="passengers:0:gender" name="passengers:0:gender" size="1">	<option value="">選択してください</option>
	<option value="M" selected="selected">男性</option>
	<option value="F">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_02" id="parentHeader02">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:1:passenger" type="checkbox" name="passengers:1:passenger" data-number="2" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:1:passenger">REO SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader02">1995年05月18日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader02">				
							<span class="formWrapper">
							<label for="passengers:1:gender" class="visuallyHidden">REO SATOの性別</label><select id="passengers:1:gender" name="passengers:1:gender" size="1">	<option value="">選択してください</option>
	<option value="M" selected="selected">男性</option>
	<option value="F">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_03" id="parentHeader03">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:2:passenger" type="checkbox" name="passengers:2:passenger" data-number="3" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:2:passenger">ERI SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader03">1963年08月14日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader03">				
							<span class="formWrapper">
							<label for="passengers:2:gender" class="visuallyHidden">ERI SATOの性別</label><select id="passengers:2:gender" name="passengers:2:gender" size="1">	<option value="">選択してください</option>
	<option value="M">男性</option>
	<option value="F" selected="selected">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_04" id="parentHeader04">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:3:passenger" type="checkbox" name="passengers:3:passenger" data-number="4" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:3:passenger">TAISHI SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader04">1992年12月03日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader04">				
							<span class="formWrapper">
							<label for="passengers:3:gender" class="visuallyHidden">TAISHI SATOの性別</label><select id="passengers:3:gender" name="passengers:3:gender" size="1">	<option value="">選択してください</option>
	<option value="M" selected="selected">男性</option>
	<option value="F">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_05" id="parentHeader05">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:4:passenger" type="checkbox" name="passengers:4:passenger" data-number="5" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:4:passenger">HIROMI FUJIMOTO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader05">1961年11月23日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader05">				
							<span class="formWrapper">
							<label for="passengers:4:gender" class="visuallyHidden">HIROMI FUJIMOTOの性別</label><select id="passengers:4:gender" name="passengers:4:gender" size="1">	<option value="">選択してください</option>
	<option value="M">男性</option>
	<option value="F" selected="selected">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
		</tbody>
	</table>
</div>
		<div class="section">
			<h2 class="midstream">連絡先情報
			</h2>
			<h3>代表者
			</h3>
	
	
	
	<div class="formInput">
		<fieldset>
			<legend><label>代表者</label></legend>
			<dl id="contactEmailAddressInfo">
				<dt class="hasRequiredIcon">メールアドレス<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span><a href="https://www.ana.co.jp/other/int/meta/0057.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="メールの宛先と内容に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a>
				</dt>
				<dd>
					<ul class="verticalList">
						<li class="nonMarginTop"><label for="representativeEmail" class="visuallyHidden">代表者メールアドレス</label><input id="representativeEmail" type="text" name="representativeEmail" value="<EMAIL>" class="widthLarge" placeholder="例：<EMAIL>" />
						</li>
						<li>
							<span class="supplementTxt" id="confirmMailAddressForRepresentative">確認のため再度ご入力ください。
							</span><label for="confirmrepresentativeEmail" class="visuallyHidden">確認用メールアドレス</label><input id="confirmrepresentativeEmail" type="text" name="confirmrepresentativeEmail" value="<EMAIL>" class="widthLarge jsNotPasteInput" placeholder="例：<EMAIL>" aria-describedby="confirmMailAddressForRepresentative" />
						</li>
					</ul>
				</dd>
					<dd style="display:none;"><input id="updateAlwaysInfo" type="checkbox" name="updateAlwaysInfo" /><label for="updateAlwaysInfo">ログイン中の会員情報へこのメールアドレスを登録</label>
					</dd>
			</dl>
			<dl>
				<dt>電話番号<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span>
				</dt>
				<dd class="js-countryWrap">
					<ul>
						<li><label for="representativeTelType" class="visuallyHidden">電話番号種別</label><select id="representativeTelType" name="representativeTelType" class="basicWidth js-typeSelect" size="1" onchange="changeTelType(this,'representativeTel')">	<option value="M1" selected="selected">携帯</option>
	<option value="B1">会社</option>
	<option value="H1">自宅</option>
</select>
						</li>
						<li><label for="representativeTelCountry" class="visuallyHidden">電話番号の国/地区選択</label><select id="representativeTelCountry" name="representativeTelCountry" class="js-countrySelect" size="1">	<option value="" data-sms-possible="true" data-country-code="---">選択してください</option>
	<option value="JP" selected="selected" data-sms-possible="true">日本</option>
	<option value="-" data-sms-possible="true" data-country-code="---">-----------</option>
	<option value="IS" data-sms-possible="true">アイスランド</option>
	<option value="IE" data-sms-possible="true">アイルランド</option>
	<option value="AZ" data-sms-possible="true">アゼルバイジャン</option>
	<option value="AF" data-sms-possible="true">アフガニスタン</option>
	<option value="US" data-sms-possible="true">アメリカ合衆国/カナダ</option>
	<option value="AE" data-sms-possible="true">アラブ首長国連邦</option>
	<option value="DZ" data-sms-possible="true">アルジェリア</option>
	<option value="AR" data-sms-possible="true">アルゼンチン</option>
	<option value="AW" data-sms-possible="true">アルバ</option>
	<option value="AL" data-sms-possible="true">アルバニア</option>
	<option value="AM" data-sms-possible="true">アルメニア</option>
	<option value="AO" data-sms-possible="true">アンゴラ</option>
	<option value="AG" data-sms-possible="true">アンティグア・バーブーダ</option>
	<option value="AD" data-sms-possible="true">アンドラ</option>
	<option value="YE" data-sms-possible="true">イエメン</option>
	<option value="GB" data-sms-possible="true">イギリス</option>
	<option value="IL" data-sms-possible="true">イスラエル</option>
	<option value="IT" data-sms-possible="true">イタリア</option>
	<option value="IQ" data-sms-possible="true">イラク</option>
	<option value="IR" data-sms-possible="true">イラン</option>
	<option value="IN" data-sms-possible="true">インド</option>
	<option value="ID" data-sms-possible="true">インドネシア</option>
	<option value="UG" data-sms-possible="true">ウガンダ</option>
	<option value="UA" data-sms-possible="true">ウクライナ</option>
	<option value="UZ" data-sms-possible="true">ウズベキスタン</option>
	<option value="UY" data-sms-possible="true">ウルグアイ</option>
	<option value="EC" data-sms-possible="true">エクアドル</option>
	<option value="EG" data-sms-possible="true">エジプト</option>
	<option value="EE" data-sms-possible="true">エストニア</option>
	<option value="SZ" data-sms-possible="true">エスワティニ</option>
	<option value="ET" data-sms-possible="true">エチオピア</option>
	<option value="ER" data-sms-possible="true">エリトリア</option>
	<option value="SV" data-sms-possible="true">エルサルバドル</option>
	<option value="AU" data-sms-possible="true">オーストラリア</option>
	<option value="AT" data-sms-possible="true">オーストリア</option>
	<option value="OM" data-sms-possible="true">オマーン</option>
	<option value="NL" data-sms-possible="true">オランダ</option>
	<option value="GH" data-sms-possible="true">ガーナ</option>
	<option value="CV" data-sms-possible="true">カーボベルデ</option>
	<option value="GY" data-sms-possible="true">ガイアナ</option>
	<option value="KZ" data-sms-possible="true">カザフスタン</option>
	<option value="QA" data-sms-possible="true">カタール</option>
	<option value="CA" data-sms-possible="true">カナダ/アメリカ合衆国</option>
	<option value="GA" data-sms-possible="true">ガボン</option>
	<option value="CM" data-sms-possible="true">カメルーン</option>
	<option value="KR" data-sms-possible="true">韓国</option>
	<option value="GM" data-sms-possible="true">ガンビア</option>
	<option value="KH" data-sms-possible="true">カンボジア</option>
	<option value="KP" data-sms-possible="true">北朝鮮</option>
	<option value="MK" data-sms-possible="true">北マケドニア</option>
	<option value="GN" data-sms-possible="true">ギニア</option>
	<option value="GW" data-sms-possible="true">ギニアビサウ</option>
	<option value="CY" data-sms-possible="true">キプロス</option>
	<option value="CU" data-sms-possible="true">キューバ</option>
	<option value="GR" data-sms-possible="true">ギリシャ</option>
	<option value="KI" data-sms-possible="true">キリバス</option>
	<option value="KG" data-sms-possible="true">キルギス</option>
	<option value="GT" data-sms-possible="true">グアテマラ</option>
	<option value="GU" data-sms-possible="true">グアム</option>
	<option value="KW" data-sms-possible="true">クウェート</option>
	<option value="GD" data-sms-possible="true">グレナダ</option>
	<option value="HR" data-sms-possible="true">クロアチア</option>
	<option value="KY" data-sms-possible="true">ケイマン諸島</option>
	<option value="KE" data-sms-possible="true">ケニア</option>
	<option value="CI" data-sms-possible="true">コートジボワール</option>
	<option value="CR" data-sms-possible="true">コスタリカ</option>
	<option value="KM" data-sms-possible="true">コモロ</option>
	<option value="CO" data-sms-possible="true">コロンビア</option>
	<option value="CG" data-sms-possible="true">コンゴ共和国</option>
	<option value="CD" data-sms-possible="true">コンゴ民主共和国</option>
	<option value="SA" data-sms-possible="true">サウジアラビア</option>
	<option value="ZM" data-sms-possible="true">ザンビア</option>
	<option value="SM" data-sms-possible="true">サンマリノ</option>
	<option value="SL" data-sms-possible="true">シエラレオネ</option>
	<option value="DJ" data-sms-possible="true">ジブチ</option>
	<option value="JM" data-sms-possible="true">ジャマイカ</option>
	<option value="GE" data-sms-possible="true">ジョージア</option>
	<option value="SY" data-sms-possible="true">シリア</option>
	<option value="SG" data-sms-possible="true">シンガポール</option>
	<option value="ZW" data-sms-possible="true">ジンバブエ</option>
	<option value="CH" data-sms-possible="true">スイス</option>
	<option value="SE" data-sms-possible="true">スウェーデン</option>
	<option value="SD" data-sms-possible="true">スーダン</option>
	<option value="ES" data-sms-possible="true">スペイン</option>
	<option value="SR" data-sms-possible="true">スリナム</option>
	<option value="LK" data-sms-possible="true">スリランカ</option>
	<option value="SK" data-sms-possible="true">スロバキア</option>
	<option value="SI" data-sms-possible="true">スロベニア</option>
	<option value="SC" data-sms-possible="true">セイシェル</option>
	<option value="GQ" data-sms-possible="true">赤道ギニア</option>
	<option value="SN" data-sms-possible="true">セネガル</option>
	<option value="RS" data-sms-possible="true">セルビア</option>
	<option value="KN" data-sms-possible="true">セントクリストファー・ネイビス</option>
	<option value="LC" data-sms-possible="true">セントルシア</option>
	<option value="SB" data-sms-possible="true">ソロモン諸島</option>
	<option value="TH" data-sms-possible="true">タイ</option>
	<option value="TW" data-sms-possible="true">台湾</option>
	<option value="TJ" data-sms-possible="true">タジキスタン</option>
	<option value="TZ" data-sms-possible="true">タンザニア</option>
	<option value="CZ" data-sms-possible="true">チェコ</option>
	<option value="TD" data-sms-possible="true">チャド</option>
	<option value="CF" data-sms-possible="true">中央アフリカ共和国</option>
	<option value="CN" data-sms-possible="true">中国</option>
	<option value="TN" data-sms-possible="true">チュニジア</option>
	<option value="CL" data-sms-possible="true">チリ</option>
	<option value="TV" data-sms-possible="true">ツバル</option>
	<option value="DK" data-sms-possible="true">デンマーク</option>
	<option value="DE" data-sms-possible="true">ドイツ</option>
	<option value="TG" data-sms-possible="true">トーゴ</option>
	<option value="DO" data-sms-possible="true">ドミニカ共和国</option>
	<option value="DM" data-sms-possible="true">ドミニカ国</option>
	<option value="TT" data-sms-possible="true">トリニダード・トバゴ</option>
	<option value="TM" data-sms-possible="true">トルクメニスタン</option>
	<option value="TR" data-sms-possible="true">トルコ</option>
	<option value="TO" data-sms-possible="true">トンガ</option>
	<option value="NG" data-sms-possible="true">ナイジェリア</option>
	<option value="NR" data-sms-possible="true">ナウル</option>
	<option value="NA" data-sms-possible="true">ナミビア</option>
	<option value="NI" data-sms-possible="true">ニカラグア</option>
	<option value="NE" data-sms-possible="true">ニジェール</option>
	<option value="NZ" data-sms-possible="true">ニュージーランド</option>
	<option value="NP" data-sms-possible="true">ネパール</option>
	<option value="NO" data-sms-possible="true">ノルウェー</option>
	<option value="BH" data-sms-possible="true">バーレーン</option>
	<option value="HT" data-sms-possible="true">ハイチ</option>
	<option value="PK" data-sms-possible="true">パキスタン</option>
	<option value="VA" data-sms-possible="true">バチカン</option>
	<option value="PA" data-sms-possible="true">パナマ</option>
	<option value="VU" data-sms-possible="true">バヌアツ</option>
	<option value="BS" data-sms-possible="true">バハマ</option>
	<option value="PG" data-sms-possible="true">パプアニューギニア</option>
	<option value="BM" data-sms-possible="true">バミューダ</option>
	<option value="PW" data-sms-possible="true">パラオ</option>
	<option value="PY" data-sms-possible="true">パラグアイ</option>
	<option value="BB" data-sms-possible="true">バルバドス</option>
	<option value="PS" data-sms-possible="true">パレスチナ</option>
	<option value="HU" data-sms-possible="true">ハンガリー</option>
	<option value="BD" data-sms-possible="true">バングラデシュ</option>
	<option value="TL" data-sms-possible="true">東ティモール</option>
	<option value="FJ" data-sms-possible="true">フィジー</option>
	<option value="PH" data-sms-possible="true">フィリピン</option>
	<option value="FI" data-sms-possible="true">フィンランド</option>
	<option value="BT" data-sms-possible="true">ブータン</option>
	<option value="PR" data-sms-possible="true">プエルトリコ</option>
	<option value="BR" data-sms-possible="true">ブラジル</option>
	<option value="FR" data-sms-possible="true">フランス</option>
	<option value="PF" data-sms-possible="true">フランス領ポリネシア</option>
	<option value="BG" data-sms-possible="true">ブルガリア</option>
	<option value="BF" data-sms-possible="true">ブルキナファソ</option>
	<option value="BN" data-sms-possible="true">ブルネイ</option>
	<option value="BI" data-sms-possible="true">ブルンジ</option>
	<option value="VN" data-sms-possible="true">ベトナム</option>
	<option value="BJ" data-sms-possible="true">ベナン</option>
	<option value="VE" data-sms-possible="true">ベネズエラ</option>
	<option value="BY" data-sms-possible="true">ベラルーシ</option>
	<option value="BZ" data-sms-possible="true">ベリーズ</option>
	<option value="PE" data-sms-possible="true">ペルー</option>
	<option value="BE" data-sms-possible="true">ベルギー</option>
	<option value="PL" data-sms-possible="true">ポーランド</option>
	<option value="BA" data-sms-possible="true">ボスニア・ヘルツェゴビナ</option>
	<option value="BW" data-sms-possible="true">ボツワナ</option>
	<option value="BO" data-sms-possible="true">ボリビア</option>
	<option value="PT" data-sms-possible="true">ポルトガル</option>
	<option value="HK" data-sms-possible="true">香港</option>
	<option value="HN" data-sms-possible="true">ホンジュラス</option>
	<option value="MH" data-sms-possible="true">マーシャル諸島</option>
	<option value="MO" data-sms-possible="true">マカオ</option>
	<option value="MG" data-sms-possible="true">マダガスカル</option>
	<option value="MW" data-sms-possible="true">マラウイ</option>
	<option value="ML" data-sms-possible="true">マリ</option>
	<option value="MT" data-sms-possible="true">マルタ</option>
	<option value="MY" data-sms-possible="true">マレーシア</option>
	<option value="FM" data-sms-possible="true">ミクロネシア連邦</option>
	<option value="ZA" data-sms-possible="true">南アフリカ</option>
	<option value="MM" data-sms-possible="true">ミャンマー</option>
	<option value="MX" data-sms-possible="true">メキシコ</option>
	<option value="MU" data-sms-possible="true">モーリシャス</option>
	<option value="MR" data-sms-possible="true">モーリタニア</option>
	<option value="MZ" data-sms-possible="true">モザンビーク</option>
	<option value="MC" data-sms-possible="true">モナコ</option>
	<option value="MV" data-sms-possible="true">モルディヴ</option>
	<option value="MD" data-sms-possible="true">モルドバ</option>
	<option value="MA" data-sms-possible="true">モロッコ</option>
	<option value="MN" data-sms-possible="true">モンゴル</option>
	<option value="ME" data-sms-possible="true">モンテネグロ</option>
	<option value="JO" data-sms-possible="true">ヨルダン</option>
	<option value="LA" data-sms-possible="true">ラオス人民民主共和国</option>
	<option value="LV" data-sms-possible="true">ラトビア</option>
	<option value="LT" data-sms-possible="true">リトアニア</option>
	<option value="LY" data-sms-possible="true">リビア</option>
	<option value="LI" data-sms-possible="true">リヒテンシュタイン</option>
	<option value="LR" data-sms-possible="true">リベリア</option>
	<option value="RO" data-sms-possible="true">ルーマニア</option>
	<option value="LU" data-sms-possible="true">ルクセンブルク</option>
	<option value="RW" data-sms-possible="true">ルワンダ</option>
	<option value="LS" data-sms-possible="true">レソト</option>
	<option value="LB" data-sms-possible="true">レバノン</option>
	<option value="RU" data-sms-possible="true">ロシア</option>
</select>
						</li>
						<li class="numberInputWrap">
							<span id="numberInputInfo1" class="supplementTxt marginTop">先頭に0(ゼロ)がある場合、0(ゼロ)を抜いてください(一部、国・地域除く)。携帯電話番号の場合は、携帯電話を契約された国を選択ください。</span>
							<div class="numberRowWrap">
								<div class="block">
									
									<span class="visuallyHidden">国番号</span>
									<span class="viewInput widthMini js-countryCode"></span>
								</div>
								<div class="block"><label for="representativeTel" class="visuallyHidden">電話番号</label><input id="representativeTel" type="text" name="representativeTel" value="9042504176" class="widthMiddle" placeholder="例：9012345678" maxlength="30" aria-describedby="numberInputInfo1" />
								</div>
							</div>
						</li>
					</ul>
				</dd>
			</dl>
		</fieldset>
	</div>
	<div class="titleWrapper">
		<h3>
			<span class="text">eメール・SMSに関するご案内<a href="https://www.ana.co.jp/other/int/meta/0057.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="eメール・SMSに関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a></span>
		</h3>
	</div>
	<div class="formInput">
	<h4 class="formAreaTitle">主に搭乗・運航に関わるeメールの送付先設定（米国へ通知いたします）<a href="https://www.ana.co.jp/other/int/meta/0629.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="米国入国用eメール・SMS・電話番号に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a>
	</h4>
	<div class="paxSettingArea js-mailFormWrap onePax is-open">
			<div class="settingArea">
					<dl>
						<dt class="breakWord">NAOKI SATO
						</dt>
						<dd>
							<fieldset>
								<legend>NAOKI SATOのeメール送付先および米国到着時に提供するメールアドレス</legend>代表者と同じ
							</fieldset>
						</dd>
					</dl>
			</div>
	</div>
	<h4 class="formAreaTitle hasTopLine">主に搭乗・運航に関わるSMSの送付先および米国で受信できる電話番号の設定（米国へ通知いたします）<a href="https://www.ana.co.jp/other/int/meta/0629.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="米国入国用eメール・SMS・電話番号に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a>
	</h4>
	<div class="paxSettingArea js-smsFormWrap onePax is-open">
			<div class="settingArea">
					<dl>
						<dt class="breakWord">NAOKI SATO<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span>
						</dt>
						<dd>
							<fieldset>
								<legend>NAOKI SATOの1つ目の電話番号</legend>
									<ul class="verticalList js-formItem">
										<li><input type="radio" id="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSameAsRepresentativeSms:radioItem" name="contactsSms:0:recipientSmsEachPersonSelection:radioGroup" value="0" checked="checked" /><label for="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSameAsRepresentativeSms:radioItem">代表者電話番号と同じ</label>
										</li>
										<li><input type="radio" id="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSmsPerPassenger:radioItem" name="contactsSms:0:recipientSmsEachPersonSelection:radioGroup" value="1" /><label for="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSmsPerPassenger:radioItem">電話番号を指定</label>
											<div class="indentArea js-formImputArea">
												<span id="confirmTelNumberForRepresentative:0" class="supplementTxt marginTop">先頭に0(ゼロ)がある場合、0(ゼロ)を抜いてください(一部、国・地域除く)。携帯電話番号の場合は、携帯電話を契約された国を選択ください。</span>
												<ul class="countryNumberInput js-countryWrap">
													<li><label for="contactsSms:0:recipientSmsEachPersonSelection:passengerSmsCountry" class="visuallyHidden">電話番号の国/地区選択</label><select id="contactsSms:0:recipientSmsEachPersonSelection:passengerSmsCountry" name="contactsSms:0:recipientSmsEachPersonSelection:passengerSmsCountry" class="countrySelect js-countrySelect" size="1">	<option value="" data-country-code="---">選択してください</option>
	<option value="JP">日本</option>
	<option value="-" data-country-code="---">-----------</option>
	<option value="IE">アイルランド</option>
	<option value="AZ">アゼルバイジャン</option>
	<option value="AF">アフガニスタン</option>
	<option value="US">アメリカ合衆国/カナダ</option>
	<option value="AE">アラブ首長国連邦</option>
	<option value="DZ">アルジェリア</option>
	<option value="AR">アルゼンチン</option>
	<option value="AW">アルバ</option>
	<option value="AL">アルバニア</option>
	<option value="AM">アルメニア</option>
	<option value="AG">アンティグア・バーブーダ</option>
	<option value="AD">アンドラ</option>
	<option value="YE">イエメン</option>
	<option value="GB">イギリス</option>
	<option value="IL">イスラエル</option>
	<option value="IT">イタリア</option>
	<option value="IQ">イラク</option>
	<option value="IR">イラン</option>
	<option value="IN">インド</option>
	<option value="ID">インドネシア</option>
	<option value="UG">ウガンダ</option>
	<option value="UA">ウクライナ</option>
	<option value="UZ">ウズベキスタン</option>
	<option value="UY">ウルグアイ</option>
	<option value="EC">エクアドル</option>
	<option value="EG">エジプト</option>
	<option value="EE">エストニア</option>
	<option value="ER">エリトリア</option>
	<option value="SV">エルサルバドル</option>
	<option value="AU">オーストラリア</option>
	<option value="AT">オーストリア</option>
	<option value="OM">オマーン</option>
	<option value="NL">オランダ</option>
	<option value="GH">ガーナ</option>
	<option value="GY">ガイアナ</option>
	<option value="KZ">カザフスタン</option>
	<option value="QA">カタール</option>
	<option value="CA">カナダ/アメリカ合衆国</option>
	<option value="GA">ガボン</option>
	<option value="CM">カメルーン</option>
	<option value="KR">韓国</option>
	<option value="GM">ガンビア</option>
	<option value="KH">カンボジア</option>
	<option value="GN">ギニア</option>
	<option value="GW">ギニアビサウ</option>
	<option value="CY">キプロス</option>
	<option value="CU">キューバ</option>
	<option value="GR">ギリシャ</option>
	<option value="KG">キルギス</option>
	<option value="GT">グアテマラ</option>
	<option value="GU">グアム</option>
	<option value="KW">クウェート</option>
	<option value="GD">グレナダ</option>
	<option value="HR">クロアチア</option>
	<option value="KY">ケイマン諸島</option>
	<option value="KE">ケニア</option>
	<option value="CI">コートジボワール</option>
	<option value="CR">コスタリカ</option>
	<option value="KM">コモロ</option>
	<option value="CO">コロンビア</option>
	<option value="CD">コンゴ民主共和国</option>
	<option value="SA">サウジアラビア</option>
	<option value="ZM">ザンビア</option>
	<option value="SL">シエラレオネ</option>
	<option value="JM">ジャマイカ</option>
	<option value="GE">ジョージア</option>
	<option value="SY">シリア</option>
	<option value="SG">シンガポール</option>
	<option value="ZW">ジンバブエ</option>
	<option value="CH">スイス</option>
	<option value="SE">スウェーデン</option>
	<option value="SD">スーダン</option>
	<option value="ES">スペイン</option>
	<option value="SR">スリナム</option>
	<option value="LK">スリランカ</option>
	<option value="SK">スロバキア</option>
	<option value="SI">スロベニア</option>
	<option value="SN">セネガル</option>
	<option value="RS">セルビア</option>
	<option value="KN">セントクリストファー・ネイビス</option>
	<option value="LC">セントルシア</option>
	<option value="TH">タイ</option>
	<option value="TW">台湾</option>
	<option value="TJ">タジキスタン</option>
	<option value="TZ">タンザニア</option>
	<option value="CZ">チェコ</option>
	<option value="TD">チャド</option>
	<option value="CF">中央アフリカ共和国</option>
	<option value="CN">中国</option>
	<option value="TN">チュニジア</option>
	<option value="CL">チリ</option>
	<option value="DK">デンマーク</option>
	<option value="DE">ドイツ</option>
	<option value="TG">トーゴ</option>
	<option value="DO">ドミニカ共和国</option>
	<option value="TT">トリニダード・トバゴ</option>
	<option value="TR">トルコ</option>
	<option value="NG">ナイジェリア</option>
	<option value="NI">ニカラグア</option>
	<option value="NE">ニジェール</option>
	<option value="NZ">ニュージーランド</option>
	<option value="NP">ネパール</option>
	<option value="NO">ノルウェー</option>
	<option value="HT">ハイチ</option>
	<option value="PK">パキスタン</option>
	<option value="PA">パナマ</option>
	<option value="BS">バハマ</option>
	<option value="PG">パプアニューギニア</option>
	<option value="BM">バミューダ</option>
	<option value="PW">パラオ</option>
	<option value="PY">パラグアイ</option>
	<option value="BB">バルバドス</option>
	<option value="PS">パレスチナ</option>
	<option value="HU">ハンガリー</option>
	<option value="BD">バングラデシュ</option>
	<option value="TL">東ティモール</option>
	<option value="FJ">フィジー</option>
	<option value="PH">フィリピン</option>
	<option value="FI">フィンランド</option>
	<option value="BT">ブータン</option>
	<option value="PR">プエルトリコ</option>
	<option value="BR">ブラジル</option>
	<option value="FR">フランス</option>
	<option value="PF">フランス領ポリネシア</option>
	<option value="BG">ブルガリア</option>
	<option value="BF">ブルキナファソ</option>
	<option value="BN">ブルネイ</option>
	<option value="BI">ブルンジ</option>
	<option value="VN">ベトナム</option>
	<option value="BJ">ベナン</option>
	<option value="VE">ベネズエラ</option>
	<option value="BY">ベラルーシ</option>
	<option value="PE">ペルー</option>
	<option value="BE">ベルギー</option>
	<option value="PL">ポーランド</option>
	<option value="BA">ボスニア・ヘルツェゴビナ</option>
	<option value="BW">ボツワナ</option>
	<option value="HK">香港</option>
	<option value="MH">マーシャル諸島</option>
	<option value="MO">マカオ</option>
	<option value="MG">マダガスカル</option>
	<option value="MW">マラウイ</option>
	<option value="ML">マリ</option>
	<option value="MT">マルタ</option>
	<option value="MY">マレーシア</option>
	<option value="ZA">南アフリカ</option>
	<option value="MM">ミャンマー</option>
	<option value="MX">メキシコ</option>
	<option value="MU">モーリシャス</option>
	<option value="MR">モーリタニア</option>
	<option value="MZ">モザンビーク</option>
	<option value="MC">モナコ</option>
	<option value="MV">モルディヴ</option>
	<option value="MA">モロッコ</option>
	<option value="MN">モンゴル</option>
	<option value="ME">モンテネグロ</option>
	<option value="JO">ヨルダン</option>
	<option value="LA">ラオス人民民主共和国</option>
	<option value="LV">ラトビア</option>
	<option value="LT">リトアニア</option>
	<option value="LY">リビア</option>
	<option value="LR">リベリア</option>
	<option value="RO">ルーマニア</option>
	<option value="LU">ルクセンブルク</option>
	<option value="RW">ルワンダ</option>
	<option value="LB">レバノン</option>
	<option value="RU">ロシア</option>
</select>
													</li>
													<li>
														
														<div class="numberRowWrap">
															<div class="block">
																<span class="visuallyHidden">国番号</span>
																<span class="countryCode viewInput js-countryCode"></span>
															</div>
															
															<div class="block"><label for="contactsSms:0:recipientSmsEachPersonSelection:flightStatusNotificationContactPointSmsDescription" class="visuallyHidden">電話番号</label><input id="contactsSms:0:recipientSmsEachPersonSelection:flightStatusNotificationContactPointSmsDescription" type="text" name="contactsSms:0:recipientSmsEachPersonSelection:flightStatusNotificationContactPointSmsDescription" class="telNumber" placeholder="例：9012345678" maxlength="30" aria-describedby="confirmTelNumberForRepresentative:0" />
															</div>
														</div>
													</li>
												</ul>
											</div>
										</li>
									</ul>
							</fieldset>
						</dd>
					</dl>
			</div>
	</div>
	</div>
	
	<div class="formInput articleLine">
		<h3>滞在中の連絡先(任意)
		</h3>
		<dl>
			
			<dt><label for="accommodationOne">現地連絡先1</label>
			</dt>
			<dd><input id="accommodationOne" type="text" name="accommodationOne" class="widthLarge" placeholder="例：09012345678 ABC HOTEL" maxlength="28" title="例：09012345678 ABC HOTEL" />
			</dd>
		</dl>
		<dl>
			
			<dt><label for="accommodationTwo">現地連絡先2</label>
			</dt>
			<dd><input id="accommodationTwo" type="text" name="accommodationTwo" class="widthLarge" placeholder="例：09012345678 ABC HOTEL" maxlength="28" title="例：09012345678 ABC HOTEL" />
			</dd>
		</dl>
	</div>
		</div>
		
		<p class="btnAreaSubmit btnArrowNext "><input id="next" type="submit" name="next" value="次へ" class="btnBase btnMainStream btnVerticalMain btnWidthVariable" onclick="return Asw.Dialog.getInstance('prebookConfirmDialog').toggle(event);return Asw.LoadingWindow.open(&quot;NORMAL&quot;, event)" />
		</p><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:2" value="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" autocomplete="off" />
</form><div id="cmnDynamicMessages" class="infoBoxSection">
				<dl class="infoBox importantInfo jsAccordionSwitch">
					<dt><strong>重要なご案内</strong></dt>
					<dd>
						<ul class="jsAccordionSwitchList" id="informationMessages">
								<li>個人情報の利用目的<a href="https://www.ana.co.jp/other/int/meta/0012.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="ANAプライバシーポリシー(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">ANAプライバシーポリシー<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>を必ずお読みいただき、内容に同意いただいた上で、お進みください。</li>
								<li><a href="https://www.ana.co.jp/other/int/meta/0058.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="Secure Flight Programについて(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">米国Secure Flightプログラム<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>に基づき、パスポート名、生年月日、性別などの情報は事前に米国へ通知いたします。</li>
								<li>入力したeメールアドレス、電話番号は入国に必要な情報として米国に通知される可能性があります。</li>
									<li class="jsHiddenFlg toggleContents">お名前のスペルがパスポート名と一致しているかご確認ください。異なる場合はインターネットではお取り扱いできません。<a href="https://www.ana.co.jp/other/int/meta/intbe0001.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="ANAお問い合わせ先(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">ANA<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>へ名前の変更をお申し出ください。</li>
									<li class="jsHiddenFlg toggleContents">二重国籍のお客様は、ご搭乗時に使用されるパスポートのお名前でご予約ください。<br />パスポートごとにお名前が異なる場合は、お客様ご自身で入国管理事務所・外務省・大使館などで各種条件をご確認のうえ、使用されるパスポートを決めてください。<br />なお、入国可否については国によって異なるため、お客様ご自身で各入国管理事務所などでご確認ください。</li>
									<li class="jsHiddenFlg toggleContents">旅行開始日時点で12歳以上の場合は大人、2歳以上12歳未満の場合は小児、2歳未満の場合は幼児の扱いとなります。なお、小児は大人と同マイル数が必要です。</li>
									<li class="jsHiddenFlg toggleContents">運航に関する情報をお客様へ通知するため、提携航空会社にもお客様のメールアドレスが提供されます。あらかじめご了承ください。</li>
									<li class="jsHiddenFlg toggleContents"><dl><dt>携帯電話へのメール送信をご希望のお客様へ</dt><dd>ドメイン指定受信をご利用の場合は、ana.co.jpとamadeus.comからのメールが受信できるようあらかじめ設定をお願いいたします。</dd></dl></li>
									<li class="jsHiddenFlg toggleContents">SMSはスマートフォンの番号に配信されます。一部の国ではSMSでのご案内をご利用いただけません。詳細は<a href="https://www.ana.co.jp/other/int/meta/0737.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="ショートメッセージ(SMS)でのご案内対象外の国について(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">ショートメッセージ(SMS)でのご案内対象外の国について<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>をご覧ください。</li>
									<li class="jsHiddenFlg toggleContents">ログインされている会員ご本人が搭乗しない場合は、再度フライト検索画面より「ログインされている会員ご本人は搭乗しない」のチェックボックスを選択してお手続きください。</li>
						</ul>
							<a href="#" class="toggleSwitch jsTriggerSwitch" role="button" aria-controls="informationMessages" aria-expanded="false"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/toggle_open.png?7bec38a" alt="開閉ボタン" height="25" width="25" class="jsRollOver" />
							</a>
					</dd>
				</dl></div>
	<!--=========== /main =============-->
	</div>

	<!--=========== Script ============-->
	<script type="text/javascript">
	$(window).on('load', function() {
		// アコーディオンのメソッドと設定
		Asw.AccordionInfo('.jsAccordionSwitch', {
			'triggerSwitch': '.jsAccordionSwitch .jsTriggerSwitch',
			'listClass': '.jsAccordionSwitchList',
			'visibilityHidden': 'toggleContents'
		});
	});
	</script>
	<!--=========== Script ============-->
	<script type="text/javascript">
		//プラットフォーム判別（サマリーエリア追従用として）
		var Asw = Asw || {};
	
	  	Asw.ClientInfo = Asw.ClientInfo  || {};
	  	Asw.ClientInfo.DeviceType = Asw.ClientInfo.DeviceType || {};
	
	  	Asw.ClientInfo.DeviceType.PC = 'PC';
	  	Asw.ClientInfo.DeviceType.SP = 'SP';
	  	Asw.ClientInfo.DeviceType.TC = 'TC';
	
	  	Asw.ClientInfo.deviceType = Asw.ClientInfo.DeviceType.PC;
	
	 	if((navigator.userAgent.indexOf('Android') > 0 && navigator.userAgent.indexOf('Mobile') == -1) || navigator.userAgent.indexOf('iPad') > 0){
	    	Asw.ClientInfo.deviceType = Asw.ClientInfo.DeviceType.TC;
	  	}
	</script>
	<!--=========== /Script ============--><div id="summaryArea">
			<div class="flightSummaryArea">
						<div class="flightSummarySection">
							<h2>区間 1
							</h2>
							<p class="flightDate"><em>6</em>月<em class="hasLeft">24</em>日（火）
							</p>
							<div class="flightSummaryRoute">
									<div class="flightSummaryRouteSection">
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation1_1">
													<span>東京(成田)
													</span>
												</p>
												<p class="time">
													<span>17:00
													</span>
												</p>
											</div>
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation1_1">
													<span>シカゴ(ORD)
													</span>
												</p>
												<p class="time">
													<span>14:55
													</span>
												</p>
											</div>
										<p class="flightNumberClass">
												<span>NH012</span><span><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/flight_ana_summaryarea.png?717d3c0" alt="ANA運航便" height="11" width="41" /></span>
												<br />特典ビジネス
										</p>
									</div>
							</div>
						</div>
						<div class="flightSummarySection">
							<h2 class="midstream">区間 2
							</h2>
							<p class="flightDate"><em>7</em>月<em class="hasLeft">23</em>日（水）
							</p>
							<div class="flightSummaryRoute">
									<div class="flightSummaryRouteSection">
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation2_1">
													<span>ホノルル(オアフ島)
													</span>
												</p>
												<p class="time">
													<span>11:35
													</span>
												</p>
											</div>
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation2_1">
													<span>東京(成田)
													</span>
												</p>
												<p class="time">
													<span>14:50
																	<span>翌日
																	</span>
													</span>
												</p>
											</div>
										<p class="flightNumberClass">
												<span>NH183</span><span><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/flight_ana_summaryarea.png?717d3c0" alt="ANA運航便" height="11" width="41" /></span>
												<br />特典エコノミー
										</p>
									</div>
							</div>
						</div>
					<div class="total">
						<p class="passenger">大人 x1
						</p>
						<div class="totalArea">
					  		<dl class="mealAmountWrap">
									<dt>有料機内食サービス
									</dt>
									<dd><em class="mealAmount">0</em><span class="currencyCode">円</span>
									</dd>
							</dl>
									<div>
										<div class="mileageAndTotal">
												<dl class="requiredMileage">
													<dt>必要マイル
													</dt>
													<dd><em class="requiredMileage">74,000</em><span class="currencyCode">マイル</span>
													</dd>
												</dl>
												<dl>
													<dt>総額
													</dt>
													<dd><em class="awardTotalPayment">68,130</em>円
													</dd>
												</dl>
												<p class="includeFareAndFuelCost">※各種税金、燃油特別付加運賃等を含みます。
												</p>
										</div>
									</div>
						</div>
					</div>
				
			</div></div>
		<!--===== /cmnWrapper =====-->
		</div>
			
		<span id="message" class="visuallyHidden" role="alert" aria-live="assertive"></span>
		<span id="politeMessage" class="visuallyHidden" role="log" aria-live="polite"></span><script type="text/javascript">
		var Asw = Asw || {};
		Asw.BaseOutput = {"sessionID":"pLeIAYEgoD","language":"ja","siteCatalystLanguageCode":"J","operationDate":"20250619","operationDateTime":"20250619204508","office":{"officeCode":"TYONH08ZZ","country2letterCode":"JP"},"bookingType":"A","pageID":"A04_P01","siteCatalystPageName":"INT_BE_AWARD_J_A04特典予約_P01旅客情報入力（必須情報）","device":{"deviceType":"PC"}};
	</script><div id="promotionArea">
		<!--===== promotionArea =====-->
		<!--===== /promotionArea =====--></div>
		<!--=========== /cmnContainer ============--></div><div id="cmnBaloonArea" aria-live="polite"></div><div id="cmnPopupArea" aria-live="polite"></div>

	

	<div id="cmnFooterWrapper">
		<!--=========== .cmnFooterWrapper ============-->
		<div id="cmnFooter">
			<!--===== cmnFooter =====-->
			<ul>
				<li><a href="https://www.ana.co.jp/other/int/meta/0012.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">プライバシーポリシー<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
					<li><a href="https://www.ana.co.jp/other/int/meta/0670.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">利用者情報の外部送信について<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
					</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/0052.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">サイト利用規約<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/be0238.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">推奨環境<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/0133.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">サイトマップ<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/0732.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">運送約款<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
					<li class="lastChild"><a href="https://www.ana.co.jp/other/int/meta/0556.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">ウェブアクセシビリティについて<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
					</li>
			</ul>
			<p class="blankText" aria-hidden="true"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="" height="10" width="10" />外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。</p>
			<p class="copyright"><span lang="en" xml:lang="en">Copyright&#169;ANA</span>
			</p>
			<p class="starAllianceLogo"><a href="http://www.staralliance.com/ja/" target="_blank"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/star_alliance_logo.png?0fe73b5" alt="A STAR ALLIANCE MEMBER" height="18" width="175" /><span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
			</p>
			<!--===== cmnFooter =====-->
		</div>
		<!--=========== /cmnFooterWrapper ============-->
	</div>

		
		<div id="cmnLoadingForAjax"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/bg_modalWindow.png?717d3c0" alt="" height="0" width="0" />
			<div class="loadingArea">
				<p class="loadingImage"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/loading.gif?717d3c0" alt="リクエストを処理しています...しばらくお待ちください。" height="33" width="33" />
				</p>
			</div>
		</div>
		
		
		<div id="popupAltSentence" style="display: none" aria-hidden="true">別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。</div>
	<div id="cmnSiteCatalyst" aria-hidden="true"><div id="cmnSiteCatalystParamArea"><script type="text/javascript">
				// ▼SiteCatalyst
				// SiteCatalyst code version: H.2.
				// Copyright 1997-2005 Omniture, Inc. More info available at http://www.omniture.com
				SiteCatalystReportSuites   = "INT";
				SiteCatalystCharSet        = "UTF-8";
				SiteCatalystChannel        = "BE_AWARD_J";
				SiteCatalystPageName       = "INT_BE_AWARD_J_A04\u7279\u5178\u4E88\u7D04_P01\u65C5\u5BA2\u60C5\u5831\u5165\u529B\uFF08\u5FC5\u9808\u60C5\u5831\uFF09";
				SiteCatalystERR            = "";
				SiteCatalystEventNum       = "";
				SiteCatalystPurchase       = "FALSE";
				SiteCatalystProductPrice   = "";
				SiteCatalystCurrencyCode   = "";
				SiteCatalystSeatAvailabilityINT   = "";
				SiteCatalystDepartDate     = "";
				SiteCatalystDepart         = "";
				SiteCatalystArrive         = "";
				SiteCatalystFlightNo       = "";
				SiteCatalystClass          = "";
				SiteCatalystFare           = "";
				SiteCatalystPurchaseID     = "";
				
				SiteCatalystLocater        = "";
				SiteCatalystPayment        = "";
				SiteCatalystIncome         = "";
				
				SiteCatalystPax            = "";
				SiteCatalystManipulateDay  = "";
				SiteCatalystCompartmentClass   = "";
				SiteCatalystOptionInfo     = "";
				SiteCatalystOutboundUpsellPrice   = "";
				SiteCatalystInboundUpsellPrice    = "";
				
				SiteCatalystSearchInput    = "";
				SiteCatalystSegment        = "";
				SiteCatalystOfficeCode     = "";
				SiteCatalystPassengerCount = "";
				SiteCatalystPassenger      = "";
				SiteCatalystAncillaryService = "";
				// End SiteCatalyst code version: H.2.
				// ▲SiteCatalyst
			</script></div><script type="text/javascript"><!-- if(navigator.appVersion.indexOf('MSIE')>=0)document.write(unescape('%3C')+'\!-'+'-') // --></script>
		<!--/DO NOT REMOVE/-->
		

		
		<!-- Google Tag Manager -->
		<script type="text/javascript" charset="Shift_JIS" src="https://www.ana.co.jp/common/js/GTMINTBE.js"></script>
		<!-- End Google Tag Manager -->
		<!-- Yahoo Tag Manager -->
		<script type="text/javascript" charset="Shift_JIS" src="https://www.ana.co.jp/common/js/YTMINTBE.js"></script>
		<!-- End Yahoo Tag Manager -->
		

	</div>

	

	<div class="mboxDefault"></div><script type="text/javascript">mboxCreate("ASW_common_A04_P01");</script>
			<script type="text/javascript" src="https://www.ana.co.jp/common/js/tealium/tealium.js"></script>
			<script type="text/javascript" src="//cdn.evgnet.com/beacon/allnipponairways/nonamcprd/scripts/evergage.min.js"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/javax.faces.resource/jsf.js.xhtml?ln=javax.faces"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/jquery.mousewheel.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/jquery.jscrollpane.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/jquery-ui-1.10.4.custom.min.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/jquery.ui.touch-punch.min.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-common.js?beb6a50"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-common-pc.js?fd88d7d"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-format.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/mask.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/loading.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-loadingwindow-pc.js?9e233c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-loadingwindow.js?51db600"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/dialog-pc.js?25d36a4"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-dialog-sequencer.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-event.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-date-select.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-calendar-pc.js?9b86b8a"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-calendar.js?db624c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/heightLine.js?0e7a1ce"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-summaryarea.js?b21c6d7"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-fingerprints.js?99001fc"></script><script type="text/javascript">
			Asw.init("https://aswbe-i.ana.co.jp/rei21c/international_asw", "ja");
			Asw.getCalendarHolidayList = function() {return [20250721,20250811,20250915,20250923,20251013,20251103,20251123,20251124,20260101,20260112,20260211,20260223,20260320,20260429,20260503,20260504,20260505,20260506,20260720,20260811,20260921,20260922,20260923,20261012,20261103,20261123,20270101,20270111,20270211,20270223,20270321,20270322,20270429,20270503,20270504,20270505,20270719,20270811,20270920,20270923,20271011,20271103,20271123,20280101,20280110,20280211,20280223,20280320,20280429,20280503,20280504,20280505,20280717,20280811,20280918,20280922,20281009,20281103,20281123,20290101,20290108,20290211,20290212,20290223,20290320,20290429,20290430,20290503,20290504,20290505,20290716,20290811,20290917,20290923,20290924,20291008,20291103,20291123];}
			Asw.ClientInfo.deviceType = "PC";
			Asw.ClientInfo.mobileDeviceType = "UNKNOWN";
			Asw.ClientInfo.osVersion = "";
			Asw.Format.init("https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/{1}/{2}/{3}?{0}");
			Asw.overwriteTargetAttributeValue("false");
		</script><script type="text/javascript">
		
		var Asw = Asw || {};
		Asw.ContactInput = Asw.ContactInput || {};
		Asw.ContactInput.Const = {
			/** data-属性名「data-type」 @constant @type {String} @default 'data-type' */
			DATA_ATTRIBUTE_NAME_TYPE: 'data-type',
			/** data-属性名「data-sms-possible」 @constant @type {String} @default 'data-sms-possible' */
			DATA_ATTRIBUTE_NAME_SMS_POSSIBLE: 'data-sms-possible',
			/** data-属性名「data-country-code」 @constant @type {String} @default 'data-country-code' */
			DATA_ATTRIBUTE_NAME_COUNTRY_CODE: 'data-country-code',
			/** data-type属性値「sms」 @constant @type {String} @default 'sms' */
			DATA_TYPE_VALUE_SMS: 'sms'
		};

		
		Asw.ContactInput.countryInfoMap = {"IS":{"internationalCallingCode":"+354","smsPossible":false},"IE":{"internationalCallingCode":"+353","smsPossible":true},"AZ":{"internationalCallingCode":"+994","smsPossible":true},"AF":{"internationalCallingCode":"+93","smsPossible":true},"US":{"internationalCallingCode":"+1","smsPossible":true},"AE":{"internationalCallingCode":"+971","smsPossible":true},"DZ":{"internationalCallingCode":"+213","smsPossible":true},"AR":{"internationalCallingCode":"+54","smsPossible":true},"AW":{"internationalCallingCode":"+297","smsPossible":true},"AL":{"internationalCallingCode":"+355","smsPossible":true},"AM":{"internationalCallingCode":"+374","smsPossible":true},"AO":{"internationalCallingCode":"+244","smsPossible":false},"AG":{"internationalCallingCode":"+1","smsPossible":true},"AD":{"internationalCallingCode":"+376","smsPossible":true},"YE":{"internationalCallingCode":"+967","smsPossible":true},"GB":{"internationalCallingCode":"+44","smsPossible":true},"IL":{"internationalCallingCode":"+972","smsPossible":true},"IT":{"internationalCallingCode":"+39","smsPossible":true},"IQ":{"internationalCallingCode":"+964","smsPossible":true},"IR":{"internationalCallingCode":"+98","smsPossible":true},"IN":{"internationalCallingCode":"+91","smsPossible":true},"ID":{"internationalCallingCode":"+62","smsPossible":true},"UG":{"internationalCallingCode":"+256","smsPossible":true},"UA":{"internationalCallingCode":"+380","smsPossible":true},"UZ":{"internationalCallingCode":"+998","smsPossible":true},"UY":{"internationalCallingCode":"+598","smsPossible":true},"EC":{"internationalCallingCode":"+593","smsPossible":true},"EG":{"internationalCallingCode":"+20","smsPossible":true},"EE":{"internationalCallingCode":"+372","smsPossible":true},"SZ":{"internationalCallingCode":"+268","smsPossible":false},"ET":{"internationalCallingCode":"+251","smsPossible":false},"ER":{"internationalCallingCode":"+291","smsPossible":true},"SV":{"internationalCallingCode":"+503","smsPossible":true},"AU":{"internationalCallingCode":"+61","smsPossible":true},"AT":{"internationalCallingCode":"+43","smsPossible":true},"OM":{"internationalCallingCode":"+968","smsPossible":true},"NL":{"internationalCallingCode":"+31","smsPossible":true},"GH":{"internationalCallingCode":"+233","smsPossible":true},"CV":{"internationalCallingCode":"+238","smsPossible":false},"GY":{"internationalCallingCode":"+592","smsPossible":true},"KZ":{"internationalCallingCode":"+7","smsPossible":true},"QA":{"internationalCallingCode":"+974","smsPossible":true},"CA":{"internationalCallingCode":"+1","smsPossible":true},"GA":{"internationalCallingCode":"+241","smsPossible":true},"CM":{"internationalCallingCode":"+237","smsPossible":true},"KR":{"internationalCallingCode":"+82","smsPossible":true},"KH":{"internationalCallingCode":"+855","smsPossible":true},"GM":{"internationalCallingCode":"+220","smsPossible":true},"KP":{"internationalCallingCode":"+850","smsPossible":false},"MK":{"internationalCallingCode":"+389","smsPossible":false},"GN":{"internationalCallingCode":"+224","smsPossible":true},"GW":{"internationalCallingCode":"+245","smsPossible":true},"CY":{"internationalCallingCode":"+357","smsPossible":true},"CU":{"internationalCallingCode":"+53","smsPossible":true},"GR":{"internationalCallingCode":"+30","smsPossible":true},"KI":{"internationalCallingCode":"+686","smsPossible":false},"KG":{"internationalCallingCode":"+996","smsPossible":true},"GT":{"internationalCallingCode":"+502","smsPossible":true},"GU":{"internationalCallingCode":"+1","smsPossible":true},"KW":{"internationalCallingCode":"+965","smsPossible":true},"KY":{"internationalCallingCode":"+1","smsPossible":true},"GD":{"internationalCallingCode":"+1","smsPossible":true},"HR":{"internationalCallingCode":"+385","smsPossible":true},"KE":{"internationalCallingCode":"+254","smsPossible":true},"CI":{"internationalCallingCode":"+225","smsPossible":true},"CR":{"internationalCallingCode":"+506","smsPossible":true},"SA":{"internationalCallingCode":"+966","smsPossible":true},"KM":{"internationalCallingCode":"+269","smsPossible":true},"CO":{"internationalCallingCode":"+57","smsPossible":true},"CG":{"internationalCallingCode":"+242","smsPossible":false},"CD":{"internationalCallingCode":"+243","smsPossible":true},"ZM":{"internationalCallingCode":"+260","smsPossible":true},"SM":{"internationalCallingCode":"+378","smsPossible":false},"SL":{"internationalCallingCode":"+232","smsPossible":true},"DJ":{"internationalCallingCode":"+253","smsPossible":false},"JM":{"internationalCallingCode":"+1","smsPossible":true},"CH":{"internationalCallingCode":"+41","smsPossible":true},"GE":{"internationalCallingCode":"+995","smsPossible":true},"SE":{"internationalCallingCode":"+46","smsPossible":true},"SY":{"internationalCallingCode":"+963","smsPossible":true},"ES":{"internationalCallingCode":"+34","smsPossible":true},"SG":{"internationalCallingCode":"+65","smsPossible":true},"LK":{"internationalCallingCode":"+94","smsPossible":true},"SK":{"internationalCallingCode":"+421","smsPossible":true},"SI":{"internationalCallingCode":"+386","smsPossible":true},"ZW":{"internationalCallingCode":"+263","smsPossible":true},"SD":{"internationalCallingCode":"+249","smsPossible":true},"KN":{"internationalCallingCode":"+1","smsPossible":true},"SR":{"internationalCallingCode":"+597","smsPossible":true},"LC":{"internationalCallingCode":"+1","smsPossible":true},"SC":{"internationalCallingCode":"+248","smsPossible":false},"SN":{"internationalCallingCode":"+221","smsPossible":true},"SB":{"internationalCallingCode":"+677","smsPossible":false},"RS":{"internationalCallingCode":"+381","smsPossible":true},"TH":{"internationalCallingCode":"+66","smsPossible":true},"TW":{"internationalCallingCode":"+886","smsPossible":true},"TJ":{"internationalCallingCode":"+992","smsPossible":true},"TZ":{"internationalCallingCode":"+255","smsPossible":true},"CZ":{"internationalCallingCode":"+420","smsPossible":true},"TD":{"internationalCallingCode":"+235","smsPossible":true},"CF":{"internationalCallingCode":"+236","smsPossible":true},"TN":{"internationalCallingCode":"+216","smsPossible":true},"CL":{"internationalCallingCode":"+56","smsPossible":true},"TV":{"internationalCallingCode":"+688","smsPossible":false},"DK":{"internationalCallingCode":"+45","smsPossible":true},"DE":{"internationalCallingCode":"+49","smsPossible":true},"TG":{"internationalCallingCode":"+228","smsPossible":true},"DO":{"internationalCallingCode":"+1","smsPossible":true},"DM":{"internationalCallingCode":"+1","smsPossible":false},"TT":{"internationalCallingCode":"+1","smsPossible":true},"TM":{"internationalCallingCode":"+993","smsPossible":false},"TR":{"internationalCallingCode":"+90","smsPossible":true},"TO":{"internationalCallingCode":"+676","smsPossible":false},"NG":{"internationalCallingCode":"+234","smsPossible":true},"NR":{"internationalCallingCode":"+674","smsPossible":false},"NA":{"internationalCallingCode":"+264","smsPossible":false},"NI":{"internationalCallingCode":"+505","smsPossible":true},"BH":{"internationalCallingCode":"+973","smsPossible":false},"NE":{"internationalCallingCode":"+227","smsPossible":true},"PK":{"internationalCallingCode":"+92","smsPossible":true},"JP":{"internationalCallingCode":"+81","smsPossible":true},"BS":{"internationalCallingCode":"+1","smsPossible":true},"NZ":{"internationalCallingCode":"+64","smsPossible":true},"BM":{"internationalCallingCode":"+1","smsPossible":true},"NP":{"internationalCallingCode":"+977","smsPossible":true},"BB":{"internationalCallingCode":"+246","smsPossible":true},"NO":{"internationalCallingCode":"+47","smsPossible":true},"HT":{"internationalCallingCode":"+509","smsPossible":true},"BD":{"internationalCallingCode":"+880","smsPossible":true},"VA":{"internationalCallingCode":"+39","smsPossible":false},"PA":{"internationalCallingCode":"+507","smsPossible":true},"VU":{"internationalCallingCode":"+678","smsPossible":false},"PG":{"internationalCallingCode":"+675","smsPossible":true},"PW":{"internationalCallingCode":"+680","smsPossible":true},"PY":{"internationalCallingCode":"+595","smsPossible":true},"PS":{"internationalCallingCode":"+970","smsPossible":true},"BR":{"internationalCallingCode":"+55","smsPossible":true},"HU":{"internationalCallingCode":"+36","smsPossible":true},"TL":{"internationalCallingCode":"+670","smsPossible":true},"BG":{"internationalCallingCode":"+359","smsPossible":true},"BF":{"internationalCallingCode":"+226","smsPossible":true},"BN":{"internationalCallingCode":"+673","smsPossible":true},"FJ":{"internationalCallingCode":"+679","smsPossible":true},"PH":{"internationalCallingCode":"+63","smsPossible":true},"FI":{"internationalCallingCode":"+358","smsPossible":true},"BT":{"internationalCallingCode":"+975","smsPossible":true},"BY":{"internationalCallingCode":"+375","smsPossible":true},"BZ":{"internationalCallingCode":"+501","smsPossible":false},"PR":{"internationalCallingCode":"+1","smsPossible":true},"BE":{"internationalCallingCode":"+32","smsPossible":true},"BA":{"internationalCallingCode":"+387","smsPossible":true},"FR":{"internationalCallingCode":"+33","smsPossible":true},"BO":{"internationalCallingCode":"+591","smsPossible":false},"PF":{"internationalCallingCode":"+689","smsPossible":true},"BI":{"internationalCallingCode":"+257","smsPossible":true},"VN":{"internationalCallingCode":"+84","smsPossible":true},"BJ":{"internationalCallingCode":"+229","smsPossible":true},"VE":{"internationalCallingCode":"+58","smsPossible":true},"PE":{"internationalCallingCode":"+51","smsPossible":true},"PL":{"internationalCallingCode":"+48","smsPossible":true},"BW":{"internationalCallingCode":"+267","smsPossible":true},"PT":{"internationalCallingCode":"+351","smsPossible":false},"HK":{"internationalCallingCode":"+852","smsPossible":true},"HN":{"internationalCallingCode":"+504","smsPossible":false},"MH":{"internationalCallingCode":"+692","smsPossible":true},"MO":{"internationalCallingCode":"+853","smsPossible":true},"MG":{"internationalCallingCode":"+261","smsPossible":true},"MW":{"internationalCallingCode":"+265","smsPossible":true},"ML":{"internationalCallingCode":"+223","smsPossible":true},"MT":{"internationalCallingCode":"+356","smsPossible":true},"MY":{"internationalCallingCode":"+60","smsPossible":true},"FM":{"internationalCallingCode":"+691","smsPossible":false},"MM":{"internationalCallingCode":"+95","smsPossible":true},"MX":{"internationalCallingCode":"+52","smsPossible":true},"MU":{"internationalCallingCode":"+230","smsPossible":true},"MR":{"internationalCallingCode":"+222","smsPossible":true},"MZ":{"internationalCallingCode":"+258","smsPossible":true},"MC":{"internationalCallingCode":"+377","smsPossible":true},"MV":{"internationalCallingCode":"+960","smsPossible":true},"MD":{"internationalCallingCode":"+373","smsPossible":false},"RO":{"internationalCallingCode":"+40","smsPossible":true},"MA":{"internationalCallingCode":"+212","smsPossible":true},"MN":{"internationalCallingCode":"+976","smsPossible":true},"ME":{"internationalCallingCode":"+382","smsPossible":true},"JO":{"internationalCallingCode":"+962","smsPossible":true},"LA":{"internationalCallingCode":"+856","smsPossible":true},"RU":{"internationalCallingCode":"+7","smsPossible":true},"LV":{"internationalCallingCode":"+371","smsPossible":true},"LT":{"internationalCallingCode":"+370","smsPossible":true},"LY":{"internationalCallingCode":"+218","smsPossible":true},"GQ":{"internationalCallingCode":"+240","smsPossible":false},"LI":{"internationalCallingCode":"+423","smsPossible":false},"LR":{"internationalCallingCode":"+231","smsPossible":true},"CN":{"internationalCallingCode":"+86","smsPossible":true},"LU":{"internationalCallingCode":"+352","smsPossible":true},"ZA":{"internationalCallingCode":"+27","smsPossible":true},"RW":{"internationalCallingCode":"+250","smsPossible":true},"LS":{"internationalCallingCode":"+266","smsPossible":false},"LB":{"internationalCallingCode":"+961","smsPossible":true}};

		$(function () {
			contactInfoInit();
		});
		
		function contactInfoInit() {
			
			
			Asw.get('representativeTelType').find('option[value="M1"]').attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_TYPE,
					Asw.ContactInput.Const.DATA_TYPE_VALUE_SMS);
			Asw.get('representativeTelCountry').children('option').each(function(i,option){
				var countryInfo = Asw.ContactInput.countryInfoMap[option.value];
				if (countryInfo === void 0) {
					
					return true;
				}
				$(option).attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_SMS_POSSIBLE, countryInfo.smsPossible);
				$(option).attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_COUNTRY_CODE, countryInfo.internationalCallingCode);
			});
			
			var smsCountrySelectSelector = 'select.js-countrySelect:not(#representativeTelCountry)'
			$(smsCountrySelectSelector).eq(0).children('option').each(function(i,option){
				var countryInfo = Asw.ContactInput.countryInfoMap[option.value];
				if (countryInfo === void 0) {
					
					return true;
				}
				$(smsCountrySelectSelector + ' option:nth-child(' + (i+1) + ')').attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_COUNTRY_CODE,
						countryInfo.internationalCallingCode);
			});
			
			var emergencyCountrySelectSelector = 'select.passengerEmergencyCountry.js-countrySelect:not(#representativeTelCountry)'
			$(emergencyCountrySelectSelector).eq(0).children('option').each(function(i,option){
				var countryInfo = Asw.ContactInput.countryInfoMap[option.value];
				if (countryInfo === void 0) {
					
					return true;
				}
				$(emergencyCountrySelectSelector + ' option:nth-child(' + (i+1) + ')').attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_COUNTRY_CODE,
						countryInfo.internationalCallingCode);
			});
		}
	</script><script type="text/javascript">
		var isInit = true;
		function changeTelType(_this, telNoInputId) {
			var $telNoInput = Asw.get(telNoInputId);
			var $telNoKind = Asw.get("representativeTelType");
			var telNo = Asw.getValueComparedToPlaceholder(telNoInputId);
			var telNoCountry = Asw.get("representativeTelCountry").val();

			if(true && !isInit) {
				if(telInputCheck(telNo, telNoCountry)) {
					if (_this.value == "M1") {
						telNo = exceptDomesticPrefix("09042504176", telNoCountry);
					} else if (_this.value == "B1") {
						telNo = exceptDomesticPrefix("03-5405-2172", telNoCountry);
					} else {
						telNo = exceptDomesticPrefix("090-4250-4176", telNoCountry);
					}
					$telNoInput.val(telNo);
				}
			}
			isInit = false;
		}
		
		function telInputCheck(telNo, telNoCountry) {
	        checkResult = false;
	        
	        var amcHndyTelNo = exceptDomesticPrefix("09042504176", telNoCountry);
	        var amcCmpTelNo = exceptDomesticPrefix("03-5405-2172", telNoCountry);
	        var amcTelNo = exceptDomesticPrefix("090-4250-4176", telNoCountry);
	
	        if(telNo == "" || telNo == amcHndyTelNo || telNo == amcCmpTelNo || telNo == amcTelNo) {
	        	checkResult = true;
	        }
	        return checkResult;
    	}
    	
    	function exceptDomesticPrefix(telNo, telNoCountry) {
    		if(telNoCountry == 'JP') {
    			if(telNo.substr(0, 1) === '0') {
    				return telNo.slice(1);
    			}
    		}
    		return telNo;
    	}
	</script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-customerinfomationform.js?6e91953"></script><script type="text/javascript">
		$(document).ready(function(){
				Asw.enableTextWhenCheckBox('.cascadedSwitch');
				Asw.notPasteInput('.jsNotPasteInput');
			$(function(){
				Asw.alignHeightToParentTdElement( '.passengerInfoInputArea' );
				
					Asw.alignHeightToParentTdElement( '.passengerInfoInputText' );
				
				
				$('select.js-countrySelect').trigger('change');
			});
		});
	</script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-session-keeper.js?6ec0730"></script><script type="text/javascript">
			$(document).ready(function() {
				Asw.SessionKeeper.initAndStart("sessionKeeperContainer", "520", "570", "PC");
			});
			</script><script type="text/javascript">
		Asw.addAjaxCallback(function(data) {
			// アコーディオンのメソッドと設定 
			if($('.jsAccordionSwitch .jsTriggerSwitch').length > 0){
				var events = $._data($('.jsAccordionSwitch .jsTriggerSwitch').get(0), "events");
				if(events === undefined || events.click === undefined) {
					Asw.AccordionInfo('.jsAccordionSwitch', {
						'triggerSwitch': '.jsAccordionSwitch .jsTriggerSwitch',
						'listClass': '.jsAccordionSwitchList',
						'visibilityHidden': 'toggleContents'
					});
				}
			}
		});
	</script><script type="text/javascript">
	Asw.Price = Asw.Price || {};
	// 整数の区切り文字
	Asw.Price.separateString = ',';
	
	// 整数の区切り桁数
	Asw.Price.separateDigitNumber = 3;
	
	// 小数点
	Asw.Price.decimalPointString = '.';
	
	// 小数点以降の有効桁数
	Asw.Price.significantDigit = 0;
	
	// 表示金額の単位
	Asw.Price.currencyUnit = '円';
	</script><script type="text/javascript">
			$(document).ready(function() {
				$('#preLoadingArea').css('visibility', 'hidden');
			});
		</script><noscript><img src="https://aswbe-i.ana.co.jp/akam/13/pixel_4e8103ad?a=dD1jZTU0OTJlMmI3NDE2OGQ1YWFkOGNhNzNiYTcwYjk5MWI1MWZhNzk0JmpzPW9mZg==" style="visibility: hidden; position: absolute; left: -999px; top: -999px;" /></noscript><script type="text/javascript"  src="/RcS9yzCRgrNnM/gY/v0Wf9fQwtpdQ/7NE3X2ruOE2SL9YE/HyNpQmYB/RWt/XOHcnHAU"></script></body>
</html>