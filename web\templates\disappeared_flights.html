{% extends 'base.html' %}

{% block content %}
{# <h1>{{ title }}</h1> #} {# 移除页面主标题 #}

<div class="row">
    {# --- 左栏：美洲消失航线 --- #}
    <div class="col-6" id="americas-column">
        {% if americas_disappeared_groups %}
            {% for city, flights in americas_disappeared_groups %}
            <div class="table-responsive mt-0 mb-0" data-city="{{ city }}">
                <table class="table table-fixed table-hover table-sm mt-0 mb-0">
                    <colgroup>
                        <col style="width:15%">
                        <col style="width:15%">
                        <col style="width:15%">
                        <col style="width:15%">
                        <col style="width:15%">
                        <col style="width:25%">
                    </colgroup>
                    <!-- 移除表头 -->
                    <tbody id="tbody-americas-{{ city }}">
                        <tr class="table-secondary">
                            <td class="p-0"></td>
                            <td class="p-0"><h6 class="fw-bold mt-0 mb-0" style="font-size:0.75rem;">{{ city }}</h6></td>
                            <td class="p-0"></td>
                            <td class="p-0"></td>
                            <td class="p-0"></td>
                            <td class="p-0"></td>
                        </tr>
                        {% for flight in flights %}
                        {% set history_key = flight.date ~ '_' ~ flight.origin ~ '_' ~ flight.destination ~ '_' ~ flight.flight_numbers ~ '_' ~ flight.cabin_class %}
                        <tr data-history-key="{{ history_key }}">
                            <td>{{ flight.date }}</td>
                            <td>{{ flight.origin }}→{{ flight.destination }}</td>
                            <td>{{ flight.flight_numbers }}</td>
                            <td>{{ flight.cabin_class }}</td>
                            <td>{{ flight.current_seats }}</td>
                            <td>{{ flight.last_updated_timestamp }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% endfor %}
        {% else %}
            <p>没有找到消失的美洲航线数据。</p>
        {% endif %}
    </div>

    {# --- 右栏：中日消失航线 --- #}
    <div class="col-6" id="china-japan-column">
        {% if china_japan_disappeared_groups %}
            {% for city, flights in china_japan_disappeared_groups %}
            <div class="table-responsive mt-0 mb-0" data-city="{{ city }}">
                <table class="table table-fixed table-hover table-sm mt-0 mb-0">
                    <colgroup>
                        <col style="width:15%">
                        <col style="width:15%">
                        <col style="width:15%">
                        <col style="width:15%">
                        <col style="width:15%">
                        <col style="width:25%">
                    </colgroup>
                    <!-- 移除表头 -->
                    <tbody id="tbody-china-japan-{{ city }}">
                        <tr class="table-secondary">
                            <td class="p-0"></td>
                            <td class="p-0"><h6 class="fw-bold mt-0 mb-0" style="font-size:0.75rem;">{{ city }}</h6></td>
                            <td class="p-0"></td>
                            <td class="p-0"></td>
                            <td class="p-0"></td>
                            <td class="p-0"></td>
                        </tr>
                        {% for flight in flights %}
                        {% set history_key = flight.date ~ '_' ~ flight.origin ~ '_' ~ flight.destination ~ '_' ~ flight.flight_numbers ~ '_' ~ flight.cabin_class %}
                        <tr data-history-key="{{ history_key }}">
                            <td>{{ flight.date }}</td>
                            <td>{{ flight.origin }}→{{ flight.destination }}</td>
                            <td>{{ flight.flight_numbers }}</td>
                            <td>{{ flight.cabin_class }}</td>
                            <td>{{ flight.current_seats }}</td>
                            <td>{{ flight.last_updated_timestamp }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% endfor %}
        {% else %}
            <p>没有找到消失的中日航线数据。</p>
        {% endif %}
    </div>
</div>

{# 历史记录提示框容器 #}
<div id="history-tooltip" class="history-tooltip"></div>

{% endblock %}

{% block scripts %}
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script> {# 确保引入了 jQuery #}
<script>
$(document).ready(function() {
    let tooltip = $('#history-tooltip');
    let activeKey = null; // Track active key for click-based tooltip

    // Function to bind click event for history
    function bindHistoryClick(selector) {
        $(selector).off('click').on('click', function(e) {
            e.stopPropagation(); // Prevent click from propagating to document
            let row = $(this);
            let historyKey = row.data('history-key');
            if (!historyKey) return;

            // If clicking the same row again, hide tooltip
            if (activeKey === historyKey) {
                tooltip.hide();
                activeKey = null;
                return;
            }

            activeKey = historyKey; // Set the new active key

            // Initial tooltip state
            tooltip.html('加载中...').show();
            let position = row.offset();
            let rowHeight = row.outerHeight();

            // Position tooltip below the row
            let topPos = position.top + rowHeight + 5;
            let leftPos = position.left + 50;

            // Adjust if tooltip goes off-screen right
            let tooltipWidth = tooltip.outerWidth() || 250; // Estimate width if not rendered
            let windowWidth = $(window).width();
            if (leftPos + tooltipWidth > windowWidth - 20) {
                leftPos = windowWidth - tooltipWidth - 20;
            }
            if (leftPos < 0) leftPos = 5; // Prevent going off-screen left
            if (topPos < 0) topPos = 5; // Prevent going off-screen top

            tooltip.css({ top: topPos, left: leftPos });

            // AJAX request for history data
            $.ajax({
                url: `/api/history/${historyKey}`,
                method: 'GET',
                success: function(data) {
                    // Only update if the tooltip for this key is still active
                    if (activeKey === historyKey) {
                        if (data && data.length > 0) {
                            let content = '<table class="table table-sm table-borderless mb-0"><tbody>';
                            data.forEach(function(item) {
                                // Improved date parsing for history
                                let ts = 'N/A';
                                try {
                                    let dt_hist;
                                    // Handle ISO format with 'T' and optional 'Z' or offset
                                    if (item.timestamp.includes('T')) {
                                        // Append 'Z' if no timezone info to treat as UTC
                                        dt_hist = new Date(item.timestamp + (item.timestamp.match(/[+-]\d{2}:?\d{2}|Z$/) ? '' : 'Z'));
                                    }
                                    // Handle space separator (assuming local or UTC if no 'T')
                                    else {
                                        // Attempt parsing as local time first
                                        dt_hist = new Date(item.timestamp.replace(/-/g, '/'));
                                        // Could potentially try UTC if needed: new Date(item.timestamp.replace(/-/g, '/') + ' GMT')
                                    }

                                    // Check if date is valid before formatting
                                    if (!isNaN(dt_hist)) {
                                        // Format to local time string (adjust options as needed)
                                        ts = dt_hist.toLocaleString('zh-CN', { year: 'numeric', month: '2-digit', day: '2-digit', hour: '2-digit', minute: '2-digit', second: '2-digit', hour12: false });
                                    } else {
                                        ts = item.timestamp; // Fallback to original string if parsing fails
                                    }
                                } catch(e) {
                                    ts = item.timestamp; // Fallback on any error
                                    console.error("Error parsing history date:", item.timestamp, e);
                                }
                                content += `<tr><td class="pe-2">${ts}</td><td>座位: ${item.seats}</td></tr>`;
                            });
                            tooltip.html(content + '</tbody></table>');
                        } else {
                            tooltip.text('没有历史记录。');
                        }
                    }
                },
                error: function(xhr, status, error) {
                    console.error("获取历史记录失败:", status, error);
                    // Only update if the tooltip for this key is still active
                    if (activeKey === historyKey) {
                        tooltip.text('加载历史记录失败。');
                    }
                }
            });
        });
    }

    // Bind click event to all relevant rows initially
    bindHistoryClick('tbody tr[data-history-key]');

    // Click anywhere else on the document to hide the tooltip
    $(document).on('click', function(e) {
        // Hide if the click is not on a history row or the tooltip itself
        if ($(e.target).closest('tbody tr[data-history-key], #history-tooltip').length === 0) {
            tooltip.hide();
            activeKey = null; // Reset active key when hiding
        }
    });
});
</script>
{% endblock %}