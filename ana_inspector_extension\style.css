body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
    background-color: #f0f2f5;
    color: #1c1e21;
    margin: 0;
    padding: 10px;
    font-size: 13px;
}

.toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 10px;
    border-bottom: 1px solid #ddd;
    margin-bottom: 10px;
}

h1 {
    font-size: 16px;
    margin: 0;
}

button {
    background-color: #007bff;
    color: white;
    border: none;
    padding: 5px 10px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    margin-left: 5px;
}

button:hover {
    opacity: 0.9;
}

.record-start { background-color: #28a745; }
.record-stop { background-color: #dc3545; }
#export-button { background-color: #17a2b8; }
#clear-button { background-color: #6c757d; }

.instructions {
    background-color: #e9f5ff;
    border: 1px solid #b3d7f0;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 15px;
    line-height: 1.6;
}

.instructions p {
    margin: 0 0 5px 0;
}

#status {
    padding: 5px 0 10px 0;
    color: #606770;
    font-style: italic;
}

.entry {
    background-color: #fff;
    border: 1px solid #ccc;
    border-radius: 4px;
    margin-bottom: 5px;
    overflow: hidden;
}

.session-request {
    border: 2px solid #28a745;
}

.header {
    background-color: #f7f7f7;
    padding: 8px 10px;
    cursor: pointer;
    font-weight: bold;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #eee;
}

.header.active {
    background-color: #e9f5ff;
}

.header:hover {
    background-color: #e9ecef;
}

.content {
    display: none;
    padding: 15px;
    line-height: 1.6;
    word-break: break-all;
    background-color: #fff;
}

.data-block, .data-table {
    margin-bottom: 15px;
}

h4 {
    margin-top: 0;
    margin-bottom: 5px;
    font-size: 13px;
    color: #606770;
    text-transform: uppercase;
    border-bottom: 1px solid #eee;
    padding-bottom: 3px;
}

.url-data {
    background: #f0f2f5;
    padding: 8px;
    border-radius: 3px;
    font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
}

table {
    width: 100%;
    border-collapse: collapse;
}

table td {
    padding: 6px;
    border: 1px solid #eee;
    vertical-align: top;
    font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
}

table td.key {
    font-weight: bold;
    width: 25%;
    color: #333;
}

table td.value {
    word-break: break-all;
    color: #555;
}

hr {
    border: none;
    border-top: 1px dashed #ccc;
    margin: 20px 0;
}
