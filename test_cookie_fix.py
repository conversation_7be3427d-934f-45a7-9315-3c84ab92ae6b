#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试cookie获取逻辑修复
"""

from curl_cffi import Session
from loguru import logger

# 配置日志
logger.add("test_cookie_fix.log", rotation="10 MB", level="DEBUG")

def test_cookie_access():
    """测试正确的cookie获取方式"""
    
    logger.info("=== 测试cookie获取逻辑修复 ===")
    
    # 创建session
    session = Session(impersonate="chrome101")
    
    # 模拟设置一些cookies
    session.cookies.set('dtPC', '1$100365956_57h45vSAEFCTSCHCNPMGLNHDEKWLKWLKAJCEQU-0e0')
    session.cookies.set('JSESSIONID', 'test_session_id')
    
    logger.info("1. 测试错误的方式（会报错）:")
    try:
        # 错误的方式 - 这会报错
        for cookie in session.cookies:
            logger.info(f"Cookie: {cookie.name}")
    except Exception as e:
        logger.warning(f"错误的方式失败: {e}")
    
    logger.info("2. 测试正确的方式:")
    try:
        # 正确的方式 - 直接获取
        dtpc_value = session.cookies.get('dtPC')
        jsession_value = session.cookies.get('JSESSIONID')
        
        logger.info(f"✅ dtPC值: {dtpc_value}")
        logger.info(f"✅ JSESSIONID值: {jsession_value}")
        
        # 测试不存在的cookie
        non_existent = session.cookies.get('NON_EXISTENT')
        logger.info(f"不存在的cookie: {non_existent}")
        
    except Exception as e:
        logger.error(f"正确的方式也失败: {e}")
    
    logger.info("3. 测试修复后的逻辑:")
    dtpc_value = None
    try:
        # 修复后的逻辑
        dtpc_value = session.cookies.get('dtPC')
        
        if not dtpc_value:
            dtpc_value = '1$100365956_57h45vSAEFCTSCHCNPMGLNHDEKWLKWLKAJCEQU-0e0'
            logger.info("未找到dtPC cookie，使用默认值")
        else:
            logger.info(f"成功获取dtPC值: {dtpc_value[:50]}...")
            
    except Exception as e:
        logger.warning(f"无法获取dtPC值: {e}")
        dtpc_value = '1$100365956_57h45vSAEFCTSCHCNPMGLNHDEKWLKWLKAJCEQU-0e0'
    
    logger.info(f"最终dtPC值: {dtpc_value}")
    
    logger.info("✅ Cookie获取逻辑修复完成！")

if __name__ == "__main__":
    test_cookie_access()
