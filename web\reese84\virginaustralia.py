import requests
import json
import execjs
from curl_cffi import requests
from loguru import logger
import random
import os

session = requests.Session(impersonate="chrome101")


script_dir = os.path.dirname(os.path.abspath(__file__))
js_file_path = os.path.join(script_dir, "re84_Pro.js")

with open(js_file_path, "r", encoding="utf-8") as f:
    js = f.read()

ctx = execjs.compile(js)

reese84_data = ctx.call(
    "get_reese84",
    "https://book.virginaustralia.com/side-you-ares-may-Exit-sition-Alaruern-Naugmen-G?d=book.virginaustralia.com",
    "P3zzFvDlRg/eIxIRb+nwJEpB63XRusRCTISFW2aZ2Ds=",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
)


headers = {
    "accept": "application/json; charset=utf-8",
    "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
    "cache-control": "no-cache",
    "content-type": "text/plain; charset=utf-8",
    "origin": "https://book.virginaustralia.com",
    "pragma": "no-cache",
    "priority": "u=1, i",
    "referer": "https://book.virginaustralia.com/dx/VADX/",
    "sec-ch-ua": '"Microsoft Edge";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": '"Windows"',
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-origin",
    "user-agent": reese84_data["ua"],
}

params = {
    "d": "book.virginaustralia.com",
}

json_data = {
    "solution": {
        "interrogation": {
            "p": reese84_data["p"],
            "st": reese84_data["st"],
            "sr": reese84_data["sr"],
            "cr": reese84_data["cr"],
            "og": 2,
        },
        "version": "stable",
    },
    "old_token": None,
    "error": None,
    "performance": {"interrogation": random.randint(100, 999)},
}


response = session.post(
    "https://book.virginaustralia.com/side-you-ares-may-Exit-sition-Alaruern-Naugmen-G",
    params=params,
    headers=headers,
    json=json_data,
    impersonate="chrome101",
)

res = response.json()
logger.debug(res)

token = res["token"]


cookies = {
    "reese84": token,
}

headers = {
    "accept": "*/*",
    "accept-language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
    "adrum": "isAjax:true",
    "application-id": "SWS1:SBR-DigConShpBk:fd34efe9a9",
    "cache-control": "no-cache",
    "content-type": "application/json",
    "conversation-id": "cmbpvelrc7v38e1sm7w6wbhcq",
    "dc-url": "",
    "execution": "40e39d50-c375-4a8d-b190-593eced6b63a",
    "origin": "https://book.virginaustralia.com",
    "pragma": "no-cache",
    "priority": "u=1, i",
    "referer": "https://book.virginaustralia.com/dx/VADX/",
    "sec-ch-ua": '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": '"Windows"',
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-origin",
    "ssgtoken": "undefined",
    "ssotoken": "undefined",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "x-sabre-storefront": "VADX",
}

json_data = {
    "operationName": "bookingAirSearch",
    "variables": {
        "airSearchInput": {
            "cabinClass": "First",
            "awardBooking": True,
            "promoCodes": [],
            "searchType": "BRANDED",
            "itineraryParts": [
                {
                    "from": {
                        "useNearbyLocations": False,
                        "code": "HND",
                    },
                    "to": {
                        "useNearbyLocations": False,
                        "code": "SFO",
                    },
                    "when": {
                        "date": "2025-06-17",
                    },
                },
            ],
            "passengers": {
                "ADT": 1,
            },
        },
    },
    "extensions": {},
    "query": "query bookingAirSearch($airSearchInput: CustomAirSearchInput) {\n  bookingAirSearch(airSearchInput: $airSearchInput) {\n    originalResponse\n    __typename\n  }\n}",
}

response = session.post(
    "https://book.virginaustralia.com/api/graphql",
    cookies=cookies,
    headers=headers,
    json=json_data,
)
logger.success(response)
logger.success(response.json())
