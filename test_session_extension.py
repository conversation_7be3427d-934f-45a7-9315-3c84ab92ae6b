#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全日空会话延长功能测试脚本
用于验证修复后的会话延长功能是否正常工作
"""

import sys
import time
from loguru import logger
from curl_cffi.requests import Session, exceptions

def test_session_configuration():
    """测试会话配置是否正确"""
    logger.info("=== 测试会话配置 ===")
    
    try:
        # 测试新的会话配置
        session = Session(
            impersonate="chrome120", 
            timeout=120,
            max_clients=10,
            http_version=1
        )
        
        # 测试基本连接
        response = session.get("https://httpbin.org/get", timeout=30)
        if response.status_code == 200:
            logger.success("✅ 会话配置测试成功")
            return True
        else:
            logger.error(f"❌ 会话配置测试失败，状态码: {response.status_code}")
            return False
            
    except Exception as e:
        logger.error(f"❌ 会话配置测试异常: {e}")
        return False
    finally:
        if 'session' in locals():
            session.close()

def test_timeout_handling():
    """测试超时处理机制"""
    logger.info("=== 测试超时处理机制 ===")
    
    try:
        session = Session(
            impersonate="chrome120", 
            timeout=5,  # 设置较短超时用于测试
            max_clients=5,
            http_version=1
        )
        
        # 测试超时情况
        try:
            # 使用一个会超时的URL进行测试
            response = session.get("https://httpbin.org/delay/10", timeout=3)
            logger.error("❌ 超时测试失败：应该超时但没有超时")
            return False
        except (exceptions.Timeout, exceptions.CurlError) as e:
            if "timeout" in str(e).lower() or "28" in str(e):
                logger.success("✅ 超时处理机制正常工作")
                return True
            else:
                logger.error(f"❌ 超时错误类型不符合预期: {e}")
                return False
                
    except Exception as e:
        logger.error(f"❌ 超时处理测试异常: {e}")
        return False
    finally:
        if 'session' in locals():
            session.close()

def test_session_recreation():
    """测试会话重建机制"""
    logger.info("=== 测试会话重建机制 ===")
    
    try:
        # 创建主会话
        main_session = Session(
            impersonate="chrome120",
            timeout=120,
            max_clients=10,
            http_version=1
        )
        
        # 设置一些测试cookies
        main_session.cookies.set('test_cookie', 'test_value')
        
        # 创建临时会话并复制cookies
        temp_session = Session(
            impersonate="chrome120",
            timeout=90,
            max_clients=5,
            http_version=1
        )
        
        temp_session.cookies.update(main_session.cookies)
        
        # 验证cookies是否正确复制
        if temp_session.cookies.get('test_cookie') == 'test_value':
            logger.success("✅ 会话重建和cookie复制机制正常工作")
            return True
        else:
            logger.error("❌ cookie复制失败")
            return False
            
    except Exception as e:
        logger.error(f"❌ 会话重建测试异常: {e}")
        return False
    finally:
        for session_name in ['main_session', 'temp_session']:
            if session_name in locals():
                locals()[session_name].close()

def test_headers_configuration():
    """测试请求头配置"""
    logger.info("=== 测试请求头配置 ===")
    
    try:
        session = Session(
            impersonate="chrome120",
            timeout=30,
            max_clients=5,
            http_version=1
        )
        
        # 测试请求头
        headers = {
            'Accept': '*/*',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Connection': 'keep-alive',
            'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'x-dtpc': '1$100365956_57h44vSAEFCTSCHCNPMGLNHDEKWLKWLKAJCEQU-0e0',
            'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"'
        }
        
        response = session.get("https://httpbin.org/headers", headers=headers)
        
        if response.status_code == 200:
            response_data = response.json()
            received_headers = response_data.get('headers', {})
            
            # 检查关键头部是否正确发送
            if (received_headers.get('X-Dtpc') == headers['x-dtpc'] and
                received_headers.get('User-Agent') == headers['User-Agent']):
                logger.success("✅ 请求头配置正常工作")
                return True
            else:
                logger.error("❌ 请求头配置不正确")
                return False
        else:
            logger.error(f"❌ 请求头测试失败，状态码: {response.status_code}")
            return False
            
    except Exception as e:
        logger.error(f"❌ 请求头测试异常: {e}")
        return False
    finally:
        if 'session' in locals():
            session.close()

def main():
    """运行所有测试"""
    logger.info("开始全日空会话延长功能测试...")
    
    tests = [
        ("会话配置", test_session_configuration),
        ("超时处理", test_timeout_handling),
        ("会话重建", test_session_recreation),
        ("请求头配置", test_headers_configuration)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n--- 运行测试: {test_name} ---")
        try:
            if test_func():
                passed += 1
                logger.success(f"✅ {test_name} 测试通过")
            else:
                logger.error(f"❌ {test_name} 测试失败")
        except Exception as e:
            logger.error(f"❌ {test_name} 测试异常: {e}")
        
        time.sleep(1)  # 测试间隔
    
    logger.info(f"\n=== 测试结果汇总 ===")
    logger.info(f"总测试数: {total}")
    logger.info(f"通过测试: {passed}")
    logger.info(f"失败测试: {total - passed}")
    
    if passed == total:
        logger.success("🎉 所有测试通过！会话延长功能修复成功。")
        return True
    else:
        logger.error(f"⚠️  有 {total - passed} 个测试失败，需要进一步检查。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
