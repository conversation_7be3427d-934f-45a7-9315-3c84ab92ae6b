2025-06-19 19:38:46.685 | INFO     | __main__:test_cookie_access:17 - === 测试cookie获取逻辑修复 ===
2025-06-19 19:38:46.686 | INFO     | __main__:test_cookie_access:26 - 1. 测试错误的方式（会报错）:
2025-06-19 19:38:46.686 | WARNING  | __main__:test_cookie_access:32 - 错误的方式失败: 'str' object has no attribute 'name'
2025-06-19 19:38:46.687 | INFO     | __main__:test_cookie_access:34 - 2. 测试正确的方式:
2025-06-19 19:38:46.688 | INFO     | __main__:test_cookie_access:40 - ✅ dtPC值: 1$100365956_57h45vSAEFCTSCHCNPMGLNHDEKWLKWLKAJCEQU-0e0
2025-06-19 19:38:46.688 | INFO     | __main__:test_cookie_access:41 - ✅ JSESSIONID值: test_session_id
2025-06-19 19:38:46.688 | INFO     | __main__:test_cookie_access:45 - 不存在的cookie: None
2025-06-19 19:38:46.689 | INFO     | __main__:test_cookie_access:50 - 3. 测试修复后的逻辑:
2025-06-19 19:38:46.689 | INFO     | __main__:test_cookie_access:60 - 成功获取dtPC值: 1$100365956_57h45vSAEFCTSCHCNPMGLNHDEKWLKWLKAJCEQU...
2025-06-19 19:38:46.690 | INFO     | __main__:test_cookie_access:66 - 最终dtPC值: 1$100365956_57h45vSAEFCTSCHCNPMGLNHDEKWLKWLKAJCEQU-0e0
2025-06-19 19:38:46.690 | INFO     | __main__:test_cookie_access:68 - ✅ Cookie获取逻辑修复完成！
