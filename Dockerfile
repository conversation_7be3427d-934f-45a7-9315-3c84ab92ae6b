# Dockerfile for VA multi-service project
FROM python:3.9-slim

# Set timezone to Asia/Shanghai
ENV TZ=Asia/Shanghai

# Configure apt sources for faster downloads (use Alibaba mirror)
RUN echo "deb https://mirrors.aliyun.com/debian/ bullseye main" > /etc/apt/sources.list && \
    echo "deb https://mirrors.aliyun.com/debian/ bullseye-updates main" >> /etc/apt/sources.list && \
    echo "deb https://mirrors.aliyun.com/debian-security bullseye-security main" >> /etc/apt/sources.list

# Install system dependencies with timeout and retry
RUN apt-get update --fix-missing && \
    apt-get install -y --no-install-recommends \
        tzdata \
        libcurl4-openssl-dev \
        nodejs \
        npm \
        curl \
        && ln -snf /usr/share/zoneinfo/$TZ /etc/localtime \
        && echo $TZ > /etc/timezone \
        && apt-get clean \
        && rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*

# Set work directory
WORKDIR /app

# Copy Python dependencies manifest FIRST to leverage layer caching
COPY requirements.txt /app/requirements.txt

# Configure pip to use Alibaba mirror and install Python dependencies with timeout
RUN pip config set global.index-url https://mirrors.aliyun.com/pypi/simple/ && \
    pip config set global.trusted-host mirrors.aliyun.com && \
    pip config set global.timeout 300 && \
    pip install --no-cache-dir --timeout 300 --retries 3 -r /app/requirements.txt

# Copy the ENTIRE project content into the work directory
COPY . /app

# Expose web server port
EXPOSE 5000

# CMD is overridden by docker-compose commands
