<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="ja-<PERSON>" xml:lang="ja-<PERSON>">

	
	<!-- skey = 2025/06/20 13:17:34 rei21c w0eLjhzFUs dljdmx+b90  --><head id="j_idt53">
    <base href="about:blank">
    <style>
        /* Hide elements that might try to load external resources, as a fallback. */
        img, svg, video, iframe, link[rel="stylesheet"], script { display: none !important; }
    </style>

		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
			<meta http-equiv="X-UA-Compatible" content="IE=EmulateIE9; IE=EmulateIE10" />
			<meta http-equiv="X-UA-Compatible" content="IE=7" />
			<meta http-equiv="X-UA-Compatible" content="IE=edge" />
		<meta name="description" content="" />
		<meta name="keywords" content="" />
		<meta name="format-detection" content="telephone=no" />
		<script type="text/javascript" src="/international_asw/dynatrace/ruxitagentjs_ICANVfhqrux_10273230920145353.js" data-dtconfig="rid=RID_546992603|rpid=-1269174706|domain=aswbe-i.ana.co.jp|reportUrl=https://bf90419wvr.bf.dynatrace.com/bf|app=78bf0b58acf6ed13|cors=1|owasp=1|featureHash=ICANVfhqrux|xb=www^bs.google-analytics^bs.com^pana^bs.demdex^bs.net^pcollect^bs.tealiumiq^bs.com^pallnipponairways^bs.australia-3^bs.evergage^bs.com^pallnipponairwayscolt^bs.tt^bs.omtrdc^bs.net|rdnt=1|uxrgce=1|cuc=k8vpgh61|mel=100000|expw=1|dpvc=1|md=mdcc1=bAsw.PnrOutput.recordLocator,mdcc2=bAsw.PnrOutput.paymentDetailInfoList[0].fopCode,mdcc3=bAsw.PnrOutput.ticketedFlag,mdcc4=bAsw.PnrOutput.nhPnrFlag,mdcc5=bAsw.PnrOutput.itinerarySummary.departureDateYyyymmdd,mdcc6=bAsw.SearchCriteriaOutput.cffCodeInput,mdcc7=a#main ^rb div.section.sectionNoFlow ^rb table ^rb tbody ^rb tr ^rb td.flightNumber.lastCol ^rb span ^rb span:nth-child(2),mdcc8=cunique|lastModification=1750184416409|tp=500,50,0|agentUri=/international_asw/dynatrace/ruxitagentjs_ICANVfhqrux_10273230920145353.js"></script><link rel="shortcut icon" href="https://www.ana.co.jp/favicon.ico" />
		<title>お客様情報入力 | ANA</title>
		<noscript><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/css/asw_noscript_pc.css?99001fc" />
		</noscript>

		<!--  [ Adobe Target ] -->
		<script type="text/javascript" src="https://www.ana.co.jp/behavior/adobetarget/target_intbe.js"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/jquery-1.8.3.min.js?717d3c0"></script><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/ja/css/asw_initialize.css?99001fc" /><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/css/asw_screen_pc.css?99001fc" /><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/css/asw_common_pc.css?99001fc" /><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/css/asw_forminput_pc.css?99001fc" /><script type="text/javascript">
		var Asw = Asw || {};
		Asw.GlobalWord = Asw.GlobalWord || {};
		
		Asw.GlobalWord.toolTipCloseIconAltText = "閉じる";
		
		</script><script >bazadebezolkohpepadr="219810135"</script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/akam/13/d1a0a00"  defer></script></head><body>
		
		<noscript>当サイトをご利用になるためには、JavaScript対応のブラウザが必要です。設定でJavaScriptを有効にしてください。有効にできない場合はANA電話窓口へお問い合わせください。
		</noscript>
		<div id="preLoadingArea"></div>
	
	<div id="cmnHeader" class="platinum">
		<!--===== cmnHeader =====-->
		<div class="headerWrapper">
			<div class="header">
				<p class="logo"><a href="https://www.ana.co.jp/asw/AMCTopServlet?type=a" tabindex="-1" aria-hidden="true"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/ana_logo_navy.png?717d3c0" alt="ANA Inspiration of JAPAN。リンク先はマイレージクラブトップページです。" height="28" width="274" /></a>
				</p>
					<ul class="headerMenu">
						<li><a href="https://www.ana.co.jp/asw/AMCTopServlet?type=a">ANAマイレージクラブトップページ</a>
						</li>
						<li><a href="https://www.ana.co.jp/other/int/meta/0005.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">お問い合わせ<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
						</li>
							<li class="btnLogoutArea"><a href="https://aswbe-i.ana.co.jp/rei21c/international_asw/rest/logout" role="button" class="btnBase btnLogout">ログアウト</a>
							</li>
					</ul>
			</div>
		</div>
			<div class="amcUserInfoWrapper">
				<div class="amcUserInfo">
					<div class="userNameArea">
						<table>
							<tr>	
									<td class="statusIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_status_05.png?717d3c0" alt="プラチナサービスメンバー" height="46" width="60" />
									</td>
								<td class="userName">佐藤 直樹 <span>様</span>
								</td>
							</tr>					
						</table>
					</div>
						<div class="balance">
								<dl class="upgradePointBalance">
									<dt>アップグレードポイント残高
									</dt>
									<dd>
											<dl>
												<dt>(2025年度)
												</dt>
												<dd>
													<em>20
													</em>ポイント
												</dd>
											</dl>
									</dd>
								</dl>
							<dl class="mileBalance">
								<dt>マイル口座残高
								</dt>
								<dd><em>75,663</em><span class="currencyCode">マイル</span>
								</dd>
							</dl>
						</div>
				</div>
					<p class="nowTime">6月20日13時17分現在
					</p>
			</div>
		<!--===== /cmnHeader =====-->
	</div>
		<div id="sessionKeeperContainer" class="sessionKeepingContainer"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/bg_modalWindow.png?717d3c0" alt="" height="0" width="0" />
			<div id="extendSessionExpiredDialog" class="modalExpire modalSmall modalContainer" role="dialog" aria-labelledby="extendSessionExpiredDialogTitle" aria-hidden="true">
<form id="sessionKeeperContainer:j_idt183" name="sessionKeeperContainer:j_idt183" method="post" action="https://aswbe-i.ana.co.jp/rei21c/international_asw/pages/award/reservation/award_mandatory_passenger_information_input.xhtml?aswcid=1&amp;rand=20250620131734w0eLjhzFUs" autocomplete="off" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="sessionKeeperContainer:j_idt183" value="sessionKeeperContainer:j_idt183" />
<input type="hidden" name="sessionKeeperContainer:j_idt183_operationTicket" value="dljdmx+b9015f85aa932eca40815ed63b22e431~VIgk5BZZKdu2Sq0e1QNWDeKa82YzdngpCbu131yb!1750393042549.aere-xml-controller-67d4778877-5bbk7" /><input type="hidden" name="sessionKeeperContainer:j_idt183_cmnPageTicket" value="4" />
					<h1 id="extendSessionExpiredDialogTitle" class="dialogTitle">まもなくセッションが終了します</h1>
					<div id="extendSessionExpiredDialogMessage" class="dialogMessage" tabindex="-1">このままお手続きを進める場合は、［延長］ボタンを押してください。</div>
					<p class="modalButton"><input id="sessionKeeperContainer:cmnSessionKeepingButton" type="submit" name="sessionKeeperContainer:cmnSessionKeepingButton" value="延長" class="btnBase btnModal btnMainStream" onclick="mojarra.ab(this,event,'action',0,0,{'onevent':function(data) {Asw.doCommonAjaxCallback(data,false, event); Asw.SessionKeeper.doSessionKeep(data);},'onerror':function(data) {Asw.doCommonAjaxErrorCallback(data); }});return false" />
					</p><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:0" value="uoPwTloUbkcMKr9ZfLK1Ou+l3Vz6Kp1WOm94MEf3D7R8+AjEWr0OmKVPrlPK98s1odgsaPwkfrQoapLvPQIkFPTr3noZeUy4BV9467ePQkz3FhB045auVkT72GTGtMylp1WchvGHcFJZnJlDtC5b/455HYw4ZXYMwH6WB9pgNfu3/H6wpGXd0hwOyHmh71ZzrzGUjTER5j7vvmfo2xvPvCUPdp+8usAEFb7mDvzHZavxK9gwEyaf+htOdS4lkLjXivHanxQAwh77GXBBo9u5CoMuCwlNq5SOCFzOTpmXYGyWhHfo8He9O+swgV6WKbGKr79yqSGnWc4KmhWOfV6mJJ5BgGAc0bhdlKXJZkIpORY0UyeSlj2MUu2nWkfz+pZIGcHNwcJnsZRdGP/twhhaPHcowlbIZH0Ld3+tL7oq5rOEbOOCl6Sxnz2tUAulogcC8zUVP4C+XOrr5kKmK5xUcY27HjOaLZw2Nm9ZHxul1PU4fy4rKl5HW73jrYpXKVr5pOWuarB+KfC51nhuS1QX9OaeuhzR3bhDayhzUFxAFL8rc8548Z+1Oru9jLv/bynVuAsrvgrEVOAif5l5k45LoPafhJmnJcy260RzlwU9UDQIelHd5mUvr87GcPs5c9jF1Ui08G3Sdyw88TI2pz+cGGEWVLIhpXzE+A+M75ujubIzfd5+3vyDd/Utz+EyMVifBVjcrLjmOzHMdvtm4KI+X0qXecVMmJAjBi4XDBpVkLPjksblFY5/wqukcD2Y+wUd0FPozx5lxJBRI+LeirJUSTTpGhOs5eISuhnlguLXDa8OD8xqdrq8Zw6/Q8oaJT/fGyBmGUwOmv7EI7znWTBgHPucz1+cNvv9X2+7UhONXay45sQQjfbIL7tq036hVSP0TVP1+0psEpkBvr7J/kgYm/hPiytJvCK//4gPLiA2eUuoac981kh04EWpaQNvtxHenr6LVGWBz/4hhyZcjRUZJ+7AttMIMvEEAEpV0CUacC30/6+tKvNUcJvLae6T8XbaArxj5RTBK0RevAzxoQlQizljbKLcMspieXrqzyk9SNv0Ekps0OsyFVaaPGrbEfFHKE1660OxRejKiXpvWh6KCvDWyDpslmGCOvmiUsHNAsoHGRx9+ElA4gXLPot3J3uTiSABAFn6UjItxaPGgoJNmOHPtyCeP/KRuH1Iit3P+TNUcv9ALd6J3pNesQpkbB5WDHS4b/Rut3cIl58eb3dhulZTJx99d94Ja43+vouRXgt1LCgvIWyupl0w5JlAmyJdNjjUe+qiUIqtJ2k2l+hTAm2EQOELws+tN1NKpboGbO9goAgU9JRW3UsF8i66sX8QF0tqGSz0+np9+1fXCaQ68NQhsspiYrcz7f+kvAKqi5gYZVbAPZpsbijeQExVEFSnPPl/EBfbBUpkPY6oZ2x+NOuvBZdWt3G4loKZWbFl9QonB9ht01/UleivrrhF7kbbBg9vdTVjw+ziQq8OqWW4C9qtW+yhT9NOoMwL9PIxS59E3Kt9nNyF2+PCyc4KvHWQizDPykLSWQaXdYlDwulsKNlv+O4Xd2qIVY8HKB/O38NW4j6BqzTCZ1Ex1IygmQEN1DgCy0297Ar6OrHFZO0GCTwCrQ2R29fn2Zfj7PqXztFTTU8KO5lYeotyyzlc7JF5KInTbTVuBQSAE1OFu0x7PvCB3sK48MjnsWsWjVX0SZwXYzH1bTMXBY2pSDuTB3JN5A8Ue6ahhz8U19BT6szggsLgetY5UP7IVOOpgsCEpvJxDNoKvybnQ06PEiXG63FLXaPBpqwfew3ZV4N3CO+MQw1U5LH53ZJNSu5B/9KU1dpclpWC4EpyJuJSuvNHKgH6vXP/lgeWC/sR0HEBIyTGlvW+c+nDC7rK6lUKeMDCg5zoJvS6MfSCbNwNsmWIVG4OEDZ6cCAJ5SGRuDd/HWZ4057iPOmXzQm6Hpz4spZ6s1u3RkVD41hnW5NRy3SlI10F06eY2fPIORXF9WOO0o5GaQwzg0moqu910tf9NaxcBBJy7qsYfkLhuppR4aXVZK5vw3nJdIRR2j3GGHuMUtRp/vhJlRGWG9+1f0XL1zCzeAPSjXeVy9VxpSiXSf0odwaw3tO6G52N4gch4HejinUxmwQFn6INufRoA28/myX1WlWnjEpinbT9+/lknpjIL4CbMWWV+pdTf9r+LHnR3GtxiVS3jntmLPDXvEgE0peCHZPJNoPJYBe38j+QzyDj5sSYS/e4fcIA+lE3V6Hm8gGFHDe9E1oPA1dx5yU/xxekwv2JLdVf+azLfHTr0R9KpnkOSTuId41p7GOkCltmPLM0a0PL6CaKxmUNyOT4LR2MoiI6EIsRCxEyo9+6wpWo0HElkaLQVbX8RPaRnIVy3fAAJp1HMZ/5h++dLI2EfcMBU/NwSRIIZb1cjzmE+O8TlsLqi1Tac1Nmq3zlKox98aEvDmRbAY+Jcj8EFBh5FYKpaCi7X45ErFP3MS1J8OB5subcjJBfw23xMp97kW5izG/BxPrHdhZXqztxfgUm3KmeIp6AspTwSVJe9Pnv2XchD9VZeePJ1YTC/niOWShiTkuOX933g+3gzZi9f7JiKZDRwcutD2mSOR3X2D5+ka1U5GrA1YCGZXh3kc4Ch8qCFV36kBYOSkkV7MAV0RHAqvepYjdZKo9W4gNo9KT5t9I4m5vtfHIDfhFOaKCFJfu6/bzaRc8HbV6Kn5jtbQvjq6wsmsYNWhu90nx7rldSTUcHCC9vZ0CGrleqLzcwmZma9nNgNV2WUcjLMMYE7mUcvQPQjjOvkIuEQuXtVgxiJvKsrEN5ewyfbpr+PoGPR6EVblO0azuTDutZ+BlfLTdrKup1NDUHH+dhqO6f6EkrRuwfs/l4mifk3G6Od2AJQixnxaViViN+QEwmkn3L/BJGaWXvYfpINFtKlsIzX0S/9WA8jD2I31bbxM7aTOdudCP9RN8aTdtNXIjwM1qVztPRAaBBln/P/bXUUgVT3BDemnWb2Elg1dSylsLQJ34XzSjuGwqW4lZfDFpZmGK6LKEfPbcaGXLxhDqkmFL2lQPj4YNAsabTUd7WpFQs/huPdUftJKgTK+3ZBOddYkEpLoQaHHiNwRdX7f+cOy8yEfc1c5EtJeSkWQQvR4+DFysaPaXGxYGLEdhg5Fgk3N4m9mtjfCvFOOREKz36lwC4b0R/xo9ZDQlvqNxV/ivdiEde4FzXvlOSUBqCqkbIS+wYRGRxPsMSg35wV14aJ+CR3aV6lVBkKSj0EtxhyFKp5j6dZs4tYWFSfwf7SoH9irFKEBqiIV/5bS5o8Ft2ObRq8gK+3JVOfqssG5YHsQ4BgFfaWyNAOjwf4cks6EaaAt1VtKE8+YZtVfEEsjSDnhJ0OZs3CXTYUlf0J/vtUM3qq8liCppl1FSuvzOPX+SXEAeYnm/HDgrGWk88HfCNcnXNhwBGWo2unvynwSEfnl6+a+9LBMSwIoXZEoBnqFn4+o5ObWazMEeVfpGW19TovAkIoYSv9rH/n2QdLQ6FrGTYh8ivrolFOmQoXVq/CkCaxlqvaegNRw9eF8tc8YQsW1FC0KzJIFMJA5Guut9fCWX8kL/nYbGsagX5HZbKxnAbxnVZd2MfiCA1yBVyoQQT9I7YwmyTQeTP0t3ts4l3pRHsawv5uiFvSsvoGwdMU5tYmOzYdKi/Yu6CwGpYyp+DHUAtXapG4gWFvMgbH0nWtmcmTVVepFwcjbLpPH2c9k3h6uRc+p6i2w7jRP5UOTOKw4GAtQM5bym40JOAyAg6DeQl0TK1j8LSoAG7FqfNrZzCig3E6AlUKaf8ZPdox009GeQUBVEnQk8+4WnA6YbCK8A38TIPCEWTVqKdeOj8fgK60c0orl7OKIdI2GUyhRKLrD1b5i+oOEwT/kmY2w282/bYKDPkTbVL09f/83h9V69vez7klwnqtCzbZQ5jM0kCr0G/6+uAakMwUS32zwtH3vFeT8R6jcO5rs/gwDgOPWD/jY3bOk59TXrrFjTp8GQ52nBStgJP1XrpvZ40G++n1QWQZCbqStYGSH0EmtTHKtslu+7tdayQRr6URAo+SxQdxSD80uxDoEG+0glWZn0PGd40a73pHEO+DNw0PDXPzYb8e7eAQtsQITYPECV5juGgMxjbL6sp9Fzu6eV9RxptfTJ9MHgwBk/xJGeQqMTmGeWbV3mYRVusmbroiFJ8+hnhoUVhIfIXrdTetm/FDube0rB+WP1FA7QyE4YSZisY120TEri2HQS0QadUcKB3KF7583FxOBquZkor/l7Nh1AW3EiroBmR8GPreS8JOE7GBTondNm25EarcrpJc4fFlr8TGaMzTZwgxGrN+mOXttrJHncsDVM2ljftucJ8gnTj62zJKCnsMMZnfiILPfNH9Iwda/+X+Gj4O/mfwmOruVv2P99uETaYTs876ajAJUyWVivQfDb4wadxoZ1DUg/LVC0PGNHcgzcaW57sPjlm7PkgnM0m0jyXyDH5sELuFNXjR+4sG44ykWq+A1N3crXkRVIRqEWlWKOxpnudEyNPfFdvwEQnn5M5J/msjZUea7YP3IFQ/MYHqpV68k4MH3Dt9tmT3gMoUFwkmR26MsIEVf/MqwcVdGdqpVZ1OVMPfKooroSBQMXQb28eE3PQilZLbehZ3xqbBNIPMbCSjOqMvGi4WZsw4n444qPwqR1SCgWU7KdMqCFWOpHw7yRiRcp1K1nbgkuBfhh4DGzMGca+ULlw+YZcrxgpV/8Q+7j5S/i3UwBvqt4Q7iCjiHyxHf3ye08cYHtv9JpNb+3lioMe/DhF1AQSfQjHfw8xc+5EvapAWvLuehLdd+HbyJHiJunnOd/rJFpgWjrBwZhrKt0eJJMhQmxJ3To8jTP972A6jsFi/YeY7X6TM4y7y7q4ZqvtNLxSwMbmSc7ik2HyjU9m+XOwrF1sVSAj6RjgCAmG2DmfTuQT73jEy0LftpiNIOAHqmhJ+UvDVUNUGBtjy1k7AMzOXdvA7Ol4yjc1e419Ln6hjmboxf0wybzJL+cfM9X4Cs9y1w2/cP1cgekapBNrPWgMfQLgVrSYcLtEJuA5Lmdk/vDfVRcfrTFMoT5Myr0zpjT7msbMcM7ql5WOb2i5UC5VtsZANZAsX/Nqwo7ipoeKjxxxtXGs699S2bViRxaa56czWOxPXXHbYx5HwMJ5iH3WrGk0U92W0mT8r3hRaqVzuM/V8dHrzbiNsuyqe++jMxlFkl9tloqAW7XT+stcWm6s7sjE7yqx+p+h+x8mgO5hO/QNowIcujLUDdEOm1TXN9BlEz0ZcO/SR1aX9Rx2TigJsnnCGr0AAXkMPpIp3sxj2gWnWww9fF8Fey2ldRu2GIKmVsdlEEdOZJdS+qqavv8lEj+Kz8+wQL70drAFzo2V5HlUjR/zIXZbNSMJImUb5iq7qnaMJIk3PaJE8DDwPAm5/zzCwUzT4DXquzZ5NuBAB/htoWL9CgQQHGosFM+WcCIa9MRHp0/CP6vfjLkCAuKtA6TqfuB782S5v3jC1yeq05pZcLMp+zIo/XK+B5vjyaVZfV4oUR0ptNZQ8ZdocWQk3V+vckSkGJh9hwTzMvxKSSTd73k5tgWAV1Xc3fdta1a4urOKnj994gunaifWEZ7qm1zne81cESni4aG6d43iVXB+1fLUCXTSJzX7jpjhhC0GimOkafvovo2T69JLKAFOXxCedfQ9iw7Zzzgh+771sNoEC3VIyYm07OB6UBL+kAAm2uaBv5TY+zJBXTx3paLytfAglTlH9RqevRej4IDU5xGz3VpjYPlLdYnrZdEhZ179y7kiJTX0JUim01elkVFqQkG+MEgKurp3LsacurG8GOgA5Y1BRy8dAXukqympG25oEdbTlDg4rO+Kfql4gRMmygg5K76J9yGJLr6xtRe7SHoO8jP4oYzrzUfKimoGa9roS/BaleWNTMGgEoXeKCkt7VOltUO3j3JU+LceWem4/tbsE7KZ58RS9FeXst5FGCbE/uqn/kd4xLugUAKGSVZrP2xUNsJYzai6NITxjNb63u8Qki/x1EsjLbnzsFRkGWvS2Pgj4KYm4Do01SaiMXI9RT0lbuwLtDCmaLUxvGeHCk1/LSC3xo5usR2K+ejyKV9E+Xr8aiuUKWhtiJDeTMKpOGxxo63yh6yofHjfEKdWLU76FYHjtB9qNSfh6QwccHc8TfVMnNiwtUDyPp32nI3WNx99bAuiobu+a3z6QgddlTmMrFrs10b1ll1v625DJFNqOqXQfzsiO6BbItY88R5aPNyTgAv8dahfT+BLzUiYV45OV7GdYr2NDX0DFJBaB+QPoqEVxcKJW9z5kW1Kbg6AhamtNpM0+fZNCsq0etzjCaVBdNYv0gcScZ0rt7Vh8D6BodiXgbheOXH3IUr2gyErek+qsJmurlA3FXsdmdYjloJr1yQwhpWHLcBQoYATiR/UpE96wzSE+U24z6hbdORmBOA0MeqUTAH/wQz546/hvCQqrXfJAFVGM36FY3Snq750NrMqHA54LM9mfu+zURVkZ+zHEaU3Vln1LI4hnd4cKwXY1Ba+XQkgf4GnrnyvWbk09cENctU1g1w4kPX7JYTrRy2ua154QfwlX2aqaLXSf3Ks9UAddz9MBNSsvSXdrV7YzT+0wCm+Z8bnNO9Wuk94xd+/lBQ1Ma/kHpHnyuXm0yl4UU5a6f6hHXg1BVtSMzS4LPEv+D5fImNmzGrtpj5BqrDqRezDGluVeYGUr0aA32NQXmGHXkWJBvHspT/GBxtoAiUsHCbOn/54SJnBSqLWshPuOYVl9LDmmaAE2HeU8DwD5/MRjLZpRC4MBQjvHaoTNnUKzjXt7XnREZx9pYAqNaT7erCOrkxEtGD66KQXB8F9SCu5AlUWG3oYl27gq0vY2/S2f8/Uh1a2cDb4+0QC1KxO0X23YZtkD7YQ9dfQ46ninvGHs0yK5smhxigewfy65ymw8eEoTAAsITHrRkuwRDfnb0RxRfxKWRd20+s5v8NF+0RJ8R1endmVp1xVq1YoQH6AfNY9Gv2ojXe4GZwzEmo9fVdg+M5qo5jL5+MovcbLcNoMc8pIanmA7lSCwHJZ/+ltaZlKd+E+WIs3HcpZO95XSVy2vG+iNpny5lGxXruyCdT3MGHX1nMXzrchbFOi03xpHUnmyA3LFoGbq11xw8PfeLaSTPPfQMsEZqgr5tdfsFO5sEEjvaFxZPDsDYcr7Jp6gCUf3oGQgnKktPnzEhfgQnEloE0JLFihJdRvjINnZ1/LRViwmzi4zKuf81eRjao2dRg3sNgqUw9+Gtb/4kkHuZfvsg2KgbGFJ9iyHmv9SxMrELU7vCJ3H8ZzpGN3gp/XcbjN/urx8JkafvdaEYnGr5PsmgDG3TmktK43ndYh+LjA8rywxOBScHQkJGpkGZVutE0gQhN8T3lKTAPz/l2LtytNNvkgldkHSKPb5YhoztdMTm1GjBHkvfIPrPP3Qav2bTQ1B03S+Zkyvos0wZnC08ruApe9ia6NbE0fLW63qkm48xapSbYkCDt8CZzeSEYNoq2jMNPfF8dqs19zsseUQf5o0L1mPJLqLw7QxnHIkSe8zyHmd2z29u8Bxs3jmJXkG42iQvP/bCLFoNfjAKodbeDtKs0GfJ+kt6ZQnQ/52As60zr7Nd1gYKfn5x/bdf+11NflLQ3qSFUNOjPgv5hVHaT0F2ATZIz2F3LVdU4JjtQt2jpeHutmijFST0hretQJSo38Dz9TdHQ2BL2Yh9yqPOFnTBRU6fKji14drqrQZYz4ZNu5KVLexGP23bwzQw6H9xMMrqeTkwgeIXfKLVXEir0Cc4SVdhnxFxnF0hEoJcLeRxeGNma5nopfdQEleslInVtFYPSWWzZb1fd49LjFeG0zGU7maBvGw/IZ5/7f8rU5NAsvqNB3h5d95s25RXUwnPMhIJQ/RLv/MjWjnX0DFI6Q1Pf6PeXUnuXAM+OzKNzlSJR8ijJCKur+WdPGzmn5vLkNiX78qMTdIugePlEtioGnyiDRRmYtIG09paUog+LdZv1jqrYa/I5+c6gy4tFeIPP6UuF9w2BPpQR4CWRS0T17IcicgLkgp3keNb+pNhyAAbxDBoT44pmpY9wukWvPbXN77IvEeL8vWN0AW2X1YSDKGIpcCZ5kTaWzGFehb71gS7r1joo6jV7F9jiKva+LEV19ABdmv7G5ruriHQZAsadXnEopF1w8xceaIH+OIui+eJ76e+5nl6rOhZ4Sn695Vipl6kWZbirYbE+UWEdAHBnCN4qaMNRbpGJ7hCkHZQkW+JRnR0JrfwNhG+WKKgMYCXQdJDi4aW++GXxkR3nR7SFcyfPRKFaFqrwKWx8mSm7AgY21JCxyOVjk1tO6qMNEONeFXok58sNn" autocomplete="off" />
</form>
			</div>
			<div id="showSessionExpiredDialog" class="modalExpire modalSmall modalContainer" role="dialog" aria-labelledby="showSessionExpiredDialogTitle" aria-hidden="true">
				<h1 id="showSessionExpiredDialogTitle" class="dialogTitle">セッションが終了しました</h1>
				<div id="showSessionExpiredDialogMessage" class="dialogMessage" tabindex="-1">一定時間操作されなかったため、お手続きを進めることができません。お手数ですが、再度トップページからお手続きください。</div>
				<p class="modalButton">
					<input type="submit" value="閉じる" class="btnBase btnModal btnMainStream" onclick="Asw.SessionKeeper.closeTimeoutDialog(); return false;" />
				</p>
			</div>
		</div>
		<!--===== dialogMessages =====--><div id="cmnModalMessages"></div>
		<div id="prebookConfirmDialog" class="modalContainer modalConfirm modalSmall " aria-hidden="true" role="dialog">
<form id="prebookConfirmDialog:j_idt403" name="prebookConfirmDialog:j_idt403" method="post" action="https://aswbe-i.ana.co.jp/rei21c/international_asw/pages/award/reservation/award_mandatory_passenger_information_input.xhtml?aswcid=1&amp;rand=20250620131734w0eLjhzFUs" autocomplete="off" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="prebookConfirmDialog:j_idt403" value="prebookConfirmDialog:j_idt403" />
<input type="hidden" name="prebookConfirmDialog:j_idt403_operationTicket" value="dljdmx+b9015f85aa932eca40815ed63b22e431~VIgk5BZZKdu2Sq0e1QNWDeKa82YzdngpCbu131yb!1750393042549.aere-xml-controller-67d4778877-5bbk7" /><input type="hidden" name="prebookConfirmDialog:j_idt403_cmnPageTicket" value="4" /><div class="dialogMessage" tabindex="0"><p>この後、名・姓の修正や変更ができません。</p><p>「搭乗者選択」欄の氏名が「名」「姓」の順番で表示されていることを再確認ください。</p><p>例）<strong class="highlight">空野 太郎</strong> 様 → MR <strong class="highlight">TARO SORANO</strong></p><p>保安上の理由により、航空券とパスポートのお名前が同じ表記でない場合はご搭乗いただくことができませんので必ず再度ご確認ください。</p></div>
					
					<ul class="modalBtnHorizontal">
							
							<li><input type="submit" name="prebookConfirmDialog:j_idt419" value="戻る" aria-controls="prebookConfirmDialog" class="btnBase btnModal btnCancel" onclick="Asw.Dialog.getInstance('prebookConfirmDialog').close(event, Asw.DialogSequencer.isActiveSequencerNotDisplayedLastDialog());  return false;" />
							</li>
						
						<li class="btnArrowNext"><input type="submit" name="prebookConfirmDialog:j_idt422" value="OK" aria-controls="prebookConfirmDialog" class="btnBase btnModal btnMainStream " onclick="Asw.Dialog.getInstance('prebookConfirmDialog').callOpener('NORMAL', event); return false;" />
						</li>
					</ul><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:1" value="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" autocomplete="off" />
</form>
		</div>
		<!--===== /dialogMessages =====--><div id="cmnContainer">
		<!--=========== .cmnContainer ============--><div id="cmnEmergencyNotice"></div><div id="cmnTitle" class="hgroup ">
		<h1 class="visuallyHidden">お客様情報入力</h1></div>

		<div id="cmnWrapper">
		<!--===== cmnWrapper =====-->
		<ol class="mainFlow" aria-label="完了までの手順、">
			<li><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_flow_01.png?717d3c0" alt="1" height="20" width="28" />フライト検索</li><!--
			--><li><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_flow_02_on.png?717d3c0" alt="2" height="20" width="28" /><span class="visuallyHidden">現在のステップ、</span>お客様情報入力</li><!--
			--><li><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_flow_03.png?717d3c0" alt="3" height="20" width="28" />お支払い情報</li><!--
			--><li class="flowLast"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_flow4_04.png?717d3c0" alt="4" height="20" width="28" />完了</li>
		</ol>

	<div id="main">
	<!--=========== main =============-->
		<ul class="iconExample">
				<li class="requiredIconExample">
					<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="" height="7" width="7" /></span>必須項目です	
				</li>
		</ul>
<form id="j_idt526" name="j_idt526" method="post" action="https://aswbe-i.ana.co.jp/rei21c/international_asw/pages/award/reservation/award_mandatory_passenger_information_input.xhtml?aswcid=1&amp;rand=20250620131734w0eLjhzFUs" autocomplete="off" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="j_idt526" value="j_idt526" />
<input type="hidden" name="j_idt526_operationTicket" value="dljdmx+b9015f85aa932eca40815ed63b22e431~VIgk5BZZKdu2Sq0e1QNWDeKa82YzdngpCbu131yb!1750393042549.aere-xml-controller-67d4778877-5bbk7" /><input type="hidden" name="j_idt526_cmnPageTicket" value="4" />

<div class="section">
	<h2>搭乗者選択<a href="https://www.ana.co.jp/other/int/meta/0086.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="特典のご利用に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a></h2>
	<table class="passengerInfo">
		<thead>
			<tr>
				
				<th class="name">氏名</th>
				
				<th class="dateOfBirthWide" id="dateOfBirthHeader">生年月日</th>
				
				<th class="gender lastCol" id="genderHeader">性別<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span></th>
				
			</tr>
		</thead>
		<tbody>
				
				<tr>
					<th class="parent_01" id="parentHeader01">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:0:passenger" type="checkbox" name="passengers:0:passenger" checked="checked" data-number="1" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:0:passenger">NAOKI SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader01">1963年06月05日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader01">				
							<span class="formWrapper">
							<label for="passengers:0:gender" class="visuallyHidden">NAOKI SATOの性別</label><select id="passengers:0:gender" name="passengers:0:gender" size="1">	<option value="">選択してください</option>
	<option value="M" selected="selected">男性</option>
	<option value="F">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_02" id="parentHeader02">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:1:passenger" type="checkbox" name="passengers:1:passenger" data-number="2" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:1:passenger">REO SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader02">1995年05月18日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader02">				
							<span class="formWrapper">
							<label for="passengers:1:gender" class="visuallyHidden">REO SATOの性別</label><select id="passengers:1:gender" name="passengers:1:gender" size="1">	<option value="">選択してください</option>
	<option value="M" selected="selected">男性</option>
	<option value="F">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_03" id="parentHeader03">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:2:passenger" type="checkbox" name="passengers:2:passenger" data-number="3" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:2:passenger">ERI SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader03">1963年08月14日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader03">				
							<span class="formWrapper">
							<label for="passengers:2:gender" class="visuallyHidden">ERI SATOの性別</label><select id="passengers:2:gender" name="passengers:2:gender" size="1">	<option value="">選択してください</option>
	<option value="M">男性</option>
	<option value="F" selected="selected">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_04" id="parentHeader04">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:3:passenger" type="checkbox" name="passengers:3:passenger" data-number="4" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:3:passenger">TAISHI SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader04">1992年12月03日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader04">				
							<span class="formWrapper">
							<label for="passengers:3:gender" class="visuallyHidden">TAISHI SATOの性別</label><select id="passengers:3:gender" name="passengers:3:gender" size="1">	<option value="">選択してください</option>
	<option value="M" selected="selected">男性</option>
	<option value="F">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_05" id="parentHeader05">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:4:passenger" type="checkbox" name="passengers:4:passenger" data-number="5" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:4:passenger">HIROMI FUJIMOTO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader05">1961年11月23日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader05">				
							<span class="formWrapper">
							<label for="passengers:4:gender" class="visuallyHidden">HIROMI FUJIMOTOの性別</label><select id="passengers:4:gender" name="passengers:4:gender" size="1">	<option value="">選択してください</option>
	<option value="M">男性</option>
	<option value="F" selected="selected">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
		</tbody>
	</table>
</div>
		<div class="section">
			<h2 class="midstream">連絡先情報
			</h2>
			<h3>代表者
			</h3>
	
	
	
	<div class="formInput">
		<fieldset>
			<legend><label>代表者</label></legend>
			<dl id="contactEmailAddressInfo">
				<dt class="hasRequiredIcon">メールアドレス<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span><a href="https://www.ana.co.jp/other/int/meta/0057.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="メールの宛先と内容に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a>
				</dt>
				<dd>
					<ul class="verticalList">
						<li class="nonMarginTop"><label for="representativeEmail" class="visuallyHidden">代表者メールアドレス</label><input id="representativeEmail" type="text" name="representativeEmail" value="<EMAIL>" class="widthLarge" placeholder="例：<EMAIL>" />
						</li>
						<li>
							<span class="supplementTxt" id="confirmMailAddressForRepresentative">確認のため再度ご入力ください。
							</span><label for="confirmrepresentativeEmail" class="visuallyHidden">確認用メールアドレス</label><input id="confirmrepresentativeEmail" type="text" name="confirmrepresentativeEmail" value="<EMAIL>" class="widthLarge jsNotPasteInput" placeholder="例：<EMAIL>" aria-describedby="confirmMailAddressForRepresentative" />
						</li>
					</ul>
				</dd>
					<dd style="display:none;"><input id="updateAlwaysInfo" type="checkbox" name="updateAlwaysInfo" /><label for="updateAlwaysInfo">ログイン中の会員情報へこのメールアドレスを登録</label>
					</dd>
			</dl>
			<dl>
				<dt>電話番号<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span>
				</dt>
				<dd class="js-countryWrap">
					<ul>
						<li><label for="representativeTelType" class="visuallyHidden">電話番号種別</label><select id="representativeTelType" name="representativeTelType" class="basicWidth js-typeSelect" size="1" onchange="changeTelType(this,'representativeTel')">	<option value="M1" selected="selected">携帯</option>
	<option value="B1">会社</option>
	<option value="H1">自宅</option>
</select>
						</li>
						<li><label for="representativeTelCountry" class="visuallyHidden">電話番号の国/地区選択</label><select id="representativeTelCountry" name="representativeTelCountry" class="js-countrySelect" size="1">	<option value="" data-sms-possible="true" data-country-code="---">選択してください</option>
	<option value="JP" selected="selected" data-sms-possible="true">日本</option>
	<option value="-" data-sms-possible="true" data-country-code="---">-----------</option>
	<option value="IS" data-sms-possible="true">アイスランド</option>
	<option value="IE" data-sms-possible="true">アイルランド</option>
	<option value="AZ" data-sms-possible="true">アゼルバイジャン</option>
	<option value="AF" data-sms-possible="true">アフガニスタン</option>
	<option value="US" data-sms-possible="true">アメリカ合衆国/カナダ</option>
	<option value="AE" data-sms-possible="true">アラブ首長国連邦</option>
	<option value="DZ" data-sms-possible="true">アルジェリア</option>
	<option value="AR" data-sms-possible="true">アルゼンチン</option>
	<option value="AW" data-sms-possible="true">アルバ</option>
	<option value="AL" data-sms-possible="true">アルバニア</option>
	<option value="AM" data-sms-possible="true">アルメニア</option>
	<option value="AO" data-sms-possible="true">アンゴラ</option>
	<option value="AG" data-sms-possible="true">アンティグア・バーブーダ</option>
	<option value="AD" data-sms-possible="true">アンドラ</option>
	<option value="YE" data-sms-possible="true">イエメン</option>
	<option value="GB" data-sms-possible="true">イギリス</option>
	<option value="IL" data-sms-possible="true">イスラエル</option>
	<option value="IT" data-sms-possible="true">イタリア</option>
	<option value="IQ" data-sms-possible="true">イラク</option>
	<option value="IR" data-sms-possible="true">イラン</option>
	<option value="IN" data-sms-possible="true">インド</option>
	<option value="ID" data-sms-possible="true">インドネシア</option>
	<option value="UG" data-sms-possible="true">ウガンダ</option>
	<option value="UA" data-sms-possible="true">ウクライナ</option>
	<option value="UZ" data-sms-possible="true">ウズベキスタン</option>
	<option value="UY" data-sms-possible="true">ウルグアイ</option>
	<option value="EC" data-sms-possible="true">エクアドル</option>
	<option value="EG" data-sms-possible="true">エジプト</option>
	<option value="EE" data-sms-possible="true">エストニア</option>
	<option value="SZ" data-sms-possible="true">エスワティニ</option>
	<option value="ET" data-sms-possible="true">エチオピア</option>
	<option value="ER" data-sms-possible="true">エリトリア</option>
	<option value="SV" data-sms-possible="true">エルサルバドル</option>
	<option value="AU" data-sms-possible="true">オーストラリア</option>
	<option value="AT" data-sms-possible="true">オーストリア</option>
	<option value="OM" data-sms-possible="true">オマーン</option>
	<option value="NL" data-sms-possible="true">オランダ</option>
	<option value="GH" data-sms-possible="true">ガーナ</option>
	<option value="CV" data-sms-possible="true">カーボベルデ</option>
	<option value="GY" data-sms-possible="true">ガイアナ</option>
	<option value="KZ" data-sms-possible="true">カザフスタン</option>
	<option value="QA" data-sms-possible="true">カタール</option>
	<option value="CA" data-sms-possible="true">カナダ/アメリカ合衆国</option>
	<option value="GA" data-sms-possible="true">ガボン</option>
	<option value="CM" data-sms-possible="true">カメルーン</option>
	<option value="KR" data-sms-possible="true">韓国</option>
	<option value="GM" data-sms-possible="true">ガンビア</option>
	<option value="KH" data-sms-possible="true">カンボジア</option>
	<option value="KP" data-sms-possible="true">北朝鮮</option>
	<option value="MK" data-sms-possible="true">北マケドニア</option>
	<option value="GN" data-sms-possible="true">ギニア</option>
	<option value="GW" data-sms-possible="true">ギニアビサウ</option>
	<option value="CY" data-sms-possible="true">キプロス</option>
	<option value="CU" data-sms-possible="true">キューバ</option>
	<option value="GR" data-sms-possible="true">ギリシャ</option>
	<option value="KI" data-sms-possible="true">キリバス</option>
	<option value="KG" data-sms-possible="true">キルギス</option>
	<option value="GT" data-sms-possible="true">グアテマラ</option>
	<option value="GU" data-sms-possible="true">グアム</option>
	<option value="KW" data-sms-possible="true">クウェート</option>
	<option value="GD" data-sms-possible="true">グレナダ</option>
	<option value="HR" data-sms-possible="true">クロアチア</option>
	<option value="KY" data-sms-possible="true">ケイマン諸島</option>
	<option value="KE" data-sms-possible="true">ケニア</option>
	<option value="CI" data-sms-possible="true">コートジボワール</option>
	<option value="CR" data-sms-possible="true">コスタリカ</option>
	<option value="KM" data-sms-possible="true">コモロ</option>
	<option value="CO" data-sms-possible="true">コロンビア</option>
	<option value="CG" data-sms-possible="true">コンゴ共和国</option>
	<option value="CD" data-sms-possible="true">コンゴ民主共和国</option>
	<option value="SA" data-sms-possible="true">サウジアラビア</option>
	<option value="ZM" data-sms-possible="true">ザンビア</option>
	<option value="SM" data-sms-possible="true">サンマリノ</option>
	<option value="SL" data-sms-possible="true">シエラレオネ</option>
	<option value="DJ" data-sms-possible="true">ジブチ</option>
	<option value="JM" data-sms-possible="true">ジャマイカ</option>
	<option value="GE" data-sms-possible="true">ジョージア</option>
	<option value="SY" data-sms-possible="true">シリア</option>
	<option value="SG" data-sms-possible="true">シンガポール</option>
	<option value="ZW" data-sms-possible="true">ジンバブエ</option>
	<option value="CH" data-sms-possible="true">スイス</option>
	<option value="SE" data-sms-possible="true">スウェーデン</option>
	<option value="SD" data-sms-possible="true">スーダン</option>
	<option value="ES" data-sms-possible="true">スペイン</option>
	<option value="SR" data-sms-possible="true">スリナム</option>
	<option value="LK" data-sms-possible="true">スリランカ</option>
	<option value="SK" data-sms-possible="true">スロバキア</option>
	<option value="SI" data-sms-possible="true">スロベニア</option>
	<option value="SC" data-sms-possible="true">セイシェル</option>
	<option value="GQ" data-sms-possible="true">赤道ギニア</option>
	<option value="SN" data-sms-possible="true">セネガル</option>
	<option value="RS" data-sms-possible="true">セルビア</option>
	<option value="KN" data-sms-possible="true">セントクリストファー・ネイビス</option>
	<option value="LC" data-sms-possible="true">セントルシア</option>
	<option value="SB" data-sms-possible="true">ソロモン諸島</option>
	<option value="TH" data-sms-possible="true">タイ</option>
	<option value="TW" data-sms-possible="true">台湾</option>
	<option value="TJ" data-sms-possible="true">タジキスタン</option>
	<option value="TZ" data-sms-possible="true">タンザニア</option>
	<option value="CZ" data-sms-possible="true">チェコ</option>
	<option value="TD" data-sms-possible="true">チャド</option>
	<option value="CF" data-sms-possible="true">中央アフリカ共和国</option>
	<option value="CN" data-sms-possible="true">中国</option>
	<option value="TN" data-sms-possible="true">チュニジア</option>
	<option value="CL" data-sms-possible="true">チリ</option>
	<option value="TV" data-sms-possible="true">ツバル</option>
	<option value="DK" data-sms-possible="true">デンマーク</option>
	<option value="DE" data-sms-possible="true">ドイツ</option>
	<option value="TG" data-sms-possible="true">トーゴ</option>
	<option value="DO" data-sms-possible="true">ドミニカ共和国</option>
	<option value="DM" data-sms-possible="true">ドミニカ国</option>
	<option value="TT" data-sms-possible="true">トリニダード・トバゴ</option>
	<option value="TM" data-sms-possible="true">トルクメニスタン</option>
	<option value="TR" data-sms-possible="true">トルコ</option>
	<option value="TO" data-sms-possible="true">トンガ</option>
	<option value="NG" data-sms-possible="true">ナイジェリア</option>
	<option value="NR" data-sms-possible="true">ナウル</option>
	<option value="NA" data-sms-possible="true">ナミビア</option>
	<option value="NI" data-sms-possible="true">ニカラグア</option>
	<option value="NE" data-sms-possible="true">ニジェール</option>
	<option value="NZ" data-sms-possible="true">ニュージーランド</option>
	<option value="NP" data-sms-possible="true">ネパール</option>
	<option value="NO" data-sms-possible="true">ノルウェー</option>
	<option value="BH" data-sms-possible="true">バーレーン</option>
	<option value="HT" data-sms-possible="true">ハイチ</option>
	<option value="PK" data-sms-possible="true">パキスタン</option>
	<option value="VA" data-sms-possible="true">バチカン</option>
	<option value="PA" data-sms-possible="true">パナマ</option>
	<option value="VU" data-sms-possible="true">バヌアツ</option>
	<option value="BS" data-sms-possible="true">バハマ</option>
	<option value="PG" data-sms-possible="true">パプアニューギニア</option>
	<option value="BM" data-sms-possible="true">バミューダ</option>
	<option value="PW" data-sms-possible="true">パラオ</option>
	<option value="PY" data-sms-possible="true">パラグアイ</option>
	<option value="BB" data-sms-possible="true">バルバドス</option>
	<option value="PS" data-sms-possible="true">パレスチナ</option>
	<option value="HU" data-sms-possible="true">ハンガリー</option>
	<option value="BD" data-sms-possible="true">バングラデシュ</option>
	<option value="TL" data-sms-possible="true">東ティモール</option>
	<option value="FJ" data-sms-possible="true">フィジー</option>
	<option value="PH" data-sms-possible="true">フィリピン</option>
	<option value="FI" data-sms-possible="true">フィンランド</option>
	<option value="BT" data-sms-possible="true">ブータン</option>
	<option value="PR" data-sms-possible="true">プエルトリコ</option>
	<option value="BR" data-sms-possible="true">ブラジル</option>
	<option value="FR" data-sms-possible="true">フランス</option>
	<option value="PF" data-sms-possible="true">フランス領ポリネシア</option>
	<option value="BG" data-sms-possible="true">ブルガリア</option>
	<option value="BF" data-sms-possible="true">ブルキナファソ</option>
	<option value="BN" data-sms-possible="true">ブルネイ</option>
	<option value="BI" data-sms-possible="true">ブルンジ</option>
	<option value="VN" data-sms-possible="true">ベトナム</option>
	<option value="BJ" data-sms-possible="true">ベナン</option>
	<option value="VE" data-sms-possible="true">ベネズエラ</option>
	<option value="BY" data-sms-possible="true">ベラルーシ</option>
	<option value="BZ" data-sms-possible="true">ベリーズ</option>
	<option value="PE" data-sms-possible="true">ペルー</option>
	<option value="BE" data-sms-possible="true">ベルギー</option>
	<option value="PL" data-sms-possible="true">ポーランド</option>
	<option value="BA" data-sms-possible="true">ボスニア・ヘルツェゴビナ</option>
	<option value="BW" data-sms-possible="true">ボツワナ</option>
	<option value="BO" data-sms-possible="true">ボリビア</option>
	<option value="PT" data-sms-possible="true">ポルトガル</option>
	<option value="HK" data-sms-possible="true">香港</option>
	<option value="HN" data-sms-possible="true">ホンジュラス</option>
	<option value="MH" data-sms-possible="true">マーシャル諸島</option>
	<option value="MO" data-sms-possible="true">マカオ</option>
	<option value="MG" data-sms-possible="true">マダガスカル</option>
	<option value="MW" data-sms-possible="true">マラウイ</option>
	<option value="ML" data-sms-possible="true">マリ</option>
	<option value="MT" data-sms-possible="true">マルタ</option>
	<option value="MY" data-sms-possible="true">マレーシア</option>
	<option value="FM" data-sms-possible="true">ミクロネシア連邦</option>
	<option value="ZA" data-sms-possible="true">南アフリカ</option>
	<option value="MM" data-sms-possible="true">ミャンマー</option>
	<option value="MX" data-sms-possible="true">メキシコ</option>
	<option value="MU" data-sms-possible="true">モーリシャス</option>
	<option value="MR" data-sms-possible="true">モーリタニア</option>
	<option value="MZ" data-sms-possible="true">モザンビーク</option>
	<option value="MC" data-sms-possible="true">モナコ</option>
	<option value="MV" data-sms-possible="true">モルディヴ</option>
	<option value="MD" data-sms-possible="true">モルドバ</option>
	<option value="MA" data-sms-possible="true">モロッコ</option>
	<option value="MN" data-sms-possible="true">モンゴル</option>
	<option value="ME" data-sms-possible="true">モンテネグロ</option>
	<option value="JO" data-sms-possible="true">ヨルダン</option>
	<option value="LA" data-sms-possible="true">ラオス人民民主共和国</option>
	<option value="LV" data-sms-possible="true">ラトビア</option>
	<option value="LT" data-sms-possible="true">リトアニア</option>
	<option value="LY" data-sms-possible="true">リビア</option>
	<option value="LI" data-sms-possible="true">リヒテンシュタイン</option>
	<option value="LR" data-sms-possible="true">リベリア</option>
	<option value="RO" data-sms-possible="true">ルーマニア</option>
	<option value="LU" data-sms-possible="true">ルクセンブルク</option>
	<option value="RW" data-sms-possible="true">ルワンダ</option>
	<option value="LS" data-sms-possible="true">レソト</option>
	<option value="LB" data-sms-possible="true">レバノン</option>
	<option value="RU" data-sms-possible="true">ロシア</option>
</select>
						</li>
						<li class="numberInputWrap">
							<span id="numberInputInfo1" class="supplementTxt marginTop">先頭に0(ゼロ)がある場合、0(ゼロ)を抜いてください(一部、国・地域除く)。携帯電話番号の場合は、携帯電話を契約された国を選択ください。</span>
							<div class="numberRowWrap">
								<div class="block">
									
									<span class="visuallyHidden">国番号</span>
									<span class="viewInput widthMini js-countryCode"></span>
								</div>
								<div class="block"><label for="representativeTel" class="visuallyHidden">電話番号</label><input id="representativeTel" type="text" name="representativeTel" value="9042504176" class="widthMiddle" placeholder="例：9012345678" maxlength="30" aria-describedby="numberInputInfo1" />
								</div>
							</div>
						</li>
					</ul>
				</dd>
			</dl>
		</fieldset>
	</div>
	<div class="titleWrapper">
		<h3>
			<span class="text">eメール・SMSに関するご案内<a href="https://www.ana.co.jp/other/int/meta/0057.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="eメール・SMSに関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a></span>
		</h3>
	</div>
	<div class="formInput">
	<h4 class="formAreaTitle">主に搭乗・運航に関わるeメールの送付先設定（米国へ通知いたします）<a href="https://www.ana.co.jp/other/int/meta/0629.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="米国入国用eメール・SMS・電話番号に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a>
	</h4>
	<div class="paxSettingArea js-mailFormWrap onePax is-open">
			<div class="settingArea">
					<dl>
						<dt class="breakWord">NAOKI SATO
						</dt>
						<dd>
							<fieldset>
								<legend>NAOKI SATOのeメール送付先および米国到着時に提供するメールアドレス</legend>代表者と同じ
							</fieldset>
						</dd>
					</dl>
			</div>
	</div>
	<h4 class="formAreaTitle hasTopLine">主に搭乗・運航に関わるSMSの送付先および米国で受信できる電話番号の設定（米国へ通知いたします）<a href="https://www.ana.co.jp/other/int/meta/0629.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="米国入国用eメール・SMS・電話番号に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a>
	</h4>
	<div class="paxSettingArea js-smsFormWrap onePax is-open">
			<div class="settingArea">
					<dl>
						<dt class="breakWord">NAOKI SATO<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span>
						</dt>
						<dd>
							<fieldset>
								<legend>NAOKI SATOの1つ目の電話番号</legend>
									<ul class="verticalList js-formItem">
										<li><input type="radio" id="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSameAsRepresentativeSms:radioItem" name="contactsSms:0:recipientSmsEachPersonSelection:radioGroup" value="0" checked="checked" /><label for="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSameAsRepresentativeSms:radioItem">代表者電話番号と同じ</label>
										</li>
										<li><input type="radio" id="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSmsPerPassenger:radioItem" name="contactsSms:0:recipientSmsEachPersonSelection:radioGroup" value="1" /><label for="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSmsPerPassenger:radioItem">電話番号を指定</label>
											<div class="indentArea js-formImputArea">
												<span id="confirmTelNumberForRepresentative:0" class="supplementTxt marginTop">先頭に0(ゼロ)がある場合、0(ゼロ)を抜いてください(一部、国・地域除く)。携帯電話番号の場合は、携帯電話を契約された国を選択ください。</span>
												<ul class="countryNumberInput js-countryWrap">
													<li><label for="contactsSms:0:recipientSmsEachPersonSelection:passengerSmsCountry" class="visuallyHidden">電話番号の国/地区選択</label><select id="contactsSms:0:recipientSmsEachPersonSelection:passengerSmsCountry" name="contactsSms:0:recipientSmsEachPersonSelection:passengerSmsCountry" class="countrySelect js-countrySelect" size="1">	<option value="" data-country-code="---">選択してください</option>
	<option value="JP">日本</option>
	<option value="-" data-country-code="---">-----------</option>
	<option value="IE">アイルランド</option>
	<option value="AZ">アゼルバイジャン</option>
	<option value="AF">アフガニスタン</option>
	<option value="US">アメリカ合衆国/カナダ</option>
	<option value="AE">アラブ首長国連邦</option>
	<option value="DZ">アルジェリア</option>
	<option value="AR">アルゼンチン</option>
	<option value="AW">アルバ</option>
	<option value="AL">アルバニア</option>
	<option value="AM">アルメニア</option>
	<option value="AG">アンティグア・バーブーダ</option>
	<option value="AD">アンドラ</option>
	<option value="YE">イエメン</option>
	<option value="GB">イギリス</option>
	<option value="IL">イスラエル</option>
	<option value="IT">イタリア</option>
	<option value="IQ">イラク</option>
	<option value="IR">イラン</option>
	<option value="IN">インド</option>
	<option value="ID">インドネシア</option>
	<option value="UG">ウガンダ</option>
	<option value="UA">ウクライナ</option>
	<option value="UZ">ウズベキスタン</option>
	<option value="UY">ウルグアイ</option>
	<option value="EC">エクアドル</option>
	<option value="EG">エジプト</option>
	<option value="EE">エストニア</option>
	<option value="ER">エリトリア</option>
	<option value="SV">エルサルバドル</option>
	<option value="AU">オーストラリア</option>
	<option value="AT">オーストリア</option>
	<option value="OM">オマーン</option>
	<option value="NL">オランダ</option>
	<option value="GH">ガーナ</option>
	<option value="GY">ガイアナ</option>
	<option value="KZ">カザフスタン</option>
	<option value="QA">カタール</option>
	<option value="CA">カナダ/アメリカ合衆国</option>
	<option value="GA">ガボン</option>
	<option value="CM">カメルーン</option>
	<option value="KR">韓国</option>
	<option value="GM">ガンビア</option>
	<option value="KH">カンボジア</option>
	<option value="GN">ギニア</option>
	<option value="GW">ギニアビサウ</option>
	<option value="CY">キプロス</option>
	<option value="CU">キューバ</option>
	<option value="GR">ギリシャ</option>
	<option value="KG">キルギス</option>
	<option value="GT">グアテマラ</option>
	<option value="GU">グアム</option>
	<option value="KW">クウェート</option>
	<option value="GD">グレナダ</option>
	<option value="HR">クロアチア</option>
	<option value="KY">ケイマン諸島</option>
	<option value="KE">ケニア</option>
	<option value="CI">コートジボワール</option>
	<option value="CR">コスタリカ</option>
	<option value="KM">コモロ</option>
	<option value="CO">コロンビア</option>
	<option value="CD">コンゴ民主共和国</option>
	<option value="SA">サウジアラビア</option>
	<option value="ZM">ザンビア</option>
	<option value="SL">シエラレオネ</option>
	<option value="JM">ジャマイカ</option>
	<option value="GE">ジョージア</option>
	<option value="SY">シリア</option>
	<option value="SG">シンガポール</option>
	<option value="ZW">ジンバブエ</option>
	<option value="CH">スイス</option>
	<option value="SE">スウェーデン</option>
	<option value="SD">スーダン</option>
	<option value="ES">スペイン</option>
	<option value="SR">スリナム</option>
	<option value="LK">スリランカ</option>
	<option value="SK">スロバキア</option>
	<option value="SI">スロベニア</option>
	<option value="SN">セネガル</option>
	<option value="RS">セルビア</option>
	<option value="KN">セントクリストファー・ネイビス</option>
	<option value="LC">セントルシア</option>
	<option value="TH">タイ</option>
	<option value="TW">台湾</option>
	<option value="TJ">タジキスタン</option>
	<option value="TZ">タンザニア</option>
	<option value="CZ">チェコ</option>
	<option value="TD">チャド</option>
	<option value="CF">中央アフリカ共和国</option>
	<option value="CN">中国</option>
	<option value="TN">チュニジア</option>
	<option value="CL">チリ</option>
	<option value="DK">デンマーク</option>
	<option value="DE">ドイツ</option>
	<option value="TG">トーゴ</option>
	<option value="DO">ドミニカ共和国</option>
	<option value="TT">トリニダード・トバゴ</option>
	<option value="TR">トルコ</option>
	<option value="NG">ナイジェリア</option>
	<option value="NI">ニカラグア</option>
	<option value="NE">ニジェール</option>
	<option value="NZ">ニュージーランド</option>
	<option value="NP">ネパール</option>
	<option value="NO">ノルウェー</option>
	<option value="HT">ハイチ</option>
	<option value="PK">パキスタン</option>
	<option value="PA">パナマ</option>
	<option value="BS">バハマ</option>
	<option value="PG">パプアニューギニア</option>
	<option value="BM">バミューダ</option>
	<option value="PW">パラオ</option>
	<option value="PY">パラグアイ</option>
	<option value="BB">バルバドス</option>
	<option value="PS">パレスチナ</option>
	<option value="HU">ハンガリー</option>
	<option value="BD">バングラデシュ</option>
	<option value="TL">東ティモール</option>
	<option value="FJ">フィジー</option>
	<option value="PH">フィリピン</option>
	<option value="FI">フィンランド</option>
	<option value="BT">ブータン</option>
	<option value="PR">プエルトリコ</option>
	<option value="BR">ブラジル</option>
	<option value="FR">フランス</option>
	<option value="PF">フランス領ポリネシア</option>
	<option value="BG">ブルガリア</option>
	<option value="BF">ブルキナファソ</option>
	<option value="BN">ブルネイ</option>
	<option value="BI">ブルンジ</option>
	<option value="VN">ベトナム</option>
	<option value="BJ">ベナン</option>
	<option value="VE">ベネズエラ</option>
	<option value="BY">ベラルーシ</option>
	<option value="PE">ペルー</option>
	<option value="BE">ベルギー</option>
	<option value="PL">ポーランド</option>
	<option value="BA">ボスニア・ヘルツェゴビナ</option>
	<option value="BW">ボツワナ</option>
	<option value="HK">香港</option>
	<option value="MH">マーシャル諸島</option>
	<option value="MO">マカオ</option>
	<option value="MG">マダガスカル</option>
	<option value="MW">マラウイ</option>
	<option value="ML">マリ</option>
	<option value="MT">マルタ</option>
	<option value="MY">マレーシア</option>
	<option value="ZA">南アフリカ</option>
	<option value="MM">ミャンマー</option>
	<option value="MX">メキシコ</option>
	<option value="MU">モーリシャス</option>
	<option value="MR">モーリタニア</option>
	<option value="MZ">モザンビーク</option>
	<option value="MC">モナコ</option>
	<option value="MV">モルディヴ</option>
	<option value="MA">モロッコ</option>
	<option value="MN">モンゴル</option>
	<option value="ME">モンテネグロ</option>
	<option value="JO">ヨルダン</option>
	<option value="LA">ラオス人民民主共和国</option>
	<option value="LV">ラトビア</option>
	<option value="LT">リトアニア</option>
	<option value="LY">リビア</option>
	<option value="LR">リベリア</option>
	<option value="RO">ルーマニア</option>
	<option value="LU">ルクセンブルク</option>
	<option value="RW">ルワンダ</option>
	<option value="LB">レバノン</option>
	<option value="RU">ロシア</option>
</select>
													</li>
													<li>
														
														<div class="numberRowWrap">
															<div class="block">
																<span class="visuallyHidden">国番号</span>
																<span class="countryCode viewInput js-countryCode"></span>
															</div>
															
															<div class="block"><label for="contactsSms:0:recipientSmsEachPersonSelection:flightStatusNotificationContactPointSmsDescription" class="visuallyHidden">電話番号</label><input id="contactsSms:0:recipientSmsEachPersonSelection:flightStatusNotificationContactPointSmsDescription" type="text" name="contactsSms:0:recipientSmsEachPersonSelection:flightStatusNotificationContactPointSmsDescription" class="telNumber" placeholder="例：9012345678" maxlength="30" aria-describedby="confirmTelNumberForRepresentative:0" />
															</div>
														</div>
													</li>
												</ul>
											</div>
										</li>
									</ul>
							</fieldset>
						</dd>
					</dl>
			</div>
	</div>
	</div>
	
	<div class="formInput articleLine">
		<h3>滞在中の連絡先(任意)
		</h3>
		<dl>
			
			<dt><label for="accommodationOne">現地連絡先1</label>
			</dt>
			<dd><input id="accommodationOne" type="text" name="accommodationOne" class="widthLarge" placeholder="例：09012345678 ABC HOTEL" maxlength="28" title="例：09012345678 ABC HOTEL" />
			</dd>
		</dl>
		<dl>
			
			<dt><label for="accommodationTwo">現地連絡先2</label>
			</dt>
			<dd><input id="accommodationTwo" type="text" name="accommodationTwo" class="widthLarge" placeholder="例：09012345678 ABC HOTEL" maxlength="28" title="例：09012345678 ABC HOTEL" />
			</dd>
		</dl>
	</div>
		</div>
		
		<p class="btnAreaSubmit btnArrowNext "><input id="next" type="submit" name="next" value="次へ" class="btnBase btnMainStream btnVerticalMain btnWidthVariable" onclick="return Asw.Dialog.getInstance('prebookConfirmDialog').toggle(event);return Asw.LoadingWindow.open(&quot;NORMAL&quot;, event)" />
		</p><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:2" value="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" autocomplete="off" />
</form><div id="cmnDynamicMessages" class="infoBoxSection">
				<dl class="infoBox importantInfo jsAccordionSwitch">
					<dt><strong>重要なご案内</strong></dt>
					<dd>
						<ul class="jsAccordionSwitchList" id="informationMessages">
								<li>個人情報の利用目的<a href="https://www.ana.co.jp/other/int/meta/0012.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="ANAプライバシーポリシー(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">ANAプライバシーポリシー<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>を必ずお読みいただき、内容に同意いただいた上で、お進みください。</li>
								<li><a href="https://www.ana.co.jp/other/int/meta/0058.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="Secure Flight Programについて(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">米国Secure Flightプログラム<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>に基づき、パスポート名、生年月日、性別などの情報は事前に米国へ通知いたします。</li>
								<li>入力したeメールアドレス、電話番号は入国に必要な情報として米国に通知される可能性があります。</li>
									<li class="jsHiddenFlg toggleContents">お名前のスペルがパスポート名と一致しているかご確認ください。異なる場合はインターネットではお取り扱いできません。<a href="https://www.ana.co.jp/other/int/meta/intbe0001.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="ANAお問い合わせ先(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">ANA<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>へ名前の変更をお申し出ください。</li>
									<li class="jsHiddenFlg toggleContents">二重国籍のお客様は、ご搭乗時に使用されるパスポートのお名前でご予約ください。<br />パスポートごとにお名前が異なる場合は、お客様ご自身で入国管理事務所・外務省・大使館などで各種条件をご確認のうえ、使用されるパスポートを決めてください。<br />なお、入国可否については国によって異なるため、お客様ご自身で各入国管理事務所などでご確認ください。</li>
									<li class="jsHiddenFlg toggleContents">旅行開始日時点で12歳以上の場合は大人、2歳以上12歳未満の場合は小児、2歳未満の場合は幼児の扱いとなります。なお、小児は大人と同マイル数が必要です。</li>
									<li class="jsHiddenFlg toggleContents">運航に関する情報をお客様へ通知するため、提携航空会社にもお客様のメールアドレスが提供されます。あらかじめご了承ください。</li>
									<li class="jsHiddenFlg toggleContents"><dl><dt>携帯電話へのメール送信をご希望のお客様へ</dt><dd>ドメイン指定受信をご利用の場合は、ana.co.jpとamadeus.comからのメールが受信できるようあらかじめ設定をお願いいたします。</dd></dl></li>
									<li class="jsHiddenFlg toggleContents">SMSはスマートフォンの番号に配信されます。一部の国ではSMSでのご案内をご利用いただけません。詳細は<a href="https://www.ana.co.jp/other/int/meta/0737.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="ショートメッセージ(SMS)でのご案内対象外の国について(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">ショートメッセージ(SMS)でのご案内対象外の国について<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>をご覧ください。</li>
									<li class="jsHiddenFlg toggleContents">ログインされている会員ご本人が搭乗しない場合は、再度フライト検索画面より「ログインされている会員ご本人は搭乗しない」のチェックボックスを選択してお手続きください。</li>
						</ul>
							<a href="#" class="toggleSwitch jsTriggerSwitch" role="button" aria-controls="informationMessages" aria-expanded="false"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/toggle_open.png?7bec38a" alt="開閉ボタン" height="25" width="25" class="jsRollOver" />
							</a>
					</dd>
				</dl></div>
	<!--=========== /main =============-->
	</div>

	<!--=========== Script ============-->
	<script type="text/javascript">
	$(window).on('load', function() {
		// アコーディオンのメソッドと設定
		Asw.AccordionInfo('.jsAccordionSwitch', {
			'triggerSwitch': '.jsAccordionSwitch .jsTriggerSwitch',
			'listClass': '.jsAccordionSwitchList',
			'visibilityHidden': 'toggleContents'
		});
	});
	</script>
	<!--=========== Script ============-->
	<script type="text/javascript">
		//プラットフォーム判別（サマリーエリア追従用として）
		var Asw = Asw || {};
	
	  	Asw.ClientInfo = Asw.ClientInfo  || {};
	  	Asw.ClientInfo.DeviceType = Asw.ClientInfo.DeviceType || {};
	
	  	Asw.ClientInfo.DeviceType.PC = 'PC';
	  	Asw.ClientInfo.DeviceType.SP = 'SP';
	  	Asw.ClientInfo.DeviceType.TC = 'TC';
	
	  	Asw.ClientInfo.deviceType = Asw.ClientInfo.DeviceType.PC;
	
	 	if((navigator.userAgent.indexOf('Android') > 0 && navigator.userAgent.indexOf('Mobile') == -1) || navigator.userAgent.indexOf('iPad') > 0){
	    	Asw.ClientInfo.deviceType = Asw.ClientInfo.DeviceType.TC;
	  	}
	</script>
	<!--=========== /Script ============--><div id="summaryArea">
			<div class="flightSummaryArea">
						<div class="flightSummarySection">
							<h2>区間 1
							</h2>
							<p class="flightDate"><em>6</em>月<em class="hasLeft">28</em>日（土）
							</p>
							<div class="flightSummaryRoute">
									<div class="flightSummaryRouteSection">
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation1_1">
													<span>東京(成田)
													</span>
												</p>
												<p class="time">
													<span>17:00
													</span>
												</p>
											</div>
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation1_1">
													<span>シカゴ(ORD)
													</span>
												</p>
												<p class="time">
													<span>14:55
													</span>
												</p>
											</div>
										<p class="flightNumberClass">
												<span>NH012</span><span><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/flight_ana_summaryarea.png?717d3c0" alt="ANA運航便" height="11" width="41" /></span>
												<br />特典ビジネス
										</p>
									</div>
							</div>
						</div>
						<div class="flightSummarySection">
							<h2 class="midstream">区間 2
							</h2>
							<p class="flightDate"><em>7</em>月<em class="hasLeft">23</em>日（水）
							</p>
							<div class="flightSummaryRoute">
									<div class="flightSummaryRouteSection">
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation2_1">
													<span>ホノルル(オアフ島)
													</span>
												</p>
												<p class="time">
													<span>11:35
													</span>
												</p>
											</div>
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation2_1">
													<span>東京(成田)
													</span>
												</p>
												<p class="time">
													<span>14:50
																	<span>翌日
																	</span>
													</span>
												</p>
											</div>
										<p class="flightNumberClass">
												<span>NH183</span><span><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/flight_ana_summaryarea.png?717d3c0" alt="ANA運航便" height="11" width="41" /></span>
												<br />特典エコノミー
										</p>
									</div>
							</div>
						</div>
					<div class="total">
						<p class="passenger">大人 x1
						</p>
						<div class="totalArea">
					  		<dl class="mealAmountWrap">
									<dt>有料機内食サービス
									</dt>
									<dd><em class="mealAmount">0</em><span class="currencyCode">円</span>
									</dd>
							</dl>
									<div>
										<div class="mileageAndTotal">
												<dl class="requiredMileage">
													<dt>必要マイル
													</dt>
													<dd><em class="requiredMileage">74,000</em><span class="currencyCode">マイル</span>
													</dd>
												</dl>
												<dl>
													<dt>総額
													</dt>
													<dd><em class="awardTotalPayment">68,130</em>円
													</dd>
												</dl>
												<p class="includeFareAndFuelCost">※各種税金、燃油特別付加運賃等を含みます。
												</p>
										</div>
									</div>
						</div>
					</div>
				
			</div></div>
		<!--===== /cmnWrapper =====-->
		</div>
			
		<span id="message" class="visuallyHidden" role="alert" aria-live="assertive"></span>
		<span id="politeMessage" class="visuallyHidden" role="log" aria-live="polite"></span><script type="text/javascript">
		var Asw = Asw || {};
		Asw.BaseOutput = {"sessionID":"w0eLjhzFUs","language":"ja","siteCatalystLanguageCode":"J","operationDate":"20250620","operationDateTime":"20250620131734","office":{"officeCode":"TYONH08ZZ","country2letterCode":"JP"},"bookingType":"A","pageID":"A04_P01","siteCatalystPageName":"INT_BE_AWARD_J_A04特典予約_P01旅客情報入力（必須情報）","device":{"deviceType":"PC"}};
	</script><div id="promotionArea">
		<!--===== promotionArea =====-->
		<!--===== /promotionArea =====--></div>
		<!--=========== /cmnContainer ============--></div><div id="cmnBaloonArea" aria-live="polite"></div><div id="cmnPopupArea" aria-live="polite"></div>

	

	<div id="cmnFooterWrapper">
		<!--=========== .cmnFooterWrapper ============-->
		<div id="cmnFooter">
			<!--===== cmnFooter =====-->
			<ul>
				<li><a href="https://www.ana.co.jp/other/int/meta/0012.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">プライバシーポリシー<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
					<li><a href="https://www.ana.co.jp/other/int/meta/0670.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">利用者情報の外部送信について<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
					</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/0052.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">サイト利用規約<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/be0238.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">推奨環境<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/0133.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">サイトマップ<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/0732.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">運送約款<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
					<li class="lastChild"><a href="https://www.ana.co.jp/other/int/meta/0556.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">ウェブアクセシビリティについて<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
					</li>
			</ul>
			<p class="blankText" aria-hidden="true"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="" height="10" width="10" />外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。</p>
			<p class="copyright"><span lang="en" xml:lang="en">Copyright&#169;ANA</span>
			</p>
			<p class="starAllianceLogo"><a href="http://www.staralliance.com/ja/" target="_blank"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/star_alliance_logo.png?0fe73b5" alt="A STAR ALLIANCE MEMBER" height="18" width="175" /><span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
			</p>
			<!--===== cmnFooter =====-->
		</div>
		<!--=========== /cmnFooterWrapper ============-->
	</div>

		
		<div id="cmnLoadingForAjax"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/bg_modalWindow.png?717d3c0" alt="" height="0" width="0" />
			<div class="loadingArea">
				<p class="loadingImage"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/loading.gif?717d3c0" alt="リクエストを処理しています...しばらくお待ちください。" height="33" width="33" />
				</p>
			</div>
		</div>
		
		
		<div id="popupAltSentence" style="display: none" aria-hidden="true">別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。</div>
	<div id="cmnSiteCatalyst" aria-hidden="true"><div id="cmnSiteCatalystParamArea"><script type="text/javascript">
				// ▼SiteCatalyst
				// SiteCatalyst code version: H.2.
				// Copyright 1997-2005 Omniture, Inc. More info available at http://www.omniture.com
				SiteCatalystReportSuites   = "INT";
				SiteCatalystCharSet        = "UTF-8";
				SiteCatalystChannel        = "BE_AWARD_J";
				SiteCatalystPageName       = "INT_BE_AWARD_J_A04\u7279\u5178\u4E88\u7D04_P01\u65C5\u5BA2\u60C5\u5831\u5165\u529B\uFF08\u5FC5\u9808\u60C5\u5831\uFF09";
				SiteCatalystERR            = "";
				SiteCatalystEventNum       = "";
				SiteCatalystPurchase       = "FALSE";
				SiteCatalystProductPrice   = "";
				SiteCatalystCurrencyCode   = "";
				SiteCatalystSeatAvailabilityINT   = "";
				SiteCatalystDepartDate     = "";
				SiteCatalystDepart         = "";
				SiteCatalystArrive         = "";
				SiteCatalystFlightNo       = "";
				SiteCatalystClass          = "";
				SiteCatalystFare           = "";
				SiteCatalystPurchaseID     = "";
				
				SiteCatalystLocater        = "";
				SiteCatalystPayment        = "";
				SiteCatalystIncome         = "";
				
				SiteCatalystPax            = "";
				SiteCatalystManipulateDay  = "";
				SiteCatalystCompartmentClass   = "";
				SiteCatalystOptionInfo     = "";
				SiteCatalystOutboundUpsellPrice   = "";
				SiteCatalystInboundUpsellPrice    = "";
				
				SiteCatalystSearchInput    = "";
				SiteCatalystSegment        = "";
				SiteCatalystOfficeCode     = "";
				SiteCatalystPassengerCount = "";
				SiteCatalystPassenger      = "";
				SiteCatalystAncillaryService = "";
				// End SiteCatalyst code version: H.2.
				// ▲SiteCatalyst
			</script></div><script type="text/javascript"><!-- if(navigator.appVersion.indexOf('MSIE')>=0)document.write(unescape('%3C')+'\!-'+'-') // --></script>
		<!--/DO NOT REMOVE/-->
		

		
		<!-- Google Tag Manager -->
		<script type="text/javascript" charset="Shift_JIS" src="https://www.ana.co.jp/common/js/GTMINTBE.js"></script>
		<!-- End Google Tag Manager -->
		<!-- Yahoo Tag Manager -->
		<script type="text/javascript" charset="Shift_JIS" src="https://www.ana.co.jp/common/js/YTMINTBE.js"></script>
		<!-- End Yahoo Tag Manager -->
		

	</div>

	

	<div class="mboxDefault"></div><script type="text/javascript">mboxCreate("ASW_common_A04_P01");</script>
			<script type="text/javascript" src="https://www.ana.co.jp/common/js/tealium/tealium.js"></script>
			<script type="text/javascript" src="//cdn.evgnet.com/beacon/allnipponairways/nonamcprd/scripts/evergage.min.js"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/javax.faces.resource/jsf.js.xhtml?ln=javax.faces"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/jquery.mousewheel.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/jquery.jscrollpane.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/jquery-ui-1.10.4.custom.min.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/jquery.ui.touch-punch.min.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-common.js?beb6a50"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-common-pc.js?fd88d7d"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-format.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/mask.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/loading.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-loadingwindow-pc.js?9e233c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-loadingwindow.js?51db600"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/dialog-pc.js?25d36a4"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-dialog-sequencer.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-event.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-date-select.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-calendar-pc.js?9b86b8a"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-calendar.js?db624c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/heightLine.js?0e7a1ce"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-summaryarea.js?b21c6d7"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-fingerprints.js?99001fc"></script><script type="text/javascript">
			Asw.init("https://aswbe-i.ana.co.jp/rei21c/international_asw", "ja");
			Asw.getCalendarHolidayList = function() {return [20250721,20250811,20250915,20250923,20251013,20251103,20251123,20251124,20260101,20260112,20260211,20260223,20260320,20260429,20260503,20260504,20260505,20260506,20260720,20260811,20260921,20260922,20260923,20261012,20261103,20261123,20270101,20270111,20270211,20270223,20270321,20270322,20270429,20270503,20270504,20270505,20270719,20270811,20270920,20270923,20271011,20271103,20271123,20280101,20280110,20280211,20280223,20280320,20280429,20280503,20280504,20280505,20280717,20280811,20280918,20280922,20281009,20281103,20281123,20290101,20290108,20290211,20290212,20290223,20290320,20290429,20290430,20290503,20290504,20290505,20290716,20290811,20290917,20290923,20290924,20291008,20291103,20291123];}
			Asw.ClientInfo.deviceType = "PC";
			Asw.ClientInfo.mobileDeviceType = "UNKNOWN";
			Asw.ClientInfo.osVersion = "";
			Asw.Format.init("https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/{1}/{2}/{3}?{0}");
			Asw.overwriteTargetAttributeValue("false");
		</script><script type="text/javascript">
		
		var Asw = Asw || {};
		Asw.ContactInput = Asw.ContactInput || {};
		Asw.ContactInput.Const = {
			/** data-属性名「data-type」 @constant @type {String} @default 'data-type' */
			DATA_ATTRIBUTE_NAME_TYPE: 'data-type',
			/** data-属性名「data-sms-possible」 @constant @type {String} @default 'data-sms-possible' */
			DATA_ATTRIBUTE_NAME_SMS_POSSIBLE: 'data-sms-possible',
			/** data-属性名「data-country-code」 @constant @type {String} @default 'data-country-code' */
			DATA_ATTRIBUTE_NAME_COUNTRY_CODE: 'data-country-code',
			/** data-type属性値「sms」 @constant @type {String} @default 'sms' */
			DATA_TYPE_VALUE_SMS: 'sms'
		};

		
		Asw.ContactInput.countryInfoMap = {"IS":{"internationalCallingCode":"+354","smsPossible":false},"IE":{"internationalCallingCode":"+353","smsPossible":true},"AZ":{"internationalCallingCode":"+994","smsPossible":true},"AF":{"internationalCallingCode":"+93","smsPossible":true},"US":{"internationalCallingCode":"+1","smsPossible":true},"AE":{"internationalCallingCode":"+971","smsPossible":true},"DZ":{"internationalCallingCode":"+213","smsPossible":true},"AR":{"internationalCallingCode":"+54","smsPossible":true},"AW":{"internationalCallingCode":"+297","smsPossible":true},"AL":{"internationalCallingCode":"+355","smsPossible":true},"AM":{"internationalCallingCode":"+374","smsPossible":true},"AO":{"internationalCallingCode":"+244","smsPossible":false},"AG":{"internationalCallingCode":"+1","smsPossible":true},"AD":{"internationalCallingCode":"+376","smsPossible":true},"YE":{"internationalCallingCode":"+967","smsPossible":true},"GB":{"internationalCallingCode":"+44","smsPossible":true},"IL":{"internationalCallingCode":"+972","smsPossible":true},"IT":{"internationalCallingCode":"+39","smsPossible":true},"IQ":{"internationalCallingCode":"+964","smsPossible":true},"IR":{"internationalCallingCode":"+98","smsPossible":true},"IN":{"internationalCallingCode":"+91","smsPossible":true},"ID":{"internationalCallingCode":"+62","smsPossible":true},"UG":{"internationalCallingCode":"+256","smsPossible":true},"UA":{"internationalCallingCode":"+380","smsPossible":true},"UZ":{"internationalCallingCode":"+998","smsPossible":true},"UY":{"internationalCallingCode":"+598","smsPossible":true},"EC":{"internationalCallingCode":"+593","smsPossible":true},"EG":{"internationalCallingCode":"+20","smsPossible":true},"EE":{"internationalCallingCode":"+372","smsPossible":true},"SZ":{"internationalCallingCode":"+268","smsPossible":false},"ET":{"internationalCallingCode":"+251","smsPossible":false},"ER":{"internationalCallingCode":"+291","smsPossible":true},"SV":{"internationalCallingCode":"+503","smsPossible":true},"AU":{"internationalCallingCode":"+61","smsPossible":true},"AT":{"internationalCallingCode":"+43","smsPossible":true},"OM":{"internationalCallingCode":"+968","smsPossible":true},"NL":{"internationalCallingCode":"+31","smsPossible":true},"GH":{"internationalCallingCode":"+233","smsPossible":true},"CV":{"internationalCallingCode":"+238","smsPossible":false},"GY":{"internationalCallingCode":"+592","smsPossible":true},"KZ":{"internationalCallingCode":"+7","smsPossible":true},"QA":{"internationalCallingCode":"+974","smsPossible":true},"CA":{"internationalCallingCode":"+1","smsPossible":true},"GA":{"internationalCallingCode":"+241","smsPossible":true},"CM":{"internationalCallingCode":"+237","smsPossible":true},"KR":{"internationalCallingCode":"+82","smsPossible":true},"KH":{"internationalCallingCode":"+855","smsPossible":true},"GM":{"internationalCallingCode":"+220","smsPossible":true},"KP":{"internationalCallingCode":"+850","smsPossible":false},"MK":{"internationalCallingCode":"+389","smsPossible":false},"GN":{"internationalCallingCode":"+224","smsPossible":true},"GW":{"internationalCallingCode":"+245","smsPossible":true},"CY":{"internationalCallingCode":"+357","smsPossible":true},"CU":{"internationalCallingCode":"+53","smsPossible":true},"GR":{"internationalCallingCode":"+30","smsPossible":true},"KI":{"internationalCallingCode":"+686","smsPossible":false},"KG":{"internationalCallingCode":"+996","smsPossible":true},"GT":{"internationalCallingCode":"+502","smsPossible":true},"GU":{"internationalCallingCode":"+1","smsPossible":true},"KW":{"internationalCallingCode":"+965","smsPossible":true},"KY":{"internationalCallingCode":"+1","smsPossible":true},"GD":{"internationalCallingCode":"+1","smsPossible":true},"HR":{"internationalCallingCode":"+385","smsPossible":true},"KE":{"internationalCallingCode":"+254","smsPossible":true},"CI":{"internationalCallingCode":"+225","smsPossible":true},"CR":{"internationalCallingCode":"+506","smsPossible":true},"SA":{"internationalCallingCode":"+966","smsPossible":true},"KM":{"internationalCallingCode":"+269","smsPossible":true},"CO":{"internationalCallingCode":"+57","smsPossible":true},"CG":{"internationalCallingCode":"+242","smsPossible":false},"CD":{"internationalCallingCode":"+243","smsPossible":true},"ZM":{"internationalCallingCode":"+260","smsPossible":true},"SM":{"internationalCallingCode":"+378","smsPossible":false},"SL":{"internationalCallingCode":"+232","smsPossible":true},"DJ":{"internationalCallingCode":"+253","smsPossible":false},"JM":{"internationalCallingCode":"+1","smsPossible":true},"CH":{"internationalCallingCode":"+41","smsPossible":true},"GE":{"internationalCallingCode":"+995","smsPossible":true},"SE":{"internationalCallingCode":"+46","smsPossible":true},"SY":{"internationalCallingCode":"+963","smsPossible":true},"ES":{"internationalCallingCode":"+34","smsPossible":true},"SG":{"internationalCallingCode":"+65","smsPossible":true},"LK":{"internationalCallingCode":"+94","smsPossible":true},"SK":{"internationalCallingCode":"+421","smsPossible":true},"SI":{"internationalCallingCode":"+386","smsPossible":true},"ZW":{"internationalCallingCode":"+263","smsPossible":true},"SD":{"internationalCallingCode":"+249","smsPossible":true},"KN":{"internationalCallingCode":"+1","smsPossible":true},"SR":{"internationalCallingCode":"+597","smsPossible":true},"LC":{"internationalCallingCode":"+1","smsPossible":true},"SC":{"internationalCallingCode":"+248","smsPossible":false},"SN":{"internationalCallingCode":"+221","smsPossible":true},"SB":{"internationalCallingCode":"+677","smsPossible":false},"RS":{"internationalCallingCode":"+381","smsPossible":true},"TH":{"internationalCallingCode":"+66","smsPossible":true},"TW":{"internationalCallingCode":"+886","smsPossible":true},"TJ":{"internationalCallingCode":"+992","smsPossible":true},"TZ":{"internationalCallingCode":"+255","smsPossible":true},"CZ":{"internationalCallingCode":"+420","smsPossible":true},"TD":{"internationalCallingCode":"+235","smsPossible":true},"CF":{"internationalCallingCode":"+236","smsPossible":true},"TN":{"internationalCallingCode":"+216","smsPossible":true},"CL":{"internationalCallingCode":"+56","smsPossible":true},"TV":{"internationalCallingCode":"+688","smsPossible":false},"DK":{"internationalCallingCode":"+45","smsPossible":true},"DE":{"internationalCallingCode":"+49","smsPossible":true},"TG":{"internationalCallingCode":"+228","smsPossible":true},"DO":{"internationalCallingCode":"+1","smsPossible":true},"DM":{"internationalCallingCode":"+1","smsPossible":false},"TT":{"internationalCallingCode":"+1","smsPossible":true},"TM":{"internationalCallingCode":"+993","smsPossible":false},"TR":{"internationalCallingCode":"+90","smsPossible":true},"TO":{"internationalCallingCode":"+676","smsPossible":false},"NG":{"internationalCallingCode":"+234","smsPossible":true},"NR":{"internationalCallingCode":"+674","smsPossible":false},"NA":{"internationalCallingCode":"+264","smsPossible":false},"NI":{"internationalCallingCode":"+505","smsPossible":true},"BH":{"internationalCallingCode":"+973","smsPossible":false},"NE":{"internationalCallingCode":"+227","smsPossible":true},"PK":{"internationalCallingCode":"+92","smsPossible":true},"JP":{"internationalCallingCode":"+81","smsPossible":true},"BS":{"internationalCallingCode":"+1","smsPossible":true},"NZ":{"internationalCallingCode":"+64","smsPossible":true},"BM":{"internationalCallingCode":"+1","smsPossible":true},"NP":{"internationalCallingCode":"+977","smsPossible":true},"BB":{"internationalCallingCode":"+246","smsPossible":true},"NO":{"internationalCallingCode":"+47","smsPossible":true},"HT":{"internationalCallingCode":"+509","smsPossible":true},"BD":{"internationalCallingCode":"+880","smsPossible":true},"VA":{"internationalCallingCode":"+39","smsPossible":false},"PA":{"internationalCallingCode":"+507","smsPossible":true},"VU":{"internationalCallingCode":"+678","smsPossible":false},"PG":{"internationalCallingCode":"+675","smsPossible":true},"PW":{"internationalCallingCode":"+680","smsPossible":true},"PY":{"internationalCallingCode":"+595","smsPossible":true},"PS":{"internationalCallingCode":"+970","smsPossible":true},"BR":{"internationalCallingCode":"+55","smsPossible":true},"HU":{"internationalCallingCode":"+36","smsPossible":true},"TL":{"internationalCallingCode":"+670","smsPossible":true},"BG":{"internationalCallingCode":"+359","smsPossible":true},"BF":{"internationalCallingCode":"+226","smsPossible":true},"BN":{"internationalCallingCode":"+673","smsPossible":true},"FJ":{"internationalCallingCode":"+679","smsPossible":true},"PH":{"internationalCallingCode":"+63","smsPossible":true},"FI":{"internationalCallingCode":"+358","smsPossible":true},"BT":{"internationalCallingCode":"+975","smsPossible":true},"BY":{"internationalCallingCode":"+375","smsPossible":true},"BZ":{"internationalCallingCode":"+501","smsPossible":false},"PR":{"internationalCallingCode":"+1","smsPossible":true},"BE":{"internationalCallingCode":"+32","smsPossible":true},"BA":{"internationalCallingCode":"+387","smsPossible":true},"FR":{"internationalCallingCode":"+33","smsPossible":true},"BO":{"internationalCallingCode":"+591","smsPossible":false},"PF":{"internationalCallingCode":"+689","smsPossible":true},"BI":{"internationalCallingCode":"+257","smsPossible":true},"VN":{"internationalCallingCode":"+84","smsPossible":true},"BJ":{"internationalCallingCode":"+229","smsPossible":true},"VE":{"internationalCallingCode":"+58","smsPossible":true},"PE":{"internationalCallingCode":"+51","smsPossible":true},"PL":{"internationalCallingCode":"+48","smsPossible":true},"BW":{"internationalCallingCode":"+267","smsPossible":true},"PT":{"internationalCallingCode":"+351","smsPossible":false},"HK":{"internationalCallingCode":"+852","smsPossible":true},"HN":{"internationalCallingCode":"+504","smsPossible":false},"MH":{"internationalCallingCode":"+692","smsPossible":true},"MO":{"internationalCallingCode":"+853","smsPossible":true},"MG":{"internationalCallingCode":"+261","smsPossible":true},"MW":{"internationalCallingCode":"+265","smsPossible":true},"ML":{"internationalCallingCode":"+223","smsPossible":true},"MT":{"internationalCallingCode":"+356","smsPossible":true},"MY":{"internationalCallingCode":"+60","smsPossible":true},"FM":{"internationalCallingCode":"+691","smsPossible":false},"MM":{"internationalCallingCode":"+95","smsPossible":true},"MX":{"internationalCallingCode":"+52","smsPossible":true},"MU":{"internationalCallingCode":"+230","smsPossible":true},"MR":{"internationalCallingCode":"+222","smsPossible":true},"MZ":{"internationalCallingCode":"+258","smsPossible":true},"MC":{"internationalCallingCode":"+377","smsPossible":true},"MV":{"internationalCallingCode":"+960","smsPossible":true},"MD":{"internationalCallingCode":"+373","smsPossible":false},"RO":{"internationalCallingCode":"+40","smsPossible":true},"MA":{"internationalCallingCode":"+212","smsPossible":true},"MN":{"internationalCallingCode":"+976","smsPossible":true},"ME":{"internationalCallingCode":"+382","smsPossible":true},"JO":{"internationalCallingCode":"+962","smsPossible":true},"LA":{"internationalCallingCode":"+856","smsPossible":true},"RU":{"internationalCallingCode":"+7","smsPossible":true},"LV":{"internationalCallingCode":"+371","smsPossible":true},"LT":{"internationalCallingCode":"+370","smsPossible":true},"LY":{"internationalCallingCode":"+218","smsPossible":true},"GQ":{"internationalCallingCode":"+240","smsPossible":false},"LI":{"internationalCallingCode":"+423","smsPossible":false},"LR":{"internationalCallingCode":"+231","smsPossible":true},"CN":{"internationalCallingCode":"+86","smsPossible":true},"LU":{"internationalCallingCode":"+352","smsPossible":true},"ZA":{"internationalCallingCode":"+27","smsPossible":true},"RW":{"internationalCallingCode":"+250","smsPossible":true},"LS":{"internationalCallingCode":"+266","smsPossible":false},"LB":{"internationalCallingCode":"+961","smsPossible":true}};

		$(function () {
			contactInfoInit();
		});
		
		function contactInfoInit() {
			
			
			Asw.get('representativeTelType').find('option[value="M1"]').attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_TYPE,
					Asw.ContactInput.Const.DATA_TYPE_VALUE_SMS);
			Asw.get('representativeTelCountry').children('option').each(function(i,option){
				var countryInfo = Asw.ContactInput.countryInfoMap[option.value];
				if (countryInfo === void 0) {
					
					return true;
				}
				$(option).attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_SMS_POSSIBLE, countryInfo.smsPossible);
				$(option).attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_COUNTRY_CODE, countryInfo.internationalCallingCode);
			});
			
			var smsCountrySelectSelector = 'select.js-countrySelect:not(#representativeTelCountry)'
			$(smsCountrySelectSelector).eq(0).children('option').each(function(i,option){
				var countryInfo = Asw.ContactInput.countryInfoMap[option.value];
				if (countryInfo === void 0) {
					
					return true;
				}
				$(smsCountrySelectSelector + ' option:nth-child(' + (i+1) + ')').attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_COUNTRY_CODE,
						countryInfo.internationalCallingCode);
			});
			
			var emergencyCountrySelectSelector = 'select.passengerEmergencyCountry.js-countrySelect:not(#representativeTelCountry)'
			$(emergencyCountrySelectSelector).eq(0).children('option').each(function(i,option){
				var countryInfo = Asw.ContactInput.countryInfoMap[option.value];
				if (countryInfo === void 0) {
					
					return true;
				}
				$(emergencyCountrySelectSelector + ' option:nth-child(' + (i+1) + ')').attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_COUNTRY_CODE,
						countryInfo.internationalCallingCode);
			});
		}
	</script><script type="text/javascript">
		var isInit = true;
		function changeTelType(_this, telNoInputId) {
			var $telNoInput = Asw.get(telNoInputId);
			var $telNoKind = Asw.get("representativeTelType");
			var telNo = Asw.getValueComparedToPlaceholder(telNoInputId);
			var telNoCountry = Asw.get("representativeTelCountry").val();

			if(true && !isInit) {
				if(telInputCheck(telNo, telNoCountry)) {
					if (_this.value == "M1") {
						telNo = exceptDomesticPrefix("09042504176", telNoCountry);
					} else if (_this.value == "B1") {
						telNo = exceptDomesticPrefix("03-5405-2172", telNoCountry);
					} else {
						telNo = exceptDomesticPrefix("090-4250-4176", telNoCountry);
					}
					$telNoInput.val(telNo);
				}
			}
			isInit = false;
		}
		
		function telInputCheck(telNo, telNoCountry) {
	        checkResult = false;
	        
	        var amcHndyTelNo = exceptDomesticPrefix("09042504176", telNoCountry);
	        var amcCmpTelNo = exceptDomesticPrefix("03-5405-2172", telNoCountry);
	        var amcTelNo = exceptDomesticPrefix("090-4250-4176", telNoCountry);
	
	        if(telNo == "" || telNo == amcHndyTelNo || telNo == amcCmpTelNo || telNo == amcTelNo) {
	        	checkResult = true;
	        }
	        return checkResult;
    	}
    	
    	function exceptDomesticPrefix(telNo, telNoCountry) {
    		if(telNoCountry == 'JP') {
    			if(telNo.substr(0, 1) === '0') {
    				return telNo.slice(1);
    			}
    		}
    		return telNo;
    	}
	</script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-customerinfomationform.js?6e91953"></script><script type="text/javascript">
		$(document).ready(function(){
				Asw.enableTextWhenCheckBox('.cascadedSwitch');
				Asw.notPasteInput('.jsNotPasteInput');
			$(function(){
				Asw.alignHeightToParentTdElement( '.passengerInfoInputArea' );
				
					Asw.alignHeightToParentTdElement( '.passengerInfoInputText' );
				
				
				$('select.js-countrySelect').trigger('change');
			});
		});
	</script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-session-keeper.js?6ec0730"></script><script type="text/javascript">
			$(document).ready(function() {
				Asw.SessionKeeper.initAndStart("sessionKeeperContainer", "520", "570", "PC");
			});
			</script><script type="text/javascript">
		Asw.addAjaxCallback(function(data) {
			// アコーディオンのメソッドと設定 
			if($('.jsAccordionSwitch .jsTriggerSwitch').length > 0){
				var events = $._data($('.jsAccordionSwitch .jsTriggerSwitch').get(0), "events");
				if(events === undefined || events.click === undefined) {
					Asw.AccordionInfo('.jsAccordionSwitch', {
						'triggerSwitch': '.jsAccordionSwitch .jsTriggerSwitch',
						'listClass': '.jsAccordionSwitchList',
						'visibilityHidden': 'toggleContents'
					});
				}
			}
		});
	</script><script type="text/javascript">
	Asw.Price = Asw.Price || {};
	// 整数の区切り文字
	Asw.Price.separateString = ',';
	
	// 整数の区切り桁数
	Asw.Price.separateDigitNumber = 3;
	
	// 小数点
	Asw.Price.decimalPointString = '.';
	
	// 小数点以降の有効桁数
	Asw.Price.significantDigit = 0;
	
	// 表示金額の単位
	Asw.Price.currencyUnit = '円';
	</script><script type="text/javascript">
			$(document).ready(function() {
				$('#preLoadingArea').css('visibility', 'hidden');
			});
		</script><noscript><img src="https://aswbe-i.ana.co.jp/akam/13/pixel_d1a0a00?a=dD0zZmViYTIzZTQyY2Q1NzU2ZWYwZjQ4YjZmZWQ1ZjFkMDRjYTVmNDg1JmpzPW9mZg==" style="visibility: hidden; position: absolute; left: -999px; top: -999px;" /></noscript><script type="text/javascript"  src="/Q1XS4o/Cn_ls/KfDO7/xA/t9OfhmEwSiGQaG/NG4dCQE/bw/QJBChQIQEB"></script></body>
</html>