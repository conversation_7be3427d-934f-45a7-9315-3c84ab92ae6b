<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="ja-<PERSON>" xml:lang="ja-<PERSON>">

	
	<!-- skey = 2025/06/19 20:09:40 rei21e QIuH4Q3dHv dljdmx+df1  --><head id="j_idt53">
    <base href="about:blank">
    <style>
        /* Hide elements that might try to load external resources, as a fallback. */
        img, svg, video, iframe, link[rel="stylesheet"], script { display: none !important; }
    </style>

		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
			<meta http-equiv="X-UA-Compatible" content="IE=EmulateIE9; IE=EmulateIE10" />
			<meta http-equiv="X-UA-Compatible" content="IE=7" />
			<meta http-equiv="X-UA-Compatible" content="IE=edge" />
		<meta name="description" content="" />
		<meta name="keywords" content="" />
		<meta name="format-detection" content="telephone=no" />
		<script type="text/javascript" src="/international_asw/dynatrace/ruxitagentjs_ICANVfhqrux_10273230920145353.js" data-dtconfig="rid=RID_-1231821857|rpid=-1064040493|domain=aswbe-i.ana.co.jp|reportUrl=https://bf90419wvr.bf.dynatrace.com/bf|app=78bf0b58acf6ed13|cors=1|owasp=1|featureHash=ICANVfhqrux|xb=www^bs.google-analytics^bs.com^pana^bs.demdex^bs.net^pcollect^bs.tealiumiq^bs.com^pallnipponairways^bs.australia-3^bs.evergage^bs.com^pallnipponairwayscolt^bs.tt^bs.omtrdc^bs.net|rdnt=1|uxrgce=1|cuc=k8vpgh61|mel=100000|expw=1|dpvc=1|md=mdcc1=bAsw.PnrOutput.recordLocator,mdcc2=bAsw.PnrOutput.paymentDetailInfoList[0].fopCode,mdcc3=bAsw.PnrOutput.ticketedFlag,mdcc4=bAsw.PnrOutput.nhPnrFlag,mdcc5=bAsw.PnrOutput.itinerarySummary.departureDateYyyymmdd,mdcc6=bAsw.SearchCriteriaOutput.cffCodeInput,mdcc7=a#main ^rb div.section.sectionNoFlow ^rb table ^rb tbody ^rb tr ^rb td.flightNumber.lastCol ^rb span ^rb span:nth-child(2),mdcc8=cunique|lastModification=1750184416409|tp=500,50,0|agentUri=/international_asw/dynatrace/ruxitagentjs_ICANVfhqrux_10273230920145353.js"></script><link rel="shortcut icon" href="https://www.ana.co.jp/favicon.ico" />
		<title>お客様情報入力 | ANA</title>
		<noscript><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/css/asw_noscript_pc.css?99001fc" />
		</noscript>

		<!--  [ Adobe Target ] -->
		<script type="text/javascript" src="https://www.ana.co.jp/behavior/adobetarget/target_intbe.js"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/script/jquery-1.8.3.min.js?717d3c0"></script><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/ja/css/asw_initialize.css?99001fc" /><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/css/asw_screen_pc.css?99001fc" /><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/css/asw_common_pc.css?99001fc" /><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/css/asw_forminput_pc.css?99001fc" /><script type="text/javascript">
		var Asw = Asw || {};
		Asw.GlobalWord = Asw.GlobalWord || {};
		
		Asw.GlobalWord.toolTipCloseIconAltText = "閉じる";
		
		</script><script >bazadebezolkohpepadr="1292357189"</script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/akam/13/4d07ccdb"  defer></script></head><body>
		
		<noscript>当サイトをご利用になるためには、JavaScript対応のブラウザが必要です。設定でJavaScriptを有効にしてください。有効にできない場合はANA電話窓口へお問い合わせください。
		</noscript>
		<div id="preLoadingArea"></div>
	
	<div id="cmnHeader" class="platinum">
		<!--===== cmnHeader =====-->
		<div class="headerWrapper">
			<div class="header">
				<p class="logo"><a href="https://www.ana.co.jp/asw/AMCTopServlet?type=a" tabindex="-1" aria-hidden="true"><img src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/image/ana_logo_navy.png?717d3c0" alt="ANA Inspiration of JAPAN。リンク先はマイレージクラブトップページです。" height="28" width="274" /></a>
				</p>
					<ul class="headerMenu">
						<li><a href="https://www.ana.co.jp/asw/AMCTopServlet?type=a">ANAマイレージクラブトップページ</a>
						</li>
						<li><a href="https://www.ana.co.jp/other/int/meta/0005.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">お問い合わせ<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
						</li>
							<li class="btnLogoutArea"><a href="https://aswbe-i.ana.co.jp/rei21e/international_asw/rest/logout" role="button" class="btnBase btnLogout">ログアウト</a>
							</li>
					</ul>
			</div>
		</div>
			<div class="amcUserInfoWrapper">
				<div class="amcUserInfo">
					<div class="userNameArea">
						<table>
							<tr>	
									<td class="statusIcon"><img src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/image/icon_status_05.png?717d3c0" alt="プラチナサービスメンバー" height="46" width="60" />
									</td>
								<td class="userName">佐藤 直樹 <span>様</span>
								</td>
							</tr>					
						</table>
					</div>
						<div class="balance">
								<dl class="upgradePointBalance">
									<dt>アップグレードポイント残高
									</dt>
									<dd>
											<dl>
												<dt>(2025年度)
												</dt>
												<dd>
													<em>20
													</em>ポイント
												</dd>
											</dl>
									</dd>
								</dl>
							<dl class="mileBalance">
								<dt>マイル口座残高
								</dt>
								<dd><em>75,663</em><span class="currencyCode">マイル</span>
								</dd>
							</dl>
						</div>
				</div>
					<p class="nowTime">6月19日20時09分現在
					</p>
			</div>
		<!--===== /cmnHeader =====-->
	</div>
		<div id="sessionKeeperContainer" class="sessionKeepingContainer"><img src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/image/bg_modalWindow.png?717d3c0" alt="" height="0" width="0" />
			<div id="extendSessionExpiredDialog" class="modalExpire modalSmall modalContainer" role="dialog" aria-labelledby="extendSessionExpiredDialogTitle" aria-hidden="true">
<form id="sessionKeeperContainer:j_idt183" name="sessionKeeperContainer:j_idt183" method="post" action="https://aswbe-i.ana.co.jp/rei21e/international_asw/pages/award/reservation/award_mandatory_passenger_information_input.xhtml?aswcid=1&amp;rand=20250619200940QIuH4Q3dHv" autocomplete="off" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="sessionKeeperContainer:j_idt183" value="sessionKeeperContainer:j_idt183" />
<input type="hidden" name="sessionKeeperContainer:j_idt183_operationTicket" value="dljdmx+df1db22ea48aeb62bf6ffbe886ffe194~0oM6ObQOuFRZy45qpJ9QdNjJeA7EoxxvEDP7ffOk!1750331368193.aere-xml-controller-67d4778877-zcp7w" /><input type="hidden" name="sessionKeeperContainer:j_idt183_cmnPageTicket" value="4" />
					<h1 id="extendSessionExpiredDialogTitle" class="dialogTitle">まもなくセッションが終了します</h1>
					<div id="extendSessionExpiredDialogMessage" class="dialogMessage" tabindex="-1">このままお手続きを進める場合は、［延長］ボタンを押してください。</div>
					<p class="modalButton"><input id="sessionKeeperContainer:cmnSessionKeepingButton" type="submit" name="sessionKeeperContainer:cmnSessionKeepingButton" value="延長" class="btnBase btnModal btnMainStream" onclick="mojarra.ab(this,event,'action',0,0,{'onevent':function(data) {Asw.doCommonAjaxCallback(data,false, event); Asw.SessionKeeper.doSessionKeep(data);},'onerror':function(data) {Asw.doCommonAjaxErrorCallback(data); }});return false" />
					</p><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:0" value="tUz9Z/rzvJqIRw24PnugFDqKyfkb8Asoy0bsAx7mTIvAwq23njLShKP98q1t+Awdnf8x9dKcR4gMj1hQ3B0kb2c7AfXF53qZQbEKLbANYyK+mJ0yMnDQ81wt18Z5o/sXqDfULwl/xO5aNxTmCBsaTUy9RU8Z/xXIdLKxFcVPDM80octpX0+ymbw+SwDv0BLuroili6VHcNKyukaKPnWYxOp4xJvk+CPjgGZjDdoot+CodPaUVhNu8sv/DTE5Sk0eTiSUvLqXfh48HGEGV+/XTh/awnQx/H5NLzN4HQL/AbwvoEzL1T5Ebd1hnPiXOTqnlCLew5TDnia24pRdElTGp9FWO+2Zlm2VHEyq6FrskdxApDHoXMp6e4WyEvCx1LytC/CUlrEtl4lc+K2l8jbYnVDrIohV9bcnH+uSrYNY48emDf5DJbQu4PA4RgPJJkE5pyl02jaHvh9uO3T040Q8avv8ty0MBaH/wIA5fXT/f6JmYdsvL1HKDM6jn6+k3tPOYTGRcq/t9YuW0UAGZbqEUhNdYEKGIh/Oo0IGxLSLR3bZ/Y35Uk7v1WvL1jsvdEwMD+5VkzACBhuM2EqInSYLZ6Qu2exiDjNnPwYAeEjNMcmKrpF+MVrVn16rcJKRp8s+AHkRfwxKKh0VddQsMp0QXdC3PxetZzoo8ERtNd7h7BFo0PQ5dVjsOnQTqPxYOGZKAEQLeec1zB5D8nCSKm067UtWqAHeCcsvvVN+zna2HglPy/TVZC2iznNA+hHR9ls7qF8/I/hJswNlkTjl9vpPGAfwwwpdMDvxJmvAx54fKILKdPl2FDn4Ew48mIbvXLO2H+FUqpcTIP8cQPAFPvCyNc6At1j5xqgCnQcEi4SAAAvF+TwaDPvqC6+63jxL+hFLGExlmnPKMmT2KWkbLWBQFvmLqV12eSMXxznUUEsuYFVFvVBA0VnbNNJLAlVraQhd+TcrXp3apHDnvq95vaZchzK+gj9eYQ3EXbkXgXj7W1qZat+SFSgxSQmVBt4jYcCagusycMZtGWfJLh1XLjbvwuvTka/dPcPl4R3U8g6/VWiieA+PgwVHf3qpSuy2uRPsunH95Y03f4+XOJJ5VU0z2TsW4d6IhyPaVrrkmgBgi0LBLwHAQ6ejFTGPPtERW4I4T612vFAJaPHvG9lrOpcHDUzAVyhJo9iTYTCcZRjQgwwGHqaig1YOOhMfaLgC7Cm1ALDQv9QJhZS7Ow69b8cJozrHmSDiwm0qGhWUzeDCY8bf33QmmPuu2Uu07dkNPY0faDeKv80RAFPUHHw5zDTzsQ4cN9IK/Y+/5dmIsQDPQGzj1yYDFlZgfjUiXMoy4T9/aljeeWMLTo0rLH5mmzhXXAIpfi/GUZGuAvE8Z+AI3d1MCTQj8P8wAu+tw1IinS6c35oog6WWLsZx2YjhM2Gx2mHbGG3zCpYtN7tDjU01I9pZ/t2qpYcSb0NVo5YUgEgZUfpdOGbT0T9OVor1WlmBxnPTQJtNMrm8s/bYvFdQI+VoWPXYcu5rosvHOs/DNqFGMCdzPqLRYotOh8otfW1iu4NMWlph1V50DS24VHO5kliBSQZPVWmT5IGaPW0hIEyfThufXNxSENFXhrVS1178E5ebO+e65t0vIG/hG0CeWrHvWSRqeqBawk48vQ0QVsURHIeikISgDR4zKJFCPVXr3LxxFXgbDwIS6GDujyweLhpp3JZfXXn9tacZNblnyIN5BpjVNPLBmRGEbQ3YWLktbUbtlQYc88g3y7M+aGZCg95IXLZVqdER0o983s8m8I6uka8hPA9L6WOmTbbHNQOYH9nWxItpWDum81ObfZRfJEs43EKUMWABNBuxO108KcmSVek1vlGvw55ZEl2df3IUgn/1mTjd/nbECnTRlwdBCtLSZYbi3cAKpRQop9yl5Ar8xveOK5/XV0l51tdNugnwSsSycWqTB5I4GIE5YVRIxAickqLrm5vLv1QvG87FaSVaYnlMsfeB/5JdLldffp6RXCwQUJx2HBL2MOdZ2MRE36ZcETeb1zdgQ+KdlUBkOhK+zG+HP5DwbkXmYbqgVJ454Ux3L79KjNdAtoE8fwl9B/F+7bOh2Kz6BVUD4xhCHznC2ZKZdW2Qwi301Gw+4vodsqiHV6jbmXa0TKmOn1t3fkjELxmGllhZ0ynQLbR1d1evcZ6xKN2Y9YydwrORbce958lC3H0xIYDOoaG7BARAGs07Olk7mhah53ckkdNNU8eoSNop2IykqsRDUGCWBItXObbd9ctPpOoBN38V0+vHR/CTeEmyMTUF+Du1tb4GLnifIsusxrN5+UPzAxXuhbd++MP7BhmZ2TxWSTdsb6RpBbMpZHXnVcyfBBv4Yr2nY4fWRwIlfx6x+u2RWnbdWbFForCwraQLsPCLpkUbCklEQY5qqn5d4IosCOl+/II0MFpvwozF7L4yTAizPRWvVFSwbQGiTYhLhH1QUiVfzXe9Uf7UsSVe6TvPaHgslmJYM3Ipx7HnGHrHiEYsnqEpTY19mTYbh865p8N2jVAw7SyL25/fjbwugGCSVl9mJVSc9K4w6wD2hwZfgTzdocaYXL+hrr+jvhyFd/iobcmPN5w/CbkP7A5qWplmzkIzXp3Er85WlSuHAZWDuRpYbHxf5EsOzlm70eTCW0ElRo+4/Qbzg0DBzkaim/yBSsHwRFvVOkml7ghU7UcJ3KWbHB5wWvVQU/9u0TXEH61hltOejlt9LnzD3uD5mP6LsLYwEbBCDB52rMAWabb7Xqy7dEYLmESaOsox/Jg24KQH3n0UMljSg9eF7BIQH5y0tFOQIEGi5efzK86ry1DGGDlAfqQHBL3TwUcx4H9Y/YIebuXYiilDxQ11X/w003SVlD7MTveKMqlmucqNOM36h7VE+KKf70sWiH3R3HHLfVkETtrCsHQUqitmcEm9BK+5tSs0D0XIuAPdrQrJSbcsUmMahz9ox8VzcdC8db8Mft0QVqSIi2gRCdgtfA+U+kV8ip8vjsXqqtxeV1+wAFGFsJpKc0eX/XRUdnf6VmdIIDBgpMMCneFvLA2Z1yo+K+Vh2ARMZNnuO0cohx9QJQIt4UoRQ/Q8sBSX17Oh9KeHWtzxKGc6B+USUmjeSUBykGt1nKTBtdBLVG1RGtHu2QfKCgHXgx65o5HAu4RpKF/sGXF9LZ7DwQd8IRQZz7T11DC3p/kqt8wdOG7/IveZtyOY6QLGVZkNezuwA5ODAGExJyroIL5fDgFV8qOyc9VWVty6G1rl/72PHTdQIIwSm3ZgZX4tdO8PsLJ+d4WHG9LjdoASd0evSNsXy2jHt6hi/0IaCWFkBYO6E3rY4wu+yQbjCZ6ry9nYjiWs1LBRX16uwZI1/JY3rCP9rZgopn85gmpoIiQEl52+9q1JIy1Sv1cJlr/mObVFE+CVqOUOSiFqWTyrBKJEhGvKMziiiQ3vuQjLQ/LK/mj1U7bvtiWDOIoBtMCu3JR9QOUiVKk4Ote58T0jOM3D8xbxUejWVlmZHsyB61TD+n2uA9E0CVdhHn1Li0xrYfbDp2KNJLlZ+xfxlczjhtL4SvxNkZBlb0ToaJtZqnHQF+qP4s2LFmgfgI6otoxMV3EFnR0S5fgwqlkU/us+kqVmMCq5fEa0CJATveQGGylbAmMwmHP8iBys09BWPrCMn4zhiWwHR7Yh9Z7NO9g91r8vDQHMwvcwbX4/FC6lARF/bR0AZbTyfGZTsvxtvm2ixlsVNmBYXsatCVEazF/dcWsS7lm5iUUURn74RdAnKG66MC3dK1qbV0iBHRVzYhtNMtHZNDBZmiSqC3ukHPfUeF/dwJY6TRqN1VFAJBrVGpYhFAsCDDH5CwiMNfJ/fLe7qi3OrDtHhmprF3q5NyJQvuuPRFOlZz3vgN0XLdezEbJqi6qkD+FyBRsjSpsEF3+Ab8CQmusweoobuSeC6ewrF5VjP9QyihSzbWbuhMH45tAdxyp9k3sk+WfieounP+W4SGh4N2zpQIRo8qbrlf9LFJeE7FOcQ58fqrPunW1i51GxBclZ+imcYIbEFp9PeOml/CidykHPEpFZVxYkoYoboe/7yHuv9MvXAlKtqxxy9uyJym3K5ILGQvtoxq5l2qw/omBsI6WVzE7un7zgMaAjC+++xYsDIhdhMZkex+wawGO1FHetnQdvwo+9qvTot6Z1i1WQOeFwPNLyb4ASgRSUWiQ4hjxI780xh4gZXbkU/q1VolaWS1HlS+Nm0KJNDIymruOerWjrck/Axm9uw8ErLguRASFan7Z6QK5LK8jvT9pXCN6gLM9bL2xLzY9uiDq70BpvybN9r50eMi8HDZAn/9etqiVVl0m1nb9KhUaY2LysHcl5vhaxnJS37U75YE4oAjQjiWobdYICcZq05wJD8lOjqipgUE2eDpUbI2BfOZ77n7Zs5/Ydmb2/Tx79uDBdyKoYKAU2/E6D913G7jbRqzVzKfA9Nx7Jv1Fh1trttUWogeHVe4aal0qpFxZMAG5k9VYkzO0SwJk/ZJDpIw1WSPcxqmMLichGOR1J4FjaJrt2JJxjNciAzs58ljt6tysRY2VH4srmz0zmxgpwOhmNQPq3hYroPQKmtYMUEPZmFjOyy1XvWTSbubEYSDoAoA6aOSdu3CkyE6E7DWL+rd4jj+1MO/K/bKdtOt45wjWKppt4iwmj7nxgvxHOfO5LxXUODYikSILMWGrwMsC8eI5gV1fbtJRKaAz7Up5mbo++OQPng3PN8Yf6OFtUH7o4c3B1LjD1iltSKc1ASq4Ql5TFvhIJ2pGa4UXraQK/pUbWiwkZzwT2wufgRshkqZ0VlrUA1COuA8FvWWW6oNrxSZoLRoEdjl4okbSyKmgf39Zfo8creJxbrMgv0K9x+BgdD9lNil1cVz83mOJ7QbkvmOpDzSc/JJDXwduTXpMosvDrO2bVz9o4cmjmoT4rtakO1EuOo2ehGEB9V+7JovUJnzZPnrjxCsm23fCegrlmaeM6zElsjmD1bnKZdpKR4B3vUE2tdpccwhDl8UBid5Z4h1bpKAqQUTXqGHrmnnOc5B705r/cUrE+YOCrKzk6MSA1YtH19yqGPEMV9SsJi4BymUeTovhvtMjkqv3NvMgmGasxDnZuP5IsVwQSDhykVvJo7VOS0KE5xwAbRlf0ra92Hqple3+V9VR4UlJPiH3/vc4V+IXowPVs7QijQbZXrCTTvByJGlZGnb/aEAEsqxlfCty5EvwVxos52ma16kaDmQen4CmUja7ZNW/cz0xVTXABlNHE/GPndQ08cCEOSssQkyMKC4w82QACxbZZbp+aAUs2F8FhUDYPc88SM65BqUbAyGr0Zlg24dz0JGRabGMLTMqXfZXgL7P7yks/BCJE2vnUj3LJrlJHoNcEvXRw4SmdQvi76GSrPfll4qmj8GUaAEqpT8tKFw7yRaTZHSMUrdChx6+8w9aIcaGqSevcnL1ZIeXUDA1jAI4RgwZw2GjgJKk5zLXyK7Z+lqg5ycsaIkCtUDJ3CC019HZrW+uqhqwAkEFr5KhLznSmnMz1VRjz4nl3b2hXTATC2Gg0HNArZXYgq+yF5wAxozyflodrs9FEe9eJ4pQr94s++olJXokPfb0s38T0Z8IczgL4tORwAyO1RkofRtucVm/frCh1dP8ZJBdDsJI/CO1P2cDuloTjHtwbt5QztJ3c0Hc4LUpOU5/UrZX27dte4BDxcMfh8ny4VgmXQPw5UJdFcwrPggrYi/JoNuAn2rLaT4W+b+FUVDMIy2lQo2jaIFpALsfQMvwZlZnnbLjfDRGZ5WgmF/46wsXQrSBsh/NMie8CvwjiObMRF/Xlr8SEinDtqrC4OeVOIMpLURbsOOtQhc7HO9fOgNtt7v00j1zg7s/Ci2UDwj8CDBc7d6vVI7djpolGa87NnKYd5LwJTpN8rd7kxFz04/Jdsausyj6j2athgVV/zglWFns9PqPw4mKt/TK6tugB4nofXxw2x2ukKIV6rHjY6sgJcmiFduim0rhPS6Pm3xNg2N6cW2qSjnC1dP23BtayhXmT8szWB53B+2ezqsw8c01hjki8VQcJSvP6PQrNzGrcjDlMQK69CZ/ffUomY0ABwpJlhPo9oJKmRTuS1W0nprAwKcuaIqkjJstiLKeZmxQF+vs8zYsanc96vN7BZYp/KbywjBarMo3pov9pstDDJce9VHoM7in3T4iFAXbfCjORM0aS70ENjZOYna/a9U8F+dVVn5fFD0qwKlCZeTKkJiSZ+bCyFspJkQrZkFhBxhFtAkGffxOtt5lIjiwSScGN026OKac0WNxvBeCYTHTuQYZcwFWJ/oz+1xTsh03Jm25ivR3TKYkjQsvl36F5TTS7VJztBhZD//OHx9dXM+THG87lrfp+I5/Ifp2bBTP9j8TfNNpnfmPNEyxUFwgAoGqgqImo5svP5UpN/IpiKVWRIxXl/eCtK54DgWpb7oYIUJ+2jIE15W3yqpoCKmjfQxMKh6RgMWxg/1gIisI9MCg/naUuTXKFXVHZ/yODBQVd8/nL/SUSD/7KxQgvIuAaxoo2xq3I2jiNK/KWUtcvcZHSnTmRS3I0C+SaQrJXZ+IVTKze9BSTopztLDcuey7WushpuEHz0KV0QhD8TobbMhVBYOwYLJHg5krc6hjdq9bpYMdBsEsAFQt+0uhV3kvQVMRguFkxFEjKwhJw73naSTfKsaWptfkbNOKdF4dLBWyxSv30lmBE9R8xPSJG8aQDvAnuuJuvL23SwWKs9z1IXhMpPVENFpVlfkjEJwNNdv9F72DfuoO8t5vNIo7OeajFXJJK7YpW105KaNGXTXvCjzC/T2ytKYV/tNdN9NpnO45klusP5QTWy2gA5mdHVgZ/St7AS7C4+renCgrTHuLF4MAn7vd5WFfBo7tqUno3M0NxMw76Ts+sZHxp/OlIjwHz4apEq23gJUP8mPRvuATFsLV9I7Fr9OqbwRWsPzQqCrlVnQ4D/FAy9FRX4q9xiythCIuIBZrYYDiJwMMeRUeqgsPjxG/pv0tyijKfcRRi3NDQrrA+yI/gD+815MVWttnses+tTCDObBnnPQ3fk4sXd/Ux/QPYOB8DUh+uSc9QUKoKMjv6g8iHYsds5rjZLecR9/J8iTnmfBXhGR5WTp9nmmOP3JK/n7J7Ow3A86TAuoNw8dxDeteBlz8UFfFpnZspswBSnv7NMu9L6O8DB7tMlulxrbIbbDeuPD5zDmI7LP5n/swWozj+81db+rXT909wbjyMAMw6EQ0fKXp1h8deHz3JXpjkwtIAt1Ua6VnXfxmSKIGeSHAnRu+h7cGbe4WrVc+RYOV8VR7M2J6qAV63wUDD7q17R3NAQx054yCndOivagBeRHsUQ1XSGHoWRFmoeG+5HCIMd7ZBJP96A5mTUK6iY6HXqmsytx8b+W5qegVjoCiS6WnakUZfINawOaka3GS2OX1noMRroHmiLdvtfFMWPZk0DVVpSRU3/pJC34v1gTgMaMsH1gkmqR0c9808gJq7iJ8/3VHRsxnd+US/oKGSf/0bc/1slH7+yGLeucsssWuyZANPN/rhTy+Bku38F9LtOHA4O7/+ksJJeh2jUjHthQqqMeIMygytSC0/kU4JtBMYvYIcZzyOCfssItU5NxI0PmtINg5sbmATS18nHh2YRCFI1FWnxZ7CYyk++3Fv9nDeHA/4PNzYYPJqHQnV+IhegJPEezqqYRCTliNI+rKpEkf+peMAmCaHTi+Tk1ib7LGl9DqSp/XZp7cNgyYW9/Dmk9AkX50go19XcXSLL5ttGh3vfq/uvAy1Tlv3fyoIiwUx02NvjmVyCJ0MlKX5IykjjFjXasWjQ6p9KkY1h8mEltGXAHZxKfFRGJ2njWwCy9nowJ7z96u0YnXr6Eaf9RwZSOmkqvabCpwTHrBQaBBpCCEv3bL+iPyLmWYkzAf9ahywvkAYUBWT69MJMJ+YBKJty/2BbwYN1WSfJyfR7Z4D+0NSiQIk2/qu7ZHj5jW0ikoMne2myuLhbsMUPpzVo6UkkgXR0Qd76k0WG3VcLrrTazedceNmth0ebAxiVU0UWN+tqaUqLLy53EQEtDbFPY9lb0bf3GDZ1rfJujfMkVO2ACcS9fwSJUwfOqT9jna29yBSBWzFofxpxK1+TN3WXuSMWFqAY7jJNxX7woofVFUGeiDsuG25BmYE/P2GwrrGG44HP2Zh8ABny/Ro0IpNhjOkAC5gCBm+vBrhL33CbqFIM2cmj32J3R1k8k75K+IxeEJluF7l0HtgO+7jagsYkbm2Iog9CDyTNMdZr3ZjuF+DykygX/aLgyg6E7yZOLxHRWRWgRCTXVzLz195" autocomplete="off" />
</form>
			</div>
			<div id="showSessionExpiredDialog" class="modalExpire modalSmall modalContainer" role="dialog" aria-labelledby="showSessionExpiredDialogTitle" aria-hidden="true">
				<h1 id="showSessionExpiredDialogTitle" class="dialogTitle">セッションが終了しました</h1>
				<div id="showSessionExpiredDialogMessage" class="dialogMessage" tabindex="-1">一定時間操作されなかったため、お手続きを進めることができません。お手数ですが、再度トップページからお手続きください。</div>
				<p class="modalButton">
					<input type="submit" value="閉じる" class="btnBase btnModal btnMainStream" onclick="Asw.SessionKeeper.closeTimeoutDialog(); return false;" />
				</p>
			</div>
		</div>
		<!--===== dialogMessages =====--><div id="cmnModalMessages"></div>
		<div id="prebookConfirmDialog" class="modalContainer modalConfirm modalSmall " aria-hidden="true" role="dialog">
<form id="prebookConfirmDialog:j_idt403" name="prebookConfirmDialog:j_idt403" method="post" action="https://aswbe-i.ana.co.jp/rei21e/international_asw/pages/award/reservation/award_mandatory_passenger_information_input.xhtml?aswcid=1&amp;rand=20250619200940QIuH4Q3dHv" autocomplete="off" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="prebookConfirmDialog:j_idt403" value="prebookConfirmDialog:j_idt403" />
<input type="hidden" name="prebookConfirmDialog:j_idt403_operationTicket" value="dljdmx+df1db22ea48aeb62bf6ffbe886ffe194~0oM6ObQOuFRZy45qpJ9QdNjJeA7EoxxvEDP7ffOk!1750331368193.aere-xml-controller-67d4778877-zcp7w" /><input type="hidden" name="prebookConfirmDialog:j_idt403_cmnPageTicket" value="4" /><div class="dialogMessage" tabindex="0"><p>この後、名・姓の修正や変更ができません。</p><p>「搭乗者選択」欄の氏名が「名」「姓」の順番で表示されていることを再確認ください。</p><p>例）<strong class="highlight">空野 太郎</strong> 様 → MR <strong class="highlight">TARO SORANO</strong></p><p>保安上の理由により、航空券とパスポートのお名前が同じ表記でない場合はご搭乗いただくことができませんので必ず再度ご確認ください。</p></div>
					
					<ul class="modalBtnHorizontal">
							
							<li><input type="submit" name="prebookConfirmDialog:j_idt419" value="戻る" aria-controls="prebookConfirmDialog" class="btnBase btnModal btnCancel" onclick="Asw.Dialog.getInstance('prebookConfirmDialog').close(event, Asw.DialogSequencer.isActiveSequencerNotDisplayedLastDialog());  return false;" />
							</li>
						
						<li class="btnArrowNext"><input type="submit" name="prebookConfirmDialog:j_idt422" value="OK" aria-controls="prebookConfirmDialog" class="btnBase btnModal btnMainStream " onclick="Asw.Dialog.getInstance('prebookConfirmDialog').callOpener('NORMAL', event); return false;" />
						</li>
					</ul><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:1" value="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" autocomplete="off" />
</form>
		</div>
		<!--===== /dialogMessages =====--><div id="cmnContainer">
		<!--=========== .cmnContainer ============--><div id="cmnEmergencyNotice"></div><div id="cmnTitle" class="hgroup ">
		<h1 class="visuallyHidden">お客様情報入力</h1></div>

		<div id="cmnWrapper">
		<!--===== cmnWrapper =====-->
		<ol class="mainFlow" aria-label="完了までの手順、">
			<li><img src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/image/icon_flow_01.png?717d3c0" alt="1" height="20" width="28" />フライト検索</li><!--
			--><li><img src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/image/icon_flow_02_on.png?717d3c0" alt="2" height="20" width="28" /><span class="visuallyHidden">現在のステップ、</span>お客様情報入力</li><!--
			--><li><img src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/image/icon_flow_03.png?717d3c0" alt="3" height="20" width="28" />お支払い情報</li><!--
			--><li class="flowLast"><img src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/image/icon_flow4_04.png?717d3c0" alt="4" height="20" width="28" />完了</li>
		</ol>

	<div id="main">
	<!--=========== main =============-->
		<ul class="iconExample">
				<li class="requiredIconExample">
					<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="" height="7" width="7" /></span>必須項目です	
				</li>
		</ul>
<form id="j_idt1529" name="j_idt1529" method="post" action="https://aswbe-i.ana.co.jp/rei21e/international_asw/pages/award/reservation/award_mandatory_passenger_information_input.xhtml?aswcid=1&amp;rand=20250619200940QIuH4Q3dHv" autocomplete="off" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="j_idt1529" value="j_idt1529" />
<input type="hidden" name="j_idt1529_operationTicket" value="dljdmx+df1db22ea48aeb62bf6ffbe886ffe194~0oM6ObQOuFRZy45qpJ9QdNjJeA7EoxxvEDP7ffOk!1750331368193.aere-xml-controller-67d4778877-zcp7w" /><input type="hidden" name="j_idt1529_cmnPageTicket" value="4" />

<div class="section">
	<h2>搭乗者選択<a href="https://www.ana.co.jp/other/int/meta/0086.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="特典のご利用に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a></h2>
	<table class="passengerInfo">
		<thead>
			<tr>
				
				<th class="name">氏名</th>
				
				<th class="dateOfBirthWide" id="dateOfBirthHeader">生年月日</th>
				
				<th class="gender lastCol" id="genderHeader">性別<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span></th>
				
			</tr>
		</thead>
		<tbody>
				
				<tr>
					<th class="parent_01" id="parentHeader01">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:0:passenger" type="checkbox" name="passengers:0:passenger" checked="checked" data-number="1" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:0:passenger">NAOKI SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader01">1963年06月05日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader01">				
							<span class="formWrapper">
							<label for="passengers:0:gender" class="visuallyHidden">NAOKI SATOの性別</label><select id="passengers:0:gender" name="passengers:0:gender" size="1">	<option value="">選択してください</option>
	<option value="M" selected="selected">男性</option>
	<option value="F">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_02" id="parentHeader02">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:1:passenger" type="checkbox" name="passengers:1:passenger" data-number="2" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:1:passenger">REO SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader02">1995年05月18日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader02">				
							<span class="formWrapper">
							<label for="passengers:1:gender" class="visuallyHidden">REO SATOの性別</label><select id="passengers:1:gender" name="passengers:1:gender" size="1">	<option value="">選択してください</option>
	<option value="M" selected="selected">男性</option>
	<option value="F">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_03" id="parentHeader03">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:2:passenger" type="checkbox" name="passengers:2:passenger" data-number="3" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:2:passenger">ERI SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader03">1963年08月14日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader03">				
							<span class="formWrapper">
							<label for="passengers:2:gender" class="visuallyHidden">ERI SATOの性別</label><select id="passengers:2:gender" name="passengers:2:gender" size="1">	<option value="">選択してください</option>
	<option value="M">男性</option>
	<option value="F" selected="selected">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_04" id="parentHeader04">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:3:passenger" type="checkbox" name="passengers:3:passenger" data-number="4" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:3:passenger">TAISHI SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader04">1992年12月03日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader04">				
							<span class="formWrapper">
							<label for="passengers:3:gender" class="visuallyHidden">TAISHI SATOの性別</label><select id="passengers:3:gender" name="passengers:3:gender" size="1">	<option value="">選択してください</option>
	<option value="M" selected="selected">男性</option>
	<option value="F">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_05" id="parentHeader05">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:4:passenger" type="checkbox" name="passengers:4:passenger" data-number="5" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:4:passenger">HIROMI FUJIMOTO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader05">1961年11月23日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader05">				
							<span class="formWrapper">
							<label for="passengers:4:gender" class="visuallyHidden">HIROMI FUJIMOTOの性別</label><select id="passengers:4:gender" name="passengers:4:gender" size="1">	<option value="">選択してください</option>
	<option value="M">男性</option>
	<option value="F" selected="selected">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
		</tbody>
	</table>
</div>
		<div class="section">
			<h2 class="midstream">連絡先情報
			</h2>
			<h3>代表者
			</h3>
	
	
	
	<div class="formInput">
		<fieldset>
			<legend><label>代表者</label></legend>
			<dl id="contactEmailAddressInfo">
				<dt class="hasRequiredIcon">メールアドレス<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span><a href="https://www.ana.co.jp/other/int/meta/0057.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="メールの宛先と内容に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a>
				</dt>
				<dd>
					<ul class="verticalList">
						<li class="nonMarginTop"><label for="representativeEmail" class="visuallyHidden">代表者メールアドレス</label><input id="representativeEmail" type="text" name="representativeEmail" value="<EMAIL>" class="widthLarge" placeholder="例：<EMAIL>" />
						</li>
						<li>
							<span class="supplementTxt" id="confirmMailAddressForRepresentative">確認のため再度ご入力ください。
							</span><label for="confirmrepresentativeEmail" class="visuallyHidden">確認用メールアドレス</label><input id="confirmrepresentativeEmail" type="text" name="confirmrepresentativeEmail" value="<EMAIL>" class="widthLarge jsNotPasteInput" placeholder="例：<EMAIL>" aria-describedby="confirmMailAddressForRepresentative" />
						</li>
					</ul>
				</dd>
					<dd style="display:none;"><input id="updateAlwaysInfo" type="checkbox" name="updateAlwaysInfo" /><label for="updateAlwaysInfo">ログイン中の会員情報へこのメールアドレスを登録</label>
					</dd>
			</dl>
			<dl>
				<dt>電話番号<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span>
				</dt>
				<dd class="js-countryWrap">
					<ul>
						<li><label for="representativeTelType" class="visuallyHidden">電話番号種別</label><select id="representativeTelType" name="representativeTelType" class="basicWidth js-typeSelect" size="1" onchange="changeTelType(this,'representativeTel')">	<option value="M1" selected="selected">携帯</option>
	<option value="B1">会社</option>
	<option value="H1">自宅</option>
</select>
						</li>
						<li><label for="representativeTelCountry" class="visuallyHidden">電話番号の国/地区選択</label><select id="representativeTelCountry" name="representativeTelCountry" class="js-countrySelect" size="1">	<option value="" data-sms-possible="true" data-country-code="---">選択してください</option>
	<option value="JP" selected="selected" data-sms-possible="true">日本</option>
	<option value="-" data-sms-possible="true" data-country-code="---">-----------</option>
	<option value="IS" data-sms-possible="true">アイスランド</option>
	<option value="IE" data-sms-possible="true">アイルランド</option>
	<option value="AZ" data-sms-possible="true">アゼルバイジャン</option>
	<option value="AF" data-sms-possible="true">アフガニスタン</option>
	<option value="US" data-sms-possible="true">アメリカ合衆国/カナダ</option>
	<option value="AE" data-sms-possible="true">アラブ首長国連邦</option>
	<option value="DZ" data-sms-possible="true">アルジェリア</option>
	<option value="AR" data-sms-possible="true">アルゼンチン</option>
	<option value="AW" data-sms-possible="true">アルバ</option>
	<option value="AL" data-sms-possible="true">アルバニア</option>
	<option value="AM" data-sms-possible="true">アルメニア</option>
	<option value="AO" data-sms-possible="true">アンゴラ</option>
	<option value="AG" data-sms-possible="true">アンティグア・バーブーダ</option>
	<option value="AD" data-sms-possible="true">アンドラ</option>
	<option value="YE" data-sms-possible="true">イエメン</option>
	<option value="GB" data-sms-possible="true">イギリス</option>
	<option value="IL" data-sms-possible="true">イスラエル</option>
	<option value="IT" data-sms-possible="true">イタリア</option>
	<option value="IQ" data-sms-possible="true">イラク</option>
	<option value="IR" data-sms-possible="true">イラン</option>
	<option value="IN" data-sms-possible="true">インド</option>
	<option value="ID" data-sms-possible="true">インドネシア</option>
	<option value="UG" data-sms-possible="true">ウガンダ</option>
	<option value="UA" data-sms-possible="true">ウクライナ</option>
	<option value="UZ" data-sms-possible="true">ウズベキスタン</option>
	<option value="UY" data-sms-possible="true">ウルグアイ</option>
	<option value="EC" data-sms-possible="true">エクアドル</option>
	<option value="EG" data-sms-possible="true">エジプト</option>
	<option value="EE" data-sms-possible="true">エストニア</option>
	<option value="SZ" data-sms-possible="true">エスワティニ</option>
	<option value="ET" data-sms-possible="true">エチオピア</option>
	<option value="ER" data-sms-possible="true">エリトリア</option>
	<option value="SV" data-sms-possible="true">エルサルバドル</option>
	<option value="AU" data-sms-possible="true">オーストラリア</option>
	<option value="AT" data-sms-possible="true">オーストリア</option>
	<option value="OM" data-sms-possible="true">オマーン</option>
	<option value="NL" data-sms-possible="true">オランダ</option>
	<option value="GH" data-sms-possible="true">ガーナ</option>
	<option value="CV" data-sms-possible="true">カーボベルデ</option>
	<option value="GY" data-sms-possible="true">ガイアナ</option>
	<option value="KZ" data-sms-possible="true">カザフスタン</option>
	<option value="QA" data-sms-possible="true">カタール</option>
	<option value="CA" data-sms-possible="true">カナダ/アメリカ合衆国</option>
	<option value="GA" data-sms-possible="true">ガボン</option>
	<option value="CM" data-sms-possible="true">カメルーン</option>
	<option value="KR" data-sms-possible="true">韓国</option>
	<option value="GM" data-sms-possible="true">ガンビア</option>
	<option value="KH" data-sms-possible="true">カンボジア</option>
	<option value="KP" data-sms-possible="true">北朝鮮</option>
	<option value="MK" data-sms-possible="true">北マケドニア</option>
	<option value="GN" data-sms-possible="true">ギニア</option>
	<option value="GW" data-sms-possible="true">ギニアビサウ</option>
	<option value="CY" data-sms-possible="true">キプロス</option>
	<option value="CU" data-sms-possible="true">キューバ</option>
	<option value="GR" data-sms-possible="true">ギリシャ</option>
	<option value="KI" data-sms-possible="true">キリバス</option>
	<option value="KG" data-sms-possible="true">キルギス</option>
	<option value="GT" data-sms-possible="true">グアテマラ</option>
	<option value="GU" data-sms-possible="true">グアム</option>
	<option value="KW" data-sms-possible="true">クウェート</option>
	<option value="GD" data-sms-possible="true">グレナダ</option>
	<option value="HR" data-sms-possible="true">クロアチア</option>
	<option value="KY" data-sms-possible="true">ケイマン諸島</option>
	<option value="KE" data-sms-possible="true">ケニア</option>
	<option value="CI" data-sms-possible="true">コートジボワール</option>
	<option value="CR" data-sms-possible="true">コスタリカ</option>
	<option value="KM" data-sms-possible="true">コモロ</option>
	<option value="CO" data-sms-possible="true">コロンビア</option>
	<option value="CG" data-sms-possible="true">コンゴ共和国</option>
	<option value="CD" data-sms-possible="true">コンゴ民主共和国</option>
	<option value="SA" data-sms-possible="true">サウジアラビア</option>
	<option value="ZM" data-sms-possible="true">ザンビア</option>
	<option value="SM" data-sms-possible="true">サンマリノ</option>
	<option value="SL" data-sms-possible="true">シエラレオネ</option>
	<option value="DJ" data-sms-possible="true">ジブチ</option>
	<option value="JM" data-sms-possible="true">ジャマイカ</option>
	<option value="GE" data-sms-possible="true">ジョージア</option>
	<option value="SY" data-sms-possible="true">シリア</option>
	<option value="SG" data-sms-possible="true">シンガポール</option>
	<option value="ZW" data-sms-possible="true">ジンバブエ</option>
	<option value="CH" data-sms-possible="true">スイス</option>
	<option value="SE" data-sms-possible="true">スウェーデン</option>
	<option value="SD" data-sms-possible="true">スーダン</option>
	<option value="ES" data-sms-possible="true">スペイン</option>
	<option value="SR" data-sms-possible="true">スリナム</option>
	<option value="LK" data-sms-possible="true">スリランカ</option>
	<option value="SK" data-sms-possible="true">スロバキア</option>
	<option value="SI" data-sms-possible="true">スロベニア</option>
	<option value="SC" data-sms-possible="true">セイシェル</option>
	<option value="GQ" data-sms-possible="true">赤道ギニア</option>
	<option value="SN" data-sms-possible="true">セネガル</option>
	<option value="RS" data-sms-possible="true">セルビア</option>
	<option value="KN" data-sms-possible="true">セントクリストファー・ネイビス</option>
	<option value="LC" data-sms-possible="true">セントルシア</option>
	<option value="SB" data-sms-possible="true">ソロモン諸島</option>
	<option value="TH" data-sms-possible="true">タイ</option>
	<option value="TW" data-sms-possible="true">台湾</option>
	<option value="TJ" data-sms-possible="true">タジキスタン</option>
	<option value="TZ" data-sms-possible="true">タンザニア</option>
	<option value="CZ" data-sms-possible="true">チェコ</option>
	<option value="TD" data-sms-possible="true">チャド</option>
	<option value="CF" data-sms-possible="true">中央アフリカ共和国</option>
	<option value="CN" data-sms-possible="true">中国</option>
	<option value="TN" data-sms-possible="true">チュニジア</option>
	<option value="CL" data-sms-possible="true">チリ</option>
	<option value="TV" data-sms-possible="true">ツバル</option>
	<option value="DK" data-sms-possible="true">デンマーク</option>
	<option value="DE" data-sms-possible="true">ドイツ</option>
	<option value="TG" data-sms-possible="true">トーゴ</option>
	<option value="DO" data-sms-possible="true">ドミニカ共和国</option>
	<option value="DM" data-sms-possible="true">ドミニカ国</option>
	<option value="TT" data-sms-possible="true">トリニダード・トバゴ</option>
	<option value="TM" data-sms-possible="true">トルクメニスタン</option>
	<option value="TR" data-sms-possible="true">トルコ</option>
	<option value="TO" data-sms-possible="true">トンガ</option>
	<option value="NG" data-sms-possible="true">ナイジェリア</option>
	<option value="NR" data-sms-possible="true">ナウル</option>
	<option value="NA" data-sms-possible="true">ナミビア</option>
	<option value="NI" data-sms-possible="true">ニカラグア</option>
	<option value="NE" data-sms-possible="true">ニジェール</option>
	<option value="NZ" data-sms-possible="true">ニュージーランド</option>
	<option value="NP" data-sms-possible="true">ネパール</option>
	<option value="NO" data-sms-possible="true">ノルウェー</option>
	<option value="BH" data-sms-possible="true">バーレーン</option>
	<option value="HT" data-sms-possible="true">ハイチ</option>
	<option value="PK" data-sms-possible="true">パキスタン</option>
	<option value="VA" data-sms-possible="true">バチカン</option>
	<option value="PA" data-sms-possible="true">パナマ</option>
	<option value="VU" data-sms-possible="true">バヌアツ</option>
	<option value="BS" data-sms-possible="true">バハマ</option>
	<option value="PG" data-sms-possible="true">パプアニューギニア</option>
	<option value="BM" data-sms-possible="true">バミューダ</option>
	<option value="PW" data-sms-possible="true">パラオ</option>
	<option value="PY" data-sms-possible="true">パラグアイ</option>
	<option value="BB" data-sms-possible="true">バルバドス</option>
	<option value="PS" data-sms-possible="true">パレスチナ</option>
	<option value="HU" data-sms-possible="true">ハンガリー</option>
	<option value="BD" data-sms-possible="true">バングラデシュ</option>
	<option value="TL" data-sms-possible="true">東ティモール</option>
	<option value="FJ" data-sms-possible="true">フィジー</option>
	<option value="PH" data-sms-possible="true">フィリピン</option>
	<option value="FI" data-sms-possible="true">フィンランド</option>
	<option value="BT" data-sms-possible="true">ブータン</option>
	<option value="PR" data-sms-possible="true">プエルトリコ</option>
	<option value="BR" data-sms-possible="true">ブラジル</option>
	<option value="FR" data-sms-possible="true">フランス</option>
	<option value="PF" data-sms-possible="true">フランス領ポリネシア</option>
	<option value="BG" data-sms-possible="true">ブルガリア</option>
	<option value="BF" data-sms-possible="true">ブルキナファソ</option>
	<option value="BN" data-sms-possible="true">ブルネイ</option>
	<option value="BI" data-sms-possible="true">ブルンジ</option>
	<option value="VN" data-sms-possible="true">ベトナム</option>
	<option value="BJ" data-sms-possible="true">ベナン</option>
	<option value="VE" data-sms-possible="true">ベネズエラ</option>
	<option value="BY" data-sms-possible="true">ベラルーシ</option>
	<option value="BZ" data-sms-possible="true">ベリーズ</option>
	<option value="PE" data-sms-possible="true">ペルー</option>
	<option value="BE" data-sms-possible="true">ベルギー</option>
	<option value="PL" data-sms-possible="true">ポーランド</option>
	<option value="BA" data-sms-possible="true">ボスニア・ヘルツェゴビナ</option>
	<option value="BW" data-sms-possible="true">ボツワナ</option>
	<option value="BO" data-sms-possible="true">ボリビア</option>
	<option value="PT" data-sms-possible="true">ポルトガル</option>
	<option value="HK" data-sms-possible="true">香港</option>
	<option value="HN" data-sms-possible="true">ホンジュラス</option>
	<option value="MH" data-sms-possible="true">マーシャル諸島</option>
	<option value="MO" data-sms-possible="true">マカオ</option>
	<option value="MG" data-sms-possible="true">マダガスカル</option>
	<option value="MW" data-sms-possible="true">マラウイ</option>
	<option value="ML" data-sms-possible="true">マリ</option>
	<option value="MT" data-sms-possible="true">マルタ</option>
	<option value="MY" data-sms-possible="true">マレーシア</option>
	<option value="FM" data-sms-possible="true">ミクロネシア連邦</option>
	<option value="ZA" data-sms-possible="true">南アフリカ</option>
	<option value="MM" data-sms-possible="true">ミャンマー</option>
	<option value="MX" data-sms-possible="true">メキシコ</option>
	<option value="MU" data-sms-possible="true">モーリシャス</option>
	<option value="MR" data-sms-possible="true">モーリタニア</option>
	<option value="MZ" data-sms-possible="true">モザンビーク</option>
	<option value="MC" data-sms-possible="true">モナコ</option>
	<option value="MV" data-sms-possible="true">モルディヴ</option>
	<option value="MD" data-sms-possible="true">モルドバ</option>
	<option value="MA" data-sms-possible="true">モロッコ</option>
	<option value="MN" data-sms-possible="true">モンゴル</option>
	<option value="ME" data-sms-possible="true">モンテネグロ</option>
	<option value="JO" data-sms-possible="true">ヨルダン</option>
	<option value="LA" data-sms-possible="true">ラオス人民民主共和国</option>
	<option value="LV" data-sms-possible="true">ラトビア</option>
	<option value="LT" data-sms-possible="true">リトアニア</option>
	<option value="LY" data-sms-possible="true">リビア</option>
	<option value="LI" data-sms-possible="true">リヒテンシュタイン</option>
	<option value="LR" data-sms-possible="true">リベリア</option>
	<option value="RO" data-sms-possible="true">ルーマニア</option>
	<option value="LU" data-sms-possible="true">ルクセンブルク</option>
	<option value="RW" data-sms-possible="true">ルワンダ</option>
	<option value="LS" data-sms-possible="true">レソト</option>
	<option value="LB" data-sms-possible="true">レバノン</option>
	<option value="RU" data-sms-possible="true">ロシア</option>
</select>
						</li>
						<li class="numberInputWrap">
							<span id="numberInputInfo1" class="supplementTxt marginTop">先頭に0(ゼロ)がある場合、0(ゼロ)を抜いてください(一部、国・地域除く)。携帯電話番号の場合は、携帯電話を契約された国を選択ください。</span>
							<div class="numberRowWrap">
								<div class="block">
									
									<span class="visuallyHidden">国番号</span>
									<span class="viewInput widthMini js-countryCode"></span>
								</div>
								<div class="block"><label for="representativeTel" class="visuallyHidden">電話番号</label><input id="representativeTel" type="text" name="representativeTel" value="9042504176" class="widthMiddle" placeholder="例：9012345678" maxlength="30" aria-describedby="numberInputInfo1" />
								</div>
							</div>
						</li>
					</ul>
				</dd>
			</dl>
		</fieldset>
	</div>
	<div class="titleWrapper">
		<h3>
			<span class="text">eメール・SMSに関するご案内<a href="https://www.ana.co.jp/other/int/meta/0057.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="eメール・SMSに関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a></span>
		</h3>
	</div>
	<div class="formInput">
	<h4 class="formAreaTitle">主に搭乗・運航に関わるeメールの送付先設定（米国へ通知いたします）<a href="https://www.ana.co.jp/other/int/meta/0629.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="米国入国用eメール・SMS・電話番号に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a>
	</h4>
	<div class="paxSettingArea js-mailFormWrap onePax is-open">
			<div class="settingArea">
					<dl>
						<dt class="breakWord">NAOKI SATO
						</dt>
						<dd>
							<fieldset>
								<legend>NAOKI SATOのeメール送付先および米国到着時に提供するメールアドレス</legend>代表者と同じ
							</fieldset>
						</dd>
					</dl>
			</div>
	</div>
	<h4 class="formAreaTitle hasTopLine">主に搭乗・運航に関わるSMSの送付先および米国で受信できる電話番号の設定（米国へ通知いたします）<a href="https://www.ana.co.jp/other/int/meta/0629.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="米国入国用eメール・SMS・電話番号に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a>
	</h4>
	<div class="paxSettingArea js-smsFormWrap onePax is-open">
			<div class="settingArea">
					<dl>
						<dt class="breakWord">NAOKI SATO<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span>
						</dt>
						<dd>
							<fieldset>
								<legend>NAOKI SATOの1つ目の電話番号</legend>
									<ul class="verticalList js-formItem">
										<li><input type="radio" id="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSameAsRepresentativeSms:radioItem" name="contactsSms:0:recipientSmsEachPersonSelection:radioGroup" value="0" checked="checked" /><label for="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSameAsRepresentativeSms:radioItem">代表者電話番号と同じ</label>
										</li>
										<li><input type="radio" id="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSmsPerPassenger:radioItem" name="contactsSms:0:recipientSmsEachPersonSelection:radioGroup" value="1" /><label for="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSmsPerPassenger:radioItem">電話番号を指定</label>
											<div class="indentArea js-formImputArea">
												<span id="confirmTelNumberForRepresentative:0" class="supplementTxt marginTop">先頭に0(ゼロ)がある場合、0(ゼロ)を抜いてください(一部、国・地域除く)。携帯電話番号の場合は、携帯電話を契約された国を選択ください。</span>
												<ul class="countryNumberInput js-countryWrap">
													<li><label for="contactsSms:0:recipientSmsEachPersonSelection:passengerSmsCountry" class="visuallyHidden">電話番号の国/地区選択</label><select id="contactsSms:0:recipientSmsEachPersonSelection:passengerSmsCountry" name="contactsSms:0:recipientSmsEachPersonSelection:passengerSmsCountry" class="countrySelect js-countrySelect" size="1">	<option value="" data-country-code="---">選択してください</option>
	<option value="JP">日本</option>
	<option value="-" data-country-code="---">-----------</option>
	<option value="IE">アイルランド</option>
	<option value="AZ">アゼルバイジャン</option>
	<option value="AF">アフガニスタン</option>
	<option value="US">アメリカ合衆国/カナダ</option>
	<option value="AE">アラブ首長国連邦</option>
	<option value="DZ">アルジェリア</option>
	<option value="AR">アルゼンチン</option>
	<option value="AW">アルバ</option>
	<option value="AL">アルバニア</option>
	<option value="AM">アルメニア</option>
	<option value="AG">アンティグア・バーブーダ</option>
	<option value="AD">アンドラ</option>
	<option value="YE">イエメン</option>
	<option value="GB">イギリス</option>
	<option value="IL">イスラエル</option>
	<option value="IT">イタリア</option>
	<option value="IQ">イラク</option>
	<option value="IR">イラン</option>
	<option value="IN">インド</option>
	<option value="ID">インドネシア</option>
	<option value="UG">ウガンダ</option>
	<option value="UA">ウクライナ</option>
	<option value="UZ">ウズベキスタン</option>
	<option value="UY">ウルグアイ</option>
	<option value="EC">エクアドル</option>
	<option value="EG">エジプト</option>
	<option value="EE">エストニア</option>
	<option value="ER">エリトリア</option>
	<option value="SV">エルサルバドル</option>
	<option value="AU">オーストラリア</option>
	<option value="AT">オーストリア</option>
	<option value="OM">オマーン</option>
	<option value="NL">オランダ</option>
	<option value="GH">ガーナ</option>
	<option value="GY">ガイアナ</option>
	<option value="KZ">カザフスタン</option>
	<option value="QA">カタール</option>
	<option value="CA">カナダ/アメリカ合衆国</option>
	<option value="GA">ガボン</option>
	<option value="CM">カメルーン</option>
	<option value="KR">韓国</option>
	<option value="GM">ガンビア</option>
	<option value="KH">カンボジア</option>
	<option value="GN">ギニア</option>
	<option value="GW">ギニアビサウ</option>
	<option value="CY">キプロス</option>
	<option value="CU">キューバ</option>
	<option value="GR">ギリシャ</option>
	<option value="KG">キルギス</option>
	<option value="GT">グアテマラ</option>
	<option value="GU">グアム</option>
	<option value="KW">クウェート</option>
	<option value="GD">グレナダ</option>
	<option value="HR">クロアチア</option>
	<option value="KY">ケイマン諸島</option>
	<option value="KE">ケニア</option>
	<option value="CI">コートジボワール</option>
	<option value="CR">コスタリカ</option>
	<option value="KM">コモロ</option>
	<option value="CO">コロンビア</option>
	<option value="CD">コンゴ民主共和国</option>
	<option value="SA">サウジアラビア</option>
	<option value="ZM">ザンビア</option>
	<option value="SL">シエラレオネ</option>
	<option value="JM">ジャマイカ</option>
	<option value="GE">ジョージア</option>
	<option value="SY">シリア</option>
	<option value="SG">シンガポール</option>
	<option value="ZW">ジンバブエ</option>
	<option value="CH">スイス</option>
	<option value="SE">スウェーデン</option>
	<option value="SD">スーダン</option>
	<option value="ES">スペイン</option>
	<option value="SR">スリナム</option>
	<option value="LK">スリランカ</option>
	<option value="SK">スロバキア</option>
	<option value="SI">スロベニア</option>
	<option value="SN">セネガル</option>
	<option value="RS">セルビア</option>
	<option value="KN">セントクリストファー・ネイビス</option>
	<option value="LC">セントルシア</option>
	<option value="TH">タイ</option>
	<option value="TW">台湾</option>
	<option value="TJ">タジキスタン</option>
	<option value="TZ">タンザニア</option>
	<option value="CZ">チェコ</option>
	<option value="TD">チャド</option>
	<option value="CF">中央アフリカ共和国</option>
	<option value="CN">中国</option>
	<option value="TN">チュニジア</option>
	<option value="CL">チリ</option>
	<option value="DK">デンマーク</option>
	<option value="DE">ドイツ</option>
	<option value="TG">トーゴ</option>
	<option value="DO">ドミニカ共和国</option>
	<option value="TT">トリニダード・トバゴ</option>
	<option value="TR">トルコ</option>
	<option value="NG">ナイジェリア</option>
	<option value="NI">ニカラグア</option>
	<option value="NE">ニジェール</option>
	<option value="NZ">ニュージーランド</option>
	<option value="NP">ネパール</option>
	<option value="NO">ノルウェー</option>
	<option value="HT">ハイチ</option>
	<option value="PK">パキスタン</option>
	<option value="PA">パナマ</option>
	<option value="BS">バハマ</option>
	<option value="PG">パプアニューギニア</option>
	<option value="BM">バミューダ</option>
	<option value="PW">パラオ</option>
	<option value="PY">パラグアイ</option>
	<option value="BB">バルバドス</option>
	<option value="PS">パレスチナ</option>
	<option value="HU">ハンガリー</option>
	<option value="BD">バングラデシュ</option>
	<option value="TL">東ティモール</option>
	<option value="FJ">フィジー</option>
	<option value="PH">フィリピン</option>
	<option value="FI">フィンランド</option>
	<option value="BT">ブータン</option>
	<option value="PR">プエルトリコ</option>
	<option value="BR">ブラジル</option>
	<option value="FR">フランス</option>
	<option value="PF">フランス領ポリネシア</option>
	<option value="BG">ブルガリア</option>
	<option value="BF">ブルキナファソ</option>
	<option value="BN">ブルネイ</option>
	<option value="BI">ブルンジ</option>
	<option value="VN">ベトナム</option>
	<option value="BJ">ベナン</option>
	<option value="VE">ベネズエラ</option>
	<option value="BY">ベラルーシ</option>
	<option value="PE">ペルー</option>
	<option value="BE">ベルギー</option>
	<option value="PL">ポーランド</option>
	<option value="BA">ボスニア・ヘルツェゴビナ</option>
	<option value="BW">ボツワナ</option>
	<option value="HK">香港</option>
	<option value="MH">マーシャル諸島</option>
	<option value="MO">マカオ</option>
	<option value="MG">マダガスカル</option>
	<option value="MW">マラウイ</option>
	<option value="ML">マリ</option>
	<option value="MT">マルタ</option>
	<option value="MY">マレーシア</option>
	<option value="ZA">南アフリカ</option>
	<option value="MM">ミャンマー</option>
	<option value="MX">メキシコ</option>
	<option value="MU">モーリシャス</option>
	<option value="MR">モーリタニア</option>
	<option value="MZ">モザンビーク</option>
	<option value="MC">モナコ</option>
	<option value="MV">モルディヴ</option>
	<option value="MA">モロッコ</option>
	<option value="MN">モンゴル</option>
	<option value="ME">モンテネグロ</option>
	<option value="JO">ヨルダン</option>
	<option value="LA">ラオス人民民主共和国</option>
	<option value="LV">ラトビア</option>
	<option value="LT">リトアニア</option>
	<option value="LY">リビア</option>
	<option value="LR">リベリア</option>
	<option value="RO">ルーマニア</option>
	<option value="LU">ルクセンブルク</option>
	<option value="RW">ルワンダ</option>
	<option value="LB">レバノン</option>
	<option value="RU">ロシア</option>
</select>
													</li>
													<li>
														
														<div class="numberRowWrap">
															<div class="block">
																<span class="visuallyHidden">国番号</span>
																<span class="countryCode viewInput js-countryCode"></span>
															</div>
															
															<div class="block"><label for="contactsSms:0:recipientSmsEachPersonSelection:flightStatusNotificationContactPointSmsDescription" class="visuallyHidden">電話番号</label><input id="contactsSms:0:recipientSmsEachPersonSelection:flightStatusNotificationContactPointSmsDescription" type="text" name="contactsSms:0:recipientSmsEachPersonSelection:flightStatusNotificationContactPointSmsDescription" class="telNumber" placeholder="例：9012345678" maxlength="30" aria-describedby="confirmTelNumberForRepresentative:0" />
															</div>
														</div>
													</li>
												</ul>
											</div>
										</li>
									</ul>
							</fieldset>
						</dd>
					</dl>
			</div>
	</div>
	</div>
	
	<div class="formInput articleLine">
		<h3>滞在中の連絡先(任意)
		</h3>
		<dl>
			
			<dt><label for="accommodationOne">現地連絡先1</label>
			</dt>
			<dd><input id="accommodationOne" type="text" name="accommodationOne" class="widthLarge" placeholder="例：09012345678 ABC HOTEL" maxlength="28" title="例：09012345678 ABC HOTEL" />
			</dd>
		</dl>
		<dl>
			
			<dt><label for="accommodationTwo">現地連絡先2</label>
			</dt>
			<dd><input id="accommodationTwo" type="text" name="accommodationTwo" class="widthLarge" placeholder="例：09012345678 ABC HOTEL" maxlength="28" title="例：09012345678 ABC HOTEL" />
			</dd>
		</dl>
	</div>
		</div>
		
		<p class="btnAreaSubmit btnArrowNext "><input id="next" type="submit" name="next" value="次へ" class="btnBase btnMainStream btnVerticalMain btnWidthVariable" onclick="return Asw.Dialog.getInstance('prebookConfirmDialog').toggle(event);return Asw.LoadingWindow.open(&quot;NORMAL&quot;, event)" />
		</p><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:2" value="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" autocomplete="off" />
</form><div id="cmnDynamicMessages" class="infoBoxSection">
				<dl class="infoBox importantInfo jsAccordionSwitch">
					<dt><strong>重要なご案内</strong></dt>
					<dd>
						<ul class="jsAccordionSwitchList" id="informationMessages">
								<li>個人情報の利用目的<a href="https://www.ana.co.jp/other/int/meta/0012.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="ANAプライバシーポリシー(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">ANAプライバシーポリシー<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>を必ずお読みいただき、内容に同意いただいた上で、お進みください。</li>
								<li><a href="https://www.ana.co.jp/other/int/meta/0058.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="Secure Flight Programについて(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">米国Secure Flightプログラム<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>に基づき、パスポート名、生年月日、性別などの情報は事前に米国へ通知いたします。</li>
								<li>入力したeメールアドレス、電話番号は入国に必要な情報として米国に通知される可能性があります。</li>
									<li class="jsHiddenFlg toggleContents">お名前のスペルがパスポート名と一致しているかご確認ください。異なる場合はインターネットではお取り扱いできません。<a href="https://www.ana.co.jp/other/int/meta/intbe0001.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="ANAお問い合わせ先(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">ANA<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>へ名前の変更をお申し出ください。</li>
									<li class="jsHiddenFlg toggleContents">二重国籍のお客様は、ご搭乗時に使用されるパスポートのお名前でご予約ください。<br />パスポートごとにお名前が異なる場合は、お客様ご自身で入国管理事務所・外務省・大使館などで各種条件をご確認のうえ、使用されるパスポートを決めてください。<br />なお、入国可否については国によって異なるため、お客様ご自身で各入国管理事務所などでご確認ください。</li>
									<li class="jsHiddenFlg toggleContents">旅行開始日時点で12歳以上の場合は大人、2歳以上12歳未満の場合は小児、2歳未満の場合は幼児の扱いとなります。なお、小児は大人と同マイル数が必要です。</li>
									<li class="jsHiddenFlg toggleContents">運航に関する情報をお客様へ通知するため、提携航空会社にもお客様のメールアドレスが提供されます。あらかじめご了承ください。</li>
									<li class="jsHiddenFlg toggleContents"><dl><dt>携帯電話へのメール送信をご希望のお客様へ</dt><dd>ドメイン指定受信をご利用の場合は、ana.co.jpとamadeus.comからのメールが受信できるようあらかじめ設定をお願いいたします。</dd></dl></li>
									<li class="jsHiddenFlg toggleContents">SMSはスマートフォンの番号に配信されます。一部の国ではSMSでのご案内をご利用いただけません。詳細は<a href="https://www.ana.co.jp/other/int/meta/0737.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="ショートメッセージ(SMS)でのご案内対象外の国について(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">ショートメッセージ(SMS)でのご案内対象外の国について<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>をご覧ください。</li>
									<li class="jsHiddenFlg toggleContents">ログインされている会員ご本人が搭乗しない場合は、再度フライト検索画面より「ログインされている会員ご本人は搭乗しない」のチェックボックスを選択してお手続きください。</li>
						</ul>
							<a href="#" class="toggleSwitch jsTriggerSwitch" role="button" aria-controls="informationMessages" aria-expanded="false"><img src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/image/toggle_open.png?7bec38a" alt="開閉ボタン" height="25" width="25" class="jsRollOver" />
							</a>
					</dd>
				</dl></div>
	<!--=========== /main =============-->
	</div>

	<!--=========== Script ============-->
	<script type="text/javascript">
	$(window).on('load', function() {
		// アコーディオンのメソッドと設定
		Asw.AccordionInfo('.jsAccordionSwitch', {
			'triggerSwitch': '.jsAccordionSwitch .jsTriggerSwitch',
			'listClass': '.jsAccordionSwitchList',
			'visibilityHidden': 'toggleContents'
		});
	});
	</script>
	<!--=========== Script ============-->
	<script type="text/javascript">
		//プラットフォーム判別（サマリーエリア追従用として）
		var Asw = Asw || {};
	
	  	Asw.ClientInfo = Asw.ClientInfo  || {};
	  	Asw.ClientInfo.DeviceType = Asw.ClientInfo.DeviceType || {};
	
	  	Asw.ClientInfo.DeviceType.PC = 'PC';
	  	Asw.ClientInfo.DeviceType.SP = 'SP';
	  	Asw.ClientInfo.DeviceType.TC = 'TC';
	
	  	Asw.ClientInfo.deviceType = Asw.ClientInfo.DeviceType.PC;
	
	 	if((navigator.userAgent.indexOf('Android') > 0 && navigator.userAgent.indexOf('Mobile') == -1) || navigator.userAgent.indexOf('iPad') > 0){
	    	Asw.ClientInfo.deviceType = Asw.ClientInfo.DeviceType.TC;
	  	}
	</script>
	<!--=========== /Script ============--><div id="summaryArea">
			<div class="flightSummaryArea">
						<div class="flightSummarySection">
							<h2>区間 1
							</h2>
							<p class="flightDate"><em>6</em>月<em class="hasLeft">24</em>日（火）
							</p>
							<div class="flightSummaryRoute">
									<div class="flightSummaryRouteSection">
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation1_1">
													<span>東京(成田)
													</span>
												</p>
												<p class="time">
													<span>17:00
													</span>
												</p>
											</div>
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation1_1">
													<span>シカゴ(ORD)
													</span>
												</p>
												<p class="time">
													<span>14:55
													</span>
												</p>
											</div>
										<p class="flightNumberClass">
												<span>NH012</span><span><img src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/image/flight_ana_summaryarea.png?717d3c0" alt="ANA運航便" height="11" width="41" /></span>
												<br />特典ビジネス
										</p>
									</div>
							</div>
						</div>
						<div class="flightSummarySection">
							<h2 class="midstream">区間 2
							</h2>
							<p class="flightDate"><em>7</em>月<em class="hasLeft">23</em>日（水）
							</p>
							<div class="flightSummaryRoute">
									<div class="flightSummaryRouteSection">
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation2_1">
													<span>ホノルル(オアフ島)
													</span>
												</p>
												<p class="time">
													<span>11:35
													</span>
												</p>
											</div>
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation2_1">
													<span>東京(成田)
													</span>
												</p>
												<p class="time">
													<span>14:50
																	<span>翌日
																	</span>
													</span>
												</p>
											</div>
										<p class="flightNumberClass">
												<span>NH183</span><span><img src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/image/flight_ana_summaryarea.png?717d3c0" alt="ANA運航便" height="11" width="41" /></span>
												<br />特典エコノミー
										</p>
									</div>
							</div>
						</div>
					<div class="total">
						<p class="passenger">大人 x1
						</p>
						<div class="totalArea">
					  		<dl class="mealAmountWrap">
									<dt>有料機内食サービス
									</dt>
									<dd><em class="mealAmount">0</em><span class="currencyCode">円</span>
									</dd>
							</dl>
									<div>
										<div class="mileageAndTotal">
												<dl class="requiredMileage">
													<dt>必要マイル
													</dt>
													<dd><em class="requiredMileage">74,000</em><span class="currencyCode">マイル</span>
													</dd>
												</dl>
												<dl>
													<dt>総額
													</dt>
													<dd><em class="awardTotalPayment">68,130</em>円
													</dd>
												</dl>
												<p class="includeFareAndFuelCost">※各種税金、燃油特別付加運賃等を含みます。
												</p>
										</div>
									</div>
						</div>
					</div>
				
			</div></div>
		<!--===== /cmnWrapper =====-->
		</div>
			
		<span id="message" class="visuallyHidden" role="alert" aria-live="assertive"></span>
		<span id="politeMessage" class="visuallyHidden" role="log" aria-live="polite"></span><script type="text/javascript">
		var Asw = Asw || {};
		Asw.BaseOutput = {"sessionID":"QIuH4Q3dHv","language":"ja","siteCatalystLanguageCode":"J","operationDate":"20250619","operationDateTime":"20250619200940","office":{"officeCode":"TYONH08ZZ","country2letterCode":"JP"},"bookingType":"A","pageID":"A04_P01","siteCatalystPageName":"INT_BE_AWARD_J_A04特典予約_P01旅客情報入力（必須情報）","device":{"deviceType":"PC"}};
	</script><div id="promotionArea">
		<!--===== promotionArea =====-->
		<!--===== /promotionArea =====--></div>
		<!--=========== /cmnContainer ============--></div><div id="cmnBaloonArea" aria-live="polite"></div><div id="cmnPopupArea" aria-live="polite"></div>

	

	<div id="cmnFooterWrapper">
		<!--=========== .cmnFooterWrapper ============-->
		<div id="cmnFooter">
			<!--===== cmnFooter =====-->
			<ul>
				<li><a href="https://www.ana.co.jp/other/int/meta/0012.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">プライバシーポリシー<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
					<li><a href="https://www.ana.co.jp/other/int/meta/0670.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">利用者情報の外部送信について<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
					</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/0052.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">サイト利用規約<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/be0238.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">推奨環境<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/0133.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">サイトマップ<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/0732.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">運送約款<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
					<li class="lastChild"><a href="https://www.ana.co.jp/other/int/meta/0556.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">ウェブアクセシビリティについて<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
					</li>
			</ul>
			<p class="blankText" aria-hidden="true"><img src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="" height="10" width="10" />外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。</p>
			<p class="copyright"><span lang="en" xml:lang="en">Copyright&#169;ANA</span>
			</p>
			<p class="starAllianceLogo"><a href="http://www.staralliance.com/ja/" target="_blank"><img src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/image/star_alliance_logo.png?0fe73b5" alt="A STAR ALLIANCE MEMBER" height="18" width="175" /><span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
			</p>
			<!--===== cmnFooter =====-->
		</div>
		<!--=========== /cmnFooterWrapper ============-->
	</div>

		
		<div id="cmnLoadingForAjax"><img src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/image/bg_modalWindow.png?717d3c0" alt="" height="0" width="0" />
			<div class="loadingArea">
				<p class="loadingImage"><img src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/image/loading.gif?717d3c0" alt="リクエストを処理しています...しばらくお待ちください。" height="33" width="33" />
				</p>
			</div>
		</div>
		
		
		<div id="popupAltSentence" style="display: none" aria-hidden="true">別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。</div>
	<div id="cmnSiteCatalyst" aria-hidden="true"><div id="cmnSiteCatalystParamArea"><script type="text/javascript">
				// ▼SiteCatalyst
				// SiteCatalyst code version: H.2.
				// Copyright 1997-2005 Omniture, Inc. More info available at http://www.omniture.com
				SiteCatalystReportSuites   = "INT";
				SiteCatalystCharSet        = "UTF-8";
				SiteCatalystChannel        = "BE_AWARD_J";
				SiteCatalystPageName       = "INT_BE_AWARD_J_A04\u7279\u5178\u4E88\u7D04_P01\u65C5\u5BA2\u60C5\u5831\u5165\u529B\uFF08\u5FC5\u9808\u60C5\u5831\uFF09";
				SiteCatalystERR            = "";
				SiteCatalystEventNum       = "";
				SiteCatalystPurchase       = "FALSE";
				SiteCatalystProductPrice   = "";
				SiteCatalystCurrencyCode   = "";
				SiteCatalystSeatAvailabilityINT   = "";
				SiteCatalystDepartDate     = "";
				SiteCatalystDepart         = "";
				SiteCatalystArrive         = "";
				SiteCatalystFlightNo       = "";
				SiteCatalystClass          = "";
				SiteCatalystFare           = "";
				SiteCatalystPurchaseID     = "";
				
				SiteCatalystLocater        = "";
				SiteCatalystPayment        = "";
				SiteCatalystIncome         = "";
				
				SiteCatalystPax            = "";
				SiteCatalystManipulateDay  = "";
				SiteCatalystCompartmentClass   = "";
				SiteCatalystOptionInfo     = "";
				SiteCatalystOutboundUpsellPrice   = "";
				SiteCatalystInboundUpsellPrice    = "";
				
				SiteCatalystSearchInput    = "";
				SiteCatalystSegment        = "";
				SiteCatalystOfficeCode     = "";
				SiteCatalystPassengerCount = "";
				SiteCatalystPassenger      = "";
				SiteCatalystAncillaryService = "";
				// End SiteCatalyst code version: H.2.
				// ▲SiteCatalyst
			</script></div><script type="text/javascript"><!-- if(navigator.appVersion.indexOf('MSIE')>=0)document.write(unescape('%3C')+'\!-'+'-') // --></script>
		<!--/DO NOT REMOVE/-->
		

		
		<!-- Google Tag Manager -->
		<script type="text/javascript" charset="Shift_JIS" src="https://www.ana.co.jp/common/js/GTMINTBE.js"></script>
		<!-- End Google Tag Manager -->
		<!-- Yahoo Tag Manager -->
		<script type="text/javascript" charset="Shift_JIS" src="https://www.ana.co.jp/common/js/YTMINTBE.js"></script>
		<!-- End Yahoo Tag Manager -->
		

	</div>

	

	<div class="mboxDefault"></div><script type="text/javascript">mboxCreate("ASW_common_A04_P01");</script>
			<script type="text/javascript" src="https://www.ana.co.jp/common/js/tealium/tealium.js"></script>
			<script type="text/javascript" src="//cdn.evgnet.com/beacon/allnipponairways/nonamcprd/scripts/evergage.min.js"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21e/international_asw/javax.faces.resource/jsf.js.xhtml?ln=javax.faces"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/script/jquery.mousewheel.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/script/jquery.jscrollpane.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/script/jquery-ui-1.10.4.custom.min.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/script/jquery.ui.touch-punch.min.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/script/asw-common.js?beb6a50"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/script/asw-common-pc.js?fd88d7d"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/script/asw-format.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/script/mask.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/script/loading.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/script/asw-loadingwindow-pc.js?9e233c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/script/asw-loadingwindow.js?51db600"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/script/dialog-pc.js?25d36a4"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/script/asw-dialog-sequencer.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/script/asw-event.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/script/asw-date-select.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/script/asw-calendar-pc.js?9b86b8a"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/script/asw-calendar.js?db624c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/script/heightLine.js?0e7a1ce"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/script/asw-summaryarea.js?b21c6d7"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/script/asw-fingerprints.js?99001fc"></script><script type="text/javascript">
			Asw.init("https://aswbe-i.ana.co.jp/rei21e/international_asw", "ja");
			Asw.getCalendarHolidayList = function() {return [20250721,20250811,20250915,20250923,20251013,20251103,20251123,20251124,20260101,20260112,20260211,20260223,20260320,20260429,20260503,20260504,20260505,20260506,20260720,20260811,20260921,20260922,20260923,20261012,20261103,20261123,20270101,20270111,20270211,20270223,20270321,20270322,20270429,20270503,20270504,20270505,20270719,20270811,20270920,20270923,20271011,20271103,20271123,20280101,20280110,20280211,20280223,20280320,20280429,20280503,20280504,20280505,20280717,20280811,20280918,20280922,20281009,20281103,20281123,20290101,20290108,20290211,20290212,20290223,20290320,20290429,20290430,20290503,20290504,20290505,20290716,20290811,20290917,20290923,20290924,20291008,20291103,20291123];}
			Asw.ClientInfo.deviceType = "PC";
			Asw.ClientInfo.mobileDeviceType = "UNKNOWN";
			Asw.ClientInfo.osVersion = "";
			Asw.Format.init("https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/{1}/{2}/{3}?{0}");
			Asw.overwriteTargetAttributeValue("false");
		</script><script type="text/javascript">
		
		var Asw = Asw || {};
		Asw.ContactInput = Asw.ContactInput || {};
		Asw.ContactInput.Const = {
			/** data-属性名「data-type」 @constant @type {String} @default 'data-type' */
			DATA_ATTRIBUTE_NAME_TYPE: 'data-type',
			/** data-属性名「data-sms-possible」 @constant @type {String} @default 'data-sms-possible' */
			DATA_ATTRIBUTE_NAME_SMS_POSSIBLE: 'data-sms-possible',
			/** data-属性名「data-country-code」 @constant @type {String} @default 'data-country-code' */
			DATA_ATTRIBUTE_NAME_COUNTRY_CODE: 'data-country-code',
			/** data-type属性値「sms」 @constant @type {String} @default 'sms' */
			DATA_TYPE_VALUE_SMS: 'sms'
		};

		
		Asw.ContactInput.countryInfoMap = {"IS":{"internationalCallingCode":"+354","smsPossible":false},"IE":{"internationalCallingCode":"+353","smsPossible":true},"AZ":{"internationalCallingCode":"+994","smsPossible":true},"AF":{"internationalCallingCode":"+93","smsPossible":true},"US":{"internationalCallingCode":"+1","smsPossible":true},"AE":{"internationalCallingCode":"+971","smsPossible":true},"DZ":{"internationalCallingCode":"+213","smsPossible":true},"AR":{"internationalCallingCode":"+54","smsPossible":true},"AW":{"internationalCallingCode":"+297","smsPossible":true},"AL":{"internationalCallingCode":"+355","smsPossible":true},"AM":{"internationalCallingCode":"+374","smsPossible":true},"AO":{"internationalCallingCode":"+244","smsPossible":false},"AG":{"internationalCallingCode":"+1","smsPossible":true},"AD":{"internationalCallingCode":"+376","smsPossible":true},"YE":{"internationalCallingCode":"+967","smsPossible":true},"GB":{"internationalCallingCode":"+44","smsPossible":true},"IL":{"internationalCallingCode":"+972","smsPossible":true},"IT":{"internationalCallingCode":"+39","smsPossible":true},"IQ":{"internationalCallingCode":"+964","smsPossible":true},"IR":{"internationalCallingCode":"+98","smsPossible":true},"IN":{"internationalCallingCode":"+91","smsPossible":true},"ID":{"internationalCallingCode":"+62","smsPossible":true},"UG":{"internationalCallingCode":"+256","smsPossible":true},"UA":{"internationalCallingCode":"+380","smsPossible":true},"UZ":{"internationalCallingCode":"+998","smsPossible":true},"UY":{"internationalCallingCode":"+598","smsPossible":true},"EC":{"internationalCallingCode":"+593","smsPossible":true},"EG":{"internationalCallingCode":"+20","smsPossible":true},"EE":{"internationalCallingCode":"+372","smsPossible":true},"SZ":{"internationalCallingCode":"+268","smsPossible":false},"ET":{"internationalCallingCode":"+251","smsPossible":false},"ER":{"internationalCallingCode":"+291","smsPossible":true},"SV":{"internationalCallingCode":"+503","smsPossible":true},"AU":{"internationalCallingCode":"+61","smsPossible":true},"AT":{"internationalCallingCode":"+43","smsPossible":true},"OM":{"internationalCallingCode":"+968","smsPossible":true},"NL":{"internationalCallingCode":"+31","smsPossible":true},"GH":{"internationalCallingCode":"+233","smsPossible":true},"CV":{"internationalCallingCode":"+238","smsPossible":false},"GY":{"internationalCallingCode":"+592","smsPossible":true},"KZ":{"internationalCallingCode":"+7","smsPossible":true},"QA":{"internationalCallingCode":"+974","smsPossible":true},"CA":{"internationalCallingCode":"+1","smsPossible":true},"GA":{"internationalCallingCode":"+241","smsPossible":true},"CM":{"internationalCallingCode":"+237","smsPossible":true},"KR":{"internationalCallingCode":"+82","smsPossible":true},"KH":{"internationalCallingCode":"+855","smsPossible":true},"GM":{"internationalCallingCode":"+220","smsPossible":true},"KP":{"internationalCallingCode":"+850","smsPossible":false},"MK":{"internationalCallingCode":"+389","smsPossible":false},"GN":{"internationalCallingCode":"+224","smsPossible":true},"GW":{"internationalCallingCode":"+245","smsPossible":true},"CY":{"internationalCallingCode":"+357","smsPossible":true},"CU":{"internationalCallingCode":"+53","smsPossible":true},"GR":{"internationalCallingCode":"+30","smsPossible":true},"KI":{"internationalCallingCode":"+686","smsPossible":false},"KG":{"internationalCallingCode":"+996","smsPossible":true},"GT":{"internationalCallingCode":"+502","smsPossible":true},"GU":{"internationalCallingCode":"+1","smsPossible":true},"KW":{"internationalCallingCode":"+965","smsPossible":true},"KY":{"internationalCallingCode":"+1","smsPossible":true},"GD":{"internationalCallingCode":"+1","smsPossible":true},"HR":{"internationalCallingCode":"+385","smsPossible":true},"KE":{"internationalCallingCode":"+254","smsPossible":true},"CI":{"internationalCallingCode":"+225","smsPossible":true},"CR":{"internationalCallingCode":"+506","smsPossible":true},"SA":{"internationalCallingCode":"+966","smsPossible":true},"KM":{"internationalCallingCode":"+269","smsPossible":true},"CO":{"internationalCallingCode":"+57","smsPossible":true},"CG":{"internationalCallingCode":"+242","smsPossible":false},"CD":{"internationalCallingCode":"+243","smsPossible":true},"ZM":{"internationalCallingCode":"+260","smsPossible":true},"SM":{"internationalCallingCode":"+378","smsPossible":false},"SL":{"internationalCallingCode":"+232","smsPossible":true},"DJ":{"internationalCallingCode":"+253","smsPossible":false},"JM":{"internationalCallingCode":"+1","smsPossible":true},"CH":{"internationalCallingCode":"+41","smsPossible":true},"GE":{"internationalCallingCode":"+995","smsPossible":true},"SE":{"internationalCallingCode":"+46","smsPossible":true},"SY":{"internationalCallingCode":"+963","smsPossible":true},"ES":{"internationalCallingCode":"+34","smsPossible":true},"SG":{"internationalCallingCode":"+65","smsPossible":true},"LK":{"internationalCallingCode":"+94","smsPossible":true},"SK":{"internationalCallingCode":"+421","smsPossible":true},"SI":{"internationalCallingCode":"+386","smsPossible":true},"ZW":{"internationalCallingCode":"+263","smsPossible":true},"SD":{"internationalCallingCode":"+249","smsPossible":true},"KN":{"internationalCallingCode":"+1","smsPossible":true},"SR":{"internationalCallingCode":"+597","smsPossible":true},"LC":{"internationalCallingCode":"+1","smsPossible":true},"SC":{"internationalCallingCode":"+248","smsPossible":false},"SN":{"internationalCallingCode":"+221","smsPossible":true},"SB":{"internationalCallingCode":"+677","smsPossible":false},"RS":{"internationalCallingCode":"+381","smsPossible":true},"TH":{"internationalCallingCode":"+66","smsPossible":true},"TW":{"internationalCallingCode":"+886","smsPossible":true},"TJ":{"internationalCallingCode":"+992","smsPossible":true},"TZ":{"internationalCallingCode":"+255","smsPossible":true},"CZ":{"internationalCallingCode":"+420","smsPossible":true},"TD":{"internationalCallingCode":"+235","smsPossible":true},"CF":{"internationalCallingCode":"+236","smsPossible":true},"TN":{"internationalCallingCode":"+216","smsPossible":true},"CL":{"internationalCallingCode":"+56","smsPossible":true},"TV":{"internationalCallingCode":"+688","smsPossible":false},"DK":{"internationalCallingCode":"+45","smsPossible":true},"DE":{"internationalCallingCode":"+49","smsPossible":true},"TG":{"internationalCallingCode":"+228","smsPossible":true},"DO":{"internationalCallingCode":"+1","smsPossible":true},"DM":{"internationalCallingCode":"+1","smsPossible":false},"TT":{"internationalCallingCode":"+1","smsPossible":true},"TM":{"internationalCallingCode":"+993","smsPossible":false},"TR":{"internationalCallingCode":"+90","smsPossible":true},"TO":{"internationalCallingCode":"+676","smsPossible":false},"NG":{"internationalCallingCode":"+234","smsPossible":true},"NR":{"internationalCallingCode":"+674","smsPossible":false},"NA":{"internationalCallingCode":"+264","smsPossible":false},"NI":{"internationalCallingCode":"+505","smsPossible":true},"BH":{"internationalCallingCode":"+973","smsPossible":false},"NE":{"internationalCallingCode":"+227","smsPossible":true},"PK":{"internationalCallingCode":"+92","smsPossible":true},"JP":{"internationalCallingCode":"+81","smsPossible":true},"BS":{"internationalCallingCode":"+1","smsPossible":true},"NZ":{"internationalCallingCode":"+64","smsPossible":true},"BM":{"internationalCallingCode":"+1","smsPossible":true},"NP":{"internationalCallingCode":"+977","smsPossible":true},"BB":{"internationalCallingCode":"+246","smsPossible":true},"NO":{"internationalCallingCode":"+47","smsPossible":true},"HT":{"internationalCallingCode":"+509","smsPossible":true},"BD":{"internationalCallingCode":"+880","smsPossible":true},"VA":{"internationalCallingCode":"+39","smsPossible":false},"PA":{"internationalCallingCode":"+507","smsPossible":true},"VU":{"internationalCallingCode":"+678","smsPossible":false},"PG":{"internationalCallingCode":"+675","smsPossible":true},"PW":{"internationalCallingCode":"+680","smsPossible":true},"PY":{"internationalCallingCode":"+595","smsPossible":true},"PS":{"internationalCallingCode":"+970","smsPossible":true},"BR":{"internationalCallingCode":"+55","smsPossible":true},"HU":{"internationalCallingCode":"+36","smsPossible":true},"TL":{"internationalCallingCode":"+670","smsPossible":true},"BG":{"internationalCallingCode":"+359","smsPossible":true},"BF":{"internationalCallingCode":"+226","smsPossible":true},"BN":{"internationalCallingCode":"+673","smsPossible":true},"FJ":{"internationalCallingCode":"+679","smsPossible":true},"PH":{"internationalCallingCode":"+63","smsPossible":true},"FI":{"internationalCallingCode":"+358","smsPossible":true},"BT":{"internationalCallingCode":"+975","smsPossible":true},"BY":{"internationalCallingCode":"+375","smsPossible":true},"BZ":{"internationalCallingCode":"+501","smsPossible":false},"PR":{"internationalCallingCode":"+1","smsPossible":true},"BE":{"internationalCallingCode":"+32","smsPossible":true},"BA":{"internationalCallingCode":"+387","smsPossible":true},"FR":{"internationalCallingCode":"+33","smsPossible":true},"BO":{"internationalCallingCode":"+591","smsPossible":false},"PF":{"internationalCallingCode":"+689","smsPossible":true},"BI":{"internationalCallingCode":"+257","smsPossible":true},"VN":{"internationalCallingCode":"+84","smsPossible":true},"BJ":{"internationalCallingCode":"+229","smsPossible":true},"VE":{"internationalCallingCode":"+58","smsPossible":true},"PE":{"internationalCallingCode":"+51","smsPossible":true},"PL":{"internationalCallingCode":"+48","smsPossible":true},"BW":{"internationalCallingCode":"+267","smsPossible":true},"PT":{"internationalCallingCode":"+351","smsPossible":false},"HK":{"internationalCallingCode":"+852","smsPossible":true},"HN":{"internationalCallingCode":"+504","smsPossible":false},"MH":{"internationalCallingCode":"+692","smsPossible":true},"MO":{"internationalCallingCode":"+853","smsPossible":true},"MG":{"internationalCallingCode":"+261","smsPossible":true},"MW":{"internationalCallingCode":"+265","smsPossible":true},"ML":{"internationalCallingCode":"+223","smsPossible":true},"MT":{"internationalCallingCode":"+356","smsPossible":true},"MY":{"internationalCallingCode":"+60","smsPossible":true},"FM":{"internationalCallingCode":"+691","smsPossible":false},"MM":{"internationalCallingCode":"+95","smsPossible":true},"MX":{"internationalCallingCode":"+52","smsPossible":true},"MU":{"internationalCallingCode":"+230","smsPossible":true},"MR":{"internationalCallingCode":"+222","smsPossible":true},"MZ":{"internationalCallingCode":"+258","smsPossible":true},"MC":{"internationalCallingCode":"+377","smsPossible":true},"MV":{"internationalCallingCode":"+960","smsPossible":true},"MD":{"internationalCallingCode":"+373","smsPossible":false},"RO":{"internationalCallingCode":"+40","smsPossible":true},"MA":{"internationalCallingCode":"+212","smsPossible":true},"MN":{"internationalCallingCode":"+976","smsPossible":true},"ME":{"internationalCallingCode":"+382","smsPossible":true},"JO":{"internationalCallingCode":"+962","smsPossible":true},"LA":{"internationalCallingCode":"+856","smsPossible":true},"RU":{"internationalCallingCode":"+7","smsPossible":true},"LV":{"internationalCallingCode":"+371","smsPossible":true},"LT":{"internationalCallingCode":"+370","smsPossible":true},"LY":{"internationalCallingCode":"+218","smsPossible":true},"GQ":{"internationalCallingCode":"+240","smsPossible":false},"LI":{"internationalCallingCode":"+423","smsPossible":false},"LR":{"internationalCallingCode":"+231","smsPossible":true},"CN":{"internationalCallingCode":"+86","smsPossible":true},"LU":{"internationalCallingCode":"+352","smsPossible":true},"ZA":{"internationalCallingCode":"+27","smsPossible":true},"RW":{"internationalCallingCode":"+250","smsPossible":true},"LS":{"internationalCallingCode":"+266","smsPossible":false},"LB":{"internationalCallingCode":"+961","smsPossible":true}};

		$(function () {
			contactInfoInit();
		});
		
		function contactInfoInit() {
			
			
			Asw.get('representativeTelType').find('option[value="M1"]').attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_TYPE,
					Asw.ContactInput.Const.DATA_TYPE_VALUE_SMS);
			Asw.get('representativeTelCountry').children('option').each(function(i,option){
				var countryInfo = Asw.ContactInput.countryInfoMap[option.value];
				if (countryInfo === void 0) {
					
					return true;
				}
				$(option).attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_SMS_POSSIBLE, countryInfo.smsPossible);
				$(option).attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_COUNTRY_CODE, countryInfo.internationalCallingCode);
			});
			
			var smsCountrySelectSelector = 'select.js-countrySelect:not(#representativeTelCountry)'
			$(smsCountrySelectSelector).eq(0).children('option').each(function(i,option){
				var countryInfo = Asw.ContactInput.countryInfoMap[option.value];
				if (countryInfo === void 0) {
					
					return true;
				}
				$(smsCountrySelectSelector + ' option:nth-child(' + (i+1) + ')').attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_COUNTRY_CODE,
						countryInfo.internationalCallingCode);
			});
			
			var emergencyCountrySelectSelector = 'select.passengerEmergencyCountry.js-countrySelect:not(#representativeTelCountry)'
			$(emergencyCountrySelectSelector).eq(0).children('option').each(function(i,option){
				var countryInfo = Asw.ContactInput.countryInfoMap[option.value];
				if (countryInfo === void 0) {
					
					return true;
				}
				$(emergencyCountrySelectSelector + ' option:nth-child(' + (i+1) + ')').attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_COUNTRY_CODE,
						countryInfo.internationalCallingCode);
			});
		}
	</script><script type="text/javascript">
		var isInit = true;
		function changeTelType(_this, telNoInputId) {
			var $telNoInput = Asw.get(telNoInputId);
			var $telNoKind = Asw.get("representativeTelType");
			var telNo = Asw.getValueComparedToPlaceholder(telNoInputId);
			var telNoCountry = Asw.get("representativeTelCountry").val();

			if(true && !isInit) {
				if(telInputCheck(telNo, telNoCountry)) {
					if (_this.value == "M1") {
						telNo = exceptDomesticPrefix("09042504176", telNoCountry);
					} else if (_this.value == "B1") {
						telNo = exceptDomesticPrefix("03-5405-2172", telNoCountry);
					} else {
						telNo = exceptDomesticPrefix("090-4250-4176", telNoCountry);
					}
					$telNoInput.val(telNo);
				}
			}
			isInit = false;
		}
		
		function telInputCheck(telNo, telNoCountry) {
	        checkResult = false;
	        
	        var amcHndyTelNo = exceptDomesticPrefix("09042504176", telNoCountry);
	        var amcCmpTelNo = exceptDomesticPrefix("03-5405-2172", telNoCountry);
	        var amcTelNo = exceptDomesticPrefix("090-4250-4176", telNoCountry);
	
	        if(telNo == "" || telNo == amcHndyTelNo || telNo == amcCmpTelNo || telNo == amcTelNo) {
	        	checkResult = true;
	        }
	        return checkResult;
    	}
    	
    	function exceptDomesticPrefix(telNo, telNoCountry) {
    		if(telNoCountry == 'JP') {
    			if(telNo.substr(0, 1) === '0') {
    				return telNo.slice(1);
    			}
    		}
    		return telNo;
    	}
	</script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/script/asw-customerinfomationform.js?6e91953"></script><script type="text/javascript">
		$(document).ready(function(){
				Asw.enableTextWhenCheckBox('.cascadedSwitch');
				Asw.notPasteInput('.jsNotPasteInput');
			$(function(){
				Asw.alignHeightToParentTdElement( '.passengerInfoInputArea' );
				
					Asw.alignHeightToParentTdElement( '.passengerInfoInputText' );
				
				
				$('select.js-countrySelect').trigger('change');
			});
		});
	</script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21e/international_asw/contents/cmn/script/asw-session-keeper.js?6ec0730"></script><script type="text/javascript">
			$(document).ready(function() {
				Asw.SessionKeeper.initAndStart("sessionKeeperContainer", "520", "570", "PC");
			});
			</script><script type="text/javascript">
		Asw.addAjaxCallback(function(data) {
			// アコーディオンのメソッドと設定 
			if($('.jsAccordionSwitch .jsTriggerSwitch').length > 0){
				var events = $._data($('.jsAccordionSwitch .jsTriggerSwitch').get(0), "events");
				if(events === undefined || events.click === undefined) {
					Asw.AccordionInfo('.jsAccordionSwitch', {
						'triggerSwitch': '.jsAccordionSwitch .jsTriggerSwitch',
						'listClass': '.jsAccordionSwitchList',
						'visibilityHidden': 'toggleContents'
					});
				}
			}
		});
	</script><script type="text/javascript">
	Asw.Price = Asw.Price || {};
	// 整数の区切り文字
	Asw.Price.separateString = ',';
	
	// 整数の区切り桁数
	Asw.Price.separateDigitNumber = 3;
	
	// 小数点
	Asw.Price.decimalPointString = '.';
	
	// 小数点以降の有効桁数
	Asw.Price.significantDigit = 0;
	
	// 表示金額の単位
	Asw.Price.currencyUnit = '円';
	</script><script type="text/javascript">
			$(document).ready(function() {
				$('#preLoadingArea').css('visibility', 'hidden');
			});
		</script><noscript><img src="https://aswbe-i.ana.co.jp/akam/13/pixel_4d07ccdb?a=dD1jNjIwMDFlM2MyYjQ2ZGYwNDI5ZGUxNzk5ODM3NTQwMWIzZTQxNmQyJmpzPW9mZg==" style="visibility: hidden; position: absolute; left: -999px; top: -999px;" /></noscript><script type="text/javascript"  src="/RcS9yzCRgrNnM/gY/v0Wf9fQwtpdQ/7NE3X2ruOE2SL9YE/HyNpQmYB/RWt/XOHcnHAU"></script></body>
</html>