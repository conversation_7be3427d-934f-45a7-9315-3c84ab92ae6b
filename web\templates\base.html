<!doctype html>
<html lang="zh">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="icon" type="image/jpeg" href="{{ url_for('static', filename='logo.jpeg') }}">
    <title>{{ title | default('va-ana外航推送') }}</title>
    <!-- 引入 Bootstrap CSS (或你选择的其他 CSS 框架) -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- 可以在这里添加自定义 CSS 文件的链接 -->
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
    <style>
        /* 基础样式 */
        body { font-family: sans-serif; padding-top: 4.5rem; }
        .navbar { margin-bottom: 1rem; }
        /* 高亮样式 */
        .table-highlight td {
            color: #0089ba !important; 
        }
        /* 历史记录提示框样式 (基础) */
        .history-tooltip {
            position: absolute;
            background-color: #333;
            color: #fff;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 0.9em;
            white-space: pre-wrap; /* 允许换行 */
            z-index: 1000;
            display: none; /* 默认隐藏 */
            max-width: 300px;
            max-height: 200px;
            overflow-y: auto;
        }
        /* 表格固定布局 */
        .table-fixed { table-layout: fixed; width: 100%; margin: 0 auto; }
        /* 列宽设置: 第一二列18%，第3-5列8%，第6列40% *
        /* 表格列居中，首列左对齐，尾列右对齐，禁止换行，统一间距 */
        .table-fixed td { text-align: center; padding: .11rem .22rem; white-space: nowrap; }
        .table-fixed td:first-child { text-align: left; }
        .table-fixed td:last-child { text-align: right; }
    </style>
</head>
<body>

<nav class="navbar navbar-expand-md navbar-dark fixed-top bg-dark">
    <div class="container-fluid">
        <a class="navbar-brand" href="{{ url_for('index') }}">
            <img src="{{ url_for('static', filename='logo.jpeg') }}" alt="Logo" style="height:1.5rem;">
        </a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarCollapse" aria-controls="navbarCollapse" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarCollapse">
            <ul class="navbar-nav me-auto mb-2 mb-md-0">
                <li class="nav-item">
                    <a class="nav-link {% if request.endpoint == 'americas_routes' %}active{% endif %}" href="{{ url_for('americas_routes') }}">美洲航线</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if request.endpoint == 'china_japan_routes' %}active{% endif %}" href="{{ url_for('china_japan_routes') }}">中日航线</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if request.endpoint == 'disappeared_routes' %}active{% endif %}" href="{{ url_for('disappeared_routes') }}">消失航线</a>
                </li>
            </ul>
        </div>
    </div>
</nav>

<main class="container">
    {% block content %}{% endblock %}
</main>

<!-- 引入 Bootstrap JS 和 Popper.js -->
<script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.7/dist/umd/popper.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.min.js"></script>
<!-- 引入 jQuery (如果需要用于 AJAX) -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.socket.io/4.5.0/socket.io.min.js"></script>
<!-- 可以在这里添加自定义 JS 文件的链接 -->
{% block scripts %}{% endblock %}

</body>
</html>