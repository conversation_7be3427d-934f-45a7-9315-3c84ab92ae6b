// 格式化后的全日空会话保持JavaScript代码
Asw.SessionKeeper = {
    _id: null,
    _extension_timer: null,
    _expired_timer: null,
    _counter: 0,
    _sessionKeepTime: 540,      // 540秒 = 9分钟
    _sessionTimeout: 590,       // 590秒 = 9分50秒
    _deviceType: "PC",
    _expiredDialogId: "extendSessionExpiredDialog",
    _timeoutDialogId: "showSessionExpiredDialog",
    _opener: null,
    _applicationRoot: Asw.applicationRoot,

    _init: function(e, c, a, b) {
        var d = this;
        d._id = e;
        d._counter = 0;
        d._sessionKeepTime = c;     // 从参数覆盖默认值
        d._sessionTimeout = a;      // 从参数覆盖默认值
        d._deviceType = b;
        
        // 绑定表单提交事件
        $("form:not([target], [enctype='multipart/form-data']), form[target='_top']:not([enctype='multipart/form-data']), form[target='_self']:not([enctype='multipart/form-data'])").submit(function() {
            d._end()
        });
        
        // 绑定链接点击事件
        $("a:not([target], [href^='#'], [href^='mailto'], [href^='javascript:']), a[target='_top'], a[target='_self']").click(function() {
            d._end()
        });
        
        // 其他事件绑定...
        Asw.addAjaxCallback(function(f) {
            if (d._isSessionKeepingRequest(f)) {
                return
            }
            switch (f.status) {
                case "begin":
                    d._counter = 0;
                    d._end();
                    break;
                case "success":
                    d._start();
                    break
            }
        })
    },

    resetSessionKeep: function() {
        var a = this;
        a._end();
        a._counter = 0;
        a._start()
    },

    _start: function(a) {
        var c = this;
        if (c._deviceType !== "SP") {
            var b = {
                "keyPermission": {"escapeKey": false},
                "fixedElement": "#displayExpireModalArea"
            };
            
            // 设置延长对话框定时器
            c._extension_timer = setTimeout(function() {
                Asw.Dialog.getInstance(c._expiredDialogId).toggle(a, b);
                c._opener = Asw.Dialog.getInstance(c._expiredDialogId).opener
            }, c._sessionKeepTime * 1000);
            
            // 设置过期定时器
            c._expired_timer = setTimeout(function() {
                var d = Asw.Dialog.getInstance(c._expiredDialogId).opener;
                Asw.Dialog.getInstance(c._expiredDialogId).close(a, true);
                Asw.Dialog.getInstance(c._timeoutDialogId).toggle(a, b);
                Asw.Dialog.getInstance(c._timeoutDialogId).opener = c._opener
            }, c._sessionTimeout * 1000)
        }
        // 移动端逻辑省略...
    },

    _closeExpiredDialog: function() {
        var a = this;
        a._closeDialog(a._expiredDialogId)
    },

    closeTimeoutDialog: function() {
        var a = this;
        a._closeDialog(a._timeoutDialogId)
    },

    _closeDialog: function(a) {
        if (this._deviceType !== "SP") {
            Asw.Dialog.getInstance(a).close()
        } else {
            Asw.Dialog.getInstance(a).closeFloatingWindow()
        }
    },

    _end: function() {
        var a = this;
        a._clearTimeout(a._extension_timer);
        a._clearTimeout(a._expired_timer)
    },

    initAndStart: function(d, c, a, b) {
        this._init(d, c, a, b);
        this._start()
    },

    _isSessionKeepingRequest: function(a) {
        return a.source.id === this._id + ":cmnSessionKeepingButton"
    },

    doSessionKeep: function(b) {
        var c = this;
        var a = b.status;
        switch (a) {
            case "begin":
                Asw.LoadingWindow.changeOpenerForAjax(c._opener);
                c._closeExpiredDialog();
                c._clearTimeout(c._expired_timer);
                break;
            case "complete":
                c._sessionKeepCountup();
                if (c._counter < 9) {
                    c._start()  // 重新开始计时
                }
                break
        }
    },

    _sessionKeepCountup: function() {
        this._counter++
    },

    _clearTimeout: function(a) {
        if (a != null) {
            clearTimeout(a)
        }
    }
};

// 初始化调用示例（从HAR文件中看到的）：
// Asw.SessionKeeper.initAndStart("sessionKeeperContainer", "520", "570", "PC");
