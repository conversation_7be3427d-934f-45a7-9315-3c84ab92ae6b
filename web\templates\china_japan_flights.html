{% extends 'base.html' %}

{% block content %}
<div class="row" data-highlight-minutes="{{ highlight_minutes|default(0) }}"> {# Set default 0 for China/Japan #}
    {# --- 左栏：东京出发 (HND -> China/Japan) --- #}
    <div class="col-md-6" id="outbound-column">
        {# 移除 <h2>东京 → 中日</h2> #}
        {% if sorted_outbound_groups %}
            {% for destination, flights in sorted_outbound_groups %}
            {# Wrap group in a div with data-city #}
            <div data-city="{{ destination }}">
                <h6 class="fw-bold mt-0 mb-1 {{ 'text-muted' if destination == 'PEK' }}">{{ destination }}</h6>
                {# Only render table if flights exist for this group #}
                {% if flights %}
                <div class="table-responsive mb-2">
                    <table class="table table-hover table-sm">
                        <tbody id="tbody-outbound-{{ destination }}">
                            {# RESTORE JINJA LOOP START #}
                            {% for flight in flights %}
                            {# 中日航线不高亮 #}
                            {% set highlight_class = '' %}
                            {# History Key and Row ID #}
                            {% set flight_key = flight.date ~ '_' ~ flight.origin ~ '_' ~ flight.destination ~ '_' ~ flight.flight_numbers ~ '_' ~ flight.cabin_class %}
                            {% set row_id = 'flight-' ~ flight_key | replace('[^a-zA-Z0-9-_]', '-') %}

                            {# 时间戳解析与格式化 #}
                            {% set timestamp_str = flight.last_updated_timestamp %}
                            {% set display_timestamp = timestamp_str %} {# Default display value #}
                            {% if timestamp_str %}
                                {% set last_updated = None %}
                                {% set parsed = False %}
                                {% if 'T' in timestamp_str %}
                                    {% set timestamp_to_parse = timestamp_str.split('.')[0] %}
                                    {% set last_updated = datetime.strptime(timestamp_to_parse, '%Y-%m-%dT%H:%M:%S') %}
                                    {% set parsed = True %}
                                {% else %}
                                    {% set last_updated = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S') %}
                                    {% set parsed = True %}
                                {% endif %}
                                {% if parsed and last_updated %}
                                    {% set display_timestamp = last_updated.strftime('%Y-%m-%d %H:%M:%S') %}
                                {% endif %}
                            {% endif %}

                            <tr id="{{ row_id }}" class="{{ highlight_class }}" data-history-key="{{ flight_key }}">
                                <td>{{ flight.date }}</td>
                                <td>{{ flight.origin }}→{{ flight.destination }}</td>
                                <td>{{ flight.flight_numbers }}</td>
                                <td>{{ flight.max_seats }}</td>
                                <td>{{ flight.current_seats }}</td>
                                <td>{{ display_timestamp }}</td> {# 使用处理后的时间戳 #}
                            </tr>
                            {% endfor %}
                            {# RESTORE JINJA LOOP END #}
                        </tbody>
                    </table>
                </div>
                {% endif %} {# End if flights #}
            </div> {# End of group div #}
            {% endfor %}
        {% else %}
            <p>没有找到东京出发到中日的航班数据。</p>
        {% endif %}
    </div>

    {# --- 右栏：到达东京 (China/Japan -> HND) --- #}
    <div class="col-md-6" id="inbound-column">
        {# 移除 <h2>中日 → 东京</h2> #}
        {% if sorted_inbound_groups %}
            {% for origin, flights in sorted_inbound_groups %}
             {# Wrap group in a div with data-city #}
            <div data-city="{{ origin }}">
                <h6 class="fw-bold mt-0 mb-1 {{ 'text-muted' if origin == 'PEK' }}">{{ origin }}</h6>
                {# Only render table if flights exist for this group #}
                {% if flights %}
                <div class="table-responsive mb-2">
                    <table class="table table-hover table-sm">
                         <tbody id="tbody-inbound-{{ origin }}">
                            {# RESTORE JINJA LOOP START #}
                            {% for flight in flights %}
                            {# 中日航线不高亮 #}
                            {% set highlight_class = '' %}
                             {# History Key and Row ID #}
                            {% set flight_key = flight.date ~ '_' ~ flight.origin ~ '_' ~ flight.destination ~ '_' ~ flight.flight_numbers ~ '_' ~ flight.cabin_class %}
                            {% set row_id = 'flight-' ~ flight_key | replace('[^a-zA-Z0-9-_]', '-') %}

                            {# 时间戳解析与格式化 #}
                             {% set timestamp_str = flight.last_updated_timestamp %}
                             {% set display_timestamp = timestamp_str %} {# Default display value #}
                             {% if timestamp_str %}
                                 {% set last_updated = None %}
                                 {% set parsed = False %}
                                 {% if 'T' in timestamp_str %}
                                     {% set timestamp_to_parse = timestamp_str.split('.')[0] %}
                                     {% set last_updated = datetime.strptime(timestamp_to_parse, '%Y-%m-%dT%H:%M:%S') %}
                                     {% set parsed = True %}
                                 {% else %}
                                     {% set last_updated = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S') %}
                                     {% set parsed = True %}
                                 {% endif %}
                                 {% if parsed and last_updated %}
                                     {% set display_timestamp = last_updated.strftime('%Y-%m-%d %H:%M:%S') %}
                                 {% endif %}
                             {% endif %}

                            <tr id="{{ row_id }}" class="{{ highlight_class }}" data-history-key="{{ flight_key }}">
                                <td>{{ flight.date }}</td>
                                <td>{{ flight.origin }}→{{ flight.destination }}</td>
                                <td>{{ flight.flight_numbers }}</td>
                                <td>{{ flight.max_seats }}</td>
                                <td>{{ flight.current_seats }}</td>
                                <td>{{ display_timestamp }}</td> {# 使用处理后的时间戳 #}
                            </tr>
                            {% endfor %}
                            {# RESTORE JINJA LOOP END #}
                        </tbody>
                    </table>
                </div>
                {% endif %} {# End if flights #}
             </div> {# End of group div #}
            {% endfor %}
        {% else %}
            <p>没有找到中日到达东京的航班数据。</p>
        {% endif %}
    </div>
</div>

{# 历史记录提示框容器 #}
<div id="history-tooltip" class="history-tooltip"></div>

{% endblock %}

{% block scripts %}
{# Socket.IO and jQuery should be loaded in base.html #}
<script>
$(function() { // 使用 $(function() {}) 确保 DOM 加载完毕
    // --- SocketIO 连接 ---
    var socket = io.connect(location.protocol + '//' + document.domain + ':' + location.port);
    socket.on('connect', function() { console.log('WebSocket 已连接 (China/Japan)'); }); // 恢复简单连接日志
    socket.on('disconnect', function() { console.log('WebSocket 已断开 (China/Japan)'); });
    socket.on('connect_error', (err) => { console.error('WebSocket 连接错误 (China/Japan): ', err); });

    // 从包含 .row 的元素读取高亮分钟数 (中日默认为 0)
    const highlightMinutesElement = document.querySelector('.row');
    const HIGHLIGHT_MINUTES = parseInt(highlightMinutesElement ? highlightMinutesElement.dataset.highlightMinutes : '0', 10);

    const CHINA_JAPAN_CITIES = ['PEK', 'PVG', 'SHA', 'HKG', 'TAO', 'SZX', 'CAN', 'NRT']; // 定义中日城市列表
    const AMERICAS_CITIES = ['JFK', 'LAX', 'ORD', 'SFO', 'IAD', 'IAH', 'YVR', 'SEA']; // 定义美洲城市列表

    // Function to parse timestamp string into display format
    function formatDisplayTimestamp(timestampStr) {
        if (!timestampStr) return 'N/A';
        let displayTimestamp = timestampStr;
        try {
            let dt;
            if (timestampStr.includes('T')) {
                dt = new Date(timestampStr + (timestampStr.match(/[+-]\\d{2}:?\\d{2}|Z$/) ? '' : 'Z'));
            } else {
                dt = new Date(timestampStr.replace(/-/g, '/'));
            }
            if (!isNaN(dt)) {
                const pad = (num) => num.toString().padStart(2, '0');
                displayTimestamp = `${dt.getFullYear()}-${pad(dt.getMonth() + 1)}-${pad(dt.getDate())} ${pad(dt.getHours())}:${pad(dt.getMinutes())}:${pad(dt.getSeconds())}`;
            }
        } catch (e) {
            console.warn("Error parsing display timestamp:", timestampStr, e);
        }
        return displayTimestamp;
    }

    // Helper function to parse timestamp and check highlight (always false for China/Japan if HIGHLIGHT_MINUTES is 0)
    function shouldHighlight(timestampStr, highlightMinutes) {
        if (!timestampStr || highlightMinutes <= 0) return false;
        try {
            let dt;
            if (timestampStr.includes('T')) {
                dt = new Date(timestampStr + (timestampStr.match(/[+-]\\d{2}:?\\d{2}|Z$/) ? '' : 'Z'));
            } else {
                dt = new Date(timestampStr.replace(/-/g, '/'));
            }
            if (isNaN(dt.getTime())) {
                console.warn("Highlight check: Invalid date parsed from:", timestampStr);
                return false;
            }
            const now = new Date();
            const diffMinutes = (now - dt) / (1000 * 60);
            return diffMinutes <= highlightMinutes;
        } catch (e) {
            console.error("Highlight check: Error parsing date:", timestampStr, e);
            return false;
        }
    }

    // --- SocketIO Update Handler ---
    socket.on('update_data', function(msg) {
        if (!msg || !msg.data) return;
        const flightsToUpdate = Array.isArray(msg.data) ? msg.data : [msg.data];

        flightsToUpdate.forEach(flightData => {
            // Filter: Only process China/Japan routes (不再处理美洲航线)
            const origin = flightData.origin;
            const destination = flightData.destination;
            // 只处理中日城市相关的航线，不再处理美洲城市
            if (!(CHINA_JAPAN_CITIES.includes(origin) || CHINA_JAPAN_CITIES.includes(destination))) {
                return; // Skip irrelevant routes
            }
            // 如果目的地是美洲城市，也跳过
            if (AMERICAS_CITIES.includes(destination)) {
                return; // Skip Americas destinations
            }
            updateOrAddFlightRow(flightData);
        });
    });

    // Function to update or add a row
    function updateOrAddFlightRow(flightData) {
        const flightKey = `${flightData.date}_${flightData.origin}_${flightData.destination}_${flightData.flight_numbers}_${flightData.cabin_class}`;
        const rowId = `flight-${flightKey.replace(/[^a-zA-Z0-9-_]/g, '-')}`;
        let row = document.getElementById(rowId);

        // Determine target column and city
        let targetColumnId = null;
        let targetTbodyId = null;
        let targetCity = null;

        if ((flightData.origin === 'HND' || flightData.origin === 'NRT') &&
            CHINA_JAPAN_CITIES.includes(flightData.destination)) { // Outbound - 只处理中日城市
            targetColumnId = 'outbound-column';
            targetCity = flightData.destination;
            targetTbodyId = `tbody-outbound-${targetCity}`;
        } else if (CHINA_JAPAN_CITIES.includes(flightData.origin) && (flightData.destination === 'HND' || flightData.destination === 'NRT')) { // Inbound
            targetColumnId = 'inbound-column';
            targetCity = flightData.origin;
            targetTbodyId = `tbody-inbound-${targetCity}`;
        } else {
            console.warn("Skipping irrelevant flight data for China/Japan page:", flightData);
            return;
        }

        const displayTimestamp = formatDisplayTimestamp(flightData.last_updated_timestamp);
        // Highlight is always false for China/Japan as HIGHLIGHT_MINUTES is 0
        const highlight = shouldHighlight(flightData.last_updated_timestamp, HIGHLIGHT_MINUTES);

        if (row) { // Row exists, update it
            row.cells[3].textContent = flightData.max_seats !== undefined ? flightData.max_seats : '';
            row.cells[4].textContent = flightData.current_seats !== undefined ? flightData.current_seats : '';
            row.cells[5].textContent = displayTimestamp;
            // No need to explicitly remove highlight class if it's never added

        } else { // Row does not exist, create and add it
            let targetTbody = document.getElementById(targetTbodyId);
            const columnElement = document.getElementById(targetColumnId);

            // If tbody doesn't exist, create the group structure
            if (!targetTbody && columnElement) {
                console.log(`Creating new group for city: ${targetCity} in column ${targetColumnId}`);
                // Find the container div for the column first
                const columnDiv = document.getElementById(targetColumnId);
                if (!columnDiv) { console.error("Target column not found: ", targetColumnId); return; }

                // Create the group container
                const newGroupContainer = document.createElement('div');
                newGroupContainer.dataset.city = targetCity;

                const newH6 = document.createElement('h6');
                newH6.className = 'fw-bold mt-0 mb-1';
                if (targetCity === 'PEK') newH6.classList.add('text-muted');
                newH6.textContent = targetCity;
                newGroupContainer.appendChild(newH6);

                 // Create table structure only if flights will be added (this check prevents empty tables initially)
                const newTableDiv = document.createElement('div');
                newTableDiv.className = 'table-responsive mb-2';
                const newTable = document.createElement('table');
                newTable.className = 'table table-hover table-sm';
                targetTbody = document.createElement('tbody');
                targetTbody.id = targetTbodyId;

                newTable.appendChild(targetTbody);
                newTableDiv.appendChild(newTable);
                newGroupContainer.appendChild(newTableDiv);

                // Append the new group container to the column
                columnDiv.appendChild(newGroupContainer);
            }

            // Add the new row to the tbody
            if (targetTbody) {
                row = targetTbody.insertRow(-1);
                row.id = rowId;
                row.dataset.historyKey = flightKey;
                // No highlight class for China/Japan

                row.innerHTML = `
                    <td>${flightData.date || ''}</td>
                    <td>${flightData.origin || ''}→${flightData.destination || ''}</td>
                    <td>${flightData.flight_numbers || ''}</td>
                    <td>${flightData.max_seats !== undefined ? flightData.max_seats : ''}</td>
                    <td>${flightData.current_seats !== undefined ? flightData.current_seats : ''}</td>
                    <td>${displayTimestamp}</td>
                `;

                // Re-bind click listener for history tooltip
                if (typeof bindHistoryClick === 'function') {
                    bindHistoryClick($(row));
                } else {
                    console.warn("bindHistoryClick function not found for new row.");
                }
            } else {
                console.error(`Could not find or create tbody with id ${targetTbodyId}`);
            }
        }
    }

    // --- History Tooltip Logic (Click-based) ---
    let tooltip = $('#history-tooltip');
    let activeKey = null;

    function bindHistoryClick(selector) {
        $(selector).off('click').on('click', function(e) {
            e.stopPropagation();
            let clickedRow = $(this);
            let historyKey = clickedRow.data('history-key');
            if (!historyKey) return;

            if (activeKey === historyKey) {
                tooltip.hide(); activeKey = null; return;
            }
            activeKey = historyKey;

            tooltip.html('加载中...').show();
            let position = clickedRow.offset();
            let rowHeight = clickedRow.outerHeight();
            let topPos = position.top + rowHeight + 5;
            let leftPos = position.left + 50;

            // Adjust tooltip position
            let tooltipWidth = tooltip.outerWidth() || 250;
            let windowWidth = $(window).width();
            if (leftPos + tooltipWidth > windowWidth - 20) {
                leftPos = windowWidth - tooltipWidth - 20;
            }
            if (leftPos < 0) leftPos = 5;
            if (topPos < 0) topPos = 5;
            tooltip.css({ top: topPos, left: leftPos });

            $.ajax({
                url: `/api/history/${historyKey}`,
                method: 'GET',
                success: function(data) {
                    if (activeKey === historyKey) {
                        if (data && data.length > 0) {
                            let content = '<table class="table table-sm table-borderless mb-0"><tbody>';
                            data.forEach(function(item) {
                                let ts = formatDisplayTimestamp(item.timestamp);
                                content += `<tr><td class="pe-2">${ts}</td><td>座位: ${item.seats}</td></tr>`;
                            });
                            tooltip.html(content + '</tbody></table>');
                        } else {
                            tooltip.text('没有历史记录。');
                        }
                    }
                },
                error: function(xhr, status, error) {
                    console.error("获取历史记录失败:", status, error);
                    if (activeKey === historyKey) {
                        tooltip.text('加载历史记录失败。');
                    }
                }
            });
        });
    }

    // Initial binding for existing rows (now happens after initial_data)
    bindHistoryClick('tbody tr[data-history-key]'); // RESTORE Initial binding here

    // Hide tooltip on document click
    $(document).on('click', function(e) {
        if ($(e.target).closest('tbody tr[data-history-key], #history-tooltip').length === 0) {
            tooltip.hide(); activeKey = null;
        }
    });
});
</script>
{% endblock %}