<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="ja-<PERSON>" xml:lang="ja-<PERSON>">

	
	<!-- skey = 2025/06/20 14:35:38 rei21c YiyL1a-aid dljdmx+3ce  --><head id="j_idt53">
    <base href="about:blank">
    <style>
        /* Hide elements that might try to load external resources, as a fallback. */
        img, svg, video, iframe, link[rel="stylesheet"], script { display: none !important; }
    </style>

		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
			<meta http-equiv="X-UA-Compatible" content="IE=EmulateIE9; IE=EmulateIE10" />
			<meta http-equiv="X-UA-Compatible" content="IE=7" />
			<meta http-equiv="X-UA-Compatible" content="IE=edge" />
		<meta name="description" content="" />
		<meta name="keywords" content="" />
		<meta name="format-detection" content="telephone=no" />
		<script type="text/javascript" src="/international_asw/dynatrace/ruxitagentjs_ICANVfhqrux_10273230920145353.js" data-dtconfig="rid=RID_-*********|rpid=-1822808455|domain=aswbe-i.ana.co.jp|reportUrl=https://bf90419wvr.bf.dynatrace.com/bf|app=78bf0b58acf6ed13|cors=1|owasp=1|featureHash=ICANVfhqrux|xb=www^bs.google-analytics^bs.com^pana^bs.demdex^bs.net^pcollect^bs.tealiumiq^bs.com^pallnipponairways^bs.australia-3^bs.evergage^bs.com^pallnipponairwayscolt^bs.tt^bs.omtrdc^bs.net|rdnt=1|uxrgce=1|cuc=k8vpgh61|mel=100000|expw=1|dpvc=1|md=mdcc1=bAsw.PnrOutput.recordLocator,mdcc2=bAsw.PnrOutput.paymentDetailInfoList[0].fopCode,mdcc3=bAsw.PnrOutput.ticketedFlag,mdcc4=bAsw.PnrOutput.nhPnrFlag,mdcc5=bAsw.PnrOutput.itinerarySummary.departureDateYyyymmdd,mdcc6=bAsw.SearchCriteriaOutput.cffCodeInput,mdcc7=a#main ^rb div.section.sectionNoFlow ^rb table ^rb tbody ^rb tr ^rb td.flightNumber.lastCol ^rb span ^rb span:nth-child(2),mdcc8=cunique|lastModification=1750184416409|tp=500,50,0|agentUri=/international_asw/dynatrace/ruxitagentjs_ICANVfhqrux_10273230920145353.js"></script><link rel="shortcut icon" href="https://www.ana.co.jp/favicon.ico" />
		<title>お客様情報入力 | ANA</title>
		<noscript><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/css/asw_noscript_pc.css?99001fc" />
		</noscript>

		<!--  [ Adobe Target ] -->
		<script type="text/javascript" src="https://www.ana.co.jp/behavior/adobetarget/target_intbe.js"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/jquery-1.8.3.min.js?717d3c0"></script><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/ja/css/asw_initialize.css?99001fc" /><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/css/asw_screen_pc.css?99001fc" /><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/css/asw_common_pc.css?99001fc" /><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/css/asw_forminput_pc.css?99001fc" /><script type="text/javascript">
		var Asw = Asw || {};
		Asw.GlobalWord = Asw.GlobalWord || {};
		
		Asw.GlobalWord.toolTipCloseIconAltText = "閉じる";
		
		</script><script >bazadebezolkohpepadr="1895046799"</script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/akam/13/70f41f14"  defer></script></head><body>
		
		<noscript>当サイトをご利用になるためには、JavaScript対応のブラウザが必要です。設定でJavaScriptを有効にしてください。有効にできない場合はANA電話窓口へお問い合わせください。
		</noscript>
		<div id="preLoadingArea"></div>
	
	<div id="cmnHeader" class="platinum">
		<!--===== cmnHeader =====-->
		<div class="headerWrapper">
			<div class="header">
				<p class="logo"><a href="https://www.ana.co.jp/asw/AMCTopServlet?type=a" tabindex="-1" aria-hidden="true"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/ana_logo_navy.png?717d3c0" alt="ANA Inspiration of JAPAN。リンク先はマイレージクラブトップページです。" height="28" width="274" /></a>
				</p>
					<ul class="headerMenu">
						<li><a href="https://www.ana.co.jp/asw/AMCTopServlet?type=a">ANAマイレージクラブトップページ</a>
						</li>
						<li><a href="https://www.ana.co.jp/other/int/meta/0005.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">お問い合わせ<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
						</li>
							<li class="btnLogoutArea"><a href="https://aswbe-i.ana.co.jp/rei21c/international_asw/rest/logout" role="button" class="btnBase btnLogout">ログアウト</a>
							</li>
					</ul>
			</div>
		</div>
			<div class="amcUserInfoWrapper">
				<div class="amcUserInfo">
					<div class="userNameArea">
						<table>
							<tr>	
									<td class="statusIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_status_05.png?717d3c0" alt="プラチナサービスメンバー" height="46" width="60" />
									</td>
								<td class="userName">佐藤 直樹 <span>様</span>
								</td>
							</tr>					
						</table>
					</div>
						<div class="balance">
								<dl class="upgradePointBalance">
									<dt>アップグレードポイント残高
									</dt>
									<dd>
											<dl>
												<dt>(2025年度)
												</dt>
												<dd>
													<em>20
													</em>ポイント
												</dd>
											</dl>
									</dd>
								</dl>
							<dl class="mileBalance">
								<dt>マイル口座残高
								</dt>
								<dd><em>75,663</em><span class="currencyCode">マイル</span>
								</dd>
							</dl>
						</div>
				</div>
					<p class="nowTime">6月20日14時35分現在
					</p>
			</div>
		<!--===== /cmnHeader =====-->
	</div>
		<div id="sessionKeeperContainer" class="sessionKeepingContainer"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/bg_modalWindow.png?717d3c0" alt="" height="0" width="0" />
			<div id="extendSessionExpiredDialog" class="modalExpire modalSmall modalContainer" role="dialog" aria-labelledby="extendSessionExpiredDialogTitle" aria-hidden="true">
<form id="sessionKeeperContainer:j_idt183" name="sessionKeeperContainer:j_idt183" method="post" action="https://aswbe-i.ana.co.jp/rei21c/international_asw/pages/award/reservation/award_mandatory_passenger_information_input.xhtml?aswcid=1&amp;rand=20250620143538YiyL1a-aid" autocomplete="off" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="sessionKeeperContainer:j_idt183" value="sessionKeeperContainer:j_idt183" />
<input type="hidden" name="sessionKeeperContainer:j_idt183_operationTicket" value="dljdmx+3ce76cdd3313e70fb33c4387a9d83db2~mzh2bpwHW6FnIiQsZ9LVnLpjwU1VC66LaHz71Sjq!1750397729773.aere-xml-controller-67d4778877-zwdk2" /><input type="hidden" name="sessionKeeperContainer:j_idt183_cmnPageTicket" value="4" />
					<h1 id="extendSessionExpiredDialogTitle" class="dialogTitle">まもなくセッションが終了します</h1>
					<div id="extendSessionExpiredDialogMessage" class="dialogMessage" tabindex="-1">このままお手続きを進める場合は、［延長］ボタンを押してください。</div>
					<p class="modalButton"><input id="sessionKeeperContainer:cmnSessionKeepingButton" type="submit" name="sessionKeeperContainer:cmnSessionKeepingButton" value="延長" class="btnBase btnModal btnMainStream" onclick="mojarra.ab(this,event,'action',0,0,{'onevent':function(data) {Asw.doCommonAjaxCallback(data,false, event); Asw.SessionKeeper.doSessionKeep(data);},'onerror':function(data) {Asw.doCommonAjaxErrorCallback(data); }});return false" />
					</p><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:0" value="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" autocomplete="off" />
</form>
			</div>
			<div id="showSessionExpiredDialog" class="modalExpire modalSmall modalContainer" role="dialog" aria-labelledby="showSessionExpiredDialogTitle" aria-hidden="true">
				<h1 id="showSessionExpiredDialogTitle" class="dialogTitle">セッションが終了しました</h1>
				<div id="showSessionExpiredDialogMessage" class="dialogMessage" tabindex="-1">一定時間操作されなかったため、お手続きを進めることができません。お手数ですが、再度トップページからお手続きください。</div>
				<p class="modalButton">
					<input type="submit" value="閉じる" class="btnBase btnModal btnMainStream" onclick="Asw.SessionKeeper.closeTimeoutDialog(); return false;" />
				</p>
			</div>
		</div>
		<!--===== dialogMessages =====--><div id="cmnModalMessages"></div>
		<div id="prebookConfirmDialog" class="modalContainer modalConfirm modalSmall " aria-hidden="true" role="dialog">
<form id="prebookConfirmDialog:j_idt403" name="prebookConfirmDialog:j_idt403" method="post" action="https://aswbe-i.ana.co.jp/rei21c/international_asw/pages/award/reservation/award_mandatory_passenger_information_input.xhtml?aswcid=1&amp;rand=20250620143538YiyL1a-aid" autocomplete="off" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="prebookConfirmDialog:j_idt403" value="prebookConfirmDialog:j_idt403" />
<input type="hidden" name="prebookConfirmDialog:j_idt403_operationTicket" value="dljdmx+3ce76cdd3313e70fb33c4387a9d83db2~mzh2bpwHW6FnIiQsZ9LVnLpjwU1VC66LaHz71Sjq!1750397729773.aere-xml-controller-67d4778877-zwdk2" /><input type="hidden" name="prebookConfirmDialog:j_idt403_cmnPageTicket" value="4" /><div class="dialogMessage" tabindex="0"><p>この後、名・姓の修正や変更ができません。</p><p>「搭乗者選択」欄の氏名が「名」「姓」の順番で表示されていることを再確認ください。</p><p>例）<strong class="highlight">空野 太郎</strong> 様 → MR <strong class="highlight">TARO SORANO</strong></p><p>保安上の理由により、航空券とパスポートのお名前が同じ表記でない場合はご搭乗いただくことができませんので必ず再度ご確認ください。</p></div>
					
					<ul class="modalBtnHorizontal">
							
							<li><input type="submit" name="prebookConfirmDialog:j_idt419" value="戻る" aria-controls="prebookConfirmDialog" class="btnBase btnModal btnCancel" onclick="Asw.Dialog.getInstance('prebookConfirmDialog').close(event, Asw.DialogSequencer.isActiveSequencerNotDisplayedLastDialog());  return false;" />
							</li>
						
						<li class="btnArrowNext"><input type="submit" name="prebookConfirmDialog:j_idt422" value="OK" aria-controls="prebookConfirmDialog" class="btnBase btnModal btnMainStream " onclick="Asw.Dialog.getInstance('prebookConfirmDialog').callOpener('NORMAL', event); return false;" />
						</li>
					</ul><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:1" value="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" autocomplete="off" />
</form>
		</div>
		<!--===== /dialogMessages =====--><div id="cmnContainer">
		<!--=========== .cmnContainer ============--><div id="cmnEmergencyNotice"></div><div id="cmnTitle" class="hgroup ">
		<h1 class="visuallyHidden">お客様情報入力</h1></div>

		<div id="cmnWrapper">
		<!--===== cmnWrapper =====-->
		<ol class="mainFlow" aria-label="完了までの手順、">
			<li><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_flow_01.png?717d3c0" alt="1" height="20" width="28" />フライト検索</li><!--
			--><li><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_flow_02_on.png?717d3c0" alt="2" height="20" width="28" /><span class="visuallyHidden">現在のステップ、</span>お客様情報入力</li><!--
			--><li><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_flow_03.png?717d3c0" alt="3" height="20" width="28" />お支払い情報</li><!--
			--><li class="flowLast"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_flow4_04.png?717d3c0" alt="4" height="20" width="28" />完了</li>
		</ol>

	<div id="main">
	<!--=========== main =============-->
		<ul class="iconExample">
				<li class="requiredIconExample">
					<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="" height="7" width="7" /></span>必須項目です	
				</li>
		</ul>
<form id="j_idt526" name="j_idt526" method="post" action="https://aswbe-i.ana.co.jp/rei21c/international_asw/pages/award/reservation/award_mandatory_passenger_information_input.xhtml?aswcid=1&amp;rand=20250620143538YiyL1a-aid" autocomplete="off" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="j_idt526" value="j_idt526" />
<input type="hidden" name="j_idt526_operationTicket" value="dljdmx+3ce76cdd3313e70fb33c4387a9d83db2~mzh2bpwHW6FnIiQsZ9LVnLpjwU1VC66LaHz71Sjq!1750397729773.aere-xml-controller-67d4778877-zwdk2" /><input type="hidden" name="j_idt526_cmnPageTicket" value="4" />

<div class="section">
	<h2>搭乗者選択<a href="https://www.ana.co.jp/other/int/meta/0086.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="特典のご利用に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a></h2>
	<table class="passengerInfo">
		<thead>
			<tr>
				
				<th class="name">氏名</th>
				
				<th class="dateOfBirthWide" id="dateOfBirthHeader">生年月日</th>
				
				<th class="gender lastCol" id="genderHeader">性別<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span></th>
				
			</tr>
		</thead>
		<tbody>
				
				<tr>
					<th class="parent_01" id="parentHeader01">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:0:passenger" type="checkbox" name="passengers:0:passenger" checked="checked" data-number="1" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:0:passenger">NAOKI SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader01">1963年06月05日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader01">				
							<span class="formWrapper">
							<label for="passengers:0:gender" class="visuallyHidden">NAOKI SATOの性別</label><select id="passengers:0:gender" name="passengers:0:gender" size="1">	<option value="">選択してください</option>
	<option value="M" selected="selected">男性</option>
	<option value="F">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_02" id="parentHeader02">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:1:passenger" type="checkbox" name="passengers:1:passenger" data-number="2" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:1:passenger">REO SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader02">1995年05月18日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader02">				
							<span class="formWrapper">
							<label for="passengers:1:gender" class="visuallyHidden">REO SATOの性別</label><select id="passengers:1:gender" name="passengers:1:gender" size="1">	<option value="">選択してください</option>
	<option value="M" selected="selected">男性</option>
	<option value="F">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_03" id="parentHeader03">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:2:passenger" type="checkbox" name="passengers:2:passenger" data-number="3" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:2:passenger">ERI SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader03">1963年08月14日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader03">				
							<span class="formWrapper">
							<label for="passengers:2:gender" class="visuallyHidden">ERI SATOの性別</label><select id="passengers:2:gender" name="passengers:2:gender" size="1">	<option value="">選択してください</option>
	<option value="M">男性</option>
	<option value="F" selected="selected">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_04" id="parentHeader04">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:3:passenger" type="checkbox" name="passengers:3:passenger" data-number="4" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:3:passenger">TAISHI SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader04">1992年12月03日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader04">				
							<span class="formWrapper">
							<label for="passengers:3:gender" class="visuallyHidden">TAISHI SATOの性別</label><select id="passengers:3:gender" name="passengers:3:gender" size="1">	<option value="">選択してください</option>
	<option value="M" selected="selected">男性</option>
	<option value="F">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_05" id="parentHeader05">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:4:passenger" type="checkbox" name="passengers:4:passenger" data-number="5" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:4:passenger">HIROMI FUJIMOTO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader05">1961年11月23日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader05">				
							<span class="formWrapper">
							<label for="passengers:4:gender" class="visuallyHidden">HIROMI FUJIMOTOの性別</label><select id="passengers:4:gender" name="passengers:4:gender" size="1">	<option value="">選択してください</option>
	<option value="M">男性</option>
	<option value="F" selected="selected">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
		</tbody>
	</table>
</div>
		<div class="section">
			<h2 class="midstream">連絡先情報
			</h2>
			<h3>代表者
			</h3>
	
	
	
	<div class="formInput">
		<fieldset>
			<legend><label>代表者</label></legend>
			<dl id="contactEmailAddressInfo">
				<dt class="hasRequiredIcon">メールアドレス<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span><a href="https://www.ana.co.jp/other/int/meta/0057.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="メールの宛先と内容に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a>
				</dt>
				<dd>
					<ul class="verticalList">
						<li class="nonMarginTop"><label for="representativeEmail" class="visuallyHidden">代表者メールアドレス</label><input id="representativeEmail" type="text" name="representativeEmail" value="<EMAIL>" class="widthLarge" placeholder="例：<EMAIL>" />
						</li>
						<li>
							<span class="supplementTxt" id="confirmMailAddressForRepresentative">確認のため再度ご入力ください。
							</span><label for="confirmrepresentativeEmail" class="visuallyHidden">確認用メールアドレス</label><input id="confirmrepresentativeEmail" type="text" name="confirmrepresentativeEmail" value="<EMAIL>" class="widthLarge jsNotPasteInput" placeholder="例：<EMAIL>" aria-describedby="confirmMailAddressForRepresentative" />
						</li>
					</ul>
				</dd>
					<dd style="display:none;"><input id="updateAlwaysInfo" type="checkbox" name="updateAlwaysInfo" /><label for="updateAlwaysInfo">ログイン中の会員情報へこのメールアドレスを登録</label>
					</dd>
			</dl>
			<dl>
				<dt>電話番号<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span>
				</dt>
				<dd class="js-countryWrap">
					<ul>
						<li><label for="representativeTelType" class="visuallyHidden">電話番号種別</label><select id="representativeTelType" name="representativeTelType" class="basicWidth js-typeSelect" size="1" onchange="changeTelType(this,'representativeTel')">	<option value="M1" selected="selected">携帯</option>
	<option value="B1">会社</option>
	<option value="H1">自宅</option>
</select>
						</li>
						<li><label for="representativeTelCountry" class="visuallyHidden">電話番号の国/地区選択</label><select id="representativeTelCountry" name="representativeTelCountry" class="js-countrySelect" size="1">	<option value="" data-sms-possible="true" data-country-code="---">選択してください</option>
	<option value="JP" selected="selected" data-sms-possible="true">日本</option>
	<option value="-" data-sms-possible="true" data-country-code="---">-----------</option>
	<option value="IS" data-sms-possible="true">アイスランド</option>
	<option value="IE" data-sms-possible="true">アイルランド</option>
	<option value="AZ" data-sms-possible="true">アゼルバイジャン</option>
	<option value="AF" data-sms-possible="true">アフガニスタン</option>
	<option value="US" data-sms-possible="true">アメリカ合衆国/カナダ</option>
	<option value="AE" data-sms-possible="true">アラブ首長国連邦</option>
	<option value="DZ" data-sms-possible="true">アルジェリア</option>
	<option value="AR" data-sms-possible="true">アルゼンチン</option>
	<option value="AW" data-sms-possible="true">アルバ</option>
	<option value="AL" data-sms-possible="true">アルバニア</option>
	<option value="AM" data-sms-possible="true">アルメニア</option>
	<option value="AO" data-sms-possible="true">アンゴラ</option>
	<option value="AG" data-sms-possible="true">アンティグア・バーブーダ</option>
	<option value="AD" data-sms-possible="true">アンドラ</option>
	<option value="YE" data-sms-possible="true">イエメン</option>
	<option value="GB" data-sms-possible="true">イギリス</option>
	<option value="IL" data-sms-possible="true">イスラエル</option>
	<option value="IT" data-sms-possible="true">イタリア</option>
	<option value="IQ" data-sms-possible="true">イラク</option>
	<option value="IR" data-sms-possible="true">イラン</option>
	<option value="IN" data-sms-possible="true">インド</option>
	<option value="ID" data-sms-possible="true">インドネシア</option>
	<option value="UG" data-sms-possible="true">ウガンダ</option>
	<option value="UA" data-sms-possible="true">ウクライナ</option>
	<option value="UZ" data-sms-possible="true">ウズベキスタン</option>
	<option value="UY" data-sms-possible="true">ウルグアイ</option>
	<option value="EC" data-sms-possible="true">エクアドル</option>
	<option value="EG" data-sms-possible="true">エジプト</option>
	<option value="EE" data-sms-possible="true">エストニア</option>
	<option value="SZ" data-sms-possible="true">エスワティニ</option>
	<option value="ET" data-sms-possible="true">エチオピア</option>
	<option value="ER" data-sms-possible="true">エリトリア</option>
	<option value="SV" data-sms-possible="true">エルサルバドル</option>
	<option value="AU" data-sms-possible="true">オーストラリア</option>
	<option value="AT" data-sms-possible="true">オーストリア</option>
	<option value="OM" data-sms-possible="true">オマーン</option>
	<option value="NL" data-sms-possible="true">オランダ</option>
	<option value="GH" data-sms-possible="true">ガーナ</option>
	<option value="CV" data-sms-possible="true">カーボベルデ</option>
	<option value="GY" data-sms-possible="true">ガイアナ</option>
	<option value="KZ" data-sms-possible="true">カザフスタン</option>
	<option value="QA" data-sms-possible="true">カタール</option>
	<option value="CA" data-sms-possible="true">カナダ/アメリカ合衆国</option>
	<option value="GA" data-sms-possible="true">ガボン</option>
	<option value="CM" data-sms-possible="true">カメルーン</option>
	<option value="KR" data-sms-possible="true">韓国</option>
	<option value="GM" data-sms-possible="true">ガンビア</option>
	<option value="KH" data-sms-possible="true">カンボジア</option>
	<option value="KP" data-sms-possible="true">北朝鮮</option>
	<option value="MK" data-sms-possible="true">北マケドニア</option>
	<option value="GN" data-sms-possible="true">ギニア</option>
	<option value="GW" data-sms-possible="true">ギニアビサウ</option>
	<option value="CY" data-sms-possible="true">キプロス</option>
	<option value="CU" data-sms-possible="true">キューバ</option>
	<option value="GR" data-sms-possible="true">ギリシャ</option>
	<option value="KI" data-sms-possible="true">キリバス</option>
	<option value="KG" data-sms-possible="true">キルギス</option>
	<option value="GT" data-sms-possible="true">グアテマラ</option>
	<option value="GU" data-sms-possible="true">グアム</option>
	<option value="KW" data-sms-possible="true">クウェート</option>
	<option value="GD" data-sms-possible="true">グレナダ</option>
	<option value="HR" data-sms-possible="true">クロアチア</option>
	<option value="KY" data-sms-possible="true">ケイマン諸島</option>
	<option value="KE" data-sms-possible="true">ケニア</option>
	<option value="CI" data-sms-possible="true">コートジボワール</option>
	<option value="CR" data-sms-possible="true">コスタリカ</option>
	<option value="KM" data-sms-possible="true">コモロ</option>
	<option value="CO" data-sms-possible="true">コロンビア</option>
	<option value="CG" data-sms-possible="true">コンゴ共和国</option>
	<option value="CD" data-sms-possible="true">コンゴ民主共和国</option>
	<option value="SA" data-sms-possible="true">サウジアラビア</option>
	<option value="ZM" data-sms-possible="true">ザンビア</option>
	<option value="SM" data-sms-possible="true">サンマリノ</option>
	<option value="SL" data-sms-possible="true">シエラレオネ</option>
	<option value="DJ" data-sms-possible="true">ジブチ</option>
	<option value="JM" data-sms-possible="true">ジャマイカ</option>
	<option value="GE" data-sms-possible="true">ジョージア</option>
	<option value="SY" data-sms-possible="true">シリア</option>
	<option value="SG" data-sms-possible="true">シンガポール</option>
	<option value="ZW" data-sms-possible="true">ジンバブエ</option>
	<option value="CH" data-sms-possible="true">スイス</option>
	<option value="SE" data-sms-possible="true">スウェーデン</option>
	<option value="SD" data-sms-possible="true">スーダン</option>
	<option value="ES" data-sms-possible="true">スペイン</option>
	<option value="SR" data-sms-possible="true">スリナム</option>
	<option value="LK" data-sms-possible="true">スリランカ</option>
	<option value="SK" data-sms-possible="true">スロバキア</option>
	<option value="SI" data-sms-possible="true">スロベニア</option>
	<option value="SC" data-sms-possible="true">セイシェル</option>
	<option value="GQ" data-sms-possible="true">赤道ギニア</option>
	<option value="SN" data-sms-possible="true">セネガル</option>
	<option value="RS" data-sms-possible="true">セルビア</option>
	<option value="KN" data-sms-possible="true">セントクリストファー・ネイビス</option>
	<option value="LC" data-sms-possible="true">セントルシア</option>
	<option value="SB" data-sms-possible="true">ソロモン諸島</option>
	<option value="TH" data-sms-possible="true">タイ</option>
	<option value="TW" data-sms-possible="true">台湾</option>
	<option value="TJ" data-sms-possible="true">タジキスタン</option>
	<option value="TZ" data-sms-possible="true">タンザニア</option>
	<option value="CZ" data-sms-possible="true">チェコ</option>
	<option value="TD" data-sms-possible="true">チャド</option>
	<option value="CF" data-sms-possible="true">中央アフリカ共和国</option>
	<option value="CN" data-sms-possible="true">中国</option>
	<option value="TN" data-sms-possible="true">チュニジア</option>
	<option value="CL" data-sms-possible="true">チリ</option>
	<option value="TV" data-sms-possible="true">ツバル</option>
	<option value="DK" data-sms-possible="true">デンマーク</option>
	<option value="DE" data-sms-possible="true">ドイツ</option>
	<option value="TG" data-sms-possible="true">トーゴ</option>
	<option value="DO" data-sms-possible="true">ドミニカ共和国</option>
	<option value="DM" data-sms-possible="true">ドミニカ国</option>
	<option value="TT" data-sms-possible="true">トリニダード・トバゴ</option>
	<option value="TM" data-sms-possible="true">トルクメニスタン</option>
	<option value="TR" data-sms-possible="true">トルコ</option>
	<option value="TO" data-sms-possible="true">トンガ</option>
	<option value="NG" data-sms-possible="true">ナイジェリア</option>
	<option value="NR" data-sms-possible="true">ナウル</option>
	<option value="NA" data-sms-possible="true">ナミビア</option>
	<option value="NI" data-sms-possible="true">ニカラグア</option>
	<option value="NE" data-sms-possible="true">ニジェール</option>
	<option value="NZ" data-sms-possible="true">ニュージーランド</option>
	<option value="NP" data-sms-possible="true">ネパール</option>
	<option value="NO" data-sms-possible="true">ノルウェー</option>
	<option value="BH" data-sms-possible="true">バーレーン</option>
	<option value="HT" data-sms-possible="true">ハイチ</option>
	<option value="PK" data-sms-possible="true">パキスタン</option>
	<option value="VA" data-sms-possible="true">バチカン</option>
	<option value="PA" data-sms-possible="true">パナマ</option>
	<option value="VU" data-sms-possible="true">バヌアツ</option>
	<option value="BS" data-sms-possible="true">バハマ</option>
	<option value="PG" data-sms-possible="true">パプアニューギニア</option>
	<option value="BM" data-sms-possible="true">バミューダ</option>
	<option value="PW" data-sms-possible="true">パラオ</option>
	<option value="PY" data-sms-possible="true">パラグアイ</option>
	<option value="BB" data-sms-possible="true">バルバドス</option>
	<option value="PS" data-sms-possible="true">パレスチナ</option>
	<option value="HU" data-sms-possible="true">ハンガリー</option>
	<option value="BD" data-sms-possible="true">バングラデシュ</option>
	<option value="TL" data-sms-possible="true">東ティモール</option>
	<option value="FJ" data-sms-possible="true">フィジー</option>
	<option value="PH" data-sms-possible="true">フィリピン</option>
	<option value="FI" data-sms-possible="true">フィンランド</option>
	<option value="BT" data-sms-possible="true">ブータン</option>
	<option value="PR" data-sms-possible="true">プエルトリコ</option>
	<option value="BR" data-sms-possible="true">ブラジル</option>
	<option value="FR" data-sms-possible="true">フランス</option>
	<option value="PF" data-sms-possible="true">フランス領ポリネシア</option>
	<option value="BG" data-sms-possible="true">ブルガリア</option>
	<option value="BF" data-sms-possible="true">ブルキナファソ</option>
	<option value="BN" data-sms-possible="true">ブルネイ</option>
	<option value="BI" data-sms-possible="true">ブルンジ</option>
	<option value="VN" data-sms-possible="true">ベトナム</option>
	<option value="BJ" data-sms-possible="true">ベナン</option>
	<option value="VE" data-sms-possible="true">ベネズエラ</option>
	<option value="BY" data-sms-possible="true">ベラルーシ</option>
	<option value="BZ" data-sms-possible="true">ベリーズ</option>
	<option value="PE" data-sms-possible="true">ペルー</option>
	<option value="BE" data-sms-possible="true">ベルギー</option>
	<option value="PL" data-sms-possible="true">ポーランド</option>
	<option value="BA" data-sms-possible="true">ボスニア・ヘルツェゴビナ</option>
	<option value="BW" data-sms-possible="true">ボツワナ</option>
	<option value="BO" data-sms-possible="true">ボリビア</option>
	<option value="PT" data-sms-possible="true">ポルトガル</option>
	<option value="HK" data-sms-possible="true">香港</option>
	<option value="HN" data-sms-possible="true">ホンジュラス</option>
	<option value="MH" data-sms-possible="true">マーシャル諸島</option>
	<option value="MO" data-sms-possible="true">マカオ</option>
	<option value="MG" data-sms-possible="true">マダガスカル</option>
	<option value="MW" data-sms-possible="true">マラウイ</option>
	<option value="ML" data-sms-possible="true">マリ</option>
	<option value="MT" data-sms-possible="true">マルタ</option>
	<option value="MY" data-sms-possible="true">マレーシア</option>
	<option value="FM" data-sms-possible="true">ミクロネシア連邦</option>
	<option value="ZA" data-sms-possible="true">南アフリカ</option>
	<option value="MM" data-sms-possible="true">ミャンマー</option>
	<option value="MX" data-sms-possible="true">メキシコ</option>
	<option value="MU" data-sms-possible="true">モーリシャス</option>
	<option value="MR" data-sms-possible="true">モーリタニア</option>
	<option value="MZ" data-sms-possible="true">モザンビーク</option>
	<option value="MC" data-sms-possible="true">モナコ</option>
	<option value="MV" data-sms-possible="true">モルディヴ</option>
	<option value="MD" data-sms-possible="true">モルドバ</option>
	<option value="MA" data-sms-possible="true">モロッコ</option>
	<option value="MN" data-sms-possible="true">モンゴル</option>
	<option value="ME" data-sms-possible="true">モンテネグロ</option>
	<option value="JO" data-sms-possible="true">ヨルダン</option>
	<option value="LA" data-sms-possible="true">ラオス人民民主共和国</option>
	<option value="LV" data-sms-possible="true">ラトビア</option>
	<option value="LT" data-sms-possible="true">リトアニア</option>
	<option value="LY" data-sms-possible="true">リビア</option>
	<option value="LI" data-sms-possible="true">リヒテンシュタイン</option>
	<option value="LR" data-sms-possible="true">リベリア</option>
	<option value="RO" data-sms-possible="true">ルーマニア</option>
	<option value="LU" data-sms-possible="true">ルクセンブルク</option>
	<option value="RW" data-sms-possible="true">ルワンダ</option>
	<option value="LS" data-sms-possible="true">レソト</option>
	<option value="LB" data-sms-possible="true">レバノン</option>
	<option value="RU" data-sms-possible="true">ロシア</option>
</select>
						</li>
						<li class="numberInputWrap">
							<span id="numberInputInfo1" class="supplementTxt marginTop">先頭に0(ゼロ)がある場合、0(ゼロ)を抜いてください(一部、国・地域除く)。携帯電話番号の場合は、携帯電話を契約された国を選択ください。</span>
							<div class="numberRowWrap">
								<div class="block">
									
									<span class="visuallyHidden">国番号</span>
									<span class="viewInput widthMini js-countryCode"></span>
								</div>
								<div class="block"><label for="representativeTel" class="visuallyHidden">電話番号</label><input id="representativeTel" type="text" name="representativeTel" value="9042504176" class="widthMiddle" placeholder="例：9012345678" maxlength="30" aria-describedby="numberInputInfo1" />
								</div>
							</div>
						</li>
					</ul>
				</dd>
			</dl>
		</fieldset>
	</div>
	<div class="titleWrapper">
		<h3>
			<span class="text">eメール・SMSに関するご案内<a href="https://www.ana.co.jp/other/int/meta/0057.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="eメール・SMSに関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a></span>
		</h3>
	</div>
	<div class="formInput">
	<h4 class="formAreaTitle">主に搭乗・運航に関わるeメールの送付先設定（米国へ通知いたします）<a href="https://www.ana.co.jp/other/int/meta/0629.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="米国入国用eメール・SMS・電話番号に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a>
	</h4>
	<div class="paxSettingArea js-mailFormWrap onePax is-open">
			<div class="settingArea">
					<dl>
						<dt class="breakWord">NAOKI SATO
						</dt>
						<dd>
							<fieldset>
								<legend>NAOKI SATOのeメール送付先および米国到着時に提供するメールアドレス</legend>代表者と同じ
							</fieldset>
						</dd>
					</dl>
			</div>
	</div>
	<h4 class="formAreaTitle hasTopLine">主に搭乗・運航に関わるSMSの送付先および米国で受信できる電話番号の設定（米国へ通知いたします）<a href="https://www.ana.co.jp/other/int/meta/0629.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="米国入国用eメール・SMS・電話番号に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a>
	</h4>
	<div class="paxSettingArea js-smsFormWrap onePax is-open">
			<div class="settingArea">
					<dl>
						<dt class="breakWord">NAOKI SATO<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span>
						</dt>
						<dd>
							<fieldset>
								<legend>NAOKI SATOの1つ目の電話番号</legend>
									<ul class="verticalList js-formItem">
										<li><input type="radio" id="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSameAsRepresentativeSms:radioItem" name="contactsSms:0:recipientSmsEachPersonSelection:radioGroup" value="0" checked="checked" /><label for="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSameAsRepresentativeSms:radioItem">代表者電話番号と同じ</label>
										</li>
										<li><input type="radio" id="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSmsPerPassenger:radioItem" name="contactsSms:0:recipientSmsEachPersonSelection:radioGroup" value="1" /><label for="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSmsPerPassenger:radioItem">電話番号を指定</label>
											<div class="indentArea js-formImputArea">
												<span id="confirmTelNumberForRepresentative:0" class="supplementTxt marginTop">先頭に0(ゼロ)がある場合、0(ゼロ)を抜いてください(一部、国・地域除く)。携帯電話番号の場合は、携帯電話を契約された国を選択ください。</span>
												<ul class="countryNumberInput js-countryWrap">
													<li><label for="contactsSms:0:recipientSmsEachPersonSelection:passengerSmsCountry" class="visuallyHidden">電話番号の国/地区選択</label><select id="contactsSms:0:recipientSmsEachPersonSelection:passengerSmsCountry" name="contactsSms:0:recipientSmsEachPersonSelection:passengerSmsCountry" class="countrySelect js-countrySelect" size="1">	<option value="" data-country-code="---">選択してください</option>
	<option value="JP">日本</option>
	<option value="-" data-country-code="---">-----------</option>
	<option value="IE">アイルランド</option>
	<option value="AZ">アゼルバイジャン</option>
	<option value="AF">アフガニスタン</option>
	<option value="US">アメリカ合衆国/カナダ</option>
	<option value="AE">アラブ首長国連邦</option>
	<option value="DZ">アルジェリア</option>
	<option value="AR">アルゼンチン</option>
	<option value="AW">アルバ</option>
	<option value="AL">アルバニア</option>
	<option value="AM">アルメニア</option>
	<option value="AG">アンティグア・バーブーダ</option>
	<option value="AD">アンドラ</option>
	<option value="YE">イエメン</option>
	<option value="GB">イギリス</option>
	<option value="IL">イスラエル</option>
	<option value="IT">イタリア</option>
	<option value="IQ">イラク</option>
	<option value="IR">イラン</option>
	<option value="IN">インド</option>
	<option value="ID">インドネシア</option>
	<option value="UG">ウガンダ</option>
	<option value="UA">ウクライナ</option>
	<option value="UZ">ウズベキスタン</option>
	<option value="UY">ウルグアイ</option>
	<option value="EC">エクアドル</option>
	<option value="EG">エジプト</option>
	<option value="EE">エストニア</option>
	<option value="ER">エリトリア</option>
	<option value="SV">エルサルバドル</option>
	<option value="AU">オーストラリア</option>
	<option value="AT">オーストリア</option>
	<option value="OM">オマーン</option>
	<option value="NL">オランダ</option>
	<option value="GH">ガーナ</option>
	<option value="GY">ガイアナ</option>
	<option value="KZ">カザフスタン</option>
	<option value="QA">カタール</option>
	<option value="CA">カナダ/アメリカ合衆国</option>
	<option value="GA">ガボン</option>
	<option value="CM">カメルーン</option>
	<option value="KR">韓国</option>
	<option value="GM">ガンビア</option>
	<option value="KH">カンボジア</option>
	<option value="GN">ギニア</option>
	<option value="GW">ギニアビサウ</option>
	<option value="CY">キプロス</option>
	<option value="CU">キューバ</option>
	<option value="GR">ギリシャ</option>
	<option value="KG">キルギス</option>
	<option value="GT">グアテマラ</option>
	<option value="GU">グアム</option>
	<option value="KW">クウェート</option>
	<option value="GD">グレナダ</option>
	<option value="HR">クロアチア</option>
	<option value="KY">ケイマン諸島</option>
	<option value="KE">ケニア</option>
	<option value="CI">コートジボワール</option>
	<option value="CR">コスタリカ</option>
	<option value="KM">コモロ</option>
	<option value="CO">コロンビア</option>
	<option value="CD">コンゴ民主共和国</option>
	<option value="SA">サウジアラビア</option>
	<option value="ZM">ザンビア</option>
	<option value="SL">シエラレオネ</option>
	<option value="JM">ジャマイカ</option>
	<option value="GE">ジョージア</option>
	<option value="SY">シリア</option>
	<option value="SG">シンガポール</option>
	<option value="ZW">ジンバブエ</option>
	<option value="CH">スイス</option>
	<option value="SE">スウェーデン</option>
	<option value="SD">スーダン</option>
	<option value="ES">スペイン</option>
	<option value="SR">スリナム</option>
	<option value="LK">スリランカ</option>
	<option value="SK">スロバキア</option>
	<option value="SI">スロベニア</option>
	<option value="SN">セネガル</option>
	<option value="RS">セルビア</option>
	<option value="KN">セントクリストファー・ネイビス</option>
	<option value="LC">セントルシア</option>
	<option value="TH">タイ</option>
	<option value="TW">台湾</option>
	<option value="TJ">タジキスタン</option>
	<option value="TZ">タンザニア</option>
	<option value="CZ">チェコ</option>
	<option value="TD">チャド</option>
	<option value="CF">中央アフリカ共和国</option>
	<option value="CN">中国</option>
	<option value="TN">チュニジア</option>
	<option value="CL">チリ</option>
	<option value="DK">デンマーク</option>
	<option value="DE">ドイツ</option>
	<option value="TG">トーゴ</option>
	<option value="DO">ドミニカ共和国</option>
	<option value="TT">トリニダード・トバゴ</option>
	<option value="TR">トルコ</option>
	<option value="NG">ナイジェリア</option>
	<option value="NI">ニカラグア</option>
	<option value="NE">ニジェール</option>
	<option value="NZ">ニュージーランド</option>
	<option value="NP">ネパール</option>
	<option value="NO">ノルウェー</option>
	<option value="HT">ハイチ</option>
	<option value="PK">パキスタン</option>
	<option value="PA">パナマ</option>
	<option value="BS">バハマ</option>
	<option value="PG">パプアニューギニア</option>
	<option value="BM">バミューダ</option>
	<option value="PW">パラオ</option>
	<option value="PY">パラグアイ</option>
	<option value="BB">バルバドス</option>
	<option value="PS">パレスチナ</option>
	<option value="HU">ハンガリー</option>
	<option value="BD">バングラデシュ</option>
	<option value="TL">東ティモール</option>
	<option value="FJ">フィジー</option>
	<option value="PH">フィリピン</option>
	<option value="FI">フィンランド</option>
	<option value="BT">ブータン</option>
	<option value="PR">プエルトリコ</option>
	<option value="BR">ブラジル</option>
	<option value="FR">フランス</option>
	<option value="PF">フランス領ポリネシア</option>
	<option value="BG">ブルガリア</option>
	<option value="BF">ブルキナファソ</option>
	<option value="BN">ブルネイ</option>
	<option value="BI">ブルンジ</option>
	<option value="VN">ベトナム</option>
	<option value="BJ">ベナン</option>
	<option value="VE">ベネズエラ</option>
	<option value="BY">ベラルーシ</option>
	<option value="PE">ペルー</option>
	<option value="BE">ベルギー</option>
	<option value="PL">ポーランド</option>
	<option value="BA">ボスニア・ヘルツェゴビナ</option>
	<option value="BW">ボツワナ</option>
	<option value="HK">香港</option>
	<option value="MH">マーシャル諸島</option>
	<option value="MO">マカオ</option>
	<option value="MG">マダガスカル</option>
	<option value="MW">マラウイ</option>
	<option value="ML">マリ</option>
	<option value="MT">マルタ</option>
	<option value="MY">マレーシア</option>
	<option value="ZA">南アフリカ</option>
	<option value="MM">ミャンマー</option>
	<option value="MX">メキシコ</option>
	<option value="MU">モーリシャス</option>
	<option value="MR">モーリタニア</option>
	<option value="MZ">モザンビーク</option>
	<option value="MC">モナコ</option>
	<option value="MV">モルディヴ</option>
	<option value="MA">モロッコ</option>
	<option value="MN">モンゴル</option>
	<option value="ME">モンテネグロ</option>
	<option value="JO">ヨルダン</option>
	<option value="LA">ラオス人民民主共和国</option>
	<option value="LV">ラトビア</option>
	<option value="LT">リトアニア</option>
	<option value="LY">リビア</option>
	<option value="LR">リベリア</option>
	<option value="RO">ルーマニア</option>
	<option value="LU">ルクセンブルク</option>
	<option value="RW">ルワンダ</option>
	<option value="LB">レバノン</option>
	<option value="RU">ロシア</option>
</select>
													</li>
													<li>
														
														<div class="numberRowWrap">
															<div class="block">
																<span class="visuallyHidden">国番号</span>
																<span class="countryCode viewInput js-countryCode"></span>
															</div>
															
															<div class="block"><label for="contactsSms:0:recipientSmsEachPersonSelection:flightStatusNotificationContactPointSmsDescription" class="visuallyHidden">電話番号</label><input id="contactsSms:0:recipientSmsEachPersonSelection:flightStatusNotificationContactPointSmsDescription" type="text" name="contactsSms:0:recipientSmsEachPersonSelection:flightStatusNotificationContactPointSmsDescription" class="telNumber" placeholder="例：9012345678" maxlength="30" aria-describedby="confirmTelNumberForRepresentative:0" />
															</div>
														</div>
													</li>
												</ul>
											</div>
										</li>
									</ul>
							</fieldset>
						</dd>
					</dl>
			</div>
	</div>
	</div>
	
	<div class="formInput articleLine">
		<h3>滞在中の連絡先(任意)
		</h3>
		<dl>
			
			<dt><label for="accommodationOne">現地連絡先1</label>
			</dt>
			<dd><input id="accommodationOne" type="text" name="accommodationOne" class="widthLarge" placeholder="例：09012345678 ABC HOTEL" maxlength="28" title="例：09012345678 ABC HOTEL" />
			</dd>
		</dl>
		<dl>
			
			<dt><label for="accommodationTwo">現地連絡先2</label>
			</dt>
			<dd><input id="accommodationTwo" type="text" name="accommodationTwo" class="widthLarge" placeholder="例：09012345678 ABC HOTEL" maxlength="28" title="例：09012345678 ABC HOTEL" />
			</dd>
		</dl>
	</div>
		</div>
		
		<p class="btnAreaSubmit btnArrowNext "><input id="next" type="submit" name="next" value="次へ" class="btnBase btnMainStream btnVerticalMain btnWidthVariable" onclick="return Asw.Dialog.getInstance('prebookConfirmDialog').toggle(event);return Asw.LoadingWindow.open(&quot;NORMAL&quot;, event)" />
		</p><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:2" value="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" autocomplete="off" />
</form><div id="cmnDynamicMessages" class="infoBoxSection">
				<dl class="infoBox importantInfo jsAccordionSwitch">
					<dt><strong>重要なご案内</strong></dt>
					<dd>
						<ul class="jsAccordionSwitchList" id="informationMessages">
								<li>個人情報の利用目的<a href="https://www.ana.co.jp/other/int/meta/0012.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="ANAプライバシーポリシー(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">ANAプライバシーポリシー<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>を必ずお読みいただき、内容に同意いただいた上で、お進みください。</li>
								<li><a href="https://www.ana.co.jp/other/int/meta/0058.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="Secure Flight Programについて(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">米国Secure Flightプログラム<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>に基づき、パスポート名、生年月日、性別などの情報は事前に米国へ通知いたします。</li>
								<li>入力したeメールアドレス、電話番号は入国に必要な情報として米国に通知される可能性があります。</li>
									<li class="jsHiddenFlg toggleContents">お名前のスペルがパスポート名と一致しているかご確認ください。異なる場合はインターネットではお取り扱いできません。<a href="https://www.ana.co.jp/other/int/meta/intbe0001.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="ANAお問い合わせ先(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">ANA<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>へ名前の変更をお申し出ください。</li>
									<li class="jsHiddenFlg toggleContents">二重国籍のお客様は、ご搭乗時に使用されるパスポートのお名前でご予約ください。<br />パスポートごとにお名前が異なる場合は、お客様ご自身で入国管理事務所・外務省・大使館などで各種条件をご確認のうえ、使用されるパスポートを決めてください。<br />なお、入国可否については国によって異なるため、お客様ご自身で各入国管理事務所などでご確認ください。</li>
									<li class="jsHiddenFlg toggleContents">旅行開始日時点で12歳以上の場合は大人、2歳以上12歳未満の場合は小児、2歳未満の場合は幼児の扱いとなります。なお、小児は大人と同マイル数が必要です。</li>
									<li class="jsHiddenFlg toggleContents">運航に関する情報をお客様へ通知するため、提携航空会社にもお客様のメールアドレスが提供されます。あらかじめご了承ください。</li>
									<li class="jsHiddenFlg toggleContents"><dl><dt>携帯電話へのメール送信をご希望のお客様へ</dt><dd>ドメイン指定受信をご利用の場合は、ana.co.jpとamadeus.comからのメールが受信できるようあらかじめ設定をお願いいたします。</dd></dl></li>
									<li class="jsHiddenFlg toggleContents">SMSはスマートフォンの番号に配信されます。一部の国ではSMSでのご案内をご利用いただけません。詳細は<a href="https://www.ana.co.jp/other/int/meta/0737.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="ショートメッセージ(SMS)でのご案内対象外の国について(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">ショートメッセージ(SMS)でのご案内対象外の国について<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>をご覧ください。</li>
									<li class="jsHiddenFlg toggleContents">ログインされている会員ご本人が搭乗しない場合は、再度フライト検索画面より「ログインされている会員ご本人は搭乗しない」のチェックボックスを選択してお手続きください。</li>
						</ul>
							<a href="#" class="toggleSwitch jsTriggerSwitch" role="button" aria-controls="informationMessages" aria-expanded="false"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/toggle_open.png?7bec38a" alt="開閉ボタン" height="25" width="25" class="jsRollOver" />
							</a>
					</dd>
				</dl></div>
	<!--=========== /main =============-->
	</div>

	<!--=========== Script ============-->
	<script type="text/javascript">
	$(window).on('load', function() {
		// アコーディオンのメソッドと設定
		Asw.AccordionInfo('.jsAccordionSwitch', {
			'triggerSwitch': '.jsAccordionSwitch .jsTriggerSwitch',
			'listClass': '.jsAccordionSwitchList',
			'visibilityHidden': 'toggleContents'
		});
	});
	</script>
	<!--=========== Script ============-->
	<script type="text/javascript">
		//プラットフォーム判別（サマリーエリア追従用として）
		var Asw = Asw || {};
	
	  	Asw.ClientInfo = Asw.ClientInfo  || {};
	  	Asw.ClientInfo.DeviceType = Asw.ClientInfo.DeviceType || {};
	
	  	Asw.ClientInfo.DeviceType.PC = 'PC';
	  	Asw.ClientInfo.DeviceType.SP = 'SP';
	  	Asw.ClientInfo.DeviceType.TC = 'TC';
	
	  	Asw.ClientInfo.deviceType = Asw.ClientInfo.DeviceType.PC;
	
	 	if((navigator.userAgent.indexOf('Android') > 0 && navigator.userAgent.indexOf('Mobile') == -1) || navigator.userAgent.indexOf('iPad') > 0){
	    	Asw.ClientInfo.deviceType = Asw.ClientInfo.DeviceType.TC;
	  	}
	</script>
	<!--=========== /Script ============--><div id="summaryArea">
			<div class="flightSummaryArea">
						<div class="flightSummarySection">
							<h2>区間 1
							</h2>
							<p class="flightDate"><em>6</em>月<em class="hasLeft">28</em>日（土）
							</p>
							<div class="flightSummaryRoute">
									<div class="flightSummaryRouteSection">
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation1_1">
													<span>東京(成田)
													</span>
												</p>
												<p class="time">
													<span>17:00
													</span>
												</p>
											</div>
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation1_1">
													<span>シカゴ(ORD)
													</span>
												</p>
												<p class="time">
													<span>14:55
													</span>
												</p>
											</div>
										<p class="flightNumberClass">
												<span>NH012</span><span><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/flight_ana_summaryarea.png?717d3c0" alt="ANA運航便" height="11" width="41" /></span>
												<br />特典ビジネス
										</p>
									</div>
							</div>
						</div>
						<div class="flightSummarySection">
							<h2 class="midstream">区間 2
							</h2>
							<p class="flightDate"><em>7</em>月<em class="hasLeft">23</em>日（水）
							</p>
							<div class="flightSummaryRoute">
									<div class="flightSummaryRouteSection">
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation2_1">
													<span>ホノルル(オアフ島)
													</span>
												</p>
												<p class="time">
													<span>11:35
													</span>
												</p>
											</div>
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation2_1">
													<span>東京(成田)
													</span>
												</p>
												<p class="time">
													<span>14:50
																	<span>翌日
																	</span>
													</span>
												</p>
											</div>
										<p class="flightNumberClass">
												<span>NH183</span><span><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/flight_ana_summaryarea.png?717d3c0" alt="ANA運航便" height="11" width="41" /></span>
												<br />特典エコノミー
										</p>
									</div>
							</div>
						</div>
					<div class="total">
						<p class="passenger">大人 x1
						</p>
						<div class="totalArea">
					  		<dl class="mealAmountWrap">
									<dt>有料機内食サービス
									</dt>
									<dd><em class="mealAmount">0</em><span class="currencyCode">円</span>
									</dd>
							</dl>
									<div>
										<div class="mileageAndTotal">
												<dl class="requiredMileage">
													<dt>必要マイル
													</dt>
													<dd><em class="requiredMileage">74,000</em><span class="currencyCode">マイル</span>
													</dd>
												</dl>
												<dl>
													<dt>総額
													</dt>
													<dd><em class="awardTotalPayment">68,130</em>円
													</dd>
												</dl>
												<p class="includeFareAndFuelCost">※各種税金、燃油特別付加運賃等を含みます。
												</p>
										</div>
									</div>
						</div>
					</div>
				
			</div></div>
		<!--===== /cmnWrapper =====-->
		</div>
			
		<span id="message" class="visuallyHidden" role="alert" aria-live="assertive"></span>
		<span id="politeMessage" class="visuallyHidden" role="log" aria-live="polite"></span><script type="text/javascript">
		var Asw = Asw || {};
		Asw.BaseOutput = {"sessionID":"YiyL1a-aid","language":"ja","siteCatalystLanguageCode":"J","operationDate":"20250620","operationDateTime":"20250620143538","office":{"officeCode":"TYONH08ZZ","country2letterCode":"JP"},"bookingType":"A","pageID":"A04_P01","siteCatalystPageName":"INT_BE_AWARD_J_A04特典予約_P01旅客情報入力（必須情報）","device":{"deviceType":"PC"}};
	</script><div id="promotionArea">
		<!--===== promotionArea =====-->
		<!--===== /promotionArea =====--></div>
		<!--=========== /cmnContainer ============--></div><div id="cmnBaloonArea" aria-live="polite"></div><div id="cmnPopupArea" aria-live="polite"></div>

	

	<div id="cmnFooterWrapper">
		<!--=========== .cmnFooterWrapper ============-->
		<div id="cmnFooter">
			<!--===== cmnFooter =====-->
			<ul>
				<li><a href="https://www.ana.co.jp/other/int/meta/0012.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">プライバシーポリシー<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
					<li><a href="https://www.ana.co.jp/other/int/meta/0670.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">利用者情報の外部送信について<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
					</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/0052.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">サイト利用規約<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/be0238.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">推奨環境<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/0133.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">サイトマップ<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/0732.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">運送約款<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
					<li class="lastChild"><a href="https://www.ana.co.jp/other/int/meta/0556.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">ウェブアクセシビリティについて<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
					</li>
			</ul>
			<p class="blankText" aria-hidden="true"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="" height="10" width="10" />外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。</p>
			<p class="copyright"><span lang="en" xml:lang="en">Copyright&#169;ANA</span>
			</p>
			<p class="starAllianceLogo"><a href="http://www.staralliance.com/ja/" target="_blank"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/star_alliance_logo.png?0fe73b5" alt="A STAR ALLIANCE MEMBER" height="18" width="175" /><span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
			</p>
			<!--===== cmnFooter =====-->
		</div>
		<!--=========== /cmnFooterWrapper ============-->
	</div>

		
		<div id="cmnLoadingForAjax"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/bg_modalWindow.png?717d3c0" alt="" height="0" width="0" />
			<div class="loadingArea">
				<p class="loadingImage"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/loading.gif?717d3c0" alt="リクエストを処理しています...しばらくお待ちください。" height="33" width="33" />
				</p>
			</div>
		</div>
		
		
		<div id="popupAltSentence" style="display: none" aria-hidden="true">別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。</div>
	<div id="cmnSiteCatalyst" aria-hidden="true"><div id="cmnSiteCatalystParamArea"><script type="text/javascript">
				// ▼SiteCatalyst
				// SiteCatalyst code version: H.2.
				// Copyright 1997-2005 Omniture, Inc. More info available at http://www.omniture.com
				SiteCatalystReportSuites   = "INT";
				SiteCatalystCharSet        = "UTF-8";
				SiteCatalystChannel        = "BE_AWARD_J";
				SiteCatalystPageName       = "INT_BE_AWARD_J_A04\u7279\u5178\u4E88\u7D04_P01\u65C5\u5BA2\u60C5\u5831\u5165\u529B\uFF08\u5FC5\u9808\u60C5\u5831\uFF09";
				SiteCatalystERR            = "";
				SiteCatalystEventNum       = "";
				SiteCatalystPurchase       = "FALSE";
				SiteCatalystProductPrice   = "";
				SiteCatalystCurrencyCode   = "";
				SiteCatalystSeatAvailabilityINT   = "";
				SiteCatalystDepartDate     = "";
				SiteCatalystDepart         = "";
				SiteCatalystArrive         = "";
				SiteCatalystFlightNo       = "";
				SiteCatalystClass          = "";
				SiteCatalystFare           = "";
				SiteCatalystPurchaseID     = "";
				
				SiteCatalystLocater        = "";
				SiteCatalystPayment        = "";
				SiteCatalystIncome         = "";
				
				SiteCatalystPax            = "";
				SiteCatalystManipulateDay  = "";
				SiteCatalystCompartmentClass   = "";
				SiteCatalystOptionInfo     = "";
				SiteCatalystOutboundUpsellPrice   = "";
				SiteCatalystInboundUpsellPrice    = "";
				
				SiteCatalystSearchInput    = "";
				SiteCatalystSegment        = "";
				SiteCatalystOfficeCode     = "";
				SiteCatalystPassengerCount = "";
				SiteCatalystPassenger      = "";
				SiteCatalystAncillaryService = "";
				// End SiteCatalyst code version: H.2.
				// ▲SiteCatalyst
			</script></div><script type="text/javascript"><!-- if(navigator.appVersion.indexOf('MSIE')>=0)document.write(unescape('%3C')+'\!-'+'-') // --></script>
		<!--/DO NOT REMOVE/-->
		

		
		<!-- Google Tag Manager -->
		<script type="text/javascript" charset="Shift_JIS" src="https://www.ana.co.jp/common/js/GTMINTBE.js"></script>
		<!-- End Google Tag Manager -->
		<!-- Yahoo Tag Manager -->
		<script type="text/javascript" charset="Shift_JIS" src="https://www.ana.co.jp/common/js/YTMINTBE.js"></script>
		<!-- End Yahoo Tag Manager -->
		

	</div>

	

	<div class="mboxDefault"></div><script type="text/javascript">mboxCreate("ASW_common_A04_P01");</script>
			<script type="text/javascript" src="https://www.ana.co.jp/common/js/tealium/tealium.js"></script>
			<script type="text/javascript" src="//cdn.evgnet.com/beacon/allnipponairways/nonamcprd/scripts/evergage.min.js"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/javax.faces.resource/jsf.js.xhtml?ln=javax.faces"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/jquery.mousewheel.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/jquery.jscrollpane.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/jquery-ui-1.10.4.custom.min.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/jquery.ui.touch-punch.min.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-common.js?beb6a50"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-common-pc.js?fd88d7d"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-format.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/mask.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/loading.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-loadingwindow-pc.js?9e233c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-loadingwindow.js?51db600"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/dialog-pc.js?25d36a4"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-dialog-sequencer.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-event.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-date-select.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-calendar-pc.js?9b86b8a"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-calendar.js?db624c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/heightLine.js?0e7a1ce"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-summaryarea.js?b21c6d7"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-fingerprints.js?99001fc"></script><script type="text/javascript">
			Asw.init("https://aswbe-i.ana.co.jp/rei21c/international_asw", "ja");
			Asw.getCalendarHolidayList = function() {return [20250721,20250811,20250915,20250923,20251013,20251103,20251123,20251124,20260101,20260112,20260211,20260223,20260320,20260429,20260503,20260504,20260505,20260506,20260720,20260811,20260921,20260922,20260923,20261012,20261103,20261123,20270101,20270111,20270211,20270223,20270321,20270322,20270429,20270503,20270504,20270505,20270719,20270811,20270920,20270923,20271011,20271103,20271123,20280101,20280110,20280211,20280223,20280320,20280429,20280503,20280504,20280505,20280717,20280811,20280918,20280922,20281009,20281103,20281123,20290101,20290108,20290211,20290212,20290223,20290320,20290429,20290430,20290503,20290504,20290505,20290716,20290811,20290917,20290923,20290924,20291008,20291103,20291123];}
			Asw.ClientInfo.deviceType = "PC";
			Asw.ClientInfo.mobileDeviceType = "UNKNOWN";
			Asw.ClientInfo.osVersion = "";
			Asw.Format.init("https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/{1}/{2}/{3}?{0}");
			Asw.overwriteTargetAttributeValue("false");
		</script><script type="text/javascript">
		
		var Asw = Asw || {};
		Asw.ContactInput = Asw.ContactInput || {};
		Asw.ContactInput.Const = {
			/** data-属性名「data-type」 @constant @type {String} @default 'data-type' */
			DATA_ATTRIBUTE_NAME_TYPE: 'data-type',
			/** data-属性名「data-sms-possible」 @constant @type {String} @default 'data-sms-possible' */
			DATA_ATTRIBUTE_NAME_SMS_POSSIBLE: 'data-sms-possible',
			/** data-属性名「data-country-code」 @constant @type {String} @default 'data-country-code' */
			DATA_ATTRIBUTE_NAME_COUNTRY_CODE: 'data-country-code',
			/** data-type属性値「sms」 @constant @type {String} @default 'sms' */
			DATA_TYPE_VALUE_SMS: 'sms'
		};

		
		Asw.ContactInput.countryInfoMap = {"IS":{"internationalCallingCode":"+354","smsPossible":false},"IE":{"internationalCallingCode":"+353","smsPossible":true},"AZ":{"internationalCallingCode":"+994","smsPossible":true},"AF":{"internationalCallingCode":"+93","smsPossible":true},"US":{"internationalCallingCode":"+1","smsPossible":true},"AE":{"internationalCallingCode":"+971","smsPossible":true},"DZ":{"internationalCallingCode":"+213","smsPossible":true},"AR":{"internationalCallingCode":"+54","smsPossible":true},"AW":{"internationalCallingCode":"+297","smsPossible":true},"AL":{"internationalCallingCode":"+355","smsPossible":true},"AM":{"internationalCallingCode":"+374","smsPossible":true},"AO":{"internationalCallingCode":"+244","smsPossible":false},"AG":{"internationalCallingCode":"+1","smsPossible":true},"AD":{"internationalCallingCode":"+376","smsPossible":true},"YE":{"internationalCallingCode":"+967","smsPossible":true},"GB":{"internationalCallingCode":"+44","smsPossible":true},"IL":{"internationalCallingCode":"+972","smsPossible":true},"IT":{"internationalCallingCode":"+39","smsPossible":true},"IQ":{"internationalCallingCode":"+964","smsPossible":true},"IR":{"internationalCallingCode":"+98","smsPossible":true},"IN":{"internationalCallingCode":"+91","smsPossible":true},"ID":{"internationalCallingCode":"+62","smsPossible":true},"UG":{"internationalCallingCode":"+256","smsPossible":true},"UA":{"internationalCallingCode":"+380","smsPossible":true},"UZ":{"internationalCallingCode":"+998","smsPossible":true},"UY":{"internationalCallingCode":"+598","smsPossible":true},"EC":{"internationalCallingCode":"+593","smsPossible":true},"EG":{"internationalCallingCode":"+20","smsPossible":true},"EE":{"internationalCallingCode":"+372","smsPossible":true},"SZ":{"internationalCallingCode":"+268","smsPossible":false},"ET":{"internationalCallingCode":"+251","smsPossible":false},"ER":{"internationalCallingCode":"+291","smsPossible":true},"SV":{"internationalCallingCode":"+503","smsPossible":true},"AU":{"internationalCallingCode":"+61","smsPossible":true},"AT":{"internationalCallingCode":"+43","smsPossible":true},"OM":{"internationalCallingCode":"+968","smsPossible":true},"NL":{"internationalCallingCode":"+31","smsPossible":true},"GH":{"internationalCallingCode":"+233","smsPossible":true},"CV":{"internationalCallingCode":"+238","smsPossible":false},"GY":{"internationalCallingCode":"+592","smsPossible":true},"KZ":{"internationalCallingCode":"+7","smsPossible":true},"QA":{"internationalCallingCode":"+974","smsPossible":true},"CA":{"internationalCallingCode":"+1","smsPossible":true},"GA":{"internationalCallingCode":"+241","smsPossible":true},"CM":{"internationalCallingCode":"+237","smsPossible":true},"KR":{"internationalCallingCode":"+82","smsPossible":true},"KH":{"internationalCallingCode":"+855","smsPossible":true},"GM":{"internationalCallingCode":"+220","smsPossible":true},"KP":{"internationalCallingCode":"+850","smsPossible":false},"MK":{"internationalCallingCode":"+389","smsPossible":false},"GN":{"internationalCallingCode":"+224","smsPossible":true},"GW":{"internationalCallingCode":"+245","smsPossible":true},"CY":{"internationalCallingCode":"+357","smsPossible":true},"CU":{"internationalCallingCode":"+53","smsPossible":true},"GR":{"internationalCallingCode":"+30","smsPossible":true},"KI":{"internationalCallingCode":"+686","smsPossible":false},"KG":{"internationalCallingCode":"+996","smsPossible":true},"GT":{"internationalCallingCode":"+502","smsPossible":true},"GU":{"internationalCallingCode":"+1","smsPossible":true},"KW":{"internationalCallingCode":"+965","smsPossible":true},"KY":{"internationalCallingCode":"+1","smsPossible":true},"GD":{"internationalCallingCode":"+1","smsPossible":true},"HR":{"internationalCallingCode":"+385","smsPossible":true},"KE":{"internationalCallingCode":"+254","smsPossible":true},"CI":{"internationalCallingCode":"+225","smsPossible":true},"CR":{"internationalCallingCode":"+506","smsPossible":true},"SA":{"internationalCallingCode":"+966","smsPossible":true},"KM":{"internationalCallingCode":"+269","smsPossible":true},"CO":{"internationalCallingCode":"+57","smsPossible":true},"CG":{"internationalCallingCode":"+242","smsPossible":false},"CD":{"internationalCallingCode":"+243","smsPossible":true},"ZM":{"internationalCallingCode":"+260","smsPossible":true},"SM":{"internationalCallingCode":"+378","smsPossible":false},"SL":{"internationalCallingCode":"+232","smsPossible":true},"DJ":{"internationalCallingCode":"+253","smsPossible":false},"JM":{"internationalCallingCode":"+1","smsPossible":true},"CH":{"internationalCallingCode":"+41","smsPossible":true},"GE":{"internationalCallingCode":"+995","smsPossible":true},"SE":{"internationalCallingCode":"+46","smsPossible":true},"SY":{"internationalCallingCode":"+963","smsPossible":true},"ES":{"internationalCallingCode":"+34","smsPossible":true},"SG":{"internationalCallingCode":"+65","smsPossible":true},"LK":{"internationalCallingCode":"+94","smsPossible":true},"SK":{"internationalCallingCode":"+421","smsPossible":true},"SI":{"internationalCallingCode":"+386","smsPossible":true},"ZW":{"internationalCallingCode":"+263","smsPossible":true},"SD":{"internationalCallingCode":"+249","smsPossible":true},"KN":{"internationalCallingCode":"+1","smsPossible":true},"SR":{"internationalCallingCode":"+597","smsPossible":true},"LC":{"internationalCallingCode":"+1","smsPossible":true},"SC":{"internationalCallingCode":"+248","smsPossible":false},"SN":{"internationalCallingCode":"+221","smsPossible":true},"SB":{"internationalCallingCode":"+677","smsPossible":false},"RS":{"internationalCallingCode":"+381","smsPossible":true},"TH":{"internationalCallingCode":"+66","smsPossible":true},"TW":{"internationalCallingCode":"+886","smsPossible":true},"TJ":{"internationalCallingCode":"+992","smsPossible":true},"TZ":{"internationalCallingCode":"+255","smsPossible":true},"CZ":{"internationalCallingCode":"+420","smsPossible":true},"TD":{"internationalCallingCode":"+235","smsPossible":true},"CF":{"internationalCallingCode":"+236","smsPossible":true},"TN":{"internationalCallingCode":"+216","smsPossible":true},"CL":{"internationalCallingCode":"+56","smsPossible":true},"TV":{"internationalCallingCode":"+688","smsPossible":false},"DK":{"internationalCallingCode":"+45","smsPossible":true},"DE":{"internationalCallingCode":"+49","smsPossible":true},"TG":{"internationalCallingCode":"+228","smsPossible":true},"DO":{"internationalCallingCode":"+1","smsPossible":true},"DM":{"internationalCallingCode":"+1","smsPossible":false},"TT":{"internationalCallingCode":"+1","smsPossible":true},"TM":{"internationalCallingCode":"+993","smsPossible":false},"TR":{"internationalCallingCode":"+90","smsPossible":true},"TO":{"internationalCallingCode":"+676","smsPossible":false},"NG":{"internationalCallingCode":"+234","smsPossible":true},"NR":{"internationalCallingCode":"+674","smsPossible":false},"NA":{"internationalCallingCode":"+264","smsPossible":false},"NI":{"internationalCallingCode":"+505","smsPossible":true},"BH":{"internationalCallingCode":"+973","smsPossible":false},"NE":{"internationalCallingCode":"+227","smsPossible":true},"PK":{"internationalCallingCode":"+92","smsPossible":true},"JP":{"internationalCallingCode":"+81","smsPossible":true},"BS":{"internationalCallingCode":"+1","smsPossible":true},"NZ":{"internationalCallingCode":"+64","smsPossible":true},"BM":{"internationalCallingCode":"+1","smsPossible":true},"NP":{"internationalCallingCode":"+977","smsPossible":true},"BB":{"internationalCallingCode":"+246","smsPossible":true},"NO":{"internationalCallingCode":"+47","smsPossible":true},"HT":{"internationalCallingCode":"+509","smsPossible":true},"BD":{"internationalCallingCode":"+880","smsPossible":true},"VA":{"internationalCallingCode":"+39","smsPossible":false},"PA":{"internationalCallingCode":"+507","smsPossible":true},"VU":{"internationalCallingCode":"+678","smsPossible":false},"PG":{"internationalCallingCode":"+675","smsPossible":true},"PW":{"internationalCallingCode":"+680","smsPossible":true},"PY":{"internationalCallingCode":"+595","smsPossible":true},"PS":{"internationalCallingCode":"+970","smsPossible":true},"BR":{"internationalCallingCode":"+55","smsPossible":true},"HU":{"internationalCallingCode":"+36","smsPossible":true},"TL":{"internationalCallingCode":"+670","smsPossible":true},"BG":{"internationalCallingCode":"+359","smsPossible":true},"BF":{"internationalCallingCode":"+226","smsPossible":true},"BN":{"internationalCallingCode":"+673","smsPossible":true},"FJ":{"internationalCallingCode":"+679","smsPossible":true},"PH":{"internationalCallingCode":"+63","smsPossible":true},"FI":{"internationalCallingCode":"+358","smsPossible":true},"BT":{"internationalCallingCode":"+975","smsPossible":true},"BY":{"internationalCallingCode":"+375","smsPossible":true},"BZ":{"internationalCallingCode":"+501","smsPossible":false},"PR":{"internationalCallingCode":"+1","smsPossible":true},"BE":{"internationalCallingCode":"+32","smsPossible":true},"BA":{"internationalCallingCode":"+387","smsPossible":true},"FR":{"internationalCallingCode":"+33","smsPossible":true},"BO":{"internationalCallingCode":"+591","smsPossible":false},"PF":{"internationalCallingCode":"+689","smsPossible":true},"BI":{"internationalCallingCode":"+257","smsPossible":true},"VN":{"internationalCallingCode":"+84","smsPossible":true},"BJ":{"internationalCallingCode":"+229","smsPossible":true},"VE":{"internationalCallingCode":"+58","smsPossible":true},"PE":{"internationalCallingCode":"+51","smsPossible":true},"PL":{"internationalCallingCode":"+48","smsPossible":true},"BW":{"internationalCallingCode":"+267","smsPossible":true},"PT":{"internationalCallingCode":"+351","smsPossible":false},"HK":{"internationalCallingCode":"+852","smsPossible":true},"HN":{"internationalCallingCode":"+504","smsPossible":false},"MH":{"internationalCallingCode":"+692","smsPossible":true},"MO":{"internationalCallingCode":"+853","smsPossible":true},"MG":{"internationalCallingCode":"+261","smsPossible":true},"MW":{"internationalCallingCode":"+265","smsPossible":true},"ML":{"internationalCallingCode":"+223","smsPossible":true},"MT":{"internationalCallingCode":"+356","smsPossible":true},"MY":{"internationalCallingCode":"+60","smsPossible":true},"FM":{"internationalCallingCode":"+691","smsPossible":false},"MM":{"internationalCallingCode":"+95","smsPossible":true},"MX":{"internationalCallingCode":"+52","smsPossible":true},"MU":{"internationalCallingCode":"+230","smsPossible":true},"MR":{"internationalCallingCode":"+222","smsPossible":true},"MZ":{"internationalCallingCode":"+258","smsPossible":true},"MC":{"internationalCallingCode":"+377","smsPossible":true},"MV":{"internationalCallingCode":"+960","smsPossible":true},"MD":{"internationalCallingCode":"+373","smsPossible":false},"RO":{"internationalCallingCode":"+40","smsPossible":true},"MA":{"internationalCallingCode":"+212","smsPossible":true},"MN":{"internationalCallingCode":"+976","smsPossible":true},"ME":{"internationalCallingCode":"+382","smsPossible":true},"JO":{"internationalCallingCode":"+962","smsPossible":true},"LA":{"internationalCallingCode":"+856","smsPossible":true},"RU":{"internationalCallingCode":"+7","smsPossible":true},"LV":{"internationalCallingCode":"+371","smsPossible":true},"LT":{"internationalCallingCode":"+370","smsPossible":true},"LY":{"internationalCallingCode":"+218","smsPossible":true},"GQ":{"internationalCallingCode":"+240","smsPossible":false},"LI":{"internationalCallingCode":"+423","smsPossible":false},"LR":{"internationalCallingCode":"+231","smsPossible":true},"CN":{"internationalCallingCode":"+86","smsPossible":true},"LU":{"internationalCallingCode":"+352","smsPossible":true},"ZA":{"internationalCallingCode":"+27","smsPossible":true},"RW":{"internationalCallingCode":"+250","smsPossible":true},"LS":{"internationalCallingCode":"+266","smsPossible":false},"LB":{"internationalCallingCode":"+961","smsPossible":true}};

		$(function () {
			contactInfoInit();
		});
		
		function contactInfoInit() {
			
			
			Asw.get('representativeTelType').find('option[value="M1"]').attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_TYPE,
					Asw.ContactInput.Const.DATA_TYPE_VALUE_SMS);
			Asw.get('representativeTelCountry').children('option').each(function(i,option){
				var countryInfo = Asw.ContactInput.countryInfoMap[option.value];
				if (countryInfo === void 0) {
					
					return true;
				}
				$(option).attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_SMS_POSSIBLE, countryInfo.smsPossible);
				$(option).attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_COUNTRY_CODE, countryInfo.internationalCallingCode);
			});
			
			var smsCountrySelectSelector = 'select.js-countrySelect:not(#representativeTelCountry)'
			$(smsCountrySelectSelector).eq(0).children('option').each(function(i,option){
				var countryInfo = Asw.ContactInput.countryInfoMap[option.value];
				if (countryInfo === void 0) {
					
					return true;
				}
				$(smsCountrySelectSelector + ' option:nth-child(' + (i+1) + ')').attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_COUNTRY_CODE,
						countryInfo.internationalCallingCode);
			});
			
			var emergencyCountrySelectSelector = 'select.passengerEmergencyCountry.js-countrySelect:not(#representativeTelCountry)'
			$(emergencyCountrySelectSelector).eq(0).children('option').each(function(i,option){
				var countryInfo = Asw.ContactInput.countryInfoMap[option.value];
				if (countryInfo === void 0) {
					
					return true;
				}
				$(emergencyCountrySelectSelector + ' option:nth-child(' + (i+1) + ')').attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_COUNTRY_CODE,
						countryInfo.internationalCallingCode);
			});
		}
	</script><script type="text/javascript">
		var isInit = true;
		function changeTelType(_this, telNoInputId) {
			var $telNoInput = Asw.get(telNoInputId);
			var $telNoKind = Asw.get("representativeTelType");
			var telNo = Asw.getValueComparedToPlaceholder(telNoInputId);
			var telNoCountry = Asw.get("representativeTelCountry").val();

			if(true && !isInit) {
				if(telInputCheck(telNo, telNoCountry)) {
					if (_this.value == "M1") {
						telNo = exceptDomesticPrefix("09042504176", telNoCountry);
					} else if (_this.value == "B1") {
						telNo = exceptDomesticPrefix("03-5405-2172", telNoCountry);
					} else {
						telNo = exceptDomesticPrefix("090-4250-4176", telNoCountry);
					}
					$telNoInput.val(telNo);
				}
			}
			isInit = false;
		}
		
		function telInputCheck(telNo, telNoCountry) {
	        checkResult = false;
	        
	        var amcHndyTelNo = exceptDomesticPrefix("09042504176", telNoCountry);
	        var amcCmpTelNo = exceptDomesticPrefix("03-5405-2172", telNoCountry);
	        var amcTelNo = exceptDomesticPrefix("090-4250-4176", telNoCountry);
	
	        if(telNo == "" || telNo == amcHndyTelNo || telNo == amcCmpTelNo || telNo == amcTelNo) {
	        	checkResult = true;
	        }
	        return checkResult;
    	}
    	
    	function exceptDomesticPrefix(telNo, telNoCountry) {
    		if(telNoCountry == 'JP') {
    			if(telNo.substr(0, 1) === '0') {
    				return telNo.slice(1);
    			}
    		}
    		return telNo;
    	}
	</script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-customerinfomationform.js?6e91953"></script><script type="text/javascript">
		$(document).ready(function(){
				Asw.enableTextWhenCheckBox('.cascadedSwitch');
				Asw.notPasteInput('.jsNotPasteInput');
			$(function(){
				Asw.alignHeightToParentTdElement( '.passengerInfoInputArea' );
				
					Asw.alignHeightToParentTdElement( '.passengerInfoInputText' );
				
				
				$('select.js-countrySelect').trigger('change');
			});
		});
	</script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-session-keeper.js?6ec0730"></script><script type="text/javascript">
			$(document).ready(function() {
				Asw.SessionKeeper.initAndStart("sessionKeeperContainer", "520", "570", "PC");
			});
			</script><script type="text/javascript">
		Asw.addAjaxCallback(function(data) {
			// アコーディオンのメソッドと設定 
			if($('.jsAccordionSwitch .jsTriggerSwitch').length > 0){
				var events = $._data($('.jsAccordionSwitch .jsTriggerSwitch').get(0), "events");
				if(events === undefined || events.click === undefined) {
					Asw.AccordionInfo('.jsAccordionSwitch', {
						'triggerSwitch': '.jsAccordionSwitch .jsTriggerSwitch',
						'listClass': '.jsAccordionSwitchList',
						'visibilityHidden': 'toggleContents'
					});
				}
			}
		});
	</script><script type="text/javascript">
	Asw.Price = Asw.Price || {};
	// 整数の区切り文字
	Asw.Price.separateString = ',';
	
	// 整数の区切り桁数
	Asw.Price.separateDigitNumber = 3;
	
	// 小数点
	Asw.Price.decimalPointString = '.';
	
	// 小数点以降の有効桁数
	Asw.Price.significantDigit = 0;
	
	// 表示金額の単位
	Asw.Price.currencyUnit = '円';
	</script><script type="text/javascript">
			$(document).ready(function() {
				$('#preLoadingArea').css('visibility', 'hidden');
			});
		</script><noscript><img src="https://aswbe-i.ana.co.jp/akam/13/pixel_70f41f14?a=dD1iM2Y3OTkzNmMwMjMzNzc1ZjI1YmVkNmQwMzg2MDI0OGM0MmM0ZGM0JmpzPW9mZg==" style="visibility: hidden; position: absolute; left: -999px; top: -999px;" /></noscript><script type="text/javascript"  src="/Q1XS4o/Cn_ls/KfDO7/xA/t9OfhmEwSiGQaG/NG4dCQE/bw/QJBChQIQEB"></script></body>
</html>