<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="ja-<PERSON>" xml:lang="ja-<PERSON>">

	
	<!-- skey = 2025/06/20 13:32:52 rei22h fCWLnDMoai dljdmx+1ad  --><head id="j_idt53">
    <base href="about:blank">
    <style>
        /* Hide elements that might try to load external resources, as a fallback. */
        img, svg, video, iframe, link[rel="stylesheet"], script { display: none !important; }
    </style>

		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
			<meta http-equiv="X-UA-Compatible" content="IE=EmulateIE9; IE=EmulateIE10" />
			<meta http-equiv="X-UA-Compatible" content="IE=7" />
			<meta http-equiv="X-UA-Compatible" content="IE=edge" />
		<meta name="description" content="" />
		<meta name="keywords" content="" />
		<meta name="format-detection" content="telephone=no" />
		<script type="text/javascript" src="/international_asw/dynatrace/ruxitagentjs_ICANVfhqrux_10273230920145353.js" data-dtconfig="rid=RID_1305803050|rpid=-1376355812|domain=aswbe-i.ana.co.jp|reportUrl=https://bf90419wvr.bf.dynatrace.com/bf|app=78bf0b58acf6ed13|cors=1|owasp=1|featureHash=ICANVfhqrux|xb=www^bs.google-analytics^bs.com^pana^bs.demdex^bs.net^pcollect^bs.tealiumiq^bs.com^pallnipponairways^bs.australia-3^bs.evergage^bs.com^pallnipponairwayscolt^bs.tt^bs.omtrdc^bs.net|rdnt=1|uxrgce=1|cuc=k8vpgh61|mel=100000|expw=1|dpvc=1|md=mdcc1=bAsw.PnrOutput.recordLocator,mdcc2=bAsw.PnrOutput.paymentDetailInfoList[0].fopCode,mdcc3=bAsw.PnrOutput.ticketedFlag,mdcc4=bAsw.PnrOutput.nhPnrFlag,mdcc5=bAsw.PnrOutput.itinerarySummary.departureDateYyyymmdd,mdcc6=bAsw.SearchCriteriaOutput.cffCodeInput,mdcc7=a#main ^rb div.section.sectionNoFlow ^rb table ^rb tbody ^rb tr ^rb td.flightNumber.lastCol ^rb span ^rb span:nth-child(2),mdcc8=cunique|lastModification=1750184416409|tp=500,50,0|agentUri=/international_asw/dynatrace/ruxitagentjs_ICANVfhqrux_10273230920145353.js"></script><link rel="shortcut icon" href="https://www.ana.co.jp/favicon.ico" />
		<title>お客様情報入力 | ANA</title>
		<noscript><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/css/asw_noscript_pc.css?99001fc" />
		</noscript>

		<!--  [ Adobe Target ] -->
		<script type="text/javascript" src="https://www.ana.co.jp/behavior/adobetarget/target_intbe.js"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/jquery-1.8.3.min.js?717d3c0"></script><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/ja/css/asw_initialize.css?99001fc" /><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/css/asw_screen_pc.css?99001fc" /><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/css/asw_common_pc.css?99001fc" /><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/css/asw_forminput_pc.css?99001fc" /><script type="text/javascript">
		var Asw = Asw || {};
		Asw.GlobalWord = Asw.GlobalWord || {};
		
		Asw.GlobalWord.toolTipCloseIconAltText = "閉じる";
		
		</script><script >bazadebezolkohpepadr="1247357069"</script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/akam/13/4a592902"  defer></script></head><body>
		
		<noscript>当サイトをご利用になるためには、JavaScript対応のブラウザが必要です。設定でJavaScriptを有効にしてください。有効にできない場合はANA電話窓口へお問い合わせください。
		</noscript>
		<div id="preLoadingArea"></div>
	
	<div id="cmnHeader" class="platinum">
		<!--===== cmnHeader =====-->
		<div class="headerWrapper">
			<div class="header">
				<p class="logo"><a href="https://www.ana.co.jp/asw/AMCTopServlet?type=a" tabindex="-1" aria-hidden="true"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/ana_logo_navy.png?717d3c0" alt="ANA Inspiration of JAPAN。リンク先はマイレージクラブトップページです。" height="28" width="274" /></a>
				</p>
					<ul class="headerMenu">
						<li><a href="https://www.ana.co.jp/asw/AMCTopServlet?type=a">ANAマイレージクラブトップページ</a>
						</li>
						<li><a href="https://www.ana.co.jp/other/int/meta/0005.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">お問い合わせ<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
						</li>
							<li class="btnLogoutArea"><a href="https://aswbe-i.ana.co.jp/rei22h/international_asw/rest/logout" role="button" class="btnBase btnLogout">ログアウト</a>
							</li>
					</ul>
			</div>
		</div>
			<div class="amcUserInfoWrapper">
				<div class="amcUserInfo">
					<div class="userNameArea">
						<table>
							<tr>	
									<td class="statusIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_status_05.png?717d3c0" alt="プラチナサービスメンバー" height="46" width="60" />
									</td>
								<td class="userName">佐藤 直樹 <span>様</span>
								</td>
							</tr>					
						</table>
					</div>
						<div class="balance">
								<dl class="upgradePointBalance">
									<dt>アップグレードポイント残高
									</dt>
									<dd>
											<dl>
												<dt>(2025年度)
												</dt>
												<dd>
													<em>20
													</em>ポイント
												</dd>
											</dl>
									</dd>
								</dl>
							<dl class="mileBalance">
								<dt>マイル口座残高
								</dt>
								<dd><em>75,663</em><span class="currencyCode">マイル</span>
								</dd>
							</dl>
						</div>
				</div>
					<p class="nowTime">6月20日13時32分現在
					</p>
			</div>
		<!--===== /cmnHeader =====-->
	</div>
		<div id="sessionKeeperContainer" class="sessionKeepingContainer"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/bg_modalWindow.png?717d3c0" alt="" height="0" width="0" />
			<div id="extendSessionExpiredDialog" class="modalExpire modalSmall modalContainer" role="dialog" aria-labelledby="extendSessionExpiredDialogTitle" aria-hidden="true">
<form id="sessionKeeperContainer:j_idt183" name="sessionKeeperContainer:j_idt183" method="post" action="https://aswbe-i.ana.co.jp/rei22h/international_asw/pages/award/reservation/award_mandatory_passenger_information_input.xhtml?aswcid=1&amp;rand=20250620133252fCWLnDMoai" autocomplete="off" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="sessionKeeperContainer:j_idt183" value="sessionKeeperContainer:j_idt183" />
<input type="hidden" name="sessionKeeperContainer:j_idt183_operationTicket" value="dljdmx+1add3e86320053bc2682c12737113d9d~6tKXT1WVo_QxgAMPpfxfS0SBhwgzLTZQJbs091ga!1750393963234.aere-xml-controller-67d4778877-hjhfm" /><input type="hidden" name="sessionKeeperContainer:j_idt183_cmnPageTicket" value="4" />
					<h1 id="extendSessionExpiredDialogTitle" class="dialogTitle">まもなくセッションが終了します</h1>
					<div id="extendSessionExpiredDialogMessage" class="dialogMessage" tabindex="-1">このままお手続きを進める場合は、［延長］ボタンを押してください。</div>
					<p class="modalButton"><input id="sessionKeeperContainer:cmnSessionKeepingButton" type="submit" name="sessionKeeperContainer:cmnSessionKeepingButton" value="延長" class="btnBase btnModal btnMainStream" onclick="mojarra.ab(this,event,'action',0,0,{'onevent':function(data) {Asw.doCommonAjaxCallback(data,false, event); Asw.SessionKeeper.doSessionKeep(data);},'onerror':function(data) {Asw.doCommonAjaxErrorCallback(data); }});return false" />
					</p><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:0" value="4HAmyIS6XzVN5bfDQZGuys+bGbiCeaz8Qnbmaj/t23gz3+yHxctRqBdQTR2D+sdlf/meVbmidz8A8+dQZPTmxhu1Ly5fNSZsGDITO92I0bE+jjil5Tb6MX+9Y5ifwUx7DaMnzN1fhvi+tpiS1oPgt4VjD+Se+qi73C/WQdQoGGZh/2mmOhEhgbeF64ObrRwLDyMe3TYz03Uyo4R5LaSGQZzvFy4vp5yflhCen5i1/7ddd9ZiKqYowbgC10ZtmC4l5pwlho0cCYvZt8BqjYgky99cO09zOpJ0H57thT52FNqwL3AJxxQg9tYCry9xyYKA/BEPIbUJW5oiL5m7NHAJhC/I/gFAWOYrCyZfQ/xSgui4IznX9Z20rpRJAKNThS5KTHgrhYnkUN4w9f1YEXBxFEFOG+/p81YT8xOKjD/YntW0J7/jQZUxCW5MCuvELk6+J5hhVW1izRvYIGEn+FuYYgUETl50GmIVd/6V25v88HduHqOuClwmq7CVh2AKZpzk9fKCxQMEuyDSKD+Tcy6VBSsILF/SwvSRxPIe9/TuEhtBFNTmgdo4SqZj0436pre5/THIfOE8merIlbHdl1yPe3uibFVLvwPJIhGMmF37ZYbHAMvMGg5xFjSfqqiTA0Lhkga4Mm1mSH1x7WrW6F2SFGhBda65MyDxgogByGNiFLT6JVFH5egER15/jm87vC7babR+kcAz62SoopnCHyg7XytJrjANKlQvBMQqLcSr4G4a2o+j1QrlpIqsP/9qt1mhZgl5mWnY1lLRcEMyR5OkE32gUduxPn9AGC7+HVXe55GdC0LxCOU/Q40Op5776ajvukF9zpsJ9ONyduktH4GH4yp8Ar3sdBHIOEpx0P5dvVPdzYCPgxU4X/AfMq/MveqTG/Erw9YO/SJOBYJefYc6Ig3n21XYk+4ifBOL4J7xByQCXDL5xcTTPx9wcMdBvKPJfV+WoHbe0jdK1w3Q8m4rGIwQ1DL07HRZl1sWykntJJQ9k8G4N0BlB+rgfMX/uDgWMirREJe12qGfeZOqH8xzPO6hZiB2/3ubgDQpjrWczxj9uMucuxTX0MehB5FQazRQemW+pPDMk1GENgumF8s8NAXZBiTBplENmnPwuomlYnPgOGrivQDnhrv0uaBg135PO3cHa4i4mfTHHWq0hBoVE7WpAV4UVEv/XPPIuFiDHEuhUdhFU+DEp9n5wzV0nA+sUvuUUn3HbPYEQBlnx09Qzc3LF56rOFFPUoFqQIyxh9RsPj2kLzzSscLByAK0oMk9FoKcurodS8tgWUY7vjk7S0YDkaE9RA/zaF6fPaFa42m0/wim5UwLN9RGliLjn+HZnvaRT8L1T4/p3Kh2gqn9jnwDS1Shuuoo2kNShvpja5OiebbqyJyOyepilaT08/r+77O5ilJo84RZu1xRHZI30B+VMnNunVTFyV4coAMfDlNGmIVR77JSk5dxtOHv0IT4p/wgFpeMsi9xyq8wRq1cAmXlhh+pwYAMIfLZQNYG7yduRmGzpPuHdr+a0iQr7ttF20LlIvd/bbrO9gC9L4I8k7N9sKa7KeRGWQXMVyJQe99RodhxqA4C+XJRDP9U4KuqQWj1mkq+Ma07UBZLx6sbfyZUGBQJtPMTRDEwzcsVL12QXrb0aK7TJH7LocJe3o9v3S/n7jO0mHKsJGAX6/UdRYY5q6jx2tO14tftYgOaa5Sduo6Ti1LMacxuqsJ57SLa81X8ZDLvFrZqQE/o/1cxHeDSzHPJfN7zUsToRdP1HJ3BTpG3hyex0r3kaVXsiaZmi5OjIuW3pcGDv4kx1hIuMxapG0viBU2X0AA8r/9QuQOptYPvwEbqzt0DaUHwNgxNcuR2lIhQKCtKZ1/1LtNEXkUzQJKPA0Y/OwqczeQ5rKVuJw+Qbc9IQbo+00YN/NnOMzquBhZqFuj/jL+Q4evuLGIAHOvDMSx06g9msha6sLnGjbrboaYGPcT/qZO7E9F8TD1WF7Oix29fsM6g4fL8GeansmmRWMFDaKvM2720bzOs2yCBc60A1zQ3pT/vUo9kXWTspKlojA5xzyOMjMuzQpN7OIYJkq+oMCJQiClb/ar9a/xlUQkwjPX9PVX4fw15J8lV1TOisei2spA6r03RfZ5y/NllmLkUSSrTMmrm+BxHjZhDDGbocBn84UbQHXct/ZrlzpGJ/XQqVyUgqdxY2C9x/3RZ346feiYmMjPYHJCdct0QihQZ66y5mIMl2AmtVA1QlI6ljS+y5L4lTBJ5be4R3150yTAbi5CpnbEYf0+VMsBo+ZQMNFgCLGlrgxJE4WJajA/QXazLuD9LRzGNyKYS3iuZir7oFTQISGltjP7FhB+Wp0skYuRHEHybjLmH2i8JVQacGxDXdcDMwX+fAxIsUhOce2pOKi22LkpdAQ+qzrj3tft7WcbR09fiUy+xfsVpXeNXtlcplXF7PQyXegJhJuQc2iOyL1G21jwljVeSgdPEcdK8lIL5Y6uR9DaxRbXN1QnKbYWk90pjSVkVsuxTroKjtVR3uFMCE2S9PYa7MzPskX8DMsne97XjSV6MfQiUSxGH0KywxXEWuT8fM9XLb6b9f7BVmymPFKa6iv8qRcft5ErsJ/XHp8OmwJZVmtaH2c7Zzd+UAQnBXTM5gXzpXSVWJ8ZGRl/KacwtYdYrnISCG/u5b7sGUtCq3AEo2igkBGO+MgDzLGJ0Ft0hEShpfhuyQXBt+D1Uu0hEn4ZFP4yJwWbhkrW7vxw7YfZWEaahridQ3ze2ZeFllkEcwgnEGaAg0NxCU+nj4Bpo+bfGUkFz5AkRzwK2UVq34otxHOUsGvQB0+kWS/FD1ioWeEKWhts+MvDac+6iTkBIonDhFRE+QRWGlJgrBuGPRr9lleMdUQ8kCpPDkTS+grnVodYE8VJpzUDcPc51fjg5Zwuhnse3pIJAyk1z8WZnNTBuMYdaRj5TCPZHPsoBrdfQ9xR9641RFn9kHdENwVrKH3Q+I+q0XYneDiHJw4I7sjRomvJ5MBbkn4ZaoUO4GG2L1vprVQIVBKPpt/nHPRxqKzXxkCjqbvl+Q6Pz7M6rVQyB2DgslZQ50agaNfqANQK6inbVPFu22ok2E54w0ySVieLZnx6unB2DBaThW8/4PQQdFW3sh71ZghQgOuYkOdXuiqJ8qpr2bvsEPHOIklyir0cPo5zTrHuuyuLBHUVebfw/cOrAMjf0RtP2shOpnpTNi4qCagkcBKdlCNDc+b3Hu/n4BJY44+dBV24yB30B4slqizh0E5/+Pe69nXPHTE6kRxnDCttVhTuiMmlWxjcWPvNFdG6I8b1BzS5pTDOh116qbQIffO3xHXU3wjtIyl8SZ3TAVGX8hosy3JTfIjd5dq8FiINJBAJxQ5jF1sfCRJZTdBXtdvphris/FbwX4yYVzt5xXU7S24WHvc9GIl6pLENdAod8OZLqIlZLC5Su9HVoCb6ZP9qsOV0ZQmO4LbAAWi8O3xnVJx9iz0fDDgjiSYkaPKPY+Tq8U14tlZ8FG7rOnhAjunnJTfVpbUhVt0SoWh7dmS0knWkNpi6lqSGqIVTBlpu+Afi1TUgFSQ3AMNJ/oHQY9F8hP44KH9aIAXgp6CFyJcuGLXdfUrI0fyu1Ngu4kkhWRnzqREhWa1WKGFkETqQuBV98dDv2ih0aqfJL6iwt9hokXYbN52+auRPFFLPZdN/wq5/l3VbJhAo8xJoz5oQDlZFs+dHiaVGUE/RnNE7Nb0KKYKP1RD4SDE6ON2e/aSj0MFIK//qPoOV0MDBcukB/bBczHgx9eTouw7sblQQBE47YQFbnwFyuqcnWg6cvPgG274aSzgLJ2ihjsiRPKZ/yljxFJDODY6l+pvW/c1jfS0TqHfc9GGWwc37MmvA1r7itx8i9YBLnw0OZ6W3k4rzoEUwyBYfa9CBy43YrK0C0UIX4eKumsq9/oxuUmjUmswh72xS3BYvesYjHTSxTdP9ZbMnOzJYUaErMU0Nkvz/ZxVCCj57HAkBwEJCKClH84VwYRT5059mI5CL2BLLQVbIScT2qBRl/YB/Bb/7X8hOfbE0jPNsiSVvMhVUMWFyWkkIlFnh41WbgFj9DWdZHB1NfJYfWK8qJIuqVMhg8XAk659BfcqyiXczy51uJiyxek2DbyWS2YF6det5Z2AnHAtD3rvlGkfccBiWQTuXvjMsSj1YVlRkSA50LniyksUPKohs2OsWsMcZ3DidgUauLT5b42dk77oK0B/Sx4nSbfG3P0gGwsR8pZEBAW7ufVUp5DoknwXvXh2a6gfRj1t855bsS2UdP+S5jgRXgZYp5vzvoft/Xl95cTlbUzVcRCjDlzTTHYGoXNFb/UUQklvW30mRm0joODFE1y7zLXR6ikmKUZrwxQmxfiZ3bs6Bxn45ecpUjTgGh3WFtgZgNV7E6e7u1I3GvY5DDGBV9ZQA/Z7yKuy9Nl3Ty4DjC6qqoYXK9csEY0pqRV+EZ1tbfO6ntdL0fHRDQN7FOLPz5l5XtOviYRs2N9h7ex340EOikX4HHtKiHRq8EpoUMkx6A10lJ9nxg1KQXvMoJGJZN0SvwHCslRRfPusHGF+Lv3GNprZCLwcuSGVIKoBlABrktHssuiwuSTXiR24WqkkM2Bi/Brk4IQOJXuFrclxaNwStoWfKxeWIw1tP74atsBdyJSdtx+ZvnfImAPZKmvXLgLZ95fhXQRY1E6St8kaKiivZUFtd/zce1rcshv1jfDU31nJOu3g+7q6vyPXWHtGVvsUduACs7lCh/meGU9hl9XKO8U+JlAzORndkMszpdwNKh1PKXXcC1hHbkGi85ZEuuf+N4ApBkczEEna7iUTRWE4cp5EgoRV5FC+fkwZWOKcgkQa56KLJQbmqoI/j18hsi9WCtZVuHjl1/kQRNl9w0xqzEp2OLZnO4sAjF286HfQCt0gTjYu4WE293WOxOUkq39gaSbdB9QG3ntP4zSUL/hSKvymJqqrW2klkSxioc0PghXjJJCgcWySXBU+isepwLU7428k6InLIsZMCy15KSnF8GIB8ymci3SprhOWG0Fc0RUOaCbKxXo3/oSDhOtKymt2CMxuRtwPe4VeMVgwRUkoYN1iHcDo12hoL1VJfv1xfT0BlEPYlzTpV33F6en/Fu3WTgWzoc3LcAXJjs2/ygM3KxkeYvOY/U1uGW32gz7/MOBrFrtq306gLj6EpmwAFaPyq6j1pQTZQgQqszrUblN/sbcbFZFKRa9ZkT+218avUP3AzQt0D47MtoWqoW4W5+kBIOJJJEat3ms0RV1akTmfYJIiigf4LTQkpzrAdU6s1MSw8wBaAv6B2VZ9y5SsEIKWcD0hn5yg6xmMdbnpuM0Hv6n7tFOAbyBwOzsAlcc7OK1Y49LTDFtW8wXdY03bubzHZ+l03YdNx/Qab6Ac2VK2vNPSktDQDD6YYAiiY0MnT3I1rVM8Xb9vmTQ8u+Z/wlJ/sdf7xz8v0Rsdiu6q3snolvlF1AbYb42MzhHTreBy5M0XEC2rYXKWR3p3ZwSO6DaqN0lbnmf8j3fwR/SZgKBP2ZbOMZC+erbtCaUiF99u7VLMhVDW5JrT/yXwQe2ve9iqpsxNw/Z2ShfvnfLwXQqXm6uiZ4CkzMknyPz9jxTy2Hz8knWz4MytL2HteTZzMZjxFsNnozep24I6W2BWGRZkQrPuMRVWsTNYKJhX+SVPhqIqS0wNKcCkwcjdzCKA0RurmXuyDG2hBHLl96cyL6qNExqlmBhncISyjPjP/e8PApC1XPaOLTBF7mpUxZxtjAYspgh6aaRCBuD1mzyJ52rVr9snaS4pF+VWoSh3gu5vIOXvfHhfqUIA3ZuSI/DFBBVoIEvI1c4+4mR/9qNEh9Z9gyvolPBexo7gAz65qLnh1F9QPqsYU/zhXNXzXGxZ0sA42CodXZt1qGuNIop3Rb8sO809I4CddeJGnJLzoJlPfDzjQtPO6gRM0QOeKn5tSzdObAgcC95jDoSGkIt6S8V35clGBGOybWxgXjamhaE5n6rhC5PeMkIQFDEAEIij1OkShPzvI7AurPnGMUGhfuBcr74lDDfTPdWxOlBTKDPioRKGkTpuaQnvEe1u1A0Gl/Kt8psHPvLzkm2cxTw1QPvpKc0GmO4hlUPdw3SMQtYYsl6a7TGptuSMNb7CsQryUvXBNqEpvaPBEUNv3+AfCcTjpfr8fVcO3vqQRd+sMGqs+81JeMlnBBm10/XCA3TIiJTAuA9VbsetBt9BGvpDCH1FcD0OBkJYqmYQsahLlj+/VFMDMSQNbLxChSZpDZtkvb8YU2Kjr5n2m9xykfqyltqHEeN6VTNPg7m7yXZBimLmZuLROLrwP0g06QDO5jG5PjikpP3Wrn5ZbmJ49PgUJsBUCUoGt0oAn61DwIOtyacI+X6v2hd7cAlBHOXo23S+ZKZBoZWurY8sMP7GvkB5oFS3KsNSs3L+yBQ/sqgZ5J8DG28nBG1V6aC9cPf1GhNAYMD/ORNg3lWCwJRd5ZftWae9lr+kzwA0hyK5P5s/ITnoupLP3MG44pqc5RY/WrFsD0+u9WY/SPOmBV/MeTqJvwDzK0Z+xx0BxCaxHvN8OHg8NaHu2TBEu5misS9zGUfvbAjGEP5lKLDWYH+105Z3HP2vZd+i2nljIrZm0STlLEhgiVsMgSdxqC2OwqJpt0RajUgtdeCCWTmRBZfUSWfEhvCc+Ak6txUfRd6fJ2Juf7+FvY3twWqva/Gy6LIUWzjesxz7NwXsffevoZNGs8nunpfd0BestKgcAFwHI3SvGk/lEGufM5PNXQGkRnqTzkoiZcM/IrXzGd/A2JNyUSe154kASqxCpLYTE1ajYcGsBe/ID7JGyGwM5kP+4OsVPI8vqDsHVEIVW+YsvkN0J0pF4C/YLvdiRo6cXlUoCJuRm1kgzBBNNw8+svcc9aM5+K0/5OWHZJL9RrP4WBe5yvZwlhsjKFgTz1y5fPWMcdP0DLIqG5gOHw6DPE0lgZD6sbE1LpwG88znY22g3G1m4VEi2F98I2MzZ9F+3AmJFfjdPb8kUNGFCWAv52bgkbo9SdblNtHRsPFPLZkMC410zHKNiOs/S3d99gWjYeqOAxiw9zPzP3ka8NZu0lUQV0xKbqxKZjZYMpqZg/y4B04O6Dmw1YWQY9iiAOyIs/XUQZGJHdiRsnC5or3w0FQVnwXog24ZaZltPG4C1Buc+ElkLddmXgXyaIsf4hgYQJWDZpTMXxXkMEeU1sgajpRI+En0bFp4RRAEIbXUhHxLcpXsbWOGED+XJa0YTHeMp5PMNqKyTsVSNWzQlApAYAAtMlRxU9GbSb7VX/1DcJ4ftWLjKdPnDfymBdUTVWdrTgauH5M8PGmdfHit4W1O5/AmZE3FIP7uYiiq90S7tELvWpfn2RaMkCZV6ht7zVVfdxwyebFu1f9vtpZrQWzU8NjyI3Gdn775fThMQMjjXD3H0UlxIayum7Bg1vFza3VOb/k2FVXb/TpyKtMwgFTiZ25YE8xM0aYZgfLJYE3AxgVn5BTQr+Yeqg3uZT2zD2fBsPP4g8yPvGeDEk7iUdk/ywLtr2TKqPO5LCX2EP1e1hCI+Y8+tAWKUMQXNhYCq2hFGtg1UZeadn4aPthN0oXpsy2pRgCjFmqa7EgOzlZfPITsdsbdMeSNRaF8OZNxjjvggD8bzIqoVHs3m/bZUBs9FBvAIyAs8Y+YeQLZvhit03f7AvKZ2+P8YYWinfF1mPe/KLdnHVoseiy4ij6LKsoB8mYurM/3FIrAG0iuXYEwrJMcB4XNnquz26HOPXuUZ6A05kJD2gN88yEBf62KS5Vqd3CFn9XB3ozdCam8YtGabpUOjrFWdXmx4HDXLFioWs4zNU1PM2+KXcbqM9mIiK2yGXTlEzhmlqS+e2eYNTrbmVwlYI9mRDZuKa6bkPVIV+a+mtRWQKgLj07amHUs0IFUGuAltfw+Prz86ZQzzCFhQLFijTBeKQine3OhGLFuc+P9gQm3deasq0mQueFTIFMIEbD7azZkSuzOr8XV7SdMc7OHXDcEPHLTBGfWro2R8WCm3OB/b7Gm0mg3lRXOSMrXPhA6J4+8gX2lKhPQc2oqs+Gk9WvBZRDa75PE+wjk5unMP07Wuj+Zs9wnUzmBIOFCDW3yCIlZCT1SKF3TdOEzDWPejlrlDJJXZT9CZykfHD5+HNRt7TJYrB0iCB+Px0SG8HN6RHky853QqH7T6t/R/t4BbWRNRhOB6mbPYyfOBjRqPjFkqW1gycTTau7tDsLcHDD1LLGb4BDBsmOlih1S1JKUW69PA5CmGp8uWigN6nN8Z8H+M5p4wOtkzHUyM3lCTGWTmxnPw3FdrYqa/syDvm6DGyHHcNet+Ga5b8l7o=" autocomplete="off" />
</form>
			</div>
			<div id="showSessionExpiredDialog" class="modalExpire modalSmall modalContainer" role="dialog" aria-labelledby="showSessionExpiredDialogTitle" aria-hidden="true">
				<h1 id="showSessionExpiredDialogTitle" class="dialogTitle">セッションが終了しました</h1>
				<div id="showSessionExpiredDialogMessage" class="dialogMessage" tabindex="-1">一定時間操作されなかったため、お手続きを進めることができません。お手数ですが、再度トップページからお手続きください。</div>
				<p class="modalButton">
					<input type="submit" value="閉じる" class="btnBase btnModal btnMainStream" onclick="Asw.SessionKeeper.closeTimeoutDialog(); return false;" />
				</p>
			</div>
		</div>
		<!--===== dialogMessages =====--><div id="cmnModalMessages"></div>
		<div id="prebookConfirmDialog" class="modalContainer modalConfirm modalSmall " aria-hidden="true" role="dialog">
<form id="prebookConfirmDialog:j_idt403" name="prebookConfirmDialog:j_idt403" method="post" action="https://aswbe-i.ana.co.jp/rei22h/international_asw/pages/award/reservation/award_mandatory_passenger_information_input.xhtml?aswcid=1&amp;rand=20250620133252fCWLnDMoai" autocomplete="off" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="prebookConfirmDialog:j_idt403" value="prebookConfirmDialog:j_idt403" />
<input type="hidden" name="prebookConfirmDialog:j_idt403_operationTicket" value="dljdmx+1add3e86320053bc2682c12737113d9d~6tKXT1WVo_QxgAMPpfxfS0SBhwgzLTZQJbs091ga!1750393963234.aere-xml-controller-67d4778877-hjhfm" /><input type="hidden" name="prebookConfirmDialog:j_idt403_cmnPageTicket" value="4" /><div class="dialogMessage" tabindex="0"><p>この後、名・姓の修正や変更ができません。</p><p>「搭乗者選択」欄の氏名が「名」「姓」の順番で表示されていることを再確認ください。</p><p>例）<strong class="highlight">空野 太郎</strong> 様 → MR <strong class="highlight">TARO SORANO</strong></p><p>保安上の理由により、航空券とパスポートのお名前が同じ表記でない場合はご搭乗いただくことができませんので必ず再度ご確認ください。</p></div>
					
					<ul class="modalBtnHorizontal">
							
							<li><input type="submit" name="prebookConfirmDialog:j_idt419" value="戻る" aria-controls="prebookConfirmDialog" class="btnBase btnModal btnCancel" onclick="Asw.Dialog.getInstance('prebookConfirmDialog').close(event, Asw.DialogSequencer.isActiveSequencerNotDisplayedLastDialog());  return false;" />
							</li>
						
						<li class="btnArrowNext"><input type="submit" name="prebookConfirmDialog:j_idt422" value="OK" aria-controls="prebookConfirmDialog" class="btnBase btnModal btnMainStream " onclick="Asw.Dialog.getInstance('prebookConfirmDialog').callOpener('NORMAL', event); return false;" />
						</li>
					</ul><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:1" value="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" autocomplete="off" />
</form>
		</div>
		<!--===== /dialogMessages =====--><div id="cmnContainer">
		<!--=========== .cmnContainer ============--><div id="cmnEmergencyNotice"></div><div id="cmnTitle" class="hgroup ">
		<h1 class="visuallyHidden">お客様情報入力</h1></div>

		<div id="cmnWrapper">
		<!--===== cmnWrapper =====-->
		<ol class="mainFlow" aria-label="完了までの手順、">
			<li><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_flow_01.png?717d3c0" alt="1" height="20" width="28" />フライト検索</li><!--
			--><li><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_flow_02_on.png?717d3c0" alt="2" height="20" width="28" /><span class="visuallyHidden">現在のステップ、</span>お客様情報入力</li><!--
			--><li><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_flow_03.png?717d3c0" alt="3" height="20" width="28" />お支払い情報</li><!--
			--><li class="flowLast"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_flow4_04.png?717d3c0" alt="4" height="20" width="28" />完了</li>
		</ol>

	<div id="main">
	<!--=========== main =============-->
		<ul class="iconExample">
				<li class="requiredIconExample">
					<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="" height="7" width="7" /></span>必須項目です	
				</li>
		</ul>
<form id="j_idt526" name="j_idt526" method="post" action="https://aswbe-i.ana.co.jp/rei22h/international_asw/pages/award/reservation/award_mandatory_passenger_information_input.xhtml?aswcid=1&amp;rand=20250620133252fCWLnDMoai" autocomplete="off" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="j_idt526" value="j_idt526" />
<input type="hidden" name="j_idt526_operationTicket" value="dljdmx+1add3e86320053bc2682c12737113d9d~6tKXT1WVo_QxgAMPpfxfS0SBhwgzLTZQJbs091ga!1750393963234.aere-xml-controller-67d4778877-hjhfm" /><input type="hidden" name="j_idt526_cmnPageTicket" value="4" />

<div class="section">
	<h2>搭乗者選択<a href="https://www.ana.co.jp/other/int/meta/0086.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="特典のご利用に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a></h2>
	<table class="passengerInfo">
		<thead>
			<tr>
				
				<th class="name">氏名</th>
				
				<th class="dateOfBirthWide" id="dateOfBirthHeader">生年月日</th>
				
				<th class="gender lastCol" id="genderHeader">性別<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span></th>
				
			</tr>
		</thead>
		<tbody>
				
				<tr>
					<th class="parent_01" id="parentHeader01">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:0:passenger" type="checkbox" name="passengers:0:passenger" checked="checked" data-number="1" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:0:passenger">NAOKI SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader01">1963年06月05日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader01">				
							<span class="formWrapper">
							<label for="passengers:0:gender" class="visuallyHidden">NAOKI SATOの性別</label><select id="passengers:0:gender" name="passengers:0:gender" size="1">	<option value="">選択してください</option>
	<option value="M" selected="selected">男性</option>
	<option value="F">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_02" id="parentHeader02">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:1:passenger" type="checkbox" name="passengers:1:passenger" data-number="2" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:1:passenger">REO SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader02">1995年05月18日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader02">				
							<span class="formWrapper">
							<label for="passengers:1:gender" class="visuallyHidden">REO SATOの性別</label><select id="passengers:1:gender" name="passengers:1:gender" size="1">	<option value="">選択してください</option>
	<option value="M" selected="selected">男性</option>
	<option value="F">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_03" id="parentHeader03">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:2:passenger" type="checkbox" name="passengers:2:passenger" data-number="3" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:2:passenger">ERI SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader03">1963年08月14日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader03">				
							<span class="formWrapper">
							<label for="passengers:2:gender" class="visuallyHidden">ERI SATOの性別</label><select id="passengers:2:gender" name="passengers:2:gender" size="1">	<option value="">選択してください</option>
	<option value="M">男性</option>
	<option value="F" selected="selected">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_04" id="parentHeader04">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:3:passenger" type="checkbox" name="passengers:3:passenger" data-number="4" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:3:passenger">TAISHI SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader04">1992年12月03日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader04">				
							<span class="formWrapper">
							<label for="passengers:3:gender" class="visuallyHidden">TAISHI SATOの性別</label><select id="passengers:3:gender" name="passengers:3:gender" size="1">	<option value="">選択してください</option>
	<option value="M" selected="selected">男性</option>
	<option value="F">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_05" id="parentHeader05">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:4:passenger" type="checkbox" name="passengers:4:passenger" data-number="5" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:4:passenger">HIROMI FUJIMOTO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader05">1961年11月23日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader05">				
							<span class="formWrapper">
							<label for="passengers:4:gender" class="visuallyHidden">HIROMI FUJIMOTOの性別</label><select id="passengers:4:gender" name="passengers:4:gender" size="1">	<option value="">選択してください</option>
	<option value="M">男性</option>
	<option value="F" selected="selected">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
		</tbody>
	</table>
</div>
		<div class="section">
			<h2 class="midstream">連絡先情報
			</h2>
			<h3>代表者
			</h3>
	
	
	
	<div class="formInput">
		<fieldset>
			<legend><label>代表者</label></legend>
			<dl id="contactEmailAddressInfo">
				<dt class="hasRequiredIcon">メールアドレス<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span><a href="https://www.ana.co.jp/other/int/meta/0057.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="メールの宛先と内容に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a>
				</dt>
				<dd>
					<ul class="verticalList">
						<li class="nonMarginTop"><label for="representativeEmail" class="visuallyHidden">代表者メールアドレス</label><input id="representativeEmail" type="text" name="representativeEmail" value="<EMAIL>" class="widthLarge" placeholder="例：<EMAIL>" />
						</li>
						<li>
							<span class="supplementTxt" id="confirmMailAddressForRepresentative">確認のため再度ご入力ください。
							</span><label for="confirmrepresentativeEmail" class="visuallyHidden">確認用メールアドレス</label><input id="confirmrepresentativeEmail" type="text" name="confirmrepresentativeEmail" value="<EMAIL>" class="widthLarge jsNotPasteInput" placeholder="例：<EMAIL>" aria-describedby="confirmMailAddressForRepresentative" />
						</li>
					</ul>
				</dd>
					<dd style="display:none;"><input id="updateAlwaysInfo" type="checkbox" name="updateAlwaysInfo" /><label for="updateAlwaysInfo">ログイン中の会員情報へこのメールアドレスを登録</label>
					</dd>
			</dl>
			<dl>
				<dt>電話番号<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span>
				</dt>
				<dd class="js-countryWrap">
					<ul>
						<li><label for="representativeTelType" class="visuallyHidden">電話番号種別</label><select id="representativeTelType" name="representativeTelType" class="basicWidth js-typeSelect" size="1" onchange="changeTelType(this,'representativeTel')">	<option value="M1" selected="selected">携帯</option>
	<option value="B1">会社</option>
	<option value="H1">自宅</option>
</select>
						</li>
						<li><label for="representativeTelCountry" class="visuallyHidden">電話番号の国/地区選択</label><select id="representativeTelCountry" name="representativeTelCountry" class="js-countrySelect" size="1">	<option value="" data-sms-possible="true" data-country-code="---">選択してください</option>
	<option value="JP" selected="selected" data-sms-possible="true">日本</option>
	<option value="-" data-sms-possible="true" data-country-code="---">-----------</option>
	<option value="IS" data-sms-possible="true">アイスランド</option>
	<option value="IE" data-sms-possible="true">アイルランド</option>
	<option value="AZ" data-sms-possible="true">アゼルバイジャン</option>
	<option value="AF" data-sms-possible="true">アフガニスタン</option>
	<option value="US" data-sms-possible="true">アメリカ合衆国/カナダ</option>
	<option value="AE" data-sms-possible="true">アラブ首長国連邦</option>
	<option value="DZ" data-sms-possible="true">アルジェリア</option>
	<option value="AR" data-sms-possible="true">アルゼンチン</option>
	<option value="AW" data-sms-possible="true">アルバ</option>
	<option value="AL" data-sms-possible="true">アルバニア</option>
	<option value="AM" data-sms-possible="true">アルメニア</option>
	<option value="AO" data-sms-possible="true">アンゴラ</option>
	<option value="AG" data-sms-possible="true">アンティグア・バーブーダ</option>
	<option value="AD" data-sms-possible="true">アンドラ</option>
	<option value="YE" data-sms-possible="true">イエメン</option>
	<option value="GB" data-sms-possible="true">イギリス</option>
	<option value="IL" data-sms-possible="true">イスラエル</option>
	<option value="IT" data-sms-possible="true">イタリア</option>
	<option value="IQ" data-sms-possible="true">イラク</option>
	<option value="IR" data-sms-possible="true">イラン</option>
	<option value="IN" data-sms-possible="true">インド</option>
	<option value="ID" data-sms-possible="true">インドネシア</option>
	<option value="UG" data-sms-possible="true">ウガンダ</option>
	<option value="UA" data-sms-possible="true">ウクライナ</option>
	<option value="UZ" data-sms-possible="true">ウズベキスタン</option>
	<option value="UY" data-sms-possible="true">ウルグアイ</option>
	<option value="EC" data-sms-possible="true">エクアドル</option>
	<option value="EG" data-sms-possible="true">エジプト</option>
	<option value="EE" data-sms-possible="true">エストニア</option>
	<option value="SZ" data-sms-possible="true">エスワティニ</option>
	<option value="ET" data-sms-possible="true">エチオピア</option>
	<option value="ER" data-sms-possible="true">エリトリア</option>
	<option value="SV" data-sms-possible="true">エルサルバドル</option>
	<option value="AU" data-sms-possible="true">オーストラリア</option>
	<option value="AT" data-sms-possible="true">オーストリア</option>
	<option value="OM" data-sms-possible="true">オマーン</option>
	<option value="NL" data-sms-possible="true">オランダ</option>
	<option value="GH" data-sms-possible="true">ガーナ</option>
	<option value="CV" data-sms-possible="true">カーボベルデ</option>
	<option value="GY" data-sms-possible="true">ガイアナ</option>
	<option value="KZ" data-sms-possible="true">カザフスタン</option>
	<option value="QA" data-sms-possible="true">カタール</option>
	<option value="CA" data-sms-possible="true">カナダ/アメリカ合衆国</option>
	<option value="GA" data-sms-possible="true">ガボン</option>
	<option value="CM" data-sms-possible="true">カメルーン</option>
	<option value="KR" data-sms-possible="true">韓国</option>
	<option value="GM" data-sms-possible="true">ガンビア</option>
	<option value="KH" data-sms-possible="true">カンボジア</option>
	<option value="KP" data-sms-possible="true">北朝鮮</option>
	<option value="MK" data-sms-possible="true">北マケドニア</option>
	<option value="GN" data-sms-possible="true">ギニア</option>
	<option value="GW" data-sms-possible="true">ギニアビサウ</option>
	<option value="CY" data-sms-possible="true">キプロス</option>
	<option value="CU" data-sms-possible="true">キューバ</option>
	<option value="GR" data-sms-possible="true">ギリシャ</option>
	<option value="KI" data-sms-possible="true">キリバス</option>
	<option value="KG" data-sms-possible="true">キルギス</option>
	<option value="GT" data-sms-possible="true">グアテマラ</option>
	<option value="GU" data-sms-possible="true">グアム</option>
	<option value="KW" data-sms-possible="true">クウェート</option>
	<option value="GD" data-sms-possible="true">グレナダ</option>
	<option value="HR" data-sms-possible="true">クロアチア</option>
	<option value="KY" data-sms-possible="true">ケイマン諸島</option>
	<option value="KE" data-sms-possible="true">ケニア</option>
	<option value="CI" data-sms-possible="true">コートジボワール</option>
	<option value="CR" data-sms-possible="true">コスタリカ</option>
	<option value="KM" data-sms-possible="true">コモロ</option>
	<option value="CO" data-sms-possible="true">コロンビア</option>
	<option value="CG" data-sms-possible="true">コンゴ共和国</option>
	<option value="CD" data-sms-possible="true">コンゴ民主共和国</option>
	<option value="SA" data-sms-possible="true">サウジアラビア</option>
	<option value="ZM" data-sms-possible="true">ザンビア</option>
	<option value="SM" data-sms-possible="true">サンマリノ</option>
	<option value="SL" data-sms-possible="true">シエラレオネ</option>
	<option value="DJ" data-sms-possible="true">ジブチ</option>
	<option value="JM" data-sms-possible="true">ジャマイカ</option>
	<option value="GE" data-sms-possible="true">ジョージア</option>
	<option value="SY" data-sms-possible="true">シリア</option>
	<option value="SG" data-sms-possible="true">シンガポール</option>
	<option value="ZW" data-sms-possible="true">ジンバブエ</option>
	<option value="CH" data-sms-possible="true">スイス</option>
	<option value="SE" data-sms-possible="true">スウェーデン</option>
	<option value="SD" data-sms-possible="true">スーダン</option>
	<option value="ES" data-sms-possible="true">スペイン</option>
	<option value="SR" data-sms-possible="true">スリナム</option>
	<option value="LK" data-sms-possible="true">スリランカ</option>
	<option value="SK" data-sms-possible="true">スロバキア</option>
	<option value="SI" data-sms-possible="true">スロベニア</option>
	<option value="SC" data-sms-possible="true">セイシェル</option>
	<option value="GQ" data-sms-possible="true">赤道ギニア</option>
	<option value="SN" data-sms-possible="true">セネガル</option>
	<option value="RS" data-sms-possible="true">セルビア</option>
	<option value="KN" data-sms-possible="true">セントクリストファー・ネイビス</option>
	<option value="LC" data-sms-possible="true">セントルシア</option>
	<option value="SB" data-sms-possible="true">ソロモン諸島</option>
	<option value="TH" data-sms-possible="true">タイ</option>
	<option value="TW" data-sms-possible="true">台湾</option>
	<option value="TJ" data-sms-possible="true">タジキスタン</option>
	<option value="TZ" data-sms-possible="true">タンザニア</option>
	<option value="CZ" data-sms-possible="true">チェコ</option>
	<option value="TD" data-sms-possible="true">チャド</option>
	<option value="CF" data-sms-possible="true">中央アフリカ共和国</option>
	<option value="CN" data-sms-possible="true">中国</option>
	<option value="TN" data-sms-possible="true">チュニジア</option>
	<option value="CL" data-sms-possible="true">チリ</option>
	<option value="TV" data-sms-possible="true">ツバル</option>
	<option value="DK" data-sms-possible="true">デンマーク</option>
	<option value="DE" data-sms-possible="true">ドイツ</option>
	<option value="TG" data-sms-possible="true">トーゴ</option>
	<option value="DO" data-sms-possible="true">ドミニカ共和国</option>
	<option value="DM" data-sms-possible="true">ドミニカ国</option>
	<option value="TT" data-sms-possible="true">トリニダード・トバゴ</option>
	<option value="TM" data-sms-possible="true">トルクメニスタン</option>
	<option value="TR" data-sms-possible="true">トルコ</option>
	<option value="TO" data-sms-possible="true">トンガ</option>
	<option value="NG" data-sms-possible="true">ナイジェリア</option>
	<option value="NR" data-sms-possible="true">ナウル</option>
	<option value="NA" data-sms-possible="true">ナミビア</option>
	<option value="NI" data-sms-possible="true">ニカラグア</option>
	<option value="NE" data-sms-possible="true">ニジェール</option>
	<option value="NZ" data-sms-possible="true">ニュージーランド</option>
	<option value="NP" data-sms-possible="true">ネパール</option>
	<option value="NO" data-sms-possible="true">ノルウェー</option>
	<option value="BH" data-sms-possible="true">バーレーン</option>
	<option value="HT" data-sms-possible="true">ハイチ</option>
	<option value="PK" data-sms-possible="true">パキスタン</option>
	<option value="VA" data-sms-possible="true">バチカン</option>
	<option value="PA" data-sms-possible="true">パナマ</option>
	<option value="VU" data-sms-possible="true">バヌアツ</option>
	<option value="BS" data-sms-possible="true">バハマ</option>
	<option value="PG" data-sms-possible="true">パプアニューギニア</option>
	<option value="BM" data-sms-possible="true">バミューダ</option>
	<option value="PW" data-sms-possible="true">パラオ</option>
	<option value="PY" data-sms-possible="true">パラグアイ</option>
	<option value="BB" data-sms-possible="true">バルバドス</option>
	<option value="PS" data-sms-possible="true">パレスチナ</option>
	<option value="HU" data-sms-possible="true">ハンガリー</option>
	<option value="BD" data-sms-possible="true">バングラデシュ</option>
	<option value="TL" data-sms-possible="true">東ティモール</option>
	<option value="FJ" data-sms-possible="true">フィジー</option>
	<option value="PH" data-sms-possible="true">フィリピン</option>
	<option value="FI" data-sms-possible="true">フィンランド</option>
	<option value="BT" data-sms-possible="true">ブータン</option>
	<option value="PR" data-sms-possible="true">プエルトリコ</option>
	<option value="BR" data-sms-possible="true">ブラジル</option>
	<option value="FR" data-sms-possible="true">フランス</option>
	<option value="PF" data-sms-possible="true">フランス領ポリネシア</option>
	<option value="BG" data-sms-possible="true">ブルガリア</option>
	<option value="BF" data-sms-possible="true">ブルキナファソ</option>
	<option value="BN" data-sms-possible="true">ブルネイ</option>
	<option value="BI" data-sms-possible="true">ブルンジ</option>
	<option value="VN" data-sms-possible="true">ベトナム</option>
	<option value="BJ" data-sms-possible="true">ベナン</option>
	<option value="VE" data-sms-possible="true">ベネズエラ</option>
	<option value="BY" data-sms-possible="true">ベラルーシ</option>
	<option value="BZ" data-sms-possible="true">ベリーズ</option>
	<option value="PE" data-sms-possible="true">ペルー</option>
	<option value="BE" data-sms-possible="true">ベルギー</option>
	<option value="PL" data-sms-possible="true">ポーランド</option>
	<option value="BA" data-sms-possible="true">ボスニア・ヘルツェゴビナ</option>
	<option value="BW" data-sms-possible="true">ボツワナ</option>
	<option value="BO" data-sms-possible="true">ボリビア</option>
	<option value="PT" data-sms-possible="true">ポルトガル</option>
	<option value="HK" data-sms-possible="true">香港</option>
	<option value="HN" data-sms-possible="true">ホンジュラス</option>
	<option value="MH" data-sms-possible="true">マーシャル諸島</option>
	<option value="MO" data-sms-possible="true">マカオ</option>
	<option value="MG" data-sms-possible="true">マダガスカル</option>
	<option value="MW" data-sms-possible="true">マラウイ</option>
	<option value="ML" data-sms-possible="true">マリ</option>
	<option value="MT" data-sms-possible="true">マルタ</option>
	<option value="MY" data-sms-possible="true">マレーシア</option>
	<option value="FM" data-sms-possible="true">ミクロネシア連邦</option>
	<option value="ZA" data-sms-possible="true">南アフリカ</option>
	<option value="MM" data-sms-possible="true">ミャンマー</option>
	<option value="MX" data-sms-possible="true">メキシコ</option>
	<option value="MU" data-sms-possible="true">モーリシャス</option>
	<option value="MR" data-sms-possible="true">モーリタニア</option>
	<option value="MZ" data-sms-possible="true">モザンビーク</option>
	<option value="MC" data-sms-possible="true">モナコ</option>
	<option value="MV" data-sms-possible="true">モルディヴ</option>
	<option value="MD" data-sms-possible="true">モルドバ</option>
	<option value="MA" data-sms-possible="true">モロッコ</option>
	<option value="MN" data-sms-possible="true">モンゴル</option>
	<option value="ME" data-sms-possible="true">モンテネグロ</option>
	<option value="JO" data-sms-possible="true">ヨルダン</option>
	<option value="LA" data-sms-possible="true">ラオス人民民主共和国</option>
	<option value="LV" data-sms-possible="true">ラトビア</option>
	<option value="LT" data-sms-possible="true">リトアニア</option>
	<option value="LY" data-sms-possible="true">リビア</option>
	<option value="LI" data-sms-possible="true">リヒテンシュタイン</option>
	<option value="LR" data-sms-possible="true">リベリア</option>
	<option value="RO" data-sms-possible="true">ルーマニア</option>
	<option value="LU" data-sms-possible="true">ルクセンブルク</option>
	<option value="RW" data-sms-possible="true">ルワンダ</option>
	<option value="LS" data-sms-possible="true">レソト</option>
	<option value="LB" data-sms-possible="true">レバノン</option>
	<option value="RU" data-sms-possible="true">ロシア</option>
</select>
						</li>
						<li class="numberInputWrap">
							<span id="numberInputInfo1" class="supplementTxt marginTop">先頭に0(ゼロ)がある場合、0(ゼロ)を抜いてください(一部、国・地域除く)。携帯電話番号の場合は、携帯電話を契約された国を選択ください。</span>
							<div class="numberRowWrap">
								<div class="block">
									
									<span class="visuallyHidden">国番号</span>
									<span class="viewInput widthMini js-countryCode"></span>
								</div>
								<div class="block"><label for="representativeTel" class="visuallyHidden">電話番号</label><input id="representativeTel" type="text" name="representativeTel" value="9042504176" class="widthMiddle" placeholder="例：9012345678" maxlength="30" aria-describedby="numberInputInfo1" />
								</div>
							</div>
						</li>
					</ul>
				</dd>
			</dl>
		</fieldset>
	</div>
	<div class="titleWrapper">
		<h3>
			<span class="text">eメール・SMSに関するご案内<a href="https://www.ana.co.jp/other/int/meta/0057.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="eメール・SMSに関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a></span>
		</h3>
	</div>
	<div class="formInput">
	<h4 class="formAreaTitle">主に搭乗・運航に関わるeメールの送付先設定（米国へ通知いたします）<a href="https://www.ana.co.jp/other/int/meta/0629.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="米国入国用eメール・SMS・電話番号に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a>
	</h4>
	<div class="paxSettingArea js-mailFormWrap onePax is-open">
			<div class="settingArea">
					<dl>
						<dt class="breakWord">NAOKI SATO
						</dt>
						<dd>
							<fieldset>
								<legend>NAOKI SATOのeメール送付先および米国到着時に提供するメールアドレス</legend>代表者と同じ
							</fieldset>
						</dd>
					</dl>
			</div>
	</div>
	<h4 class="formAreaTitle hasTopLine">主に搭乗・運航に関わるSMSの送付先および米国で受信できる電話番号の設定（米国へ通知いたします）<a href="https://www.ana.co.jp/other/int/meta/0629.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="米国入国用eメール・SMS・電話番号に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a>
	</h4>
	<div class="paxSettingArea js-smsFormWrap onePax is-open">
			<div class="settingArea">
					<dl>
						<dt class="breakWord">NAOKI SATO<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span>
						</dt>
						<dd>
							<fieldset>
								<legend>NAOKI SATOの1つ目の電話番号</legend>
									<ul class="verticalList js-formItem">
										<li><input type="radio" id="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSameAsRepresentativeSms:radioItem" name="contactsSms:0:recipientSmsEachPersonSelection:radioGroup" value="0" checked="checked" /><label for="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSameAsRepresentativeSms:radioItem">代表者電話番号と同じ</label>
										</li>
										<li><input type="radio" id="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSmsPerPassenger:radioItem" name="contactsSms:0:recipientSmsEachPersonSelection:radioGroup" value="1" /><label for="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSmsPerPassenger:radioItem">電話番号を指定</label>
											<div class="indentArea js-formImputArea">
												<span id="confirmTelNumberForRepresentative:0" class="supplementTxt marginTop">先頭に0(ゼロ)がある場合、0(ゼロ)を抜いてください(一部、国・地域除く)。携帯電話番号の場合は、携帯電話を契約された国を選択ください。</span>
												<ul class="countryNumberInput js-countryWrap">
													<li><label for="contactsSms:0:recipientSmsEachPersonSelection:passengerSmsCountry" class="visuallyHidden">電話番号の国/地区選択</label><select id="contactsSms:0:recipientSmsEachPersonSelection:passengerSmsCountry" name="contactsSms:0:recipientSmsEachPersonSelection:passengerSmsCountry" class="countrySelect js-countrySelect" size="1">	<option value="" data-country-code="---">選択してください</option>
	<option value="JP">日本</option>
	<option value="-" data-country-code="---">-----------</option>
	<option value="IE">アイルランド</option>
	<option value="AZ">アゼルバイジャン</option>
	<option value="AF">アフガニスタン</option>
	<option value="US">アメリカ合衆国/カナダ</option>
	<option value="AE">アラブ首長国連邦</option>
	<option value="DZ">アルジェリア</option>
	<option value="AR">アルゼンチン</option>
	<option value="AW">アルバ</option>
	<option value="AL">アルバニア</option>
	<option value="AM">アルメニア</option>
	<option value="AG">アンティグア・バーブーダ</option>
	<option value="AD">アンドラ</option>
	<option value="YE">イエメン</option>
	<option value="GB">イギリス</option>
	<option value="IL">イスラエル</option>
	<option value="IT">イタリア</option>
	<option value="IQ">イラク</option>
	<option value="IR">イラン</option>
	<option value="IN">インド</option>
	<option value="ID">インドネシア</option>
	<option value="UG">ウガンダ</option>
	<option value="UA">ウクライナ</option>
	<option value="UZ">ウズベキスタン</option>
	<option value="UY">ウルグアイ</option>
	<option value="EC">エクアドル</option>
	<option value="EG">エジプト</option>
	<option value="EE">エストニア</option>
	<option value="ER">エリトリア</option>
	<option value="SV">エルサルバドル</option>
	<option value="AU">オーストラリア</option>
	<option value="AT">オーストリア</option>
	<option value="OM">オマーン</option>
	<option value="NL">オランダ</option>
	<option value="GH">ガーナ</option>
	<option value="GY">ガイアナ</option>
	<option value="KZ">カザフスタン</option>
	<option value="QA">カタール</option>
	<option value="CA">カナダ/アメリカ合衆国</option>
	<option value="GA">ガボン</option>
	<option value="CM">カメルーン</option>
	<option value="KR">韓国</option>
	<option value="GM">ガンビア</option>
	<option value="KH">カンボジア</option>
	<option value="GN">ギニア</option>
	<option value="GW">ギニアビサウ</option>
	<option value="CY">キプロス</option>
	<option value="CU">キューバ</option>
	<option value="GR">ギリシャ</option>
	<option value="KG">キルギス</option>
	<option value="GT">グアテマラ</option>
	<option value="GU">グアム</option>
	<option value="KW">クウェート</option>
	<option value="GD">グレナダ</option>
	<option value="HR">クロアチア</option>
	<option value="KY">ケイマン諸島</option>
	<option value="KE">ケニア</option>
	<option value="CI">コートジボワール</option>
	<option value="CR">コスタリカ</option>
	<option value="KM">コモロ</option>
	<option value="CO">コロンビア</option>
	<option value="CD">コンゴ民主共和国</option>
	<option value="SA">サウジアラビア</option>
	<option value="ZM">ザンビア</option>
	<option value="SL">シエラレオネ</option>
	<option value="JM">ジャマイカ</option>
	<option value="GE">ジョージア</option>
	<option value="SY">シリア</option>
	<option value="SG">シンガポール</option>
	<option value="ZW">ジンバブエ</option>
	<option value="CH">スイス</option>
	<option value="SE">スウェーデン</option>
	<option value="SD">スーダン</option>
	<option value="ES">スペイン</option>
	<option value="SR">スリナム</option>
	<option value="LK">スリランカ</option>
	<option value="SK">スロバキア</option>
	<option value="SI">スロベニア</option>
	<option value="SN">セネガル</option>
	<option value="RS">セルビア</option>
	<option value="KN">セントクリストファー・ネイビス</option>
	<option value="LC">セントルシア</option>
	<option value="TH">タイ</option>
	<option value="TW">台湾</option>
	<option value="TJ">タジキスタン</option>
	<option value="TZ">タンザニア</option>
	<option value="CZ">チェコ</option>
	<option value="TD">チャド</option>
	<option value="CF">中央アフリカ共和国</option>
	<option value="CN">中国</option>
	<option value="TN">チュニジア</option>
	<option value="CL">チリ</option>
	<option value="DK">デンマーク</option>
	<option value="DE">ドイツ</option>
	<option value="TG">トーゴ</option>
	<option value="DO">ドミニカ共和国</option>
	<option value="TT">トリニダード・トバゴ</option>
	<option value="TR">トルコ</option>
	<option value="NG">ナイジェリア</option>
	<option value="NI">ニカラグア</option>
	<option value="NE">ニジェール</option>
	<option value="NZ">ニュージーランド</option>
	<option value="NP">ネパール</option>
	<option value="NO">ノルウェー</option>
	<option value="HT">ハイチ</option>
	<option value="PK">パキスタン</option>
	<option value="PA">パナマ</option>
	<option value="BS">バハマ</option>
	<option value="PG">パプアニューギニア</option>
	<option value="BM">バミューダ</option>
	<option value="PW">パラオ</option>
	<option value="PY">パラグアイ</option>
	<option value="BB">バルバドス</option>
	<option value="PS">パレスチナ</option>
	<option value="HU">ハンガリー</option>
	<option value="BD">バングラデシュ</option>
	<option value="TL">東ティモール</option>
	<option value="FJ">フィジー</option>
	<option value="PH">フィリピン</option>
	<option value="FI">フィンランド</option>
	<option value="BT">ブータン</option>
	<option value="PR">プエルトリコ</option>
	<option value="BR">ブラジル</option>
	<option value="FR">フランス</option>
	<option value="PF">フランス領ポリネシア</option>
	<option value="BG">ブルガリア</option>
	<option value="BF">ブルキナファソ</option>
	<option value="BN">ブルネイ</option>
	<option value="BI">ブルンジ</option>
	<option value="VN">ベトナム</option>
	<option value="BJ">ベナン</option>
	<option value="VE">ベネズエラ</option>
	<option value="BY">ベラルーシ</option>
	<option value="PE">ペルー</option>
	<option value="BE">ベルギー</option>
	<option value="PL">ポーランド</option>
	<option value="BA">ボスニア・ヘルツェゴビナ</option>
	<option value="BW">ボツワナ</option>
	<option value="HK">香港</option>
	<option value="MH">マーシャル諸島</option>
	<option value="MO">マカオ</option>
	<option value="MG">マダガスカル</option>
	<option value="MW">マラウイ</option>
	<option value="ML">マリ</option>
	<option value="MT">マルタ</option>
	<option value="MY">マレーシア</option>
	<option value="ZA">南アフリカ</option>
	<option value="MM">ミャンマー</option>
	<option value="MX">メキシコ</option>
	<option value="MU">モーリシャス</option>
	<option value="MR">モーリタニア</option>
	<option value="MZ">モザンビーク</option>
	<option value="MC">モナコ</option>
	<option value="MV">モルディヴ</option>
	<option value="MA">モロッコ</option>
	<option value="MN">モンゴル</option>
	<option value="ME">モンテネグロ</option>
	<option value="JO">ヨルダン</option>
	<option value="LA">ラオス人民民主共和国</option>
	<option value="LV">ラトビア</option>
	<option value="LT">リトアニア</option>
	<option value="LY">リビア</option>
	<option value="LR">リベリア</option>
	<option value="RO">ルーマニア</option>
	<option value="LU">ルクセンブルク</option>
	<option value="RW">ルワンダ</option>
	<option value="LB">レバノン</option>
	<option value="RU">ロシア</option>
</select>
													</li>
													<li>
														
														<div class="numberRowWrap">
															<div class="block">
																<span class="visuallyHidden">国番号</span>
																<span class="countryCode viewInput js-countryCode"></span>
															</div>
															
															<div class="block"><label for="contactsSms:0:recipientSmsEachPersonSelection:flightStatusNotificationContactPointSmsDescription" class="visuallyHidden">電話番号</label><input id="contactsSms:0:recipientSmsEachPersonSelection:flightStatusNotificationContactPointSmsDescription" type="text" name="contactsSms:0:recipientSmsEachPersonSelection:flightStatusNotificationContactPointSmsDescription" class="telNumber" placeholder="例：9012345678" maxlength="30" aria-describedby="confirmTelNumberForRepresentative:0" />
															</div>
														</div>
													</li>
												</ul>
											</div>
										</li>
									</ul>
							</fieldset>
						</dd>
					</dl>
			</div>
	</div>
	</div>
	
	<div class="formInput articleLine">
		<h3>滞在中の連絡先(任意)
		</h3>
		<dl>
			
			<dt><label for="accommodationOne">現地連絡先1</label>
			</dt>
			<dd><input id="accommodationOne" type="text" name="accommodationOne" class="widthLarge" placeholder="例：09012345678 ABC HOTEL" maxlength="28" title="例：09012345678 ABC HOTEL" />
			</dd>
		</dl>
		<dl>
			
			<dt><label for="accommodationTwo">現地連絡先2</label>
			</dt>
			<dd><input id="accommodationTwo" type="text" name="accommodationTwo" class="widthLarge" placeholder="例：09012345678 ABC HOTEL" maxlength="28" title="例：09012345678 ABC HOTEL" />
			</dd>
		</dl>
	</div>
		</div>
		
		<p class="btnAreaSubmit btnArrowNext "><input id="next" type="submit" name="next" value="次へ" class="btnBase btnMainStream btnVerticalMain btnWidthVariable" onclick="return Asw.Dialog.getInstance('prebookConfirmDialog').toggle(event);return Asw.LoadingWindow.open(&quot;NORMAL&quot;, event)" />
		</p><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:2" value="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" autocomplete="off" />
</form><div id="cmnDynamicMessages" class="infoBoxSection">
				<dl class="infoBox importantInfo jsAccordionSwitch">
					<dt><strong>重要なご案内</strong></dt>
					<dd>
						<ul class="jsAccordionSwitchList" id="informationMessages">
								<li>個人情報の利用目的<a href="https://www.ana.co.jp/other/int/meta/0012.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="ANAプライバシーポリシー(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">ANAプライバシーポリシー<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>を必ずお読みいただき、内容に同意いただいた上で、お進みください。</li>
								<li><a href="https://www.ana.co.jp/other/int/meta/0058.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="Secure Flight Programについて(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">米国Secure Flightプログラム<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>に基づき、パスポート名、生年月日、性別などの情報は事前に米国へ通知いたします。</li>
								<li>入力したeメールアドレス、電話番号は入国に必要な情報として米国に通知される可能性があります。</li>
									<li class="jsHiddenFlg toggleContents">お名前のスペルがパスポート名と一致しているかご確認ください。異なる場合はインターネットではお取り扱いできません。<a href="https://www.ana.co.jp/other/int/meta/intbe0001.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="ANAお問い合わせ先(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">ANA<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>へ名前の変更をお申し出ください。</li>
									<li class="jsHiddenFlg toggleContents">二重国籍のお客様は、ご搭乗時に使用されるパスポートのお名前でご予約ください。<br />パスポートごとにお名前が異なる場合は、お客様ご自身で入国管理事務所・外務省・大使館などで各種条件をご確認のうえ、使用されるパスポートを決めてください。<br />なお、入国可否については国によって異なるため、お客様ご自身で各入国管理事務所などでご確認ください。</li>
									<li class="jsHiddenFlg toggleContents">旅行開始日時点で12歳以上の場合は大人、2歳以上12歳未満の場合は小児、2歳未満の場合は幼児の扱いとなります。なお、小児は大人と同マイル数が必要です。</li>
									<li class="jsHiddenFlg toggleContents">運航に関する情報をお客様へ通知するため、提携航空会社にもお客様のメールアドレスが提供されます。あらかじめご了承ください。</li>
									<li class="jsHiddenFlg toggleContents"><dl><dt>携帯電話へのメール送信をご希望のお客様へ</dt><dd>ドメイン指定受信をご利用の場合は、ana.co.jpとamadeus.comからのメールが受信できるようあらかじめ設定をお願いいたします。</dd></dl></li>
									<li class="jsHiddenFlg toggleContents">SMSはスマートフォンの番号に配信されます。一部の国ではSMSでのご案内をご利用いただけません。詳細は<a href="https://www.ana.co.jp/other/int/meta/0737.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="ショートメッセージ(SMS)でのご案内対象外の国について(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">ショートメッセージ(SMS)でのご案内対象外の国について<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>をご覧ください。</li>
									<li class="jsHiddenFlg toggleContents">ログインされている会員ご本人が搭乗しない場合は、再度フライト検索画面より「ログインされている会員ご本人は搭乗しない」のチェックボックスを選択してお手続きください。</li>
						</ul>
							<a href="#" class="toggleSwitch jsTriggerSwitch" role="button" aria-controls="informationMessages" aria-expanded="false"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/toggle_open.png?7bec38a" alt="開閉ボタン" height="25" width="25" class="jsRollOver" />
							</a>
					</dd>
				</dl></div>
	<!--=========== /main =============-->
	</div>

	<!--=========== Script ============-->
	<script type="text/javascript">
	$(window).on('load', function() {
		// アコーディオンのメソッドと設定
		Asw.AccordionInfo('.jsAccordionSwitch', {
			'triggerSwitch': '.jsAccordionSwitch .jsTriggerSwitch',
			'listClass': '.jsAccordionSwitchList',
			'visibilityHidden': 'toggleContents'
		});
	});
	</script>
	<!--=========== Script ============-->
	<script type="text/javascript">
		//プラットフォーム判別（サマリーエリア追従用として）
		var Asw = Asw || {};
	
	  	Asw.ClientInfo = Asw.ClientInfo  || {};
	  	Asw.ClientInfo.DeviceType = Asw.ClientInfo.DeviceType || {};
	
	  	Asw.ClientInfo.DeviceType.PC = 'PC';
	  	Asw.ClientInfo.DeviceType.SP = 'SP';
	  	Asw.ClientInfo.DeviceType.TC = 'TC';
	
	  	Asw.ClientInfo.deviceType = Asw.ClientInfo.DeviceType.PC;
	
	 	if((navigator.userAgent.indexOf('Android') > 0 && navigator.userAgent.indexOf('Mobile') == -1) || navigator.userAgent.indexOf('iPad') > 0){
	    	Asw.ClientInfo.deviceType = Asw.ClientInfo.DeviceType.TC;
	  	}
	</script>
	<!--=========== /Script ============--><div id="summaryArea">
			<div class="flightSummaryArea">
						<div class="flightSummarySection">
							<h2>区間 1
							</h2>
							<p class="flightDate"><em>6</em>月<em class="hasLeft">28</em>日（土）
							</p>
							<div class="flightSummaryRoute">
									<div class="flightSummaryRouteSection">
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation1_1">
													<span>東京(成田)
													</span>
												</p>
												<p class="time">
													<span>17:00
													</span>
												</p>
											</div>
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation1_1">
													<span>シカゴ(ORD)
													</span>
												</p>
												<p class="time">
													<span>14:55
													</span>
												</p>
											</div>
										<p class="flightNumberClass">
												<span>NH012</span><span><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/flight_ana_summaryarea.png?717d3c0" alt="ANA運航便" height="11" width="41" /></span>
												<br />特典ビジネス
										</p>
									</div>
							</div>
						</div>
						<div class="flightSummarySection">
							<h2 class="midstream">区間 2
							</h2>
							<p class="flightDate"><em>7</em>月<em class="hasLeft">23</em>日（水）
							</p>
							<div class="flightSummaryRoute">
									<div class="flightSummaryRouteSection">
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation2_1">
													<span>ホノルル(オアフ島)
													</span>
												</p>
												<p class="time">
													<span>11:35
													</span>
												</p>
											</div>
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation2_1">
													<span>東京(成田)
													</span>
												</p>
												<p class="time">
													<span>14:50
																	<span>翌日
																	</span>
													</span>
												</p>
											</div>
										<p class="flightNumberClass">
												<span>NH183</span><span><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/flight_ana_summaryarea.png?717d3c0" alt="ANA運航便" height="11" width="41" /></span>
												<br />特典エコノミー
										</p>
									</div>
							</div>
						</div>
					<div class="total">
						<p class="passenger">大人 x1
						</p>
						<div class="totalArea">
					  		<dl class="mealAmountWrap">
									<dt>有料機内食サービス
									</dt>
									<dd><em class="mealAmount">0</em><span class="currencyCode">円</span>
									</dd>
							</dl>
									<div>
										<div class="mileageAndTotal">
												<dl class="requiredMileage">
													<dt>必要マイル
													</dt>
													<dd><em class="requiredMileage">74,000</em><span class="currencyCode">マイル</span>
													</dd>
												</dl>
												<dl>
													<dt>総額
													</dt>
													<dd><em class="awardTotalPayment">68,130</em>円
													</dd>
												</dl>
												<p class="includeFareAndFuelCost">※各種税金、燃油特別付加運賃等を含みます。
												</p>
										</div>
									</div>
						</div>
					</div>
				
			</div></div>
		<!--===== /cmnWrapper =====-->
		</div>
			
		<span id="message" class="visuallyHidden" role="alert" aria-live="assertive"></span>
		<span id="politeMessage" class="visuallyHidden" role="log" aria-live="polite"></span><script type="text/javascript">
		var Asw = Asw || {};
		Asw.BaseOutput = {"sessionID":"fCWLnDMoai","language":"ja","siteCatalystLanguageCode":"J","operationDate":"20250620","operationDateTime":"20250620133252","office":{"officeCode":"TYONH08ZZ","country2letterCode":"JP"},"bookingType":"A","pageID":"A04_P01","siteCatalystPageName":"INT_BE_AWARD_J_A04特典予約_P01旅客情報入力（必須情報）","device":{"deviceType":"PC"}};
	</script><div id="promotionArea">
		<!--===== promotionArea =====-->
		<!--===== /promotionArea =====--></div>
		<!--=========== /cmnContainer ============--></div><div id="cmnBaloonArea" aria-live="polite"></div><div id="cmnPopupArea" aria-live="polite"></div>

	

	<div id="cmnFooterWrapper">
		<!--=========== .cmnFooterWrapper ============-->
		<div id="cmnFooter">
			<!--===== cmnFooter =====-->
			<ul>
				<li><a href="https://www.ana.co.jp/other/int/meta/0012.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">プライバシーポリシー<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
					<li><a href="https://www.ana.co.jp/other/int/meta/0670.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">利用者情報の外部送信について<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
					</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/0052.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">サイト利用規約<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/be0238.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">推奨環境<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/0133.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">サイトマップ<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/0732.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">運送約款<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
					<li class="lastChild"><a href="https://www.ana.co.jp/other/int/meta/0556.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">ウェブアクセシビリティについて<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
					</li>
			</ul>
			<p class="blankText" aria-hidden="true"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="" height="10" width="10" />外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。</p>
			<p class="copyright"><span lang="en" xml:lang="en">Copyright&#169;ANA</span>
			</p>
			<p class="starAllianceLogo"><a href="http://www.staralliance.com/ja/" target="_blank"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/star_alliance_logo.png?0fe73b5" alt="A STAR ALLIANCE MEMBER" height="18" width="175" /><span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
			</p>
			<!--===== cmnFooter =====-->
		</div>
		<!--=========== /cmnFooterWrapper ============-->
	</div>

		
		<div id="cmnLoadingForAjax"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/bg_modalWindow.png?717d3c0" alt="" height="0" width="0" />
			<div class="loadingArea">
				<p class="loadingImage"><img src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/image/loading.gif?717d3c0" alt="リクエストを処理しています...しばらくお待ちください。" height="33" width="33" />
				</p>
			</div>
		</div>
		
		
		<div id="popupAltSentence" style="display: none" aria-hidden="true">別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。</div>
	<div id="cmnSiteCatalyst" aria-hidden="true"><div id="cmnSiteCatalystParamArea"><script type="text/javascript">
				// ▼SiteCatalyst
				// SiteCatalyst code version: H.2.
				// Copyright 1997-2005 Omniture, Inc. More info available at http://www.omniture.com
				SiteCatalystReportSuites   = "INT";
				SiteCatalystCharSet        = "UTF-8";
				SiteCatalystChannel        = "BE_AWARD_J";
				SiteCatalystPageName       = "INT_BE_AWARD_J_A04\u7279\u5178\u4E88\u7D04_P01\u65C5\u5BA2\u60C5\u5831\u5165\u529B\uFF08\u5FC5\u9808\u60C5\u5831\uFF09";
				SiteCatalystERR            = "";
				SiteCatalystEventNum       = "";
				SiteCatalystPurchase       = "FALSE";
				SiteCatalystProductPrice   = "";
				SiteCatalystCurrencyCode   = "";
				SiteCatalystSeatAvailabilityINT   = "";
				SiteCatalystDepartDate     = "";
				SiteCatalystDepart         = "";
				SiteCatalystArrive         = "";
				SiteCatalystFlightNo       = "";
				SiteCatalystClass          = "";
				SiteCatalystFare           = "";
				SiteCatalystPurchaseID     = "";
				
				SiteCatalystLocater        = "";
				SiteCatalystPayment        = "";
				SiteCatalystIncome         = "";
				
				SiteCatalystPax            = "";
				SiteCatalystManipulateDay  = "";
				SiteCatalystCompartmentClass   = "";
				SiteCatalystOptionInfo     = "";
				SiteCatalystOutboundUpsellPrice   = "";
				SiteCatalystInboundUpsellPrice    = "";
				
				SiteCatalystSearchInput    = "";
				SiteCatalystSegment        = "";
				SiteCatalystOfficeCode     = "";
				SiteCatalystPassengerCount = "";
				SiteCatalystPassenger      = "";
				SiteCatalystAncillaryService = "";
				// End SiteCatalyst code version: H.2.
				// ▲SiteCatalyst
			</script></div><script type="text/javascript"><!-- if(navigator.appVersion.indexOf('MSIE')>=0)document.write(unescape('%3C')+'\!-'+'-') // --></script>
		<!--/DO NOT REMOVE/-->
		

		
		<!-- Google Tag Manager -->
		<script type="text/javascript" charset="Shift_JIS" src="https://www.ana.co.jp/common/js/GTMINTBE.js"></script>
		<!-- End Google Tag Manager -->
		<!-- Yahoo Tag Manager -->
		<script type="text/javascript" charset="Shift_JIS" src="https://www.ana.co.jp/common/js/YTMINTBE.js"></script>
		<!-- End Yahoo Tag Manager -->
		

	</div>

	

	<div class="mboxDefault"></div><script type="text/javascript">mboxCreate("ASW_common_A04_P01");</script>
			<script type="text/javascript" src="https://www.ana.co.jp/common/js/tealium/tealium.js"></script>
			<script type="text/javascript" src="//cdn.evgnet.com/beacon/allnipponairways/nonamcprd/scripts/evergage.min.js"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/javax.faces.resource/jsf.js.xhtml?ln=javax.faces"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/jquery.mousewheel.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/jquery.jscrollpane.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/jquery-ui-1.10.4.custom.min.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/jquery.ui.touch-punch.min.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/asw-common.js?beb6a50"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/asw-common-pc.js?fd88d7d"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/asw-format.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/mask.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/loading.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/asw-loadingwindow-pc.js?9e233c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/asw-loadingwindow.js?51db600"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/dialog-pc.js?25d36a4"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/asw-dialog-sequencer.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/asw-event.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/asw-date-select.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/asw-calendar-pc.js?9b86b8a"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/asw-calendar.js?db624c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/heightLine.js?0e7a1ce"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/asw-summaryarea.js?b21c6d7"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/asw-fingerprints.js?99001fc"></script><script type="text/javascript">
			Asw.init("https://aswbe-i.ana.co.jp/rei22h/international_asw", "ja");
			Asw.getCalendarHolidayList = function() {return [20250721,20250811,20250915,20250923,20251013,20251103,20251123,20251124,20260101,20260112,20260211,20260223,20260320,20260429,20260503,20260504,20260505,20260506,20260720,20260811,20260921,20260922,20260923,20261012,20261103,20261123,20270101,20270111,20270211,20270223,20270321,20270322,20270429,20270503,20270504,20270505,20270719,20270811,20270920,20270923,20271011,20271103,20271123,20280101,20280110,20280211,20280223,20280320,20280429,20280503,20280504,20280505,20280717,20280811,20280918,20280922,20281009,20281103,20281123,20290101,20290108,20290211,20290212,20290223,20290320,20290429,20290430,20290503,20290504,20290505,20290716,20290811,20290917,20290923,20290924,20291008,20291103,20291123];}
			Asw.ClientInfo.deviceType = "PC";
			Asw.ClientInfo.mobileDeviceType = "UNKNOWN";
			Asw.ClientInfo.osVersion = "";
			Asw.Format.init("https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/{1}/{2}/{3}?{0}");
			Asw.overwriteTargetAttributeValue("false");
		</script><script type="text/javascript">
		
		var Asw = Asw || {};
		Asw.ContactInput = Asw.ContactInput || {};
		Asw.ContactInput.Const = {
			/** data-属性名「data-type」 @constant @type {String} @default 'data-type' */
			DATA_ATTRIBUTE_NAME_TYPE: 'data-type',
			/** data-属性名「data-sms-possible」 @constant @type {String} @default 'data-sms-possible' */
			DATA_ATTRIBUTE_NAME_SMS_POSSIBLE: 'data-sms-possible',
			/** data-属性名「data-country-code」 @constant @type {String} @default 'data-country-code' */
			DATA_ATTRIBUTE_NAME_COUNTRY_CODE: 'data-country-code',
			/** data-type属性値「sms」 @constant @type {String} @default 'sms' */
			DATA_TYPE_VALUE_SMS: 'sms'
		};

		
		Asw.ContactInput.countryInfoMap = {"IS":{"internationalCallingCode":"+354","smsPossible":false},"IE":{"internationalCallingCode":"+353","smsPossible":true},"AZ":{"internationalCallingCode":"+994","smsPossible":true},"AF":{"internationalCallingCode":"+93","smsPossible":true},"US":{"internationalCallingCode":"+1","smsPossible":true},"AE":{"internationalCallingCode":"+971","smsPossible":true},"DZ":{"internationalCallingCode":"+213","smsPossible":true},"AR":{"internationalCallingCode":"+54","smsPossible":true},"AW":{"internationalCallingCode":"+297","smsPossible":true},"AL":{"internationalCallingCode":"+355","smsPossible":true},"AM":{"internationalCallingCode":"+374","smsPossible":true},"AO":{"internationalCallingCode":"+244","smsPossible":false},"AG":{"internationalCallingCode":"+1","smsPossible":true},"AD":{"internationalCallingCode":"+376","smsPossible":true},"YE":{"internationalCallingCode":"+967","smsPossible":true},"GB":{"internationalCallingCode":"+44","smsPossible":true},"IL":{"internationalCallingCode":"+972","smsPossible":true},"IT":{"internationalCallingCode":"+39","smsPossible":true},"IQ":{"internationalCallingCode":"+964","smsPossible":true},"IR":{"internationalCallingCode":"+98","smsPossible":true},"IN":{"internationalCallingCode":"+91","smsPossible":true},"ID":{"internationalCallingCode":"+62","smsPossible":true},"UG":{"internationalCallingCode":"+256","smsPossible":true},"UA":{"internationalCallingCode":"+380","smsPossible":true},"UZ":{"internationalCallingCode":"+998","smsPossible":true},"UY":{"internationalCallingCode":"+598","smsPossible":true},"EC":{"internationalCallingCode":"+593","smsPossible":true},"EG":{"internationalCallingCode":"+20","smsPossible":true},"EE":{"internationalCallingCode":"+372","smsPossible":true},"SZ":{"internationalCallingCode":"+268","smsPossible":false},"ET":{"internationalCallingCode":"+251","smsPossible":false},"ER":{"internationalCallingCode":"+291","smsPossible":true},"SV":{"internationalCallingCode":"+503","smsPossible":true},"AU":{"internationalCallingCode":"+61","smsPossible":true},"AT":{"internationalCallingCode":"+43","smsPossible":true},"OM":{"internationalCallingCode":"+968","smsPossible":true},"NL":{"internationalCallingCode":"+31","smsPossible":true},"GH":{"internationalCallingCode":"+233","smsPossible":true},"CV":{"internationalCallingCode":"+238","smsPossible":false},"GY":{"internationalCallingCode":"+592","smsPossible":true},"KZ":{"internationalCallingCode":"+7","smsPossible":true},"QA":{"internationalCallingCode":"+974","smsPossible":true},"CA":{"internationalCallingCode":"+1","smsPossible":true},"GA":{"internationalCallingCode":"+241","smsPossible":true},"CM":{"internationalCallingCode":"+237","smsPossible":true},"KR":{"internationalCallingCode":"+82","smsPossible":true},"KH":{"internationalCallingCode":"+855","smsPossible":true},"GM":{"internationalCallingCode":"+220","smsPossible":true},"KP":{"internationalCallingCode":"+850","smsPossible":false},"MK":{"internationalCallingCode":"+389","smsPossible":false},"GN":{"internationalCallingCode":"+224","smsPossible":true},"GW":{"internationalCallingCode":"+245","smsPossible":true},"CY":{"internationalCallingCode":"+357","smsPossible":true},"CU":{"internationalCallingCode":"+53","smsPossible":true},"GR":{"internationalCallingCode":"+30","smsPossible":true},"KI":{"internationalCallingCode":"+686","smsPossible":false},"KG":{"internationalCallingCode":"+996","smsPossible":true},"GT":{"internationalCallingCode":"+502","smsPossible":true},"GU":{"internationalCallingCode":"+1","smsPossible":true},"KW":{"internationalCallingCode":"+965","smsPossible":true},"KY":{"internationalCallingCode":"+1","smsPossible":true},"GD":{"internationalCallingCode":"+1","smsPossible":true},"HR":{"internationalCallingCode":"+385","smsPossible":true},"KE":{"internationalCallingCode":"+254","smsPossible":true},"CI":{"internationalCallingCode":"+225","smsPossible":true},"CR":{"internationalCallingCode":"+506","smsPossible":true},"SA":{"internationalCallingCode":"+966","smsPossible":true},"KM":{"internationalCallingCode":"+269","smsPossible":true},"CO":{"internationalCallingCode":"+57","smsPossible":true},"CG":{"internationalCallingCode":"+242","smsPossible":false},"CD":{"internationalCallingCode":"+243","smsPossible":true},"ZM":{"internationalCallingCode":"+260","smsPossible":true},"SM":{"internationalCallingCode":"+378","smsPossible":false},"SL":{"internationalCallingCode":"+232","smsPossible":true},"DJ":{"internationalCallingCode":"+253","smsPossible":false},"JM":{"internationalCallingCode":"+1","smsPossible":true},"CH":{"internationalCallingCode":"+41","smsPossible":true},"GE":{"internationalCallingCode":"+995","smsPossible":true},"SE":{"internationalCallingCode":"+46","smsPossible":true},"SY":{"internationalCallingCode":"+963","smsPossible":true},"ES":{"internationalCallingCode":"+34","smsPossible":true},"SG":{"internationalCallingCode":"+65","smsPossible":true},"LK":{"internationalCallingCode":"+94","smsPossible":true},"SK":{"internationalCallingCode":"+421","smsPossible":true},"SI":{"internationalCallingCode":"+386","smsPossible":true},"ZW":{"internationalCallingCode":"+263","smsPossible":true},"SD":{"internationalCallingCode":"+249","smsPossible":true},"KN":{"internationalCallingCode":"+1","smsPossible":true},"SR":{"internationalCallingCode":"+597","smsPossible":true},"LC":{"internationalCallingCode":"+1","smsPossible":true},"SC":{"internationalCallingCode":"+248","smsPossible":false},"SN":{"internationalCallingCode":"+221","smsPossible":true},"SB":{"internationalCallingCode":"+677","smsPossible":false},"RS":{"internationalCallingCode":"+381","smsPossible":true},"TH":{"internationalCallingCode":"+66","smsPossible":true},"TW":{"internationalCallingCode":"+886","smsPossible":true},"TJ":{"internationalCallingCode":"+992","smsPossible":true},"TZ":{"internationalCallingCode":"+255","smsPossible":true},"CZ":{"internationalCallingCode":"+420","smsPossible":true},"TD":{"internationalCallingCode":"+235","smsPossible":true},"CF":{"internationalCallingCode":"+236","smsPossible":true},"TN":{"internationalCallingCode":"+216","smsPossible":true},"CL":{"internationalCallingCode":"+56","smsPossible":true},"TV":{"internationalCallingCode":"+688","smsPossible":false},"DK":{"internationalCallingCode":"+45","smsPossible":true},"DE":{"internationalCallingCode":"+49","smsPossible":true},"TG":{"internationalCallingCode":"+228","smsPossible":true},"DO":{"internationalCallingCode":"+1","smsPossible":true},"DM":{"internationalCallingCode":"+1","smsPossible":false},"TT":{"internationalCallingCode":"+1","smsPossible":true},"TM":{"internationalCallingCode":"+993","smsPossible":false},"TR":{"internationalCallingCode":"+90","smsPossible":true},"TO":{"internationalCallingCode":"+676","smsPossible":false},"NG":{"internationalCallingCode":"+234","smsPossible":true},"NR":{"internationalCallingCode":"+674","smsPossible":false},"NA":{"internationalCallingCode":"+264","smsPossible":false},"NI":{"internationalCallingCode":"+505","smsPossible":true},"BH":{"internationalCallingCode":"+973","smsPossible":false},"NE":{"internationalCallingCode":"+227","smsPossible":true},"PK":{"internationalCallingCode":"+92","smsPossible":true},"JP":{"internationalCallingCode":"+81","smsPossible":true},"BS":{"internationalCallingCode":"+1","smsPossible":true},"NZ":{"internationalCallingCode":"+64","smsPossible":true},"BM":{"internationalCallingCode":"+1","smsPossible":true},"NP":{"internationalCallingCode":"+977","smsPossible":true},"BB":{"internationalCallingCode":"+246","smsPossible":true},"NO":{"internationalCallingCode":"+47","smsPossible":true},"HT":{"internationalCallingCode":"+509","smsPossible":true},"BD":{"internationalCallingCode":"+880","smsPossible":true},"VA":{"internationalCallingCode":"+39","smsPossible":false},"PA":{"internationalCallingCode":"+507","smsPossible":true},"VU":{"internationalCallingCode":"+678","smsPossible":false},"PG":{"internationalCallingCode":"+675","smsPossible":true},"PW":{"internationalCallingCode":"+680","smsPossible":true},"PY":{"internationalCallingCode":"+595","smsPossible":true},"PS":{"internationalCallingCode":"+970","smsPossible":true},"BR":{"internationalCallingCode":"+55","smsPossible":true},"HU":{"internationalCallingCode":"+36","smsPossible":true},"TL":{"internationalCallingCode":"+670","smsPossible":true},"BG":{"internationalCallingCode":"+359","smsPossible":true},"BF":{"internationalCallingCode":"+226","smsPossible":true},"BN":{"internationalCallingCode":"+673","smsPossible":true},"FJ":{"internationalCallingCode":"+679","smsPossible":true},"PH":{"internationalCallingCode":"+63","smsPossible":true},"FI":{"internationalCallingCode":"+358","smsPossible":true},"BT":{"internationalCallingCode":"+975","smsPossible":true},"BY":{"internationalCallingCode":"+375","smsPossible":true},"BZ":{"internationalCallingCode":"+501","smsPossible":false},"PR":{"internationalCallingCode":"+1","smsPossible":true},"BE":{"internationalCallingCode":"+32","smsPossible":true},"BA":{"internationalCallingCode":"+387","smsPossible":true},"FR":{"internationalCallingCode":"+33","smsPossible":true},"BO":{"internationalCallingCode":"+591","smsPossible":false},"PF":{"internationalCallingCode":"+689","smsPossible":true},"BI":{"internationalCallingCode":"+257","smsPossible":true},"VN":{"internationalCallingCode":"+84","smsPossible":true},"BJ":{"internationalCallingCode":"+229","smsPossible":true},"VE":{"internationalCallingCode":"+58","smsPossible":true},"PE":{"internationalCallingCode":"+51","smsPossible":true},"PL":{"internationalCallingCode":"+48","smsPossible":true},"BW":{"internationalCallingCode":"+267","smsPossible":true},"PT":{"internationalCallingCode":"+351","smsPossible":false},"HK":{"internationalCallingCode":"+852","smsPossible":true},"HN":{"internationalCallingCode":"+504","smsPossible":false},"MH":{"internationalCallingCode":"+692","smsPossible":true},"MO":{"internationalCallingCode":"+853","smsPossible":true},"MG":{"internationalCallingCode":"+261","smsPossible":true},"MW":{"internationalCallingCode":"+265","smsPossible":true},"ML":{"internationalCallingCode":"+223","smsPossible":true},"MT":{"internationalCallingCode":"+356","smsPossible":true},"MY":{"internationalCallingCode":"+60","smsPossible":true},"FM":{"internationalCallingCode":"+691","smsPossible":false},"MM":{"internationalCallingCode":"+95","smsPossible":true},"MX":{"internationalCallingCode":"+52","smsPossible":true},"MU":{"internationalCallingCode":"+230","smsPossible":true},"MR":{"internationalCallingCode":"+222","smsPossible":true},"MZ":{"internationalCallingCode":"+258","smsPossible":true},"MC":{"internationalCallingCode":"+377","smsPossible":true},"MV":{"internationalCallingCode":"+960","smsPossible":true},"MD":{"internationalCallingCode":"+373","smsPossible":false},"RO":{"internationalCallingCode":"+40","smsPossible":true},"MA":{"internationalCallingCode":"+212","smsPossible":true},"MN":{"internationalCallingCode":"+976","smsPossible":true},"ME":{"internationalCallingCode":"+382","smsPossible":true},"JO":{"internationalCallingCode":"+962","smsPossible":true},"LA":{"internationalCallingCode":"+856","smsPossible":true},"RU":{"internationalCallingCode":"+7","smsPossible":true},"LV":{"internationalCallingCode":"+371","smsPossible":true},"LT":{"internationalCallingCode":"+370","smsPossible":true},"LY":{"internationalCallingCode":"+218","smsPossible":true},"GQ":{"internationalCallingCode":"+240","smsPossible":false},"LI":{"internationalCallingCode":"+423","smsPossible":false},"LR":{"internationalCallingCode":"+231","smsPossible":true},"CN":{"internationalCallingCode":"+86","smsPossible":true},"LU":{"internationalCallingCode":"+352","smsPossible":true},"ZA":{"internationalCallingCode":"+27","smsPossible":true},"RW":{"internationalCallingCode":"+250","smsPossible":true},"LS":{"internationalCallingCode":"+266","smsPossible":false},"LB":{"internationalCallingCode":"+961","smsPossible":true}};

		$(function () {
			contactInfoInit();
		});
		
		function contactInfoInit() {
			
			
			Asw.get('representativeTelType').find('option[value="M1"]').attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_TYPE,
					Asw.ContactInput.Const.DATA_TYPE_VALUE_SMS);
			Asw.get('representativeTelCountry').children('option').each(function(i,option){
				var countryInfo = Asw.ContactInput.countryInfoMap[option.value];
				if (countryInfo === void 0) {
					
					return true;
				}
				$(option).attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_SMS_POSSIBLE, countryInfo.smsPossible);
				$(option).attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_COUNTRY_CODE, countryInfo.internationalCallingCode);
			});
			
			var smsCountrySelectSelector = 'select.js-countrySelect:not(#representativeTelCountry)'
			$(smsCountrySelectSelector).eq(0).children('option').each(function(i,option){
				var countryInfo = Asw.ContactInput.countryInfoMap[option.value];
				if (countryInfo === void 0) {
					
					return true;
				}
				$(smsCountrySelectSelector + ' option:nth-child(' + (i+1) + ')').attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_COUNTRY_CODE,
						countryInfo.internationalCallingCode);
			});
			
			var emergencyCountrySelectSelector = 'select.passengerEmergencyCountry.js-countrySelect:not(#representativeTelCountry)'
			$(emergencyCountrySelectSelector).eq(0).children('option').each(function(i,option){
				var countryInfo = Asw.ContactInput.countryInfoMap[option.value];
				if (countryInfo === void 0) {
					
					return true;
				}
				$(emergencyCountrySelectSelector + ' option:nth-child(' + (i+1) + ')').attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_COUNTRY_CODE,
						countryInfo.internationalCallingCode);
			});
		}
	</script><script type="text/javascript">
		var isInit = true;
		function changeTelType(_this, telNoInputId) {
			var $telNoInput = Asw.get(telNoInputId);
			var $telNoKind = Asw.get("representativeTelType");
			var telNo = Asw.getValueComparedToPlaceholder(telNoInputId);
			var telNoCountry = Asw.get("representativeTelCountry").val();

			if(true && !isInit) {
				if(telInputCheck(telNo, telNoCountry)) {
					if (_this.value == "M1") {
						telNo = exceptDomesticPrefix("09042504176", telNoCountry);
					} else if (_this.value == "B1") {
						telNo = exceptDomesticPrefix("03-5405-2172", telNoCountry);
					} else {
						telNo = exceptDomesticPrefix("090-4250-4176", telNoCountry);
					}
					$telNoInput.val(telNo);
				}
			}
			isInit = false;
		}
		
		function telInputCheck(telNo, telNoCountry) {
	        checkResult = false;
	        
	        var amcHndyTelNo = exceptDomesticPrefix("09042504176", telNoCountry);
	        var amcCmpTelNo = exceptDomesticPrefix("03-5405-2172", telNoCountry);
	        var amcTelNo = exceptDomesticPrefix("090-4250-4176", telNoCountry);
	
	        if(telNo == "" || telNo == amcHndyTelNo || telNo == amcCmpTelNo || telNo == amcTelNo) {
	        	checkResult = true;
	        }
	        return checkResult;
    	}
    	
    	function exceptDomesticPrefix(telNo, telNoCountry) {
    		if(telNoCountry == 'JP') {
    			if(telNo.substr(0, 1) === '0') {
    				return telNo.slice(1);
    			}
    		}
    		return telNo;
    	}
	</script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/asw-customerinfomationform.js?6e91953"></script><script type="text/javascript">
		$(document).ready(function(){
				Asw.enableTextWhenCheckBox('.cascadedSwitch');
				Asw.notPasteInput('.jsNotPasteInput');
			$(function(){
				Asw.alignHeightToParentTdElement( '.passengerInfoInputArea' );
				
					Asw.alignHeightToParentTdElement( '.passengerInfoInputText' );
				
				
				$('select.js-countrySelect').trigger('change');
			});
		});
	</script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22h/international_asw/contents/cmn/script/asw-session-keeper.js?6ec0730"></script><script type="text/javascript">
			$(document).ready(function() {
				Asw.SessionKeeper.initAndStart("sessionKeeperContainer", "520", "570", "PC");
			});
			</script><script type="text/javascript">
		Asw.addAjaxCallback(function(data) {
			// アコーディオンのメソッドと設定 
			if($('.jsAccordionSwitch .jsTriggerSwitch').length > 0){
				var events = $._data($('.jsAccordionSwitch .jsTriggerSwitch').get(0), "events");
				if(events === undefined || events.click === undefined) {
					Asw.AccordionInfo('.jsAccordionSwitch', {
						'triggerSwitch': '.jsAccordionSwitch .jsTriggerSwitch',
						'listClass': '.jsAccordionSwitchList',
						'visibilityHidden': 'toggleContents'
					});
				}
			}
		});
	</script><script type="text/javascript">
	Asw.Price = Asw.Price || {};
	// 整数の区切り文字
	Asw.Price.separateString = ',';
	
	// 整数の区切り桁数
	Asw.Price.separateDigitNumber = 3;
	
	// 小数点
	Asw.Price.decimalPointString = '.';
	
	// 小数点以降の有効桁数
	Asw.Price.significantDigit = 0;
	
	// 表示金額の単位
	Asw.Price.currencyUnit = '円';
	</script><script type="text/javascript">
			$(document).ready(function() {
				$('#preLoadingArea').css('visibility', 'hidden');
			});
		</script><noscript><img src="https://aswbe-i.ana.co.jp/akam/13/pixel_4a592902?a=dD02YTQzYzE5MzgwMDk1MTcxZWEzOWMyYjNkZDQ4MGRjZjZiZDA1MjUxJmpzPW9mZg==" style="visibility: hidden; position: absolute; left: -999px; top: -999px;" /></noscript><script type="text/javascript"  src="/Q1XS4o/Cn_ls/KfDO7/xA/t9OfhmEwSiGQaG/NG4dCQE/bw/QJBChQIQEB"></script></body>
</html>