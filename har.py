import json
import sys
import os
from datetime import datetime

def filter_har_file(input_file, output_file=None):
    """
    过滤HAR文件，提取可能与会话延长相关的请求
    """
    try:
        # 读取HAR文件
        with open(input_file, 'r', encoding='utf-8') as f:
            har_data = json.load(f)
        
        # 提取所有请求
        entries = har_data.get('log', {}).get('entries', [])
        print(f"原始HAR文件包含 {len(entries)} 个请求")
        
        # 过滤可能与会话延长相关的请求
        session_keeping_requests = []
        form_submissions = []
        
        for entry in entries:
            request = entry.get('request', {})
            url = request.get('url', '')
            method = request.get('method', '')
            
            # 检查POST请求
            if method == 'POST':
                # 检查请求体
                post_data = request.get('postData', {})
                post_text = post_data.get('text', '')
                
                # 检查是否包含会话延长相关关键词
                keywords = [
                    'SessionKeeping', 
                    'cmnSessionKeepingButton',
                    'sessionKeeper',
                    '延長',
                    'extend',
                    'javax.faces.ViewState'
                ]
                
                if any(keyword in post_text for keyword in keywords) or any(keyword in url for keyword in keywords):
                    session_keeping_requests.append(entry)
                
                # 收集所有表单提交
                if post_data.get('mimeType') == 'application/x-www-form-urlencoded':
                    form_submissions.append(entry)
        
        # 如果没有找到明确的会话延长请求，使用所有表单提交
        filtered_entries = session_keeping_requests if session_keeping_requests else form_submissions
        
        print(f"过滤后保留 {len(filtered_entries)} 个可能与会话延长相关的请求")
        
        # 创建更易读的输出格式
        readable_requests = []
        for entry in filtered_entries:
            request = entry.get('request', {})
            response = entry.get('response', {})
            
            # 提取请求时间
            started_time = entry.get('startedDateTime', '')
            try:
                dt = datetime.fromisoformat(started_time.replace('Z', '+00:00'))
                time_str = dt.strftime('%Y-%m-%d %H:%M:%S')
            except:
                time_str = started_time
            
            # 提取请求详情
            url = request.get('url', '')
            method = request.get('method', '')
            
            # 提取请求头
            headers = {h['name']: h['value'] for h in request.get('headers', [])}
            
            # 提取请求体
            post_data = request.get('postData', {})
            post_text = post_data.get('text', '')
            
            # 尝试解析表单数据
            form_data = {}
            if post_data.get('mimeType') == 'application/x-www-form-urlencoded' and post_text:
                try:
                    for param in post_text.split('&'):
                        if '=' in param:
                            key, value = param.split('=', 1)
                            form_data[key] = value
                except:
                    form_data = {'raw': post_text}
            
            # 提取响应状态
            status = response.get('status', 0)
            status_text = response.get('statusText', '')
            
            # 创建易读格式
            readable_request = {
                'time': time_str,
                'method': method,
                'url': url,
                'status': f"{status} {status_text}",
                'headers': headers,
                'form_data': form_data,
                'raw_post_data': post_text if len(post_text) < 1000 else f"{post_text[:1000]}... (截断，总长度: {len(post_text)})"
            }
            
            readable_requests.append(readable_request)
        
        # 生成输出文件名
        if output_file is None:
            base_name = os.path.splitext(input_file)[0]
            output_file = f"{base_name}_filtered.json"
        
        # 保存过滤后的结果
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(readable_requests, f, ensure_ascii=False, indent=2)
        
        print(f"过滤结果已保存到: {output_file}")
        return output_file
    
    except Exception as e:
        print(f"处理HAR文件时出错: {e}")
        return None

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("用法: python har.py <har文件路径> [输出文件路径]")
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_file = sys.argv[2] if len(sys.argv) > 2 else None
    
    filter_har_file(input_file, output_file)