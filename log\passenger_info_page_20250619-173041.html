<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="ja-<PERSON>" xml:lang="ja-<PERSON>">

	
	<!-- skey = 2025/06/19 18:30:42 rei21c NEWHhoOyXT dljdmx+ab0  --><head id="j_idt53">
    <base href="about:blank">
    <style>
        /* Hide elements that might try to load external resources, as a fallback. */
        img, svg, video, iframe, link[rel="stylesheet"], script { display: none !important; }
    </style>

		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
			<meta http-equiv="X-UA-Compatible" content="IE=EmulateIE9; IE=EmulateIE10" />
			<meta http-equiv="X-UA-Compatible" content="IE=7" />
			<meta http-equiv="X-UA-Compatible" content="IE=edge" />
		<meta name="description" content="" />
		<meta name="keywords" content="" />
		<meta name="format-detection" content="telephone=no" />
		<script type="text/javascript" src="/international_asw/dynatrace/ruxitagentjs_ICANVfhqrux_10273230920145353.js" data-dtconfig="rid=RID_710847359|rpid=*********|domain=aswbe-i.ana.co.jp|reportUrl=https://bf90419wvr.bf.dynatrace.com/bf|app=78bf0b58acf6ed13|cors=1|owasp=1|featureHash=ICANVfhqrux|xb=www^bs.google-analytics^bs.com^pana^bs.demdex^bs.net^pcollect^bs.tealiumiq^bs.com^pallnipponairways^bs.australia-3^bs.evergage^bs.com^pallnipponairwayscolt^bs.tt^bs.omtrdc^bs.net|rdnt=1|uxrgce=1|cuc=k8vpgh61|mel=100000|expw=1|dpvc=1|md=mdcc1=bAsw.PnrOutput.recordLocator,mdcc2=bAsw.PnrOutput.paymentDetailInfoList[0].fopCode,mdcc3=bAsw.PnrOutput.ticketedFlag,mdcc4=bAsw.PnrOutput.nhPnrFlag,mdcc5=bAsw.PnrOutput.itinerarySummary.departureDateYyyymmdd,mdcc6=bAsw.SearchCriteriaOutput.cffCodeInput,mdcc7=a#main ^rb div.section.sectionNoFlow ^rb table ^rb tbody ^rb tr ^rb td.flightNumber.lastCol ^rb span ^rb span:nth-child(2),mdcc8=cunique|lastModification=1750184416409|tp=500,50,0|agentUri=/international_asw/dynatrace/ruxitagentjs_ICANVfhqrux_10273230920145353.js"></script><link rel="shortcut icon" href="https://www.ana.co.jp/favicon.ico" />
		<title>お客様情報入力 | ANA</title>
		<noscript><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/css/asw_noscript_pc.css?99001fc" />
		</noscript>

		<!--  [ Adobe Target ] -->
		<script type="text/javascript" src="https://www.ana.co.jp/behavior/adobetarget/target_intbe.js"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/jquery-1.8.3.min.js?717d3c0"></script><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/ja/css/asw_initialize.css?99001fc" /><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/css/asw_screen_pc.css?99001fc" /><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/css/asw_common_pc.css?99001fc" /><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/css/asw_forminput_pc.css?99001fc" /><script type="text/javascript">
		var Asw = Asw || {};
		Asw.GlobalWord = Asw.GlobalWord || {};
		
		Asw.GlobalWord.toolTipCloseIconAltText = "閉じる";
		
		</script><script >bazadebezolkohpepadr="1117624512"</script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/akam/13/429d9996"  defer></script></head><body>
		
		<noscript>当サイトをご利用になるためには、JavaScript対応のブラウザが必要です。設定でJavaScriptを有効にしてください。有効にできない場合はANA電話窓口へお問い合わせください。
		</noscript>
		<div id="preLoadingArea"></div>
	
	<div id="cmnHeader" class="platinum">
		<!--===== cmnHeader =====-->
		<div class="headerWrapper">
			<div class="header">
				<p class="logo"><a href="https://www.ana.co.jp/asw/AMCTopServlet?type=a" tabindex="-1" aria-hidden="true"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/ana_logo_navy.png?717d3c0" alt="ANA Inspiration of JAPAN。リンク先はマイレージクラブトップページです。" height="28" width="274" /></a>
				</p>
					<ul class="headerMenu">
						<li><a href="https://www.ana.co.jp/asw/AMCTopServlet?type=a">ANAマイレージクラブトップページ</a>
						</li>
						<li><a href="https://www.ana.co.jp/other/int/meta/0005.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">お問い合わせ<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
						</li>
							<li class="btnLogoutArea"><a href="https://aswbe-i.ana.co.jp/rei21c/international_asw/rest/logout" role="button" class="btnBase btnLogout">ログアウト</a>
							</li>
					</ul>
			</div>
		</div>
			<div class="amcUserInfoWrapper">
				<div class="amcUserInfo">
					<div class="userNameArea">
						<table>
							<tr>	
									<td class="statusIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_status_05.png?717d3c0" alt="プラチナサービスメンバー" height="46" width="60" />
									</td>
								<td class="userName">佐藤 直樹 <span>様</span>
								</td>
							</tr>					
						</table>
					</div>
						<div class="balance">
								<dl class="upgradePointBalance">
									<dt>アップグレードポイント残高
									</dt>
									<dd>
											<dl>
												<dt>(2025年度)
												</dt>
												<dd>
													<em>20
													</em>ポイント
												</dd>
											</dl>
									</dd>
								</dl>
							<dl class="mileBalance">
								<dt>マイル口座残高
								</dt>
								<dd><em>75,663</em><span class="currencyCode">マイル</span>
								</dd>
							</dl>
						</div>
				</div>
					<p class="nowTime">6月19日18時30分現在
					</p>
			</div>
		<!--===== /cmnHeader =====-->
	</div>
		<div id="sessionKeeperContainer" class="sessionKeepingContainer"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/bg_modalWindow.png?717d3c0" alt="" height="0" width="0" />
			<div id="extendSessionExpiredDialog" class="modalExpire modalSmall modalContainer" role="dialog" aria-labelledby="extendSessionExpiredDialogTitle" aria-hidden="true">
<form id="sessionKeeperContainer:j_idt183" name="sessionKeeperContainer:j_idt183" method="post" action="https://aswbe-i.ana.co.jp/rei21c/international_asw/pages/award/reservation/award_mandatory_passenger_information_input.xhtml?aswcid=1&amp;rand=20250619183042NEWHhoOyXT" autocomplete="off" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="sessionKeeperContainer:j_idt183" value="sessionKeeperContainer:j_idt183" />
<input type="hidden" name="sessionKeeperContainer:j_idt183_operationTicket" value="dljdmx+ab0f014be734f4f8c1a5e1eea3f40fdc~846i5TifbQ8HQb6XX6JSk84kfblfG45aPX4xFSm8!1750325432781.aere-xml-controller-67d4778877-cpqbk" /><input type="hidden" name="sessionKeeperContainer:j_idt183_cmnPageTicket" value="4" />
					<h1 id="extendSessionExpiredDialogTitle" class="dialogTitle">まもなくセッションが終了します</h1>
					<div id="extendSessionExpiredDialogMessage" class="dialogMessage" tabindex="-1">このままお手続きを進める場合は、［延長］ボタンを押してください。</div>
					<p class="modalButton"><input id="sessionKeeperContainer:cmnSessionKeepingButton" type="submit" name="sessionKeeperContainer:cmnSessionKeepingButton" value="延長" class="btnBase btnModal btnMainStream" onclick="mojarra.ab(this,event,'action',0,0,{'onevent':function(data) {Asw.doCommonAjaxCallback(data,false, event); Asw.SessionKeeper.doSessionKeep(data);},'onerror':function(data) {Asw.doCommonAjaxErrorCallback(data); }});return false" />
					</p><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:0" value="Vwm2vrgoTmLh7EseqQdTTK7Pj5rW8dZqupsfc84EEyW+u+zCb05R4bHsfQtoVMUMxKJUrtuFGD2+tPUgmR8angFuCuBovmrfWQaUwX3YKQi+oLCsO4nvNiwTZx2j6j2A0MZo4SkRDuF2x9O7hdmzFNUXLcr15OeDVhH1sIeDf6QKlLXgcbfB0mWb4u1U6ZMfQmNaIhtP+ZuwG8nIMGnQiG0vba0miXLGS6F2YnPMksezhNO2dq5afmm4ITsfO7xLiB0MyY7qrW98MhVZY4oEZiwc/aNHkkahe8/XJnlQ8lXv7N1bE5T34oGkkQTYjK6M2gh3/hXacJkoyeTMYGspSpCSwsB5PELleRYMhsxgLE6VcU2SS85Qg898vpMJewL7FyTQRYobmBmHVo9dlnfqijpNnJV8xeZkDYBGWeLMAy7LF4j3PPkC4jSkG7CVUPDg0upLe/+As4g+58D442NxigKHLLsYoDK7J4jeVHwjtgdQiytLv9as3dG42+VpTVGB8LKGW8eSf+HcA12zQFs30QpGtdQw92pzHog57KGrZJkL+CbQzh2P5gtJy8uOK0XicZhrDmOymhvbj2e/gPp+ueR7oNd+ApXI1Tuf+m/gRGP64aJMWwRm82pKZMHXmfORyhCtKF8X2iDzcb5RjxiTQomFtMQhI0R2XPqYBB/nv+nxaP0Tulgs8u/MPddrvmhMpegxhAq6QalbIU4xKXHjSBLEdi2s6A7u/cRYPhj4mWYGpEAs2q+RvmT0i5rb+Oe4jvM7zfMlpU1FLrShNyOXPipAYhA+QtvmmUQYKbWZ50MlMqsLFHOQJtG8lykQLkGt3NBIqOHXq96fqJM6ONcd5CYkCboCc/d3py2w2JbwWvyJ8ivbKAqfJ3krFLUD7Q6PEYpq1uFBa1S4/c9c8U9UAY7XIsXRb2CNaJx9aN4ioBxoeltsy/95Nx/WAw1zvuaeoh0VCYX5kpGNnOEPP/6JNIJvUkEviGs9eMaSTas+tZkXGMmrgz+pFUIllYUn+Ot86uV8aurP1Efj2ukmsfh4Ee76owkOmc/HihUy0CONs3Fyacdl9c7Sbk927WdV7Qi+0ugmMfv7Rj+1uDzIRAYDpc/j1HR9zbaA9fbEOdpWQ7cS0t48AopZPyt5YNQbvX8oSoDI9A4VRqDOyYXzAlkofiHascKIa+Lix4Z404BGU5o8dzPd7ijX0OHwb/2S2225MGbX5b6yjtXNUew4rA8rp+uCFacpXVzobp8FxW1ytFKr92YKXW1rnlF9Q0nFoZFM/ZgPINQqBm1er7A5kS5z5MJqNVNxd97nOzg/HMP+jOvorkWVaym4zvQJ1msjzHHnktYG1Esj76bazj0Ttt2nfshezDHrm7MEsLF/xXWvzFcq7IzaAKUM3Nu5bs+ItkA3c3I/iZa8eeuAgAmaXSx5ozL/lTxvPItS+9sidWOaQaiQHku6mOUIvofdiMRjz45X/pc4zXOTZvAYXLfmiiupQkjxCq/eez2gEsIEpAQu95eMh766fWt7tOfRb75bSnf8q6tIzBYmvfDE8Zh3x+p1oyj+GgYtQEsZYSzFnZJRfg9YN8qPldP7b9uPia47wSU/COMHwIzzggGMjpV1xMZyEBdR4tX79grUeQDkE4RFaQsdFDIoY+IzSPzn3U4Y4n4ZzZhiibIwN582hf1K0jV3dXjMKDzjQ8Zy5pLSyZh1zqnQSPblLeudPCl4pzBabiWI1s9SBLelDThiSOjytyEHGbz6MKZmfxBb55W8x6qo4Rhmq+u/bze345fTas32ogtDsuxdGIRZywN7mYtRf0u23jBR2EstDXErB87WiWTRKdRcW+Rgsh9W5M+gNGn0miKjw3OAbYgTUXzJwGYRj1e+mKbPwJehEKaAvvpEq1WNlEPfTty59lWtGbOIKZSfQQ4woXoHK7aBlvUHNr+0F+WRu5VVqNjv9VEg3WD3DHNLC51SiusiuL9Sbl0PLXJBwljayplp6m2+mJznF+Kqc7iICGd5qdpKhDhFVvCIgDuya07SuFsyqbf/eJ/VP4ry0nZvEwiYo4ZnhrHX9dhVJ6S4iUG1Q6PiSfN1oglI1Apbhdpna2nySbSsaW27u6d+erxTKNIJRO/cpljiG2sBm0A8nUE6R9D4VogsZ9umJyhmMgcNsRcPW8rG/HpDHYIpPwDeByPMfOKnVF3VLtiZ5w5Jg84bEdyejyh7u5Pa+18KgtZEkJxk3xUdBSB42pzfHSYcifYiHusUrGGm2e2ofvoprYVIiIBBb7N22efuIHkZm1v3egEzjUBtE8h6VHmyVGttIfXsWYIrs/9fJfKUlxOSUHS1IyWRQR9//miv9BMBFAArx86KyVTck1tsF999eu/L9x2RgF59Wir02R0Xv8Ctpqn4myY/fO0pBmoafTbDwVk+n+yxFcV3oxQ3+cgr0xSI64O0AVNLOdJ2r2Gauys3NoLhCI88eOBfPHqCb4tcxscpkU7byxtADSny6q5aw09f/SuOjQHp74Hho1UKEpH1/uqS65DR7jUfCTvGJ/PrWJwo3bOr/u0sRZBjbqMYZLzJhC53nq0tu3/6fkWG1+WmyN0ZASI7ieG2zBbiKlF1w4dNCeINSfMyvtPiwr/q8pjN0lXstdM7J/NhF81tCFnR2jfAuWCw9FH2ryO0hdWrnjcoA9SiMEzfb7x5lcPGhwCiOTroKlpzFOygeRW/V6zq3UriyTS/bbMSBP5DcQ+H0xkJUJ7/A0KPfQAeX6hXvodfDk6NGolcPc7GHeQaaGAVkr6VHVlaOz9SBEuEM3Skkc58oylYVR7SoIyms0XTZh/YR7ritLK38nwL6hQGFXc3D6YYSorRjLokjgOLojgVwFW5P93j40d4NcMGdj8Vwu6BsVwyDJHdQPJVtOBIMYs556ayBb9UMi8SltNir3QjAdfRloBYMLUE7fjye/kZyYL3AI8RFE8aoM+oobep7Kwj0A2j/+rQt6/vzV7vlgxC/PbVztAWs2+PR8cnhkvv2L1MOt3+N3wM8TpO/NNIbIlYGKxpVPin+A2Jt1rFy+2kilwzI0VP2gnkefesBKak0H8yQP9C3zvDr0H6tEocFo+2jul9/2xyRGUXV/QhthYjEifxxpSQgsTPLOcYYcfnT0SlPZonbsCAjsvnveS/rc3VzZrbgoWMhXW/+axgMbOerH4/O/8XrlhpDoufenAb/DJni1oqde5TeQ0ahPwwXTx+oaceW/HQkceeSe7ML6sSwxTtVif1W1R+jprvX7W/yn+mreyVZU9UzmVAjtsoSjD6vYDdausTrclw7ufp8+yKYtdU1kRiMiUyK+nxYjSLczvcaeEdQzozPFAm7DE7ge9E6K25TvaBQPLPPRzqGuL/bk+i20Aa2+ZMLybSPo5K60Ek+jTyZNelD3ZKQIW0GfW0MbxkHXIFuF4kd0bAI/xCaO6xuCEqx8ryUetr4QMk6o8IAlTPVa3bgIqu5u1kx2MCt9ELUueoejpGwnbC0CozpDe3OtYre3D0how2m+mtx9cvP6ul3HU7KlnmrMs58U+u7VQiX8AFo34HTMawRYWceH6FpmqVJRJdqBpMNyAinQDFytbUvScZDLkeBHPZpGKGk60ZhP5t6c7+Hf9LbZayjYgO7l7IG6FxqtpUS9V2yufidJe28LPBiw67ZCP/lv0V7/KSQJa4xfJWpy4gro4lZQAQkFJnz5OZNZtq1XRnrhj+jCGX6Do3DSBUW/GIkMwpj94y1vUbxB5Uxk/NBd2IY77VL4ImyMRC3xEmHJyq7DU8eCYuGfDEiI/3U9A3dXM2Gxej27/2Rk13yw51gFlpi95d2pVBCc8H0lsPE94PICa81ODhAG3Ecj06+JU4N0zvV4sKMdrho9Zuw3TSkvdW8ggBzU7zJH+RjBCWiw8kzmOMQjfEGZEuormWjmVD5qfpL2ByHADGE7JETRtoYvm3lDy5AlO1harGF6H34RWCoWbynugzhfNlGsGl2RvvxNxPIsOlt1bPHRDh6A3l0kmcawzQzSK6MzHZe8Ex4gg7N5Gfzh5rvlFZTPhgWrZZ+z8L/RfBXJ2GsfYgrG2x3GHF4sIZrWdto3EAJjaRZuMaTvuRLGs/Z/sxbVJFjZwf2bpm7T7mTlKwAwEHWF2qntt109/d93DxVVQF/MrepdHRGWHhTdbMnY1XHmBYi3XJ033SXXj//LFFjql9+vH9BJdUCUpjGECsbgdqED//5l1OzywtaY6olk0DLd3n8yLirrtwQCI7qJKBgoYlq+OkeSNg2f0H7/xX2O2GGNhoKWTsfQGFzgcTdF4plKyH4Cjtev9AffQozE+UxEiZz75RBXuTs0LKKmiwjND83SFV4cio+U2SbGdNoxGfDBxkI35HokFDQcGSCkNTzJ5tRoSHAurEqQn8qTQfKIN2R1t3qqB+PbIy7UzD2Nft7GF/cyaVMkOJW0Bi/stouJ/U0/2XC/Y9wjyd+MiN0oo1aMrNpNmhbC8RI2V5KAMNIGYmrPFnFiq22BaGa5i2W6+XrgJxOhhcbfe4FXToc8OtimPPnFiAFB2wQgibYyjNnJ08UDs641MRJm2xl3+W8wO9SPzP3x60FCvHiCqAWU5FPsGcO2oTF0T0vAswtIldWvl3mrb7efO+GDU/Yq0pJQmwpU/+tSSLKFdxUJa4XIBGBBOATmqcbUMCbbGy1l1xg9LNAAwR7WZOToXNRDJgRnsCU2NBs9+5pqhWrQAxyqYeYHDIiM22MfDkAb6q6gd1x3aNSU+KliwGUhsRjD13LwpDmBBi2TPyqUytDALL6LP0G6oiJhpp070kFSgeBj39+Ny96w9Gy0ydCcVe5zUtmNUxsM0EsGPsrWpuUs3lfzTMTkHqUsgzVcRqNAdDj7Ez4YdVr0ecY8isxUNnt6q/5eFAVO5qAJMcBmnRne1JAPTTcr//PWo8HN4EWz8Fsue6HsP6/17KBtGcniftoh7Rv6ip8w5bHb/3A3kzr/wKpHmG6SxLSdou3NRzR0lDrF1e4uq86kZ/b0ET0aKDaXOo40YCfT+umJ7nB2qJa0o1E9LSWsrHG7r7/RzKywqoVfqy1ri92Ovz+2hajzeb1S3kL56mseir8r+ZwlSFjt576Tgug3Iv7Oa/G38XNm7EN53hDKRdtrK7Q+1bTZ3BnmpJxDJME8LIaeSDwjsUY/SnvTfu9EK7yL7r5ydt/J+zhMz8NEoE2wr6nvsC3wAl+1sMGGer9AEhnj4q5XBl1nvyKIpIxRmpTZiXpXkbX/LaeJ6l1jdmxxh2I6csHKsXoyRN4Zz6mlO9eT/scayNo8ei10lPDa/jJRXKoZEo4cYDCz+H3gGHcZKY0WTaQ2QUEp7QuxW9qvDUsq23bZJLWjVqt13XI79CZXS4KTo2cLWu1Zm7BZwh+NStU+2UPOv+mfyLg88Ig5QtSDZsYJrwLSvlis7anRnJIDTciXGFcQ298LKjgRGZ9/o7bzeccaw+E2ncjMs0rb0Xq2k0tT9DCmYhYReLh0/ImwsCdyaJxwt/i+YC5tF5OC3T/js7EXw7cvtN9PuUgKfyFOU8Ux9siSCIGpmWcrRkZ5xBEIEwf0P3I1r/kfewRYjK90bzYSAllct9ukn2R7DcZ5adR5tsCpngmAnAXwfwngp3V4TZ6SnqGFZ3ZX64fCokbLiQ3bUIBPk8e/Jsl9o/REcTElZTmLhrS3NdSwnJqQVEfnNO6mi6Utxvr3Xj2/jxinAQBLEtUu1+ybDa7WYcba71tXKPtfgAPCxOgZpcg3zHW2kylDmOZhgFV6YEi07T+o5GUqIvZZxewA8O+yvRjSo0sjU0o9n8RkUS5vFKosfYMzWM2wIlNE5qXwF+zgu8iZe3ow/8tHsCRrNTgxbP2Aqw3aDaPactltSAxf8/NaNR1yvhZlY8UpD/2GTetBve/VtvtwUsq1wAfugNvSp2zTLC+JQkdRvQ7DKe45xTRyPuOwgjDW78cfOcFnaFbulfwxEK9+c958tKxlI8b96nNDvgOWBzKVCCmMQTZOKs8EQRt4qWvOXoeukRkVW1yaTX04FDkATZL1cznfnYI/aNgeR+2Utfz1uTGKgCXWlzjre5cOoBywd5mbbPDjxBWuAfX3tNmYmqZ0ExSIP9atyNUkBxXGNVqHXiABzULitpqzvlxnyj36b9bOWk6rJsPmxqovCAjh6f9k2pfcuwDZYedOMfRmB1Y7tzzz/JRo0Q+4lG+GP1zANwRgOI8A7ENV5ZpBDumd1POsiZV7aWqL7d4byuxR1H/HX6BpV2ePjjhm4sS2cwyDFaKKcHWqZpkAcDT7nxokjnFNHy467u+r3GvLmLBiqcb8yBWHkXqY1WU1cT3WTT5DnoxXV2EMU5UwfHRsm7vq21zw3kObMl5/nap97ZGuQbDXsJiW3BFoXM/1PKtGoDa8d6yw4n7kh9TnbG76blecxYLa7OuK2ZvHuUdILK8UjppHMc49YVlpryF0hNXKTsBe0JizOG//cj/RsUJquiBFnf2I+jYmalw++tcTp8QvbDNNeys2VMmxSkbTdyoVunr5VL1vdfAZ2uL+5K2HTE38TFQ4TVT+Qp67JuNhipj55QpwCp0fuJxqdsyGC0voe7iA6zwqmrg1RNl9BSBJvJ3Lahw8Q8VjsLyuuLSNOMyoBuYgBrVwRYctUb53owson7kS+jUHf7wN1UjV6VS8leeG6D8f18Knoaf7vFeNhLofryg/UOOz108I494xDo253rTH3qKU3SneIz+10IHZGym4076TTUxODC5siuh8ZYpjWu/egayXOnxEJVbYRHVHEpxlnfam+NnLeLrH9orVKd66bgqCeEr3tzyFCqyTyOosbJzxJUReEGvOyWNabPh6bxWzd8RMDds59HOtuoDRIscnyZEXo7xmXJyvIy4AxxyuKTnhDccmmnInTkOraB3cs/BHiuNNpLKyUU4lqnHzlykTvxZpi3d+5Ot1Q2klmYrLjuzLuXD/IgtKnLZkcFK4D1GV8s5cmr70nOHhJ8h9U+5tWSEcZ3xPtSatFz1zyI7xcpxM80PGV14KKGeqgInPp+Lc4QPdHvqvZKPUd4RyZ+R4YxA7xeDjZ+jBgl/RscpQQi+oSvzwgR8VkOWDoapMMnEamN0j4lBR8hGbP09ZA0MFpzhw44OT5Y9mtOWFxr9WwFDuEibgpTIUHwaJeUlDAt74yigSkD+IX2PODZ2midMVi289SC6x5uj3Gn6/goLXIoM/tdu09zQFV3ECnu7bCTUsFAm5JBkkUeRikqfaZ1Ngal22cgR59jPfiN3Yzw9l3lRQ4AMPmTaCoab3C0Gft6SzsJ+hv/GO9r2e1OC/PiVqR4dnKHBtqT38nLyBzyciO8EGVf0iDbxQOJKXmbLduHnCywhR3hplaHteV3M6kshQPaGHQhkqijLMWddFirW6n9eqbVlfyrX60/k9UeGFFxLXx24Jyc/qdKBjjcRRvlfDwGFb6jaQD4/dCa67nFVf2rP6WI7URQeplySbPnwyVr1a+U1dKXDsmJ6xn2Wq7sF6TArrrkTqmMvlFBwOjdoLc/KTDTcUU+zUbkr7RcP4kTmm/VBr5lOM0UWt+scy8ErcQU2kuKTGJ1RTW5m0JnBTYOGOcXewVZSWlScIRyhsPnpzvbUffCnbJRCAFLqotgaSu0AvsK//XPpxytt1Q0yqGsFsg8+RjXC87XYvpw/QALHSkJQzjm40sH3g709LLnCSnyGFj/TuaW9IZgzM5Kb0eC8r2SsTIQmig86mKrDOli1deNYPACiyyCFmbUOToG50pTx5Yow0trfQLD5+kY7ioO6m1MgPW3283apU8ro0A7CULShuVev+UeUEXHobqtKwOBV8wi1PKGyv7skrSlajZIRE59fBCsGjbqeAuv4JuqGtSRCYJzjPYN5Bk5oOfv6O0eWfO4lBSSd4GCvJRMTuzmLIFqUFhH1ZDibG3Pts1h591LZ3b2gZcHHaaQhMpE90idUBSOlDpUWn8GaHKIJI11Kn6fgc8QFeqU9DCRjIUnjbiMlJ+tL7E40vFTmXQme5zYNP8vgdW6E6hGj63f8tmzCndLwfwr0gdrBHFWilStFhtmPlGZZuvbLIWl6erytQIgdiNrEzKHl8hBIAdYuDv7nMLMmda1QWURWSB6yUSrWlWj8ngCiIMlucZFhn9Nymmr9Y047LpjntyWiaInx6M3/uSHCSvEhRZlx7FxVnXDPKWlsOfJieZU0d2Pt/qQOWwHISuTSeYUM63jOxDa4qlrL3jUCSEYJN9rveZ8yJArAovjG20gpeJRrt3xsVSrv+e6kiDgsXu+zj01V95ZPPoNdURUodBclW9jCzHjjtyvvDGVDFdCv9upZzW3gZqtqyNCOuD0oM+OqvbTdT7PGuQpTQQdCDuPK4gt2gz1vRKy" autocomplete="off" />
</form>
			</div>
			<div id="showSessionExpiredDialog" class="modalExpire modalSmall modalContainer" role="dialog" aria-labelledby="showSessionExpiredDialogTitle" aria-hidden="true">
				<h1 id="showSessionExpiredDialogTitle" class="dialogTitle">セッションが終了しました</h1>
				<div id="showSessionExpiredDialogMessage" class="dialogMessage" tabindex="-1">一定時間操作されなかったため、お手続きを進めることができません。お手数ですが、再度トップページからお手続きください。</div>
				<p class="modalButton">
					<input type="submit" value="閉じる" class="btnBase btnModal btnMainStream" onclick="Asw.SessionKeeper.closeTimeoutDialog(); return false;" />
				</p>
			</div>
		</div>
		<!--===== dialogMessages =====--><div id="cmnModalMessages"></div>
		<div id="prebookConfirmDialog" class="modalContainer modalConfirm modalSmall " aria-hidden="true" role="dialog">
<form id="prebookConfirmDialog:j_idt403" name="prebookConfirmDialog:j_idt403" method="post" action="https://aswbe-i.ana.co.jp/rei21c/international_asw/pages/award/reservation/award_mandatory_passenger_information_input.xhtml?aswcid=1&amp;rand=20250619183042NEWHhoOyXT" autocomplete="off" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="prebookConfirmDialog:j_idt403" value="prebookConfirmDialog:j_idt403" />
<input type="hidden" name="prebookConfirmDialog:j_idt403_operationTicket" value="dljdmx+ab0f014be734f4f8c1a5e1eea3f40fdc~846i5TifbQ8HQb6XX6JSk84kfblfG45aPX4xFSm8!1750325432781.aere-xml-controller-67d4778877-cpqbk" /><input type="hidden" name="prebookConfirmDialog:j_idt403_cmnPageTicket" value="4" /><div class="dialogMessage" tabindex="0"><p>この後、名・姓の修正や変更ができません。</p><p>「搭乗者選択」欄の氏名が「名」「姓」の順番で表示されていることを再確認ください。</p><p>例）<strong class="highlight">空野 太郎</strong> 様 → MR <strong class="highlight">TARO SORANO</strong></p><p>保安上の理由により、航空券とパスポートのお名前が同じ表記でない場合はご搭乗いただくことができませんので必ず再度ご確認ください。</p></div>
					
					<ul class="modalBtnHorizontal">
							
							<li><input type="submit" name="prebookConfirmDialog:j_idt419" value="戻る" aria-controls="prebookConfirmDialog" class="btnBase btnModal btnCancel" onclick="Asw.Dialog.getInstance('prebookConfirmDialog').close(event, Asw.DialogSequencer.isActiveSequencerNotDisplayedLastDialog());  return false;" />
							</li>
						
						<li class="btnArrowNext"><input type="submit" name="prebookConfirmDialog:j_idt422" value="OK" aria-controls="prebookConfirmDialog" class="btnBase btnModal btnMainStream " onclick="Asw.Dialog.getInstance('prebookConfirmDialog').callOpener('NORMAL', event); return false;" />
						</li>
					</ul><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:1" value="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" autocomplete="off" />
</form>
		</div>
		<!--===== /dialogMessages =====--><div id="cmnContainer">
		<!--=========== .cmnContainer ============--><div id="cmnEmergencyNotice"></div><div id="cmnTitle" class="hgroup ">
		<h1 class="visuallyHidden">お客様情報入力</h1></div>

		<div id="cmnWrapper">
		<!--===== cmnWrapper =====-->
		<ol class="mainFlow" aria-label="完了までの手順、">
			<li><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_flow_01.png?717d3c0" alt="1" height="20" width="28" />フライト検索</li><!--
			--><li><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_flow_02_on.png?717d3c0" alt="2" height="20" width="28" /><span class="visuallyHidden">現在のステップ、</span>お客様情報入力</li><!--
			--><li><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_flow_03.png?717d3c0" alt="3" height="20" width="28" />お支払い情報</li><!--
			--><li class="flowLast"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_flow4_04.png?717d3c0" alt="4" height="20" width="28" />完了</li>
		</ol>

	<div id="main">
	<!--=========== main =============-->
		<ul class="iconExample">
				<li class="requiredIconExample">
					<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="" height="7" width="7" /></span>必須項目です	
				</li>
		</ul>
<form id="j_idt526" name="j_idt526" method="post" action="https://aswbe-i.ana.co.jp/rei21c/international_asw/pages/award/reservation/award_mandatory_passenger_information_input.xhtml?aswcid=1&amp;rand=20250619183042NEWHhoOyXT" autocomplete="off" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="j_idt526" value="j_idt526" />
<input type="hidden" name="j_idt526_operationTicket" value="dljdmx+ab0f014be734f4f8c1a5e1eea3f40fdc~846i5TifbQ8HQb6XX6JSk84kfblfG45aPX4xFSm8!1750325432781.aere-xml-controller-67d4778877-cpqbk" /><input type="hidden" name="j_idt526_cmnPageTicket" value="4" />

<div class="section">
	<h2>搭乗者選択<a href="https://www.ana.co.jp/other/int/meta/0086.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="特典のご利用に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a></h2>
	<table class="passengerInfo">
		<thead>
			<tr>
				
				<th class="name">氏名</th>
				
				<th class="dateOfBirthWide" id="dateOfBirthHeader">生年月日</th>
				
				<th class="gender lastCol" id="genderHeader">性別<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span></th>
				
			</tr>
		</thead>
		<tbody>
				
				<tr>
					<th class="parent_01" id="parentHeader01">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:0:passenger" type="checkbox" name="passengers:0:passenger" checked="checked" data-number="1" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:0:passenger">NAOKI SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader01">1963年06月05日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader01">				
							<span class="formWrapper">
							<label for="passengers:0:gender" class="visuallyHidden">NAOKI SATOの性別</label><select id="passengers:0:gender" name="passengers:0:gender" size="1">	<option value="">選択してください</option>
	<option value="M" selected="selected">男性</option>
	<option value="F">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_02" id="parentHeader02">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:1:passenger" type="checkbox" name="passengers:1:passenger" data-number="2" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:1:passenger">REO SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader02">1995年05月18日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader02">				
							<span class="formWrapper">
							<label for="passengers:1:gender" class="visuallyHidden">REO SATOの性別</label><select id="passengers:1:gender" name="passengers:1:gender" size="1">	<option value="">選択してください</option>
	<option value="M" selected="selected">男性</option>
	<option value="F">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_03" id="parentHeader03">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:2:passenger" type="checkbox" name="passengers:2:passenger" data-number="3" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:2:passenger">ERI SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader03">1963年08月14日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader03">				
							<span class="formWrapper">
							<label for="passengers:2:gender" class="visuallyHidden">ERI SATOの性別</label><select id="passengers:2:gender" name="passengers:2:gender" size="1">	<option value="">選択してください</option>
	<option value="M">男性</option>
	<option value="F" selected="selected">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_04" id="parentHeader04">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:3:passenger" type="checkbox" name="passengers:3:passenger" data-number="4" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:3:passenger">TAISHI SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader04">1992年12月03日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader04">				
							<span class="formWrapper">
							<label for="passengers:3:gender" class="visuallyHidden">TAISHI SATOの性別</label><select id="passengers:3:gender" name="passengers:3:gender" size="1">	<option value="">選択してください</option>
	<option value="M" selected="selected">男性</option>
	<option value="F">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_05" id="parentHeader05">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:4:passenger" type="checkbox" name="passengers:4:passenger" data-number="5" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:4:passenger">HIROMI FUJIMOTO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader05">1961年11月23日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader05">				
							<span class="formWrapper">
							<label for="passengers:4:gender" class="visuallyHidden">HIROMI FUJIMOTOの性別</label><select id="passengers:4:gender" name="passengers:4:gender" size="1">	<option value="">選択してください</option>
	<option value="M">男性</option>
	<option value="F" selected="selected">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
		</tbody>
	</table>
</div>
		<div class="section">
			<h2 class="midstream">連絡先情報
			</h2>
			<h3>代表者
			</h3>
	
	
	
	<div class="formInput">
		<fieldset>
			<legend><label>代表者</label></legend>
			<dl id="contactEmailAddressInfo">
				<dt class="hasRequiredIcon">メールアドレス<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span><a href="https://www.ana.co.jp/other/int/meta/0057.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="メールの宛先と内容に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a>
				</dt>
				<dd>
					<ul class="verticalList">
						<li class="nonMarginTop"><label for="representativeEmail" class="visuallyHidden">代表者メールアドレス</label><input id="representativeEmail" type="text" name="representativeEmail" value="<EMAIL>" class="widthLarge" placeholder="例：<EMAIL>" />
						</li>
						<li>
							<span class="supplementTxt" id="confirmMailAddressForRepresentative">確認のため再度ご入力ください。
							</span><label for="confirmrepresentativeEmail" class="visuallyHidden">確認用メールアドレス</label><input id="confirmrepresentativeEmail" type="text" name="confirmrepresentativeEmail" value="<EMAIL>" class="widthLarge jsNotPasteInput" placeholder="例：<EMAIL>" aria-describedby="confirmMailAddressForRepresentative" />
						</li>
					</ul>
				</dd>
					<dd style="display:none;"><input id="updateAlwaysInfo" type="checkbox" name="updateAlwaysInfo" /><label for="updateAlwaysInfo">ログイン中の会員情報へこのメールアドレスを登録</label>
					</dd>
			</dl>
			<dl>
				<dt>電話番号<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span>
				</dt>
				<dd class="js-countryWrap">
					<ul>
						<li><label for="representativeTelType" class="visuallyHidden">電話番号種別</label><select id="representativeTelType" name="representativeTelType" class="basicWidth js-typeSelect" size="1" onchange="changeTelType(this,'representativeTel')">	<option value="M1" selected="selected">携帯</option>
	<option value="B1">会社</option>
	<option value="H1">自宅</option>
</select>
						</li>
						<li><label for="representativeTelCountry" class="visuallyHidden">電話番号の国/地区選択</label><select id="representativeTelCountry" name="representativeTelCountry" class="js-countrySelect" size="1">	<option value="" data-sms-possible="true" data-country-code="---">選択してください</option>
	<option value="JP" selected="selected" data-sms-possible="true">日本</option>
	<option value="-" data-sms-possible="true" data-country-code="---">-----------</option>
	<option value="IS" data-sms-possible="true">アイスランド</option>
	<option value="IE" data-sms-possible="true">アイルランド</option>
	<option value="AZ" data-sms-possible="true">アゼルバイジャン</option>
	<option value="AF" data-sms-possible="true">アフガニスタン</option>
	<option value="US" data-sms-possible="true">アメリカ合衆国/カナダ</option>
	<option value="AE" data-sms-possible="true">アラブ首長国連邦</option>
	<option value="DZ" data-sms-possible="true">アルジェリア</option>
	<option value="AR" data-sms-possible="true">アルゼンチン</option>
	<option value="AW" data-sms-possible="true">アルバ</option>
	<option value="AL" data-sms-possible="true">アルバニア</option>
	<option value="AM" data-sms-possible="true">アルメニア</option>
	<option value="AO" data-sms-possible="true">アンゴラ</option>
	<option value="AG" data-sms-possible="true">アンティグア・バーブーダ</option>
	<option value="AD" data-sms-possible="true">アンドラ</option>
	<option value="YE" data-sms-possible="true">イエメン</option>
	<option value="GB" data-sms-possible="true">イギリス</option>
	<option value="IL" data-sms-possible="true">イスラエル</option>
	<option value="IT" data-sms-possible="true">イタリア</option>
	<option value="IQ" data-sms-possible="true">イラク</option>
	<option value="IR" data-sms-possible="true">イラン</option>
	<option value="IN" data-sms-possible="true">インド</option>
	<option value="ID" data-sms-possible="true">インドネシア</option>
	<option value="UG" data-sms-possible="true">ウガンダ</option>
	<option value="UA" data-sms-possible="true">ウクライナ</option>
	<option value="UZ" data-sms-possible="true">ウズベキスタン</option>
	<option value="UY" data-sms-possible="true">ウルグアイ</option>
	<option value="EC" data-sms-possible="true">エクアドル</option>
	<option value="EG" data-sms-possible="true">エジプト</option>
	<option value="EE" data-sms-possible="true">エストニア</option>
	<option value="SZ" data-sms-possible="true">エスワティニ</option>
	<option value="ET" data-sms-possible="true">エチオピア</option>
	<option value="ER" data-sms-possible="true">エリトリア</option>
	<option value="SV" data-sms-possible="true">エルサルバドル</option>
	<option value="AU" data-sms-possible="true">オーストラリア</option>
	<option value="AT" data-sms-possible="true">オーストリア</option>
	<option value="OM" data-sms-possible="true">オマーン</option>
	<option value="NL" data-sms-possible="true">オランダ</option>
	<option value="GH" data-sms-possible="true">ガーナ</option>
	<option value="CV" data-sms-possible="true">カーボベルデ</option>
	<option value="GY" data-sms-possible="true">ガイアナ</option>
	<option value="KZ" data-sms-possible="true">カザフスタン</option>
	<option value="QA" data-sms-possible="true">カタール</option>
	<option value="CA" data-sms-possible="true">カナダ/アメリカ合衆国</option>
	<option value="GA" data-sms-possible="true">ガボン</option>
	<option value="CM" data-sms-possible="true">カメルーン</option>
	<option value="KR" data-sms-possible="true">韓国</option>
	<option value="GM" data-sms-possible="true">ガンビア</option>
	<option value="KH" data-sms-possible="true">カンボジア</option>
	<option value="KP" data-sms-possible="true">北朝鮮</option>
	<option value="MK" data-sms-possible="true">北マケドニア</option>
	<option value="GN" data-sms-possible="true">ギニア</option>
	<option value="GW" data-sms-possible="true">ギニアビサウ</option>
	<option value="CY" data-sms-possible="true">キプロス</option>
	<option value="CU" data-sms-possible="true">キューバ</option>
	<option value="GR" data-sms-possible="true">ギリシャ</option>
	<option value="KI" data-sms-possible="true">キリバス</option>
	<option value="KG" data-sms-possible="true">キルギス</option>
	<option value="GT" data-sms-possible="true">グアテマラ</option>
	<option value="GU" data-sms-possible="true">グアム</option>
	<option value="KW" data-sms-possible="true">クウェート</option>
	<option value="GD" data-sms-possible="true">グレナダ</option>
	<option value="HR" data-sms-possible="true">クロアチア</option>
	<option value="KY" data-sms-possible="true">ケイマン諸島</option>
	<option value="KE" data-sms-possible="true">ケニア</option>
	<option value="CI" data-sms-possible="true">コートジボワール</option>
	<option value="CR" data-sms-possible="true">コスタリカ</option>
	<option value="KM" data-sms-possible="true">コモロ</option>
	<option value="CO" data-sms-possible="true">コロンビア</option>
	<option value="CG" data-sms-possible="true">コンゴ共和国</option>
	<option value="CD" data-sms-possible="true">コンゴ民主共和国</option>
	<option value="SA" data-sms-possible="true">サウジアラビア</option>
	<option value="ZM" data-sms-possible="true">ザンビア</option>
	<option value="SM" data-sms-possible="true">サンマリノ</option>
	<option value="SL" data-sms-possible="true">シエラレオネ</option>
	<option value="DJ" data-sms-possible="true">ジブチ</option>
	<option value="JM" data-sms-possible="true">ジャマイカ</option>
	<option value="GE" data-sms-possible="true">ジョージア</option>
	<option value="SY" data-sms-possible="true">シリア</option>
	<option value="SG" data-sms-possible="true">シンガポール</option>
	<option value="ZW" data-sms-possible="true">ジンバブエ</option>
	<option value="CH" data-sms-possible="true">スイス</option>
	<option value="SE" data-sms-possible="true">スウェーデン</option>
	<option value="SD" data-sms-possible="true">スーダン</option>
	<option value="ES" data-sms-possible="true">スペイン</option>
	<option value="SR" data-sms-possible="true">スリナム</option>
	<option value="LK" data-sms-possible="true">スリランカ</option>
	<option value="SK" data-sms-possible="true">スロバキア</option>
	<option value="SI" data-sms-possible="true">スロベニア</option>
	<option value="SC" data-sms-possible="true">セイシェル</option>
	<option value="GQ" data-sms-possible="true">赤道ギニア</option>
	<option value="SN" data-sms-possible="true">セネガル</option>
	<option value="RS" data-sms-possible="true">セルビア</option>
	<option value="KN" data-sms-possible="true">セントクリストファー・ネイビス</option>
	<option value="LC" data-sms-possible="true">セントルシア</option>
	<option value="SB" data-sms-possible="true">ソロモン諸島</option>
	<option value="TH" data-sms-possible="true">タイ</option>
	<option value="TW" data-sms-possible="true">台湾</option>
	<option value="TJ" data-sms-possible="true">タジキスタン</option>
	<option value="TZ" data-sms-possible="true">タンザニア</option>
	<option value="CZ" data-sms-possible="true">チェコ</option>
	<option value="TD" data-sms-possible="true">チャド</option>
	<option value="CF" data-sms-possible="true">中央アフリカ共和国</option>
	<option value="CN" data-sms-possible="true">中国</option>
	<option value="TN" data-sms-possible="true">チュニジア</option>
	<option value="CL" data-sms-possible="true">チリ</option>
	<option value="TV" data-sms-possible="true">ツバル</option>
	<option value="DK" data-sms-possible="true">デンマーク</option>
	<option value="DE" data-sms-possible="true">ドイツ</option>
	<option value="TG" data-sms-possible="true">トーゴ</option>
	<option value="DO" data-sms-possible="true">ドミニカ共和国</option>
	<option value="DM" data-sms-possible="true">ドミニカ国</option>
	<option value="TT" data-sms-possible="true">トリニダード・トバゴ</option>
	<option value="TM" data-sms-possible="true">トルクメニスタン</option>
	<option value="TR" data-sms-possible="true">トルコ</option>
	<option value="TO" data-sms-possible="true">トンガ</option>
	<option value="NG" data-sms-possible="true">ナイジェリア</option>
	<option value="NR" data-sms-possible="true">ナウル</option>
	<option value="NA" data-sms-possible="true">ナミビア</option>
	<option value="NI" data-sms-possible="true">ニカラグア</option>
	<option value="NE" data-sms-possible="true">ニジェール</option>
	<option value="NZ" data-sms-possible="true">ニュージーランド</option>
	<option value="NP" data-sms-possible="true">ネパール</option>
	<option value="NO" data-sms-possible="true">ノルウェー</option>
	<option value="BH" data-sms-possible="true">バーレーン</option>
	<option value="HT" data-sms-possible="true">ハイチ</option>
	<option value="PK" data-sms-possible="true">パキスタン</option>
	<option value="VA" data-sms-possible="true">バチカン</option>
	<option value="PA" data-sms-possible="true">パナマ</option>
	<option value="VU" data-sms-possible="true">バヌアツ</option>
	<option value="BS" data-sms-possible="true">バハマ</option>
	<option value="PG" data-sms-possible="true">パプアニューギニア</option>
	<option value="BM" data-sms-possible="true">バミューダ</option>
	<option value="PW" data-sms-possible="true">パラオ</option>
	<option value="PY" data-sms-possible="true">パラグアイ</option>
	<option value="BB" data-sms-possible="true">バルバドス</option>
	<option value="PS" data-sms-possible="true">パレスチナ</option>
	<option value="HU" data-sms-possible="true">ハンガリー</option>
	<option value="BD" data-sms-possible="true">バングラデシュ</option>
	<option value="TL" data-sms-possible="true">東ティモール</option>
	<option value="FJ" data-sms-possible="true">フィジー</option>
	<option value="PH" data-sms-possible="true">フィリピン</option>
	<option value="FI" data-sms-possible="true">フィンランド</option>
	<option value="BT" data-sms-possible="true">ブータン</option>
	<option value="PR" data-sms-possible="true">プエルトリコ</option>
	<option value="BR" data-sms-possible="true">ブラジル</option>
	<option value="FR" data-sms-possible="true">フランス</option>
	<option value="PF" data-sms-possible="true">フランス領ポリネシア</option>
	<option value="BG" data-sms-possible="true">ブルガリア</option>
	<option value="BF" data-sms-possible="true">ブルキナファソ</option>
	<option value="BN" data-sms-possible="true">ブルネイ</option>
	<option value="BI" data-sms-possible="true">ブルンジ</option>
	<option value="VN" data-sms-possible="true">ベトナム</option>
	<option value="BJ" data-sms-possible="true">ベナン</option>
	<option value="VE" data-sms-possible="true">ベネズエラ</option>
	<option value="BY" data-sms-possible="true">ベラルーシ</option>
	<option value="BZ" data-sms-possible="true">ベリーズ</option>
	<option value="PE" data-sms-possible="true">ペルー</option>
	<option value="BE" data-sms-possible="true">ベルギー</option>
	<option value="PL" data-sms-possible="true">ポーランド</option>
	<option value="BA" data-sms-possible="true">ボスニア・ヘルツェゴビナ</option>
	<option value="BW" data-sms-possible="true">ボツワナ</option>
	<option value="BO" data-sms-possible="true">ボリビア</option>
	<option value="PT" data-sms-possible="true">ポルトガル</option>
	<option value="HK" data-sms-possible="true">香港</option>
	<option value="HN" data-sms-possible="true">ホンジュラス</option>
	<option value="MH" data-sms-possible="true">マーシャル諸島</option>
	<option value="MO" data-sms-possible="true">マカオ</option>
	<option value="MG" data-sms-possible="true">マダガスカル</option>
	<option value="MW" data-sms-possible="true">マラウイ</option>
	<option value="ML" data-sms-possible="true">マリ</option>
	<option value="MT" data-sms-possible="true">マルタ</option>
	<option value="MY" data-sms-possible="true">マレーシア</option>
	<option value="FM" data-sms-possible="true">ミクロネシア連邦</option>
	<option value="ZA" data-sms-possible="true">南アフリカ</option>
	<option value="MM" data-sms-possible="true">ミャンマー</option>
	<option value="MX" data-sms-possible="true">メキシコ</option>
	<option value="MU" data-sms-possible="true">モーリシャス</option>
	<option value="MR" data-sms-possible="true">モーリタニア</option>
	<option value="MZ" data-sms-possible="true">モザンビーク</option>
	<option value="MC" data-sms-possible="true">モナコ</option>
	<option value="MV" data-sms-possible="true">モルディヴ</option>
	<option value="MD" data-sms-possible="true">モルドバ</option>
	<option value="MA" data-sms-possible="true">モロッコ</option>
	<option value="MN" data-sms-possible="true">モンゴル</option>
	<option value="ME" data-sms-possible="true">モンテネグロ</option>
	<option value="JO" data-sms-possible="true">ヨルダン</option>
	<option value="LA" data-sms-possible="true">ラオス人民民主共和国</option>
	<option value="LV" data-sms-possible="true">ラトビア</option>
	<option value="LT" data-sms-possible="true">リトアニア</option>
	<option value="LY" data-sms-possible="true">リビア</option>
	<option value="LI" data-sms-possible="true">リヒテンシュタイン</option>
	<option value="LR" data-sms-possible="true">リベリア</option>
	<option value="RO" data-sms-possible="true">ルーマニア</option>
	<option value="LU" data-sms-possible="true">ルクセンブルク</option>
	<option value="RW" data-sms-possible="true">ルワンダ</option>
	<option value="LS" data-sms-possible="true">レソト</option>
	<option value="LB" data-sms-possible="true">レバノン</option>
	<option value="RU" data-sms-possible="true">ロシア</option>
</select>
						</li>
						<li class="numberInputWrap">
							<span id="numberInputInfo1" class="supplementTxt marginTop">先頭に0(ゼロ)がある場合、0(ゼロ)を抜いてください(一部、国・地域除く)。携帯電話番号の場合は、携帯電話を契約された国を選択ください。</span>
							<div class="numberRowWrap">
								<div class="block">
									
									<span class="visuallyHidden">国番号</span>
									<span class="viewInput widthMini js-countryCode"></span>
								</div>
								<div class="block"><label for="representativeTel" class="visuallyHidden">電話番号</label><input id="representativeTel" type="text" name="representativeTel" value="9042504176" class="widthMiddle" placeholder="例：9012345678" maxlength="30" aria-describedby="numberInputInfo1" />
								</div>
							</div>
						</li>
					</ul>
				</dd>
			</dl>
		</fieldset>
	</div>
	<div class="titleWrapper">
		<h3>
			<span class="text">eメール・SMSに関するご案内<a href="https://www.ana.co.jp/other/int/meta/0057.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="eメール・SMSに関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a></span>
		</h3>
	</div>
	<div class="formInput">
	<h4 class="formAreaTitle">主に搭乗・運航に関わるeメールの送付先設定（米国へ通知いたします）<a href="https://www.ana.co.jp/other/int/meta/0629.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="米国入国用eメール・SMS・電話番号に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a>
	</h4>
	<div class="paxSettingArea js-mailFormWrap onePax is-open">
			<div class="settingArea">
					<dl>
						<dt class="breakWord">NAOKI SATO
						</dt>
						<dd>
							<fieldset>
								<legend>NAOKI SATOのeメール送付先および米国到着時に提供するメールアドレス</legend>代表者と同じ
							</fieldset>
						</dd>
					</dl>
			</div>
	</div>
	<h4 class="formAreaTitle hasTopLine">主に搭乗・運航に関わるSMSの送付先および米国で受信できる電話番号の設定（米国へ通知いたします）<a href="https://www.ana.co.jp/other/int/meta/0629.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="米国入国用eメール・SMS・電話番号に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a>
	</h4>
	<div class="paxSettingArea js-smsFormWrap onePax is-open">
			<div class="settingArea">
					<dl>
						<dt class="breakWord">NAOKI SATO<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span>
						</dt>
						<dd>
							<fieldset>
								<legend>NAOKI SATOの1つ目の電話番号</legend>
									<ul class="verticalList js-formItem">
										<li><input type="radio" id="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSameAsRepresentativeSms:radioItem" name="contactsSms:0:recipientSmsEachPersonSelection:radioGroup" value="0" checked="checked" /><label for="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSameAsRepresentativeSms:radioItem">代表者電話番号と同じ</label>
										</li>
										<li><input type="radio" id="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSmsPerPassenger:radioItem" name="contactsSms:0:recipientSmsEachPersonSelection:radioGroup" value="1" /><label for="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSmsPerPassenger:radioItem">電話番号を指定</label>
											<div class="indentArea js-formImputArea">
												<span id="confirmTelNumberForRepresentative:0" class="supplementTxt marginTop">先頭に0(ゼロ)がある場合、0(ゼロ)を抜いてください(一部、国・地域除く)。携帯電話番号の場合は、携帯電話を契約された国を選択ください。</span>
												<ul class="countryNumberInput js-countryWrap">
													<li><label for="contactsSms:0:recipientSmsEachPersonSelection:passengerSmsCountry" class="visuallyHidden">電話番号の国/地区選択</label><select id="contactsSms:0:recipientSmsEachPersonSelection:passengerSmsCountry" name="contactsSms:0:recipientSmsEachPersonSelection:passengerSmsCountry" class="countrySelect js-countrySelect" size="1">	<option value="" data-country-code="---">選択してください</option>
	<option value="JP">日本</option>
	<option value="-" data-country-code="---">-----------</option>
	<option value="IE">アイルランド</option>
	<option value="AZ">アゼルバイジャン</option>
	<option value="AF">アフガニスタン</option>
	<option value="US">アメリカ合衆国/カナダ</option>
	<option value="AE">アラブ首長国連邦</option>
	<option value="DZ">アルジェリア</option>
	<option value="AR">アルゼンチン</option>
	<option value="AW">アルバ</option>
	<option value="AL">アルバニア</option>
	<option value="AM">アルメニア</option>
	<option value="AG">アンティグア・バーブーダ</option>
	<option value="AD">アンドラ</option>
	<option value="YE">イエメン</option>
	<option value="GB">イギリス</option>
	<option value="IL">イスラエル</option>
	<option value="IT">イタリア</option>
	<option value="IQ">イラク</option>
	<option value="IR">イラン</option>
	<option value="IN">インド</option>
	<option value="ID">インドネシア</option>
	<option value="UG">ウガンダ</option>
	<option value="UA">ウクライナ</option>
	<option value="UZ">ウズベキスタン</option>
	<option value="UY">ウルグアイ</option>
	<option value="EC">エクアドル</option>
	<option value="EG">エジプト</option>
	<option value="EE">エストニア</option>
	<option value="ER">エリトリア</option>
	<option value="SV">エルサルバドル</option>
	<option value="AU">オーストラリア</option>
	<option value="AT">オーストリア</option>
	<option value="OM">オマーン</option>
	<option value="NL">オランダ</option>
	<option value="GH">ガーナ</option>
	<option value="GY">ガイアナ</option>
	<option value="KZ">カザフスタン</option>
	<option value="QA">カタール</option>
	<option value="CA">カナダ/アメリカ合衆国</option>
	<option value="GA">ガボン</option>
	<option value="CM">カメルーン</option>
	<option value="KR">韓国</option>
	<option value="GM">ガンビア</option>
	<option value="KH">カンボジア</option>
	<option value="GN">ギニア</option>
	<option value="GW">ギニアビサウ</option>
	<option value="CY">キプロス</option>
	<option value="CU">キューバ</option>
	<option value="GR">ギリシャ</option>
	<option value="KG">キルギス</option>
	<option value="GT">グアテマラ</option>
	<option value="GU">グアム</option>
	<option value="KW">クウェート</option>
	<option value="GD">グレナダ</option>
	<option value="HR">クロアチア</option>
	<option value="KY">ケイマン諸島</option>
	<option value="KE">ケニア</option>
	<option value="CI">コートジボワール</option>
	<option value="CR">コスタリカ</option>
	<option value="KM">コモロ</option>
	<option value="CO">コロンビア</option>
	<option value="CD">コンゴ民主共和国</option>
	<option value="SA">サウジアラビア</option>
	<option value="ZM">ザンビア</option>
	<option value="SL">シエラレオネ</option>
	<option value="JM">ジャマイカ</option>
	<option value="GE">ジョージア</option>
	<option value="SY">シリア</option>
	<option value="SG">シンガポール</option>
	<option value="ZW">ジンバブエ</option>
	<option value="CH">スイス</option>
	<option value="SE">スウェーデン</option>
	<option value="SD">スーダン</option>
	<option value="ES">スペイン</option>
	<option value="SR">スリナム</option>
	<option value="LK">スリランカ</option>
	<option value="SK">スロバキア</option>
	<option value="SI">スロベニア</option>
	<option value="SN">セネガル</option>
	<option value="RS">セルビア</option>
	<option value="KN">セントクリストファー・ネイビス</option>
	<option value="LC">セントルシア</option>
	<option value="TH">タイ</option>
	<option value="TW">台湾</option>
	<option value="TJ">タジキスタン</option>
	<option value="TZ">タンザニア</option>
	<option value="CZ">チェコ</option>
	<option value="TD">チャド</option>
	<option value="CF">中央アフリカ共和国</option>
	<option value="CN">中国</option>
	<option value="TN">チュニジア</option>
	<option value="CL">チリ</option>
	<option value="DK">デンマーク</option>
	<option value="DE">ドイツ</option>
	<option value="TG">トーゴ</option>
	<option value="DO">ドミニカ共和国</option>
	<option value="TT">トリニダード・トバゴ</option>
	<option value="TR">トルコ</option>
	<option value="NG">ナイジェリア</option>
	<option value="NI">ニカラグア</option>
	<option value="NE">ニジェール</option>
	<option value="NZ">ニュージーランド</option>
	<option value="NP">ネパール</option>
	<option value="NO">ノルウェー</option>
	<option value="HT">ハイチ</option>
	<option value="PK">パキスタン</option>
	<option value="PA">パナマ</option>
	<option value="BS">バハマ</option>
	<option value="PG">パプアニューギニア</option>
	<option value="BM">バミューダ</option>
	<option value="PW">パラオ</option>
	<option value="PY">パラグアイ</option>
	<option value="BB">バルバドス</option>
	<option value="PS">パレスチナ</option>
	<option value="HU">ハンガリー</option>
	<option value="BD">バングラデシュ</option>
	<option value="TL">東ティモール</option>
	<option value="FJ">フィジー</option>
	<option value="PH">フィリピン</option>
	<option value="FI">フィンランド</option>
	<option value="BT">ブータン</option>
	<option value="PR">プエルトリコ</option>
	<option value="BR">ブラジル</option>
	<option value="FR">フランス</option>
	<option value="PF">フランス領ポリネシア</option>
	<option value="BG">ブルガリア</option>
	<option value="BF">ブルキナファソ</option>
	<option value="BN">ブルネイ</option>
	<option value="BI">ブルンジ</option>
	<option value="VN">ベトナム</option>
	<option value="BJ">ベナン</option>
	<option value="VE">ベネズエラ</option>
	<option value="BY">ベラルーシ</option>
	<option value="PE">ペルー</option>
	<option value="BE">ベルギー</option>
	<option value="PL">ポーランド</option>
	<option value="BA">ボスニア・ヘルツェゴビナ</option>
	<option value="BW">ボツワナ</option>
	<option value="HK">香港</option>
	<option value="MH">マーシャル諸島</option>
	<option value="MO">マカオ</option>
	<option value="MG">マダガスカル</option>
	<option value="MW">マラウイ</option>
	<option value="ML">マリ</option>
	<option value="MT">マルタ</option>
	<option value="MY">マレーシア</option>
	<option value="ZA">南アフリカ</option>
	<option value="MM">ミャンマー</option>
	<option value="MX">メキシコ</option>
	<option value="MU">モーリシャス</option>
	<option value="MR">モーリタニア</option>
	<option value="MZ">モザンビーク</option>
	<option value="MC">モナコ</option>
	<option value="MV">モルディヴ</option>
	<option value="MA">モロッコ</option>
	<option value="MN">モンゴル</option>
	<option value="ME">モンテネグロ</option>
	<option value="JO">ヨルダン</option>
	<option value="LA">ラオス人民民主共和国</option>
	<option value="LV">ラトビア</option>
	<option value="LT">リトアニア</option>
	<option value="LY">リビア</option>
	<option value="LR">リベリア</option>
	<option value="RO">ルーマニア</option>
	<option value="LU">ルクセンブルク</option>
	<option value="RW">ルワンダ</option>
	<option value="LB">レバノン</option>
	<option value="RU">ロシア</option>
</select>
													</li>
													<li>
														
														<div class="numberRowWrap">
															<div class="block">
																<span class="visuallyHidden">国番号</span>
																<span class="countryCode viewInput js-countryCode"></span>
															</div>
															
															<div class="block"><label for="contactsSms:0:recipientSmsEachPersonSelection:flightStatusNotificationContactPointSmsDescription" class="visuallyHidden">電話番号</label><input id="contactsSms:0:recipientSmsEachPersonSelection:flightStatusNotificationContactPointSmsDescription" type="text" name="contactsSms:0:recipientSmsEachPersonSelection:flightStatusNotificationContactPointSmsDescription" class="telNumber" placeholder="例：9012345678" maxlength="30" aria-describedby="confirmTelNumberForRepresentative:0" />
															</div>
														</div>
													</li>
												</ul>
											</div>
										</li>
									</ul>
							</fieldset>
						</dd>
					</dl>
			</div>
	</div>
	</div>
	
	<div class="formInput articleLine">
		<h3>滞在中の連絡先(任意)
		</h3>
		<dl>
			
			<dt><label for="accommodationOne">現地連絡先1</label>
			</dt>
			<dd><input id="accommodationOne" type="text" name="accommodationOne" class="widthLarge" placeholder="例：09012345678 ABC HOTEL" maxlength="28" title="例：09012345678 ABC HOTEL" />
			</dd>
		</dl>
		<dl>
			
			<dt><label for="accommodationTwo">現地連絡先2</label>
			</dt>
			<dd><input id="accommodationTwo" type="text" name="accommodationTwo" class="widthLarge" placeholder="例：09012345678 ABC HOTEL" maxlength="28" title="例：09012345678 ABC HOTEL" />
			</dd>
		</dl>
	</div>
		</div>
		
		<p class="btnAreaSubmit btnArrowNext "><input id="next" type="submit" name="next" value="次へ" class="btnBase btnMainStream btnVerticalMain btnWidthVariable" onclick="return Asw.Dialog.getInstance('prebookConfirmDialog').toggle(event);return Asw.LoadingWindow.open(&quot;NORMAL&quot;, event)" />
		</p><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:2" value="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" autocomplete="off" />
</form><div id="cmnDynamicMessages" class="infoBoxSection">
				<dl class="infoBox importantInfo jsAccordionSwitch">
					<dt><strong>重要なご案内</strong></dt>
					<dd>
						<ul class="jsAccordionSwitchList" id="informationMessages">
								<li>個人情報の利用目的<a href="https://www.ana.co.jp/other/int/meta/0012.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="ANAプライバシーポリシー(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">ANAプライバシーポリシー<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>を必ずお読みいただき、内容に同意いただいた上で、お進みください。</li>
								<li><a href="https://www.ana.co.jp/other/int/meta/0058.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="Secure Flight Programについて(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">米国Secure Flightプログラム<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>に基づき、パスポート名、生年月日、性別などの情報は事前に米国へ通知いたします。</li>
								<li>入力したeメールアドレス、電話番号は入国に必要な情報として米国に通知される可能性があります。</li>
									<li class="jsHiddenFlg toggleContents">お名前のスペルがパスポート名と一致しているかご確認ください。異なる場合はインターネットではお取り扱いできません。<a href="https://www.ana.co.jp/other/int/meta/intbe0001.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="ANAお問い合わせ先(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">ANA<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>へ名前の変更をお申し出ください。</li>
									<li class="jsHiddenFlg toggleContents">二重国籍のお客様は、ご搭乗時に使用されるパスポートのお名前でご予約ください。<br />パスポートごとにお名前が異なる場合は、お客様ご自身で入国管理事務所・外務省・大使館などで各種条件をご確認のうえ、使用されるパスポートを決めてください。<br />なお、入国可否については国によって異なるため、お客様ご自身で各入国管理事務所などでご確認ください。</li>
									<li class="jsHiddenFlg toggleContents">旅行開始日時点で12歳以上の場合は大人、2歳以上12歳未満の場合は小児、2歳未満の場合は幼児の扱いとなります。なお、小児は大人と同マイル数が必要です。</li>
									<li class="jsHiddenFlg toggleContents">運航に関する情報をお客様へ通知するため、提携航空会社にもお客様のメールアドレスが提供されます。あらかじめご了承ください。</li>
									<li class="jsHiddenFlg toggleContents"><dl><dt>携帯電話へのメール送信をご希望のお客様へ</dt><dd>ドメイン指定受信をご利用の場合は、ana.co.jpとamadeus.comからのメールが受信できるようあらかじめ設定をお願いいたします。</dd></dl></li>
									<li class="jsHiddenFlg toggleContents">SMSはスマートフォンの番号に配信されます。一部の国ではSMSでのご案内をご利用いただけません。詳細は<a href="https://www.ana.co.jp/other/int/meta/0737.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="ショートメッセージ(SMS)でのご案内対象外の国について(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">ショートメッセージ(SMS)でのご案内対象外の国について<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>をご覧ください。</li>
									<li class="jsHiddenFlg toggleContents">ログインされている会員ご本人が搭乗しない場合は、再度フライト検索画面より「ログインされている会員ご本人は搭乗しない」のチェックボックスを選択してお手続きください。</li>
						</ul>
							<a href="#" class="toggleSwitch jsTriggerSwitch" role="button" aria-controls="informationMessages" aria-expanded="false"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/toggle_open.png?7bec38a" alt="開閉ボタン" height="25" width="25" class="jsRollOver" />
							</a>
					</dd>
				</dl></div>
	<!--=========== /main =============-->
	</div>

	<!--=========== Script ============-->
	<script type="text/javascript">
	$(window).on('load', function() {
		// アコーディオンのメソッドと設定
		Asw.AccordionInfo('.jsAccordionSwitch', {
			'triggerSwitch': '.jsAccordionSwitch .jsTriggerSwitch',
			'listClass': '.jsAccordionSwitchList',
			'visibilityHidden': 'toggleContents'
		});
	});
	</script>
	<!--=========== Script ============-->
	<script type="text/javascript">
		//プラットフォーム判別（サマリーエリア追従用として）
		var Asw = Asw || {};
	
	  	Asw.ClientInfo = Asw.ClientInfo  || {};
	  	Asw.ClientInfo.DeviceType = Asw.ClientInfo.DeviceType || {};
	
	  	Asw.ClientInfo.DeviceType.PC = 'PC';
	  	Asw.ClientInfo.DeviceType.SP = 'SP';
	  	Asw.ClientInfo.DeviceType.TC = 'TC';
	
	  	Asw.ClientInfo.deviceType = Asw.ClientInfo.DeviceType.PC;
	
	 	if((navigator.userAgent.indexOf('Android') > 0 && navigator.userAgent.indexOf('Mobile') == -1) || navigator.userAgent.indexOf('iPad') > 0){
	    	Asw.ClientInfo.deviceType = Asw.ClientInfo.DeviceType.TC;
	  	}
	</script>
	<!--=========== /Script ============--><div id="summaryArea">
			<div class="flightSummaryArea">
						<div class="flightSummarySection">
							<h2>区間 1
							</h2>
							<p class="flightDate"><em>6</em>月<em class="hasLeft">24</em>日（火）
							</p>
							<div class="flightSummaryRoute">
									<div class="flightSummaryRouteSection">
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation1_1">
													<span>東京(成田)
													</span>
												</p>
												<p class="time">
													<span>17:00
													</span>
												</p>
											</div>
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation1_1">
													<span>シカゴ(ORD)
													</span>
												</p>
												<p class="time">
													<span>14:55
													</span>
												</p>
											</div>
										<p class="flightNumberClass">
												<span>NH012</span><span><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/flight_ana_summaryarea.png?717d3c0" alt="ANA運航便" height="11" width="41" /></span>
												<br />特典ビジネス
										</p>
									</div>
							</div>
						</div>
						<div class="flightSummarySection">
							<h2 class="midstream">区間 2
							</h2>
							<p class="flightDate"><em>7</em>月<em class="hasLeft">23</em>日（水）
							</p>
							<div class="flightSummaryRoute">
									<div class="flightSummaryRouteSection">
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation2_1">
													<span>ホノルル(オアフ島)
													</span>
												</p>
												<p class="time">
													<span>11:35
													</span>
												</p>
											</div>
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation2_1">
													<span>東京(成田)
													</span>
												</p>
												<p class="time">
													<span>14:50
																	<span>翌日
																	</span>
													</span>
												</p>
											</div>
										<p class="flightNumberClass">
												<span>NH183</span><span><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/flight_ana_summaryarea.png?717d3c0" alt="ANA運航便" height="11" width="41" /></span>
												<br />特典エコノミー
										</p>
									</div>
							</div>
						</div>
					<div class="total">
						<p class="passenger">大人 x1
						</p>
						<div class="totalArea">
					  		<dl class="mealAmountWrap">
									<dt>有料機内食サービス
									</dt>
									<dd><em class="mealAmount">0</em><span class="currencyCode">円</span>
									</dd>
							</dl>
									<div>
										<div class="mileageAndTotal">
												<dl class="requiredMileage">
													<dt>必要マイル
													</dt>
													<dd><em class="requiredMileage">74,000</em><span class="currencyCode">マイル</span>
													</dd>
												</dl>
												<dl>
													<dt>総額
													</dt>
													<dd><em class="awardTotalPayment">68,130</em>円
													</dd>
												</dl>
												<p class="includeFareAndFuelCost">※各種税金、燃油特別付加運賃等を含みます。
												</p>
										</div>
									</div>
						</div>
					</div>
				
			</div></div>
		<!--===== /cmnWrapper =====-->
		</div>
			
		<span id="message" class="visuallyHidden" role="alert" aria-live="assertive"></span>
		<span id="politeMessage" class="visuallyHidden" role="log" aria-live="polite"></span><script type="text/javascript">
		var Asw = Asw || {};
		Asw.BaseOutput = {"sessionID":"NEWHhoOyXT","language":"ja","siteCatalystLanguageCode":"J","operationDate":"20250619","operationDateTime":"20250619183042","office":{"officeCode":"TYONH08ZZ","country2letterCode":"JP"},"bookingType":"A","pageID":"A04_P01","siteCatalystPageName":"INT_BE_AWARD_J_A04特典予約_P01旅客情報入力（必須情報）","device":{"deviceType":"PC"}};
	</script><div id="promotionArea">
		<!--===== promotionArea =====-->
		<!--===== /promotionArea =====--></div>
		<!--=========== /cmnContainer ============--></div><div id="cmnBaloonArea" aria-live="polite"></div><div id="cmnPopupArea" aria-live="polite"></div>

	

	<div id="cmnFooterWrapper">
		<!--=========== .cmnFooterWrapper ============-->
		<div id="cmnFooter">
			<!--===== cmnFooter =====-->
			<ul>
				<li><a href="https://www.ana.co.jp/other/int/meta/0012.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">プライバシーポリシー<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
					<li><a href="https://www.ana.co.jp/other/int/meta/0670.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">利用者情報の外部送信について<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
					</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/0052.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">サイト利用規約<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/be0238.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">推奨環境<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/0133.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">サイトマップ<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/0732.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">運送約款<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
					<li class="lastChild"><a href="https://www.ana.co.jp/other/int/meta/0556.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">ウェブアクセシビリティについて<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
					</li>
			</ul>
			<p class="blankText" aria-hidden="true"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="" height="10" width="10" />外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。</p>
			<p class="copyright"><span lang="en" xml:lang="en">Copyright&#169;ANA</span>
			</p>
			<p class="starAllianceLogo"><a href="http://www.staralliance.com/ja/" target="_blank"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/star_alliance_logo.png?0fe73b5" alt="A STAR ALLIANCE MEMBER" height="18" width="175" /><span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
			</p>
			<!--===== cmnFooter =====-->
		</div>
		<!--=========== /cmnFooterWrapper ============-->
	</div>

		
		<div id="cmnLoadingForAjax"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/bg_modalWindow.png?717d3c0" alt="" height="0" width="0" />
			<div class="loadingArea">
				<p class="loadingImage"><img src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/image/loading.gif?717d3c0" alt="リクエストを処理しています...しばらくお待ちください。" height="33" width="33" />
				</p>
			</div>
		</div>
		
		
		<div id="popupAltSentence" style="display: none" aria-hidden="true">別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。</div>
	<div id="cmnSiteCatalyst" aria-hidden="true"><div id="cmnSiteCatalystParamArea"><script type="text/javascript">
				// ▼SiteCatalyst
				// SiteCatalyst code version: H.2.
				// Copyright 1997-2005 Omniture, Inc. More info available at http://www.omniture.com
				SiteCatalystReportSuites   = "INT";
				SiteCatalystCharSet        = "UTF-8";
				SiteCatalystChannel        = "BE_AWARD_J";
				SiteCatalystPageName       = "INT_BE_AWARD_J_A04\u7279\u5178\u4E88\u7D04_P01\u65C5\u5BA2\u60C5\u5831\u5165\u529B\uFF08\u5FC5\u9808\u60C5\u5831\uFF09";
				SiteCatalystERR            = "";
				SiteCatalystEventNum       = "";
				SiteCatalystPurchase       = "FALSE";
				SiteCatalystProductPrice   = "";
				SiteCatalystCurrencyCode   = "";
				SiteCatalystSeatAvailabilityINT   = "";
				SiteCatalystDepartDate     = "";
				SiteCatalystDepart         = "";
				SiteCatalystArrive         = "";
				SiteCatalystFlightNo       = "";
				SiteCatalystClass          = "";
				SiteCatalystFare           = "";
				SiteCatalystPurchaseID     = "";
				
				SiteCatalystLocater        = "";
				SiteCatalystPayment        = "";
				SiteCatalystIncome         = "";
				
				SiteCatalystPax            = "";
				SiteCatalystManipulateDay  = "";
				SiteCatalystCompartmentClass   = "";
				SiteCatalystOptionInfo     = "";
				SiteCatalystOutboundUpsellPrice   = "";
				SiteCatalystInboundUpsellPrice    = "";
				
				SiteCatalystSearchInput    = "";
				SiteCatalystSegment        = "";
				SiteCatalystOfficeCode     = "";
				SiteCatalystPassengerCount = "";
				SiteCatalystPassenger      = "";
				SiteCatalystAncillaryService = "";
				// End SiteCatalyst code version: H.2.
				// ▲SiteCatalyst
			</script></div><script type="text/javascript"><!-- if(navigator.appVersion.indexOf('MSIE')>=0)document.write(unescape('%3C')+'\!-'+'-') // --></script>
		<!--/DO NOT REMOVE/-->
		

		
		<!-- Google Tag Manager -->
		<script type="text/javascript" charset="Shift_JIS" src="https://www.ana.co.jp/common/js/GTMINTBE.js"></script>
		<!-- End Google Tag Manager -->
		<!-- Yahoo Tag Manager -->
		<script type="text/javascript" charset="Shift_JIS" src="https://www.ana.co.jp/common/js/YTMINTBE.js"></script>
		<!-- End Yahoo Tag Manager -->
		

	</div>

	

	<div class="mboxDefault"></div><script type="text/javascript">mboxCreate("ASW_common_A04_P01");</script>
			<script type="text/javascript" src="https://www.ana.co.jp/common/js/tealium/tealium.js"></script>
			<script type="text/javascript" src="//cdn.evgnet.com/beacon/allnipponairways/nonamcprd/scripts/evergage.min.js"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/javax.faces.resource/jsf.js.xhtml?ln=javax.faces"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/jquery.mousewheel.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/jquery.jscrollpane.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/jquery-ui-1.10.4.custom.min.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/jquery.ui.touch-punch.min.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-common.js?beb6a50"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-common-pc.js?fd88d7d"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-format.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/mask.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/loading.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-loadingwindow-pc.js?9e233c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-loadingwindow.js?51db600"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/dialog-pc.js?25d36a4"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-dialog-sequencer.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-event.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-date-select.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-calendar-pc.js?9b86b8a"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-calendar.js?db624c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/heightLine.js?0e7a1ce"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-summaryarea.js?b21c6d7"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-fingerprints.js?99001fc"></script><script type="text/javascript">
			Asw.init("https://aswbe-i.ana.co.jp/rei21c/international_asw", "ja");
			Asw.getCalendarHolidayList = function() {return [20250721,20250811,20250915,20250923,20251013,20251103,20251123,20251124,20260101,20260112,20260211,20260223,20260320,20260429,20260503,20260504,20260505,20260506,20260720,20260811,20260921,20260922,20260923,20261012,20261103,20261123,20270101,20270111,20270211,20270223,20270321,20270322,20270429,20270503,20270504,20270505,20270719,20270811,20270920,20270923,20271011,20271103,20271123,20280101,20280110,20280211,20280223,20280320,20280429,20280503,20280504,20280505,20280717,20280811,20280918,20280922,20281009,20281103,20281123,20290101,20290108,20290211,20290212,20290223,20290320,20290429,20290430,20290503,20290504,20290505,20290716,20290811,20290917,20290923,20290924,20291008,20291103,20291123];}
			Asw.ClientInfo.deviceType = "PC";
			Asw.ClientInfo.mobileDeviceType = "UNKNOWN";
			Asw.ClientInfo.osVersion = "";
			Asw.Format.init("https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/{1}/{2}/{3}?{0}");
			Asw.overwriteTargetAttributeValue("false");
		</script><script type="text/javascript">
		
		var Asw = Asw || {};
		Asw.ContactInput = Asw.ContactInput || {};
		Asw.ContactInput.Const = {
			/** data-属性名「data-type」 @constant @type {String} @default 'data-type' */
			DATA_ATTRIBUTE_NAME_TYPE: 'data-type',
			/** data-属性名「data-sms-possible」 @constant @type {String} @default 'data-sms-possible' */
			DATA_ATTRIBUTE_NAME_SMS_POSSIBLE: 'data-sms-possible',
			/** data-属性名「data-country-code」 @constant @type {String} @default 'data-country-code' */
			DATA_ATTRIBUTE_NAME_COUNTRY_CODE: 'data-country-code',
			/** data-type属性値「sms」 @constant @type {String} @default 'sms' */
			DATA_TYPE_VALUE_SMS: 'sms'
		};

		
		Asw.ContactInput.countryInfoMap = {"IS":{"internationalCallingCode":"+354","smsPossible":false},"IE":{"internationalCallingCode":"+353","smsPossible":true},"AZ":{"internationalCallingCode":"+994","smsPossible":true},"AF":{"internationalCallingCode":"+93","smsPossible":true},"US":{"internationalCallingCode":"+1","smsPossible":true},"AE":{"internationalCallingCode":"+971","smsPossible":true},"DZ":{"internationalCallingCode":"+213","smsPossible":true},"AR":{"internationalCallingCode":"+54","smsPossible":true},"AW":{"internationalCallingCode":"+297","smsPossible":true},"AL":{"internationalCallingCode":"+355","smsPossible":true},"AM":{"internationalCallingCode":"+374","smsPossible":true},"AO":{"internationalCallingCode":"+244","smsPossible":false},"AG":{"internationalCallingCode":"+1","smsPossible":true},"AD":{"internationalCallingCode":"+376","smsPossible":true},"YE":{"internationalCallingCode":"+967","smsPossible":true},"GB":{"internationalCallingCode":"+44","smsPossible":true},"IL":{"internationalCallingCode":"+972","smsPossible":true},"IT":{"internationalCallingCode":"+39","smsPossible":true},"IQ":{"internationalCallingCode":"+964","smsPossible":true},"IR":{"internationalCallingCode":"+98","smsPossible":true},"IN":{"internationalCallingCode":"+91","smsPossible":true},"ID":{"internationalCallingCode":"+62","smsPossible":true},"UG":{"internationalCallingCode":"+256","smsPossible":true},"UA":{"internationalCallingCode":"+380","smsPossible":true},"UZ":{"internationalCallingCode":"+998","smsPossible":true},"UY":{"internationalCallingCode":"+598","smsPossible":true},"EC":{"internationalCallingCode":"+593","smsPossible":true},"EG":{"internationalCallingCode":"+20","smsPossible":true},"EE":{"internationalCallingCode":"+372","smsPossible":true},"SZ":{"internationalCallingCode":"+268","smsPossible":false},"ET":{"internationalCallingCode":"+251","smsPossible":false},"ER":{"internationalCallingCode":"+291","smsPossible":true},"SV":{"internationalCallingCode":"+503","smsPossible":true},"AU":{"internationalCallingCode":"+61","smsPossible":true},"AT":{"internationalCallingCode":"+43","smsPossible":true},"OM":{"internationalCallingCode":"+968","smsPossible":true},"NL":{"internationalCallingCode":"+31","smsPossible":true},"GH":{"internationalCallingCode":"+233","smsPossible":true},"CV":{"internationalCallingCode":"+238","smsPossible":false},"GY":{"internationalCallingCode":"+592","smsPossible":true},"KZ":{"internationalCallingCode":"+7","smsPossible":true},"QA":{"internationalCallingCode":"+974","smsPossible":true},"CA":{"internationalCallingCode":"+1","smsPossible":true},"GA":{"internationalCallingCode":"+241","smsPossible":true},"CM":{"internationalCallingCode":"+237","smsPossible":true},"KR":{"internationalCallingCode":"+82","smsPossible":true},"KH":{"internationalCallingCode":"+855","smsPossible":true},"GM":{"internationalCallingCode":"+220","smsPossible":true},"KP":{"internationalCallingCode":"+850","smsPossible":false},"MK":{"internationalCallingCode":"+389","smsPossible":false},"GN":{"internationalCallingCode":"+224","smsPossible":true},"GW":{"internationalCallingCode":"+245","smsPossible":true},"CY":{"internationalCallingCode":"+357","smsPossible":true},"CU":{"internationalCallingCode":"+53","smsPossible":true},"GR":{"internationalCallingCode":"+30","smsPossible":true},"KI":{"internationalCallingCode":"+686","smsPossible":false},"KG":{"internationalCallingCode":"+996","smsPossible":true},"GT":{"internationalCallingCode":"+502","smsPossible":true},"GU":{"internationalCallingCode":"+1","smsPossible":true},"KW":{"internationalCallingCode":"+965","smsPossible":true},"KY":{"internationalCallingCode":"+1","smsPossible":true},"GD":{"internationalCallingCode":"+1","smsPossible":true},"HR":{"internationalCallingCode":"+385","smsPossible":true},"KE":{"internationalCallingCode":"+254","smsPossible":true},"CI":{"internationalCallingCode":"+225","smsPossible":true},"CR":{"internationalCallingCode":"+506","smsPossible":true},"SA":{"internationalCallingCode":"+966","smsPossible":true},"KM":{"internationalCallingCode":"+269","smsPossible":true},"CO":{"internationalCallingCode":"+57","smsPossible":true},"CG":{"internationalCallingCode":"+242","smsPossible":false},"CD":{"internationalCallingCode":"+243","smsPossible":true},"ZM":{"internationalCallingCode":"+260","smsPossible":true},"SM":{"internationalCallingCode":"+378","smsPossible":false},"SL":{"internationalCallingCode":"+232","smsPossible":true},"DJ":{"internationalCallingCode":"+253","smsPossible":false},"JM":{"internationalCallingCode":"+1","smsPossible":true},"CH":{"internationalCallingCode":"+41","smsPossible":true},"GE":{"internationalCallingCode":"+995","smsPossible":true},"SE":{"internationalCallingCode":"+46","smsPossible":true},"SY":{"internationalCallingCode":"+963","smsPossible":true},"ES":{"internationalCallingCode":"+34","smsPossible":true},"SG":{"internationalCallingCode":"+65","smsPossible":true},"LK":{"internationalCallingCode":"+94","smsPossible":true},"SK":{"internationalCallingCode":"+421","smsPossible":true},"SI":{"internationalCallingCode":"+386","smsPossible":true},"ZW":{"internationalCallingCode":"+263","smsPossible":true},"SD":{"internationalCallingCode":"+249","smsPossible":true},"KN":{"internationalCallingCode":"+1","smsPossible":true},"SR":{"internationalCallingCode":"+597","smsPossible":true},"LC":{"internationalCallingCode":"+1","smsPossible":true},"SC":{"internationalCallingCode":"+248","smsPossible":false},"SN":{"internationalCallingCode":"+221","smsPossible":true},"SB":{"internationalCallingCode":"+677","smsPossible":false},"RS":{"internationalCallingCode":"+381","smsPossible":true},"TH":{"internationalCallingCode":"+66","smsPossible":true},"TW":{"internationalCallingCode":"+886","smsPossible":true},"TJ":{"internationalCallingCode":"+992","smsPossible":true},"TZ":{"internationalCallingCode":"+255","smsPossible":true},"CZ":{"internationalCallingCode":"+420","smsPossible":true},"TD":{"internationalCallingCode":"+235","smsPossible":true},"CF":{"internationalCallingCode":"+236","smsPossible":true},"TN":{"internationalCallingCode":"+216","smsPossible":true},"CL":{"internationalCallingCode":"+56","smsPossible":true},"TV":{"internationalCallingCode":"+688","smsPossible":false},"DK":{"internationalCallingCode":"+45","smsPossible":true},"DE":{"internationalCallingCode":"+49","smsPossible":true},"TG":{"internationalCallingCode":"+228","smsPossible":true},"DO":{"internationalCallingCode":"+1","smsPossible":true},"DM":{"internationalCallingCode":"+1","smsPossible":false},"TT":{"internationalCallingCode":"+1","smsPossible":true},"TM":{"internationalCallingCode":"+993","smsPossible":false},"TR":{"internationalCallingCode":"+90","smsPossible":true},"TO":{"internationalCallingCode":"+676","smsPossible":false},"NG":{"internationalCallingCode":"+234","smsPossible":true},"NR":{"internationalCallingCode":"+674","smsPossible":false},"NA":{"internationalCallingCode":"+264","smsPossible":false},"NI":{"internationalCallingCode":"+505","smsPossible":true},"BH":{"internationalCallingCode":"+973","smsPossible":false},"NE":{"internationalCallingCode":"+227","smsPossible":true},"PK":{"internationalCallingCode":"+92","smsPossible":true},"JP":{"internationalCallingCode":"+81","smsPossible":true},"BS":{"internationalCallingCode":"+1","smsPossible":true},"NZ":{"internationalCallingCode":"+64","smsPossible":true},"BM":{"internationalCallingCode":"+1","smsPossible":true},"NP":{"internationalCallingCode":"+977","smsPossible":true},"BB":{"internationalCallingCode":"+246","smsPossible":true},"NO":{"internationalCallingCode":"+47","smsPossible":true},"HT":{"internationalCallingCode":"+509","smsPossible":true},"BD":{"internationalCallingCode":"+880","smsPossible":true},"VA":{"internationalCallingCode":"+39","smsPossible":false},"PA":{"internationalCallingCode":"+507","smsPossible":true},"VU":{"internationalCallingCode":"+678","smsPossible":false},"PG":{"internationalCallingCode":"+675","smsPossible":true},"PW":{"internationalCallingCode":"+680","smsPossible":true},"PY":{"internationalCallingCode":"+595","smsPossible":true},"PS":{"internationalCallingCode":"+970","smsPossible":true},"BR":{"internationalCallingCode":"+55","smsPossible":true},"HU":{"internationalCallingCode":"+36","smsPossible":true},"TL":{"internationalCallingCode":"+670","smsPossible":true},"BG":{"internationalCallingCode":"+359","smsPossible":true},"BF":{"internationalCallingCode":"+226","smsPossible":true},"BN":{"internationalCallingCode":"+673","smsPossible":true},"FJ":{"internationalCallingCode":"+679","smsPossible":true},"PH":{"internationalCallingCode":"+63","smsPossible":true},"FI":{"internationalCallingCode":"+358","smsPossible":true},"BT":{"internationalCallingCode":"+975","smsPossible":true},"BY":{"internationalCallingCode":"+375","smsPossible":true},"BZ":{"internationalCallingCode":"+501","smsPossible":false},"PR":{"internationalCallingCode":"+1","smsPossible":true},"BE":{"internationalCallingCode":"+32","smsPossible":true},"BA":{"internationalCallingCode":"+387","smsPossible":true},"FR":{"internationalCallingCode":"+33","smsPossible":true},"BO":{"internationalCallingCode":"+591","smsPossible":false},"PF":{"internationalCallingCode":"+689","smsPossible":true},"BI":{"internationalCallingCode":"+257","smsPossible":true},"VN":{"internationalCallingCode":"+84","smsPossible":true},"BJ":{"internationalCallingCode":"+229","smsPossible":true},"VE":{"internationalCallingCode":"+58","smsPossible":true},"PE":{"internationalCallingCode":"+51","smsPossible":true},"PL":{"internationalCallingCode":"+48","smsPossible":true},"BW":{"internationalCallingCode":"+267","smsPossible":true},"PT":{"internationalCallingCode":"+351","smsPossible":false},"HK":{"internationalCallingCode":"+852","smsPossible":true},"HN":{"internationalCallingCode":"+504","smsPossible":false},"MH":{"internationalCallingCode":"+692","smsPossible":true},"MO":{"internationalCallingCode":"+853","smsPossible":true},"MG":{"internationalCallingCode":"+261","smsPossible":true},"MW":{"internationalCallingCode":"+265","smsPossible":true},"ML":{"internationalCallingCode":"+223","smsPossible":true},"MT":{"internationalCallingCode":"+356","smsPossible":true},"MY":{"internationalCallingCode":"+60","smsPossible":true},"FM":{"internationalCallingCode":"+691","smsPossible":false},"MM":{"internationalCallingCode":"+95","smsPossible":true},"MX":{"internationalCallingCode":"+52","smsPossible":true},"MU":{"internationalCallingCode":"+230","smsPossible":true},"MR":{"internationalCallingCode":"+222","smsPossible":true},"MZ":{"internationalCallingCode":"+258","smsPossible":true},"MC":{"internationalCallingCode":"+377","smsPossible":true},"MV":{"internationalCallingCode":"+960","smsPossible":true},"MD":{"internationalCallingCode":"+373","smsPossible":false},"RO":{"internationalCallingCode":"+40","smsPossible":true},"MA":{"internationalCallingCode":"+212","smsPossible":true},"MN":{"internationalCallingCode":"+976","smsPossible":true},"ME":{"internationalCallingCode":"+382","smsPossible":true},"JO":{"internationalCallingCode":"+962","smsPossible":true},"LA":{"internationalCallingCode":"+856","smsPossible":true},"RU":{"internationalCallingCode":"+7","smsPossible":true},"LV":{"internationalCallingCode":"+371","smsPossible":true},"LT":{"internationalCallingCode":"+370","smsPossible":true},"LY":{"internationalCallingCode":"+218","smsPossible":true},"GQ":{"internationalCallingCode":"+240","smsPossible":false},"LI":{"internationalCallingCode":"+423","smsPossible":false},"LR":{"internationalCallingCode":"+231","smsPossible":true},"CN":{"internationalCallingCode":"+86","smsPossible":true},"LU":{"internationalCallingCode":"+352","smsPossible":true},"ZA":{"internationalCallingCode":"+27","smsPossible":true},"RW":{"internationalCallingCode":"+250","smsPossible":true},"LS":{"internationalCallingCode":"+266","smsPossible":false},"LB":{"internationalCallingCode":"+961","smsPossible":true}};

		$(function () {
			contactInfoInit();
		});
		
		function contactInfoInit() {
			
			
			Asw.get('representativeTelType').find('option[value="M1"]').attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_TYPE,
					Asw.ContactInput.Const.DATA_TYPE_VALUE_SMS);
			Asw.get('representativeTelCountry').children('option').each(function(i,option){
				var countryInfo = Asw.ContactInput.countryInfoMap[option.value];
				if (countryInfo === void 0) {
					
					return true;
				}
				$(option).attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_SMS_POSSIBLE, countryInfo.smsPossible);
				$(option).attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_COUNTRY_CODE, countryInfo.internationalCallingCode);
			});
			
			var smsCountrySelectSelector = 'select.js-countrySelect:not(#representativeTelCountry)'
			$(smsCountrySelectSelector).eq(0).children('option').each(function(i,option){
				var countryInfo = Asw.ContactInput.countryInfoMap[option.value];
				if (countryInfo === void 0) {
					
					return true;
				}
				$(smsCountrySelectSelector + ' option:nth-child(' + (i+1) + ')').attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_COUNTRY_CODE,
						countryInfo.internationalCallingCode);
			});
			
			var emergencyCountrySelectSelector = 'select.passengerEmergencyCountry.js-countrySelect:not(#representativeTelCountry)'
			$(emergencyCountrySelectSelector).eq(0).children('option').each(function(i,option){
				var countryInfo = Asw.ContactInput.countryInfoMap[option.value];
				if (countryInfo === void 0) {
					
					return true;
				}
				$(emergencyCountrySelectSelector + ' option:nth-child(' + (i+1) + ')').attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_COUNTRY_CODE,
						countryInfo.internationalCallingCode);
			});
		}
	</script><script type="text/javascript">
		var isInit = true;
		function changeTelType(_this, telNoInputId) {
			var $telNoInput = Asw.get(telNoInputId);
			var $telNoKind = Asw.get("representativeTelType");
			var telNo = Asw.getValueComparedToPlaceholder(telNoInputId);
			var telNoCountry = Asw.get("representativeTelCountry").val();

			if(true && !isInit) {
				if(telInputCheck(telNo, telNoCountry)) {
					if (_this.value == "M1") {
						telNo = exceptDomesticPrefix("09042504176", telNoCountry);
					} else if (_this.value == "B1") {
						telNo = exceptDomesticPrefix("03-5405-2172", telNoCountry);
					} else {
						telNo = exceptDomesticPrefix("090-4250-4176", telNoCountry);
					}
					$telNoInput.val(telNo);
				}
			}
			isInit = false;
		}
		
		function telInputCheck(telNo, telNoCountry) {
	        checkResult = false;
	        
	        var amcHndyTelNo = exceptDomesticPrefix("09042504176", telNoCountry);
	        var amcCmpTelNo = exceptDomesticPrefix("03-5405-2172", telNoCountry);
	        var amcTelNo = exceptDomesticPrefix("090-4250-4176", telNoCountry);
	
	        if(telNo == "" || telNo == amcHndyTelNo || telNo == amcCmpTelNo || telNo == amcTelNo) {
	        	checkResult = true;
	        }
	        return checkResult;
    	}
    	
    	function exceptDomesticPrefix(telNo, telNoCountry) {
    		if(telNoCountry == 'JP') {
    			if(telNo.substr(0, 1) === '0') {
    				return telNo.slice(1);
    			}
    		}
    		return telNo;
    	}
	</script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-customerinfomationform.js?6e91953"></script><script type="text/javascript">
		$(document).ready(function(){
				Asw.enableTextWhenCheckBox('.cascadedSwitch');
				Asw.notPasteInput('.jsNotPasteInput');
			$(function(){
				Asw.alignHeightToParentTdElement( '.passengerInfoInputArea' );
				
					Asw.alignHeightToParentTdElement( '.passengerInfoInputText' );
				
				
				$('select.js-countrySelect').trigger('change');
			});
		});
	</script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei21c/international_asw/contents/cmn/script/asw-session-keeper.js?6ec0730"></script><script type="text/javascript">
			$(document).ready(function() {
				Asw.SessionKeeper.initAndStart("sessionKeeperContainer", "520", "570", "PC");
			});
			</script><script type="text/javascript">
		Asw.addAjaxCallback(function(data) {
			// アコーディオンのメソッドと設定 
			if($('.jsAccordionSwitch .jsTriggerSwitch').length > 0){
				var events = $._data($('.jsAccordionSwitch .jsTriggerSwitch').get(0), "events");
				if(events === undefined || events.click === undefined) {
					Asw.AccordionInfo('.jsAccordionSwitch', {
						'triggerSwitch': '.jsAccordionSwitch .jsTriggerSwitch',
						'listClass': '.jsAccordionSwitchList',
						'visibilityHidden': 'toggleContents'
					});
				}
			}
		});
	</script><script type="text/javascript">
	Asw.Price = Asw.Price || {};
	// 整数の区切り文字
	Asw.Price.separateString = ',';
	
	// 整数の区切り桁数
	Asw.Price.separateDigitNumber = 3;
	
	// 小数点
	Asw.Price.decimalPointString = '.';
	
	// 小数点以降の有効桁数
	Asw.Price.significantDigit = 0;
	
	// 表示金額の単位
	Asw.Price.currencyUnit = '円';
	</script><script type="text/javascript">
			$(document).ready(function() {
				$('#preLoadingArea').css('visibility', 'hidden');
			});
		</script><noscript><img src="https://aswbe-i.ana.co.jp/akam/13/pixel_429d9996?a=dD04MzNmNGMxYTk1MWRmY2M0ODE2NmViMzkxMTEyYzIyOGZjMmI5NzI3JmpzPW9mZg==" style="visibility: hidden; position: absolute; left: -999px; top: -999px;" /></noscript><script type="text/javascript"  src="/RcS9yzCRgrNnM/gY/v0Wf9fQwtpdQ/7NE3X2ruOE2SL9YE/HyNpQmYB/RWt/XOHcnHAU"></script></body>
</html>