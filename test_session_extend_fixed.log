2025-06-19 19:24:53.230 | INFO     | __main__:<module>:105 - 开始测试ANA会话延长功能最终修复...
2025-06-19 19:24:53.230 | INFO     | __main__:test_final_session_extend_fix:18 - === ANA会话延长问题最终修复方案 ===
2025-06-19 19:24:53.231 | INFO     | __main__:test_final_session_extend_fix:20 - 🔍 问题根本原因分析:
2025-06-19 19:24:53.232 | INFO     | __main__:test_final_session_extend_fix:21 - 1. JSF AJAX字段值错误
2025-06-19 19:24:53.232 | INFO     | __main__:test_final_session_extend_fix:22 - 2. 缺少关键的x-dtpc头部匹配
2025-06-19 19:24:53.233 | INFO     | __main__:test_final_session_extend_fix:23 - 3. 请求格式与浏览器不完全一致
2025-06-19 19:24:53.233 | INFO     | __main__:test_final_session_extend_fix:25 - 🔧 修复方案:
2025-06-19 19:24:53.234 | INFO     | __main__:test_final_session_extend_fix:26 - 1. 修正JSF AJAX字段:
2025-06-19 19:24:53.234 | INFO     | __main__:test_final_session_extend_fix:27 -    - javax.faces.partial.event: 'click' (不是'action')
2025-06-19 19:24:53.235 | INFO     | __main__:test_final_session_extend_fix:28 -    - javax.faces.partial.execute: 重复的按钮ID
2025-06-19 19:24:53.235 | INFO     | __main__:test_final_session_extend_fix:29 -    - 移除不存在的按钮值字段
2025-06-19 19:24:53.235 | INFO     | __main__:test_final_session_extend_fix:31 - 2. 修复关键的x-dtpc头部:
2025-06-19 19:24:53.236 | INFO     | __main__:test_final_session_extend_fix:32 -    - 从当前会话cookies中动态获取dtPC值
2025-06-19 19:24:53.236 | INFO     | __main__:test_final_session_extend_fix:33 -    - 确保DynaTrace监控头与会话匹配
2025-06-19 19:24:53.237 | INFO     | __main__:test_final_session_extend_fix:35 - 3. 完整的修复后payload结构:
2025-06-19 19:24:53.238 | INFO     | __main__:test_final_session_extend_fix:51 - Payload字段:
2025-06-19 19:24:53.238 | INFO     | __main__:test_final_session_extend_fix:54 -   sessionKeeperContainer:j_idt170: sessionKeeperContainer:j_idt170
2025-06-19 19:24:53.239 | INFO     | __main__:test_final_session_extend_fix:54 -   sessionKeeperContainer:j_idt170_operationTicket: token_value
2025-06-19 19:24:53.239 | INFO     | __main__:test_final_session_extend_fix:54 -   sessionKeeperContainer:j_idt170_cmnPageTicket: 5
2025-06-19 19:24:53.240 | INFO     | __main__:test_final_session_extend_fix:54 -   javax.faces.ViewState: view_state_value
2025-06-19 19:24:53.240 | INFO     | __main__:test_final_session_extend_fix:54 -   javax.faces.source: sessionKeeperContainer:cmnSessionKeepingButton
2025-06-19 19:24:53.240 | INFO     | __main__:test_final_session_extend_fix:54 -   javax.faces.partial.event: click
2025-06-19 19:24:53.241 | INFO     | __main__:test_final_session_extend_fix:54 -   javax.faces.partial.execute: sessionKeeperContainer:cmnSessionKeepingButton ses...
2025-06-19 19:24:53.241 | INFO     | __main__:test_final_session_extend_fix:54 -   javax.faces.behavior.event: action
2025-06-19 19:24:53.242 | INFO     | __main__:test_final_session_extend_fix:54 -   javax.faces.partial.ajax: true
2025-06-19 19:24:53.242 | INFO     | __main__:test_final_session_extend_fix:56 - 4. 修复后的关键头部:
2025-06-19 19:24:53.243 | INFO     | __main__:test_final_session_extend_fix:66 - Headers:
2025-06-19 19:24:53.243 | INFO     | __main__:test_final_session_extend_fix:68 -   Content-Type: application/x-www-form-urlencoded;charset=UTF-8
2025-06-19 19:24:53.244 | INFO     | __main__:test_final_session_extend_fix:68 -   Faces-Request: partial/ajax
2025-06-19 19:24:53.244 | INFO     | __main__:test_final_session_extend_fix:68 -   x-dtpc: 动态从cookies获取
2025-06-19 19:24:53.245 | INFO     | __main__:test_final_session_extend_fix:68 -   Accept: */*
2025-06-19 19:24:53.245 | INFO     | __main__:test_final_session_extend_fix:68 -   Origin: https://aswbe-i.ana.co.jp
2025-06-19 19:24:53.246 | INFO     | __main__:test_final_session_extend_fix:68 -   Referer: 当前页面URL
2025-06-19 19:24:53.247 | INFO     | __main__:test_final_session_extend_fix:70 - 🎯 预期效果:
2025-06-19 19:24:53.247 | INFO     | __main__:test_final_session_extend_fix:71 - 1. 不再出现60秒超时错误
2025-06-19 19:24:53.248 | INFO     | __main__:test_final_session_extend_fix:72 - 2. 收到正确的JSF AJAX XML响应
2025-06-19 19:24:53.248 | INFO     | __main__:test_final_session_extend_fix:73 - 3. 成功延长会话9次，每次间隔8分40秒
2025-06-19 19:24:53.248 | INFO     | __main__:test_final_session_extend_fix:74 - 4. 完成125分钟的完整占位保持
2025-06-19 19:24:53.249 | INFO     | __main__:test_final_session_extend_fix:76 - 📋 代码修改位置:
2025-06-19 19:24:53.249 | INFO     | __main__:test_final_session_extend_fix:77 - 文件: ana_search.py
2025-06-19 19:24:53.250 | INFO     | __main__:test_final_session_extend_fix:78 - 函数: keep_session_alive
2025-06-19 19:24:53.250 | INFO     | __main__:test_final_session_extend_fix:79 - 修改: payload构建和headers中的x-dtpc值
2025-06-19 19:24:53.251 | INFO     | __main__:test_final_session_extend_fix:81 - ✅ 修复完成！现在可以测试会话延长功能。
2025-06-19 19:24:53.252 | INFO     | __main__:show_comparison:88 - === 修复前后对比 ===
2025-06-19 19:24:53.252 | INFO     | __main__:show_comparison:90 - ❌ 修复前的问题:
2025-06-19 19:24:53.253 | INFO     | __main__:show_comparison:91 - 1. javax.faces.partial.event = 'action' (错误)
2025-06-19 19:24:53.253 | INFO     | __main__:show_comparison:92 - 2. javax.faces.partial.execute = 单个按钮ID (错误)
2025-06-19 19:24:53.254 | INFO     | __main__:show_comparison:93 - 3. 包含按钮值字段 (多余)
2025-06-19 19:24:53.254 | INFO     | __main__:show_comparison:94 - 4. x-dtpc使用固定值 (不匹配)
2025-06-19 19:24:53.255 | INFO     | __main__:show_comparison:95 - 5. 结果: 60秒超时，服务器拒绝请求
2025-06-19 19:24:53.255 | INFO     | __main__:show_comparison:97 - ✅ 修复后的正确方式:
2025-06-19 19:24:53.256 | INFO     | __main__:show_comparison:98 - 1. javax.faces.partial.event = 'click' (正确)
2025-06-19 19:24:53.256 | INFO     | __main__:show_comparison:99 - 2. javax.faces.partial.execute = 重复的按钮ID (正确)
2025-06-19 19:24:53.257 | INFO     | __main__:show_comparison:100 - 3. 移除按钮值字段 (正确)
2025-06-19 19:24:53.257 | INFO     | __main__:show_comparison:101 - 4. x-dtpc从cookies动态获取 (正确)
2025-06-19 19:24:53.258 | INFO     | __main__:show_comparison:102 - 5. 结果: 成功延长会话，收到200响应
2025-06-19 19:24:53.259 | INFO     | __main__:<module>:108 - 测试完成！修复方案已准备就绪。
