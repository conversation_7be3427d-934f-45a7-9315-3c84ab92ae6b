Asw.SessionKeeper={_id:null,_extension_timer:null,_expired_timer:null,_counter:0,_sessionKeepTime:540,_sessionTimeout:590,_deviceType:"PC",_expiredDialogId:"extendSessionExpiredDialog",_timeoutDialogId:"showSessionExpiredDialog",_opener:null,_applicationRoot:Asw.applicationRoot,_init:function(e,c,a,b){var d=this;d._id=e;d._counter=0;d._sessionKeepTime=c;d._sessionTimeout=a;d._deviceType=b;$("form:not([target], [enctype='multipart/form-data']), form[target='_top']:not([enctype='multipart/form-data']), form[target='_self']:not([enctype='multipart/form-data'])").submit(function(){d._end()});$("a:not([target], [href^='#'], [href^='mailto'], [href^='javascript:']), a[target='_top'], a[target='_self']").click(function(){d._end()});$("form[target]:not([target='_top'], [target='_self'])[action*='"+d._applicationRoot+"']").submit(function(){d.resetSessionKeep()});$("a[target]:not([target='_top'], [target='_self'])[href*='"+d._applicationRoot+"']").click(function(){d.resetSessionKeep()});$(function(){var f=window.open;window.open=function(h,g,j,i){if(h.indexOf(d._applicationRoot)===0){Asw.SessionKeeper.resetSessionKeep()}return f(h,g,j,i)}});$(function(){var f=mojarra.jsfcljs;if(typeof f!=="function"){return}mojarra.jsfcljs=function(h,g,i){if(i&&i!=="_self"&&i!=="_top"){Asw.SessionKeeper.resetSessionKeep()}else{Asw.SessionKeeper._end()}f(h,g,i)}});Asw.addAjaxCallback(function(f){if(d._isSessionKeepingRequest(f)){return}switch(f.status){case"begin":d._counter=0;d._end();break;case"success":d._start();break}})},resetSessionKeep:function(){var a=this;a._end();a._counter=0;a._start()},_start:function(a){var c=this;if(c._deviceType!=="SP"){var b={"keyPermission":{"escapeKey":false},"fixedElement":"#displayExpireModalArea"};c._extension_timer=setTimeout(function(){Asw.Dialog.getInstance(c._expiredDialogId).toggle(a,b);c._opener=Asw.Dialog.getInstance(c._expiredDialogId).opener},c._sessionKeepTime*1000);c._expired_timer=setTimeout(function(){var d=Asw.Dialog.getInstance(c._expiredDialogId).opener;Asw.Dialog.getInstance(c._expiredDialogId).close(a,true);Asw.Dialog.getInstance(c._timeoutDialogId).toggle(a,b);Asw.Dialog.getInstance(c._timeoutDialogId).opener=c._opener},c._sessionTimeout*1000)}else{var b={"type":"float","keyPermission":{"escapeKey":false},"fixedElement":"#displayExpireModalArea"};c._extension_timer=setTimeout(function(){Asw.Dialog.getInstance(c._expiredDialogId,b).toggle(a);c._opener=Asw.Dialog.getInstance(c._expiredDialogId).opener},c._sessionKeepTime*1000);c._expired_timer=setTimeout(function(){var d=Asw.Dialog.getInstance(c._expiredDialogId).opener;Asw.Dialog.getInstance(c._expiredDialogId).closeFloatingWindow();Asw.Dialog.getInstance(c._timeoutDialogId,b).toggle(a);Asw.Dialog.getInstance(c._timeoutDialogId).opener=c._opener},c._sessionTimeout*1000)}},_closeExpiredDialog:function(){var a=this;a._closeDialog(a._expiredDialogId)},closeTimeoutDialog:function(){var a=this;a._closeDialog(a._timeoutDialogId)},_closeDialog:function(a){if(this._deviceType!=="SP"){Asw.Dialog.getInstance(a).close()}else{Asw.Dialog.getInstance(a).closeFloatingWindow()}},_end:function(){var a=this;a._clearTimeout(a._extension_timer);a._clearTimeout(a._expired_timer)},initAndStart:function(d,c,a,b){this._init(d,c,a,b);this._start()},_isSessionKeepingRequest:function(a){return a.source.id===this._id+":cmnSessionKeepingButton"},doSessionKeep:function(b){var c=this;var a=b.status;switch(a){case"begin":Asw.LoadingWindow.changeOpenerForAjax(c._opener);c._closeExpiredDialog();c._clearTimeout(c._expired_timer);break;case"complete":c._sessionKeepCountup();if(c._counter<9){c._start()}break}},_sessionKeepCountup:function(){this._counter++},_clearTimeout:function(a){if(a!=null){clearTimeout(a)}}};