# -*- coding: utf-8 -*-
import json
import os
import sys
from flask import Flask, render_template, jsonify, redirect, url_for
from datetime import datetime, timedelta
import traceback
from collections import defaultdict # 导入 defaultdict
from flask_socketio import SocketIO
from threading import Lock
import time # 添加 time 模块导入
import redis
import orjson # <-- 使用 orjson 替换 json

# --- 配置 ---
DATABASE_PATH = os.path.join(os.path.dirname(__file__), '..', 'va_flights.db') # DB文件在上一级目录
PROXY_CONFIG_PATH = os.path.join(os.path.dirname(__file__), '..', 'proxy_config.json') # 代理配置文件路径
HIGHLIGHT_MINUTES = 2
HIDE_HOURS = 2
HIDE_MINUTES = 10
HIDE_TIMEDELTA = timedelta(hours=HIDE_HOURS, minutes=HIDE_MINUTES)
HIGHLIGHT_TIMEDELTA = timedelta(minutes=HIGHLIGHT_MINUTES)
# 消失航线的时间阈值（130分钟）
DISAPPEARED_MINUTES = 130
DISAPPEARED_TIMEDELTA = timedelta(minutes=DISAPPEARED_MINUTES)

app = Flask(__name__)
app.json.ensure_ascii = False # 支持中文字符

REDIS_HOST = os.getenv('REDIS_HOST', 'localhost')
REDIS_PORT = int(os.getenv('REDIS_PORT', '6379'))
REDIS_DB = int(os.getenv('REDIS_DB', '0'))

socketio = SocketIO(app, cors_allowed_origins="*", message_queue=f"redis://{REDIS_HOST}:{REDIS_PORT}/{REDIS_DB}")
redis_client = redis.Redis(host=REDIS_HOST, port=REDIS_PORT, db=REDIS_DB, decode_responses=True)
thread = None
thread_lock = Lock()

# !!! 修改 datetime 添加方式到 Jinja 环境 !!!
app.jinja_env.globals['datetime'] = datetime

# 定义城市列表
AMERICAS_CITIES = ['JFK', 'LAX', 'ORD', 'SFO', 'IAD', 'IAH', 'YVR', 'SEA']
CHINA_JAPAN_CITIES = ['PEK', 'PVG', 'SHA', 'HKG', 'TAO', 'SZX', 'CAN', 'NRT'] # 香港和成田也算在这里
# 定义城市优先级 (用于组内排序)
PRIORITY_CITIES = [
    'JFK', 'LAX', 'ORD', 'SFO', 'IAD', 'IAH', 'YVR', 'SEA', # 美洲优先级
    'PVG', 'SHA', 'PEK', 'CAN', 'HKG', 'TAO', 'SZX', 'NRT'  # 中日优先级
]
priority_map = {city: i for i, city in enumerate(PRIORITY_CITIES)}

# --- 路由 ---
@app.route('/')
def index():
    """根目录，重定向到美洲航线"""
    # 改回指向 americas_routes
    return redirect(url_for('americas_routes'))

@app.route('/americas')
def americas_routes():
    """美洲航线页面 (使用新的 Redis 结构)"""
    flights_data = []
    try:
        # 1. 计算时间戳阈值 (使用 HIDE_TIMEDELTA)
        threshold_dt = datetime.now() - HIDE_TIMEDELTA
        min_timestamp = threshold_dt.timestamp()

        # 2. 从 ZSET 获取活跃航班 key
        active_flight_keys = redis_client.zrangebyscore('flights:active', min_timestamp, '+inf')

        if active_flight_keys:
            # 3. 批量从 Hash 获取航班数据
            # 使用 pipeline 提高效率
            pipe = redis_client.pipeline()
            pipe.hmget('flights:americas:latest', active_flight_keys)
            # 同时获取中日区域的数据，以获取NRT相关的美洲航线
            pipe.hmget('flights:china_japan:latest', active_flight_keys)
            results = pipe.execute()

            # 注意: hmget 对于不存在的 key 会返回 None
            americas_data_raw = results[0] # 第一个 hmget 的结果
            china_japan_data_raw = results[1] # 第二个 hmget 的结果

            valid_flight_json_strings = [item for item in americas_data_raw if item is not None]
            # 合并中日区域的数据
            valid_flight_json_strings.extend([item for item in china_japan_data_raw if item is not None])

            # 4. 使用 orjson 解析 JSON (更快)
            all_flights_raw = [orjson.loads(s) for s in valid_flight_json_strings]

            # 5. 筛选属于美洲的航班 (ZSET 可能包含其他区域的 key，需要二次确认)
            # 这一步筛选理论上可以省略，如果写入时保证了区域划分的准确性
            # 但为了保险起见，可以保留
            filtered_flights = [
                f for f in all_flights_raw
                if f.get('origin') in AMERICAS_CITIES or f.get('destination') in AMERICAS_CITIES
            ]
            flights_data = filtered_flights
        else:
            app.logger.info("[americas_routes] No active flight keys found in ZSET within the time window.")

    except redis.exceptions.RedisError as e:
        app.logger.error(f"[americas_routes] Redis error fetching data: {e}")
        app.logger.error(traceback.format_exc())
        # 出错时返回空列表，页面将显示无数据
    except Exception as e:
        app.logger.error(f"[americas_routes] Error processing flight data: {e}")
        app.logger.error(traceback.format_exc())
        # 出错时返回空列表

    # --- 分组和排序逻辑 (修改以支持NRT-LAX等航线) ---
    inbound_grouped = defaultdict(list)
    outbound_grouped = defaultdict(list)
    for flight in flights_data:
        origin = flight['origin']
        destination = flight['destination']
        # 确保 origin 和 destination 存在
        if not origin or not destination:
            continue
        # 分组逻辑已在筛选时确认过是美洲航线
        if destination == 'HND' or destination == 'NRT':
            inbound_grouped[origin].append(flight)
        elif origin == 'HND' or origin == 'NRT':
            outbound_grouped[destination].append(flight)

    # --- 修改排序逻辑 ---
    # def sort_key_group(group_tuple): # 不再需要
    #     city = group_tuple[0]
    #     return priority_map.get(city, float('inf')), city

    # 从 AMERICAS_CITIES 列表生成分组，确保所有城市都出现
    # 对每个分组内的航班列表进行排序：日期降序，航班号升序
    sorted_inbound_groups = []
    for city in AMERICAS_CITIES:
        flights = inbound_grouped.get(city, [])
        # 1. 按航班号升序排序
        flights.sort(key=lambda f: f.get('flight_numbers', ''))
        # 2. 按日期降序排序 (稳定排序)
        flights.sort(key=lambda f: f.get('date', ''), reverse=False)
        sorted_inbound_groups.append((city, flights))

    sorted_outbound_groups = []
    for city in AMERICAS_CITIES:
        flights = outbound_grouped.get(city, [])
        # 1. 按航班号升序排序
        flights.sort(key=lambda f: f.get('flight_numbers', ''))
        # 2. 按日期降序排序 (稳定排序)
        flights.sort(key=lambda f: f.get('date', ''), reverse=False)
        sorted_outbound_groups.append((city, flights))

    # sorted_inbound_groups = [(city, sorted(inbound_grouped.get(city, []), key=lambda f: (f['date'], f['flight_numbers']))) for city in AMERICAS_CITIES] # 原排序
    # sorted_outbound_groups = [(city, sorted(outbound_grouped.get(city, []), key=lambda f: (f['date'], f['flight_numbers']))) for city in AMERICAS_CITIES] # 原排序

    # 传递原始的 HIGHLIGHT_MINUTES 到模板
    return render_template('americas_flights.html',
                           sorted_inbound_groups=sorted_inbound_groups,
                           sorted_outbound_groups=sorted_outbound_groups,
                           highlight_minutes=HIGHLIGHT_MINUTES)

@app.route('/china_japan')
def china_japan_routes():
    """中日航线页面 (使用新的 Redis 结构)"""
    flights_data = []
    try:
        # 1. 计算时间戳阈值
        threshold_dt = datetime.now() - HIDE_TIMEDELTA
        min_timestamp = threshold_dt.timestamp()

        # 2. 从 ZSET 获取活跃航班 key
        active_flight_keys = redis_client.zrangebyscore('flights:active', min_timestamp, '+inf')

        if active_flight_keys:
            # 3. 批量从 Hash 获取航班数据
            pipe = redis_client.pipeline()
            pipe.hmget('flights:china_japan:latest', active_flight_keys)
            results = pipe.execute()
            china_japan_data_raw = results[0]

            valid_flight_json_strings = [item for item in china_japan_data_raw if item is not None]

            # 4. 使用 orjson 解析 JSON
            all_flights_raw = [orjson.loads(s) for s in valid_flight_json_strings]

            # 5. 筛选属于中日的航班 (可选，增加健壮性)
            filtered_flights = [
                f for f in all_flights_raw
                if f.get('origin') in CHINA_JAPAN_CITIES or f.get('destination') in CHINA_JAPAN_CITIES
            ]
            flights_data = filtered_flights
        else:
             app.logger.info("[china_japan_routes] No active flight keys found in ZSET within the time window.")

    except redis.exceptions.RedisError as e:
        app.logger.error(f"[china_japan_routes] Redis error fetching data: {e}")
        app.logger.error(traceback.format_exc())
    except Exception as e:
        app.logger.error(f"[china_japan_routes] Error processing flight data: {e}")
        app.logger.error(traceback.format_exc())

    # --- 分组和排序逻辑 (修改以支持NRT-LAX等航线) ---
    inbound_grouped = defaultdict(list)
    outbound_grouped = defaultdict(list)
    # 初始化空分组，确保变量已定义
    sorted_inbound_groups = []
    sorted_outbound_groups = []
    if flights_data:
        app.logger.debug(f"[china_japan_routes] 获取到 {len(flights_data)} 条有效记录进行分组")
        for flight in flights_data:
            origin = flight['origin']
            destination = flight['destination']
            if not origin or not destination: continue
            if destination == 'HND' or destination == 'NRT':
                inbound_grouped[origin].append(flight)
            elif origin == 'HND' or origin == 'NRT':
                outbound_grouped[destination].append(flight)

        # --- 修改排序逻辑 ---
        # def sort_key_group(group_tuple): # 不再需要
        #     city = group_tuple[0]
        #     return priority_map.get(city, float('inf')), city

        # 从 CHINA_JAPAN_CITIES 列表生成分组
        # 对每个分组内的航班列表进行排序：日期降序，航班号升序
        for city in CHINA_JAPAN_CITIES:
            flights = inbound_grouped.get(city, [])
            # 1. 按航班号升序排序
            flights.sort(key=lambda f: f.get('flight_numbers', ''))
            # 2. 按日期降序排序 (稳定排序)
            flights.sort(key=lambda f: f.get('date', ''), reverse=False)
            sorted_inbound_groups.append((city, flights))

        # 获取所有目的地城市，包括美洲城市
        all_destinations = set(outbound_grouped.keys())
        app.logger.debug(f"[china_japan_routes] 从东京出发的目的地: {all_destinations}")

        # 先处理中日城市
        for city in CHINA_JAPAN_CITIES:
            flights = outbound_grouped.get(city, [])
            # 1. 按航班号升序排序
            flights.sort(key=lambda f: f.get('flight_numbers', ''))
            # 2. 按日期降序排序 (稳定排序)
            flights.sort(key=lambda f: f.get('date', ''), reverse=False)
            sorted_outbound_groups.append((city, flights))

        # 不再处理美洲城市，只显示中日城市的航线
        # 移除以下代码以避免在中日航线页面显示美洲城市
        # for city in AMERICAS_CITIES:
        #     if city in all_destinations:
        #         flights = outbound_grouped.get(city, [])
        #         flights.sort(key=lambda f: f.get('flight_numbers', ''))
        #         flights.sort(key=lambda f: f.get('date', ''), reverse=False)
        #         sorted_outbound_groups.append((city, flights))
        #         app.logger.debug(f"[china_japan_routes] 添加美洲目的地: {city}, 航班数: {len(flights)}")

        # sorted_inbound_groups = [(city, sorted(inbound_grouped.get(city, []), key=lambda f: (f['date'], f['flight_numbers']))) for city in CHINA_JAPAN_CITIES] # 原排序
        # sorted_outbound_groups = [(city, sorted(outbound_grouped.get(city, []), key=lambda f: (f['date'], f['flight_numbers']))) for city in CHINA_JAPAN_CITIES] # 原排序

    # 中日航线不启用高亮
    return render_template('china_japan_flights.html',
                           sorted_inbound_groups=sorted_inbound_groups,
                           sorted_outbound_groups=sorted_outbound_groups,
                           highlight_minutes=0)

@app.route('/disappeared')
def disappeared_routes():
    """消失航线页面 (包含美洲和中日，分组显示)"""
    # --- 此路由保持 SSR 不变，因为它逻辑不同且可能数据量大 ---
    americas_disappeared = []
    china_japan_disappeared = []
    all_disappeared_flights = [] # 用于计算 max_seats

    try:
        # 1. 计算时间戳阈值 - 使用DISAPPEARED_TIMEDELTA（130分钟）
        threshold_dt = datetime.now() - DISAPPEARED_TIMEDELTA
        max_timestamp = threshold_dt.timestamp() # 获取此时间点之前的数据
        min_timestamp = 0 # 或者更早的时间戳，取决于你想看多旧的数据
        app.logger.info(f"[disappeared_routes] 查找 {DISAPPEARED_MINUTES} 分钟前（{threshold_dt}）之前未更新的航班")

        # 2. 获取所有活跃航班
        active_flight_keys = redis_client.zrangebyscore('flights:active', min_timestamp, '+inf')
        app.logger.info(f"[disappeared_routes] 找到 {len(active_flight_keys)} 个活跃航班")

        # 3. 从对应的 Hash 中批量获取这些 key 的数据
        all_active_flights = []
        if active_flight_keys:
            pipe = redis_client.pipeline()
            pipe.hmget('flights:americas:latest', active_flight_keys)
            pipe.hmget('flights:china_japan:latest', active_flight_keys)
            results = pipe.execute()

            # 合并两个 Hash 的结果，过滤掉 None
            all_active_json = [item for res_list in results for item in res_list if item is not None]
            # 使用 orjson 解析
            all_active_flights = [orjson.loads(s) for s in all_active_json]
            app.logger.info(f"[disappeared_routes] 解析了 {len(all_active_flights)} 个活跃航班数据")

        # 4. 筛选出最后更新时间超过阈值的航班，且日期是未来的
        all_disappeared_flights = []
        now = datetime.now()
        today = now.strftime('%Y-%m-%d')

        for flight in all_active_flights:
            last_updated = flight.get('last_updated_timestamp')
            flight_date = flight.get('date')

            if not last_updated or not flight_date:
                continue

            # 检查航班日期是否是未来的（大于等于今天）
            if flight_date < today:
                continue  # 跳过过去的航班

            try:
                # 解析时间戳
                if 'T' in last_updated:
                    # ISO格式
                    timestamp_to_parse = last_updated.split('.')[0]
                    last_updated_dt = datetime.strptime(timestamp_to_parse, '%Y-%m-%dT%H:%M:%S')
                else:
                    # 普通格式
                    last_updated_dt = datetime.strptime(last_updated, '%Y-%m-%d %H:%M:%S')

                # 检查是否超过阈值
                if (now - last_updated_dt) > DISAPPEARED_TIMEDELTA:
                    all_disappeared_flights.append(flight)
            except Exception as e:
                app.logger.error(f"[disappeared_routes] 解析时间戳失败: {last_updated}, error: {e}")
                continue

        app.logger.info(f"[disappeared_routes] 找到 {len(all_disappeared_flights)} 个超过 {DISAPPEARED_MINUTES} 分钟未更新且日期是未来的航班")

        # 5. 分组到美洲和中日列表
        for flight in all_disappeared_flights:
            origin = flight.get('origin')
            destination = flight.get('destination')
            if origin and destination:
                if origin in AMERICAS_CITIES or destination in AMERICAS_CITIES:
                    americas_disappeared.append(flight)
                elif origin in CHINA_JAPAN_CITIES or destination in CHINA_JAPAN_CITIES:
                    china_japan_disappeared.append(flight)
                # else: # 可以选择忽略其他区域
        app.logger.info(f"[disappeared_routes] 分组结果: 美洲={len(americas_disappeared)}, 中日={len(china_japan_disappeared)}.")

        # 6. 对两个列表分别排序 (按最后更新时间倒序，最新的在前面)
        americas_disappeared.sort(key=lambda f: f.get('last_updated_timestamp', ''), reverse=True)
        china_japan_disappeared.sort(key=lambda f: f.get('last_updated_timestamp', ''), reverse=True)

        # 7. 按城市分组
        americas_disappeared_groups = defaultdict(list)
        china_japan_disappeared_groups = defaultdict(list)

        # 美洲航线分组
        for flight in americas_disappeared:
            origin = flight.get('origin')
            destination = flight.get('destination')

            # 确定分组的城市
            if origin in AMERICAS_CITIES:
                group_city = origin
            elif destination in AMERICAS_CITIES:
                group_city = destination
            else:
                continue  # 跳过无法确定分组的航班

            americas_disappeared_groups[group_city].append(flight)

        # 中日航线分组
        for flight in china_japan_disappeared:
            origin = flight.get('origin')
            destination = flight.get('destination')

            # 确定分组的城市
            if origin in CHINA_JAPAN_CITIES:
                group_city = origin
            elif destination in CHINA_JAPAN_CITIES:
                group_city = destination
            else:
                continue  # 跳过无法确定分组的航班

            china_japan_disappeared_groups[group_city].append(flight)

        # 8. 按城市优先级排序分组
        sorted_americas_disappeared_groups = []
        for city in AMERICAS_CITIES:
            if city in americas_disappeared_groups:
                sorted_americas_disappeared_groups.append((city, americas_disappeared_groups[city]))

        sorted_china_japan_disappeared_groups = []
        for city in CHINA_JAPAN_CITIES:
            if city in china_japan_disappeared_groups:
                sorted_china_japan_disappeared_groups.append((city, china_japan_disappeared_groups[city]))

    except redis.exceptions.RedisError as e:
        app.logger.error(f"[disappeared_routes] Redis error fetching potentially disappeared data: {e}")
        # 保留空列表
    except Exception as e:
        app.logger.error(f"[disappeared_routes] Error processing potentially disappeared data: {e}")
        # 保留空列表

    # --- 计算历史最大座位数 (对所有找到的消失航班) ---
    history_map = {}
    try:
        # 优化：只获取最近N条历史记录？例如最近 5000 条？ lrange(key, 0, 4999)
        history_items = redis_client.lrange('flights:history', 0, -1) # 保持获取全部，如果性能可接受
        app.logger.debug(f"[disappeared_routes] Fetched {len(history_items)} items from flights:history.")
        for item in history_items:
            try:
                data = orjson.loads(item)
                nums = data.get('flight_numbers')
                if not nums: continue
                cabin = data.get('cabin_class', '')
                # 兼容旧 key 格式（如果有）或新 key 格式
                hkey = f"{data.get('date', '')}_{data.get('origin', '')}_{data.get('destination', '')}_{nums}_{cabin}"
                history_map.setdefault(hkey, []).append(data.get('current_seats', 0))
            except orjson.JSONDecodeError:
                app.logger.warning(f"[disappeared_routes] Failed to decode history item: {item[:100]}...")
                continue
        app.logger.debug(f"[disappeared_routes] Built history_map with {len(history_map)} keys.")
    except redis.exceptions.RedisError as e:
         app.logger.error(f"[disappeared_routes] Redis error fetching history: {e}")

    # --- 为消失的航班添加 max_seats (遍历分组数据) ---
    def add_max_seats(flight_list):
        for flight in flight_list:
            key = f"{flight.get('date', '')}_{flight.get('origin', '')}_{flight.get('destination', '')}_{flight.get('flight_numbers', '')}_{flight.get('cabin_class', '')}"
            seats_history = history_map.get(key, [])
            try:
                # 如果历史记录为空，max_seats 就是当前记录的座位数（如果存在）
                current_seats_val = flight.get('current_seats')
                flight['max_seats'] = max(seats_history) if seats_history else (current_seats_val if current_seats_val is not None else 0)
            except Exception as e:
                app.logger.error(f"计算 max_seats 异常 for {key}: {e}")
                flight['max_seats'] = flight.get('current_seats', 0)

    # 为所有航班添加max_seats
    for flight in americas_disappeared:
        add_max_seats([flight])

    for flight in china_japan_disappeared:
        add_max_seats([flight])

    # 传递分组数据到模板
    return render_template('disappeared_flights.html',
                           title='消失航线',
                           americas_disappeared_groups=sorted_americas_disappeared_groups,
                           china_japan_disappeared_groups=sorted_china_japan_disappeared_groups)

@app.route('/api/history/<flight_key>')
def flight_history(flight_key):
    """获取指定航班的历史座位记录 API"""
    history = []
    # 解析 flight_key (格式: date_origin_destination_flight_numbers_cabin_class)
    try:
        parts = flight_key.split('_')
        if len(parts) != 5:
            return jsonify({'error': 'Invalid flight key format'}), 400
        # 我们只需要验证格式，不需要使用这些变量
        # f_date, f_origin, f_dest, f_nums, f_cabin = parts
    except Exception as e:
         app.logger.error(f"解析 flight_key 失败: {flight_key}, error: {e}")
         return jsonify({'error': 'Error parsing flight key'}), 400

    # 从 Redis 列表获取历史记录并去重（保留每个座位数的最新记录）
    try:
        # !!! FIX: 使用正确的 Redis Key 'flights:history' !!!
        # !!! OPTIMIZE: Limit the range to avoid loading huge lists !!!
        history_limit = 200000 # 设置一个合理的历史记录条数限制
        app.logger.debug(f"[API History {flight_key}] Fetching latest {history_limit} items from flights:history")
        raw_list = redis_client.lrange('flights:history', 0, history_limit - 1)
    except Exception as e:
        app.logger.error(f"从 Redis 获取历史记录失败: {e}")
        return jsonify({'error': 'Redis query failed'}), 500

    seen_seats = set()
    processed_count = 0 # Debug: 记录处理了多少条
    matched_count = 0 # Debug: 记录匹配了多少条

    for item_json in raw_list:
        processed_count += 1
        try:
            data = json.loads(item_json)
            # Debug: 打印正在处理的数据的关键信息
            # app.logger.debug(f"[API History {flight_key}] Processing item: {data.get('date')} {data.get('origin')}->{data.get('destination')} Seats: {data.get('current_seats')} TS: {data.get('last_updated_timestamp')}")
        except Exception as parse_e:
            # app.logger.warning(f"[API History {flight_key}] Failed to parse history item: {parse_e}. Item: {item_json[:100]}...")
            continue # 跳过无法解析的记录

        key = f"{data.get('date', '')}_{data.get('origin', '')}_{data.get('destination', '')}_{data.get('flight_numbers', '')}_{data.get('cabin_class', '')}"

        # Debug: 检查 key 是否匹配
        # app.logger.debug(f"[API History {flight_key}] Comparing: API Key='{flight_key}' vs Record Key='{key}'")

        if key != flight_key:
            continue # 跳过不匹配的航班

        matched_count += 1 # 匹配成功
        seat = data.get('current_seats')

        # Debug: 打印匹配到的记录和座位信息
        # app.logger.debug(f"[API History {flight_key}] Matched flight. Seat: {seat}, Seen: {seat in seen_seats}")

        if seat is None: # 跳过没有座位信息的记录
             continue

        if seat in seen_seats:
            continue # 如果这个座位数已经从更新的记录中获取过，则跳过

        seen_seats.add(seat)
        history.append({'seats': seat, 'timestamp': data.get('last_updated_timestamp')})
        # Debug: 记录添加到最终结果的记录
        # app.logger.debug(f"[API History {flight_key}] Added to result: Seat={seat}, Timestamp={data.get('last_updated_timestamp')}")

    # Debug: 打印最终处理统计
    app.logger.debug(f"[API History {flight_key}] Processed {processed_count} history items, matched {matched_count} items for this flight. Found timestamps for {len(history)} unique seat counts.")

    # 对返回的历史记录做本地时区校正（减去 8 小时）
    for rec in history:
        try:
            # 假设存储的是 YYYY-MM-DD HH:MM:SS 格式的北京时间
            dt = datetime.strptime(rec['timestamp'], '%Y-%m-%d %H:%M:%S')
            # !!! REMOVE UTC CONVERSION: Directly format the Beijing time !!!
            rec['timestamp'] = dt.strftime('%Y-%m-%d %H:%M:%S')
            # rec['timestamp'] = (dt - timedelta(hours=8)).strftime('%Y-%m-%d %H:%M:%S') # 移除减8小时
        except (ValueError, TypeError):
            # 如果时间戳格式不正确或不是字符串，则保留原始值
            app.logger.warning(f"[API History {flight_key}] Could not parse timestamp: {rec.get('timestamp')}")
            pass # 保留原始时间戳字符串
    return jsonify(history)

@socketio.on('connect')
def handle_connect():
    """处理客户端连接，启动后台更新线程（如果需要）"""
    global thread
    with thread_lock:
        if thread is None:
            app.logger.info("客户端已连接，启动后台更新线程。")
            # 使用 socketio.start_background_task 启动线程
            thread = socketio.start_background_task(target=background_thread)
        else:
            app.logger.info("客户端已连接，后台更新线程已在运行。")
    # 注意：不在此处立即发送数据，等待客户端请求

@socketio.on('request_initial_data')
def handle_request_initial_data(data):
    """处理客户端请求初始数据"""
    page = data.get('page', '')
    app.logger.info(f"客户端请求初始数据: {page}")

    try:
        # 根据页面类型获取不同的数据
        if page == 'americas':
            # 获取美洲航线数据
            flights_data = get_americas_flights_data()
            socketio.emit('update_data', {'data': flights_data}, namespace='/')
            app.logger.info(f"已发送美洲航线初始数据: {len(flights_data)} 条记录")
        elif page == 'china_japan':
            # 获取中日航线数据
            flights_data = get_china_japan_flights_data()
            socketio.emit('update_data', {'data': flights_data}, namespace='/')
            app.logger.info(f"已发送中日航线初始数据: {len(flights_data)} 条记录")
    except Exception as e:
        app.logger.error(f"处理初始数据请求失败: {e}")
        app.logger.error(traceback.format_exc())

def get_americas_flights_data():
    """获取美洲航线数据"""
    flights_data = []
    try:
        # 计算时间戳阈值
        threshold_dt = datetime.now() - HIDE_TIMEDELTA
        min_timestamp = threshold_dt.timestamp()

        # 从 ZSET 获取活跃航班 key
        active_flight_keys = redis_client.zrangebyscore('flights:active', min_timestamp, '+inf')

        if active_flight_keys:
            # 批量从 Hash 获取航班数据
            pipe = redis_client.pipeline()
            pipe.hmget('flights:americas:latest', active_flight_keys)
            pipe.hmget('flights:china_japan:latest', active_flight_keys)
            results = pipe.execute()

            americas_data_raw = results[0]
            china_japan_data_raw = results[1]

            valid_flight_json_strings = [item for item in americas_data_raw if item is not None]
            valid_flight_json_strings.extend([item for item in china_japan_data_raw if item is not None])

            all_flights_raw = [orjson.loads(s) for s in valid_flight_json_strings]

            filtered_flights = [
                f for f in all_flights_raw
                if f.get('origin') in AMERICAS_CITIES or f.get('destination') in AMERICAS_CITIES
            ]
            flights_data = filtered_flights
    except Exception as e:
        app.logger.error(f"获取美洲航线数据失败: {e}")
        app.logger.error(traceback.format_exc())

    return flights_data

def get_china_japan_flights_data():
    """获取中日航线数据"""
    flights_data = []
    try:
        # 计算时间戳阈值
        threshold_dt = datetime.now() - HIDE_TIMEDELTA
        min_timestamp = threshold_dt.timestamp()

        # 从 ZSET 获取活跃航班 key
        active_flight_keys = redis_client.zrangebyscore('flights:active', min_timestamp, '+inf')

        if active_flight_keys:
            # 批量从 Hash 获取航班数据
            pipe = redis_client.pipeline()
            pipe.hmget('flights:china_japan:latest', active_flight_keys)
            results = pipe.execute()
            china_japan_data_raw = results[0]

            valid_flight_json_strings = [item for item in china_japan_data_raw if item is not None]
            all_flights_raw = [orjson.loads(s) for s in valid_flight_json_strings]

            filtered_flights = [
                f for f in all_flights_raw
                if f.get('origin') in CHINA_JAPAN_CITIES or f.get('destination') in CHINA_JAPAN_CITIES
            ]
            flights_data = filtered_flights
    except Exception as e:
        app.logger.error(f"获取中日航线数据失败: {e}")
        app.logger.error(traceback.format_exc())

    return flights_data

def background_thread():
    """后台线程：订阅 Redis PubSub 航班更新并推送给客户端"""
    app.logger.info("后台更新 PubSub 线程已启动。")
    pubsub = redis_client.pubsub(ignore_subscribe_messages=True)
    pubsub.subscribe('flights_update')
    for message in pubsub.listen():
        if message and message['type'] == 'message':
            try:
                flight = json.loads(message['data'])
                socketio.emit('update_data', {'data': [flight]}, namespace='/')
                app.logger.debug(f"PubSub 推送更新行: {flight['date']}_{flight['origin']}_{flight['destination']}_{flight['flight_numbers']}_{flight['cabin_class']}")
            except Exception as e:
                app.logger.error(f"处理 PubSub 消息失败: {e}")

if __name__ == '__main__':
    # 使用 socketio.run() 启动应用
    app.logger.info("启动 Flask-SocketIO 服务器...")
    socketio.run(app, debug=True, host='0.0.0.0', port=5000, use_reloader=False, allow_unsafe_werkzeug=True) # allow unsafe Werkzeug for Docker
