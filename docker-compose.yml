version: '3.8'

services:
  redis:
    image: redis:7.0-alpine
    container_name: va_redis
    restart: always
    volumes:
      - redisdata:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf
    command: ["redis-server", "/usr/local/etc/redis/redis.conf"]
    ports:
      - "6379:6379"
    environment:
      - TZ=Asia/Shanghai

  web:
    build: .
    container_name: va_web
    restart: on-failure
    depends_on:
      - redis
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DB=0
      - TZ=Asia/Shanghai
    ports:
      - "5000:5000"
    # Use eventlet worker for WebSocket support
    command: ["gunicorn", "--worker-class", "eventlet", "-w", "1", "--bind", "0.0.0.0:5000", "web.app:app"]
    logging:
      driver: "json-file"
      options:
        max-size: "500m"
        max-file: "5"

  monitor:
    build: .
    container_name: va_monitor
    restart: on-failure
    depends_on:
      - redis
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DB=0
      - TZ=Asia/Shanghai
    # Path is now relative to /app workdir
    command: ["python", "va_main_optimized.py"]
    logging:
      driver: "json-file"
      options:
        max-size: "500m"
        max-file: "3"

  token:
    build: .
    container_name: va_token
    restart: on-failure
    depends_on:
      - redis
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DB=0
      - TZ=Asia/Shanghai
    # Path is now relative to /app workdir
    command: ["python", "web/reese84/main.py"]
    logging:
      driver: "json-file"
      options:
        max-size: "500m"
        max-file: "3"

  seat_holder:
    build: .
    container_name: va_seat_holder
    restart: on-failure
    depends_on:
      - redis
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DB=0
      - TZ=Asia/Shanghai
    # 运行我们修改后的占位脚本
    command: ["python", "ana_search.py"]
    logging:
      driver: "json-file"
      options:
        max-size: "500m"
        max-file: "3"

volumes:
  redisdata: