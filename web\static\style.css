/* 自定义样式可以写在这里 */

/* 调整表格单元格内边距，使内容更均匀分布 */
.table.table-sm td,
.table.table-sm th {
    padding-top: 0.1rem !important;
    padding-bottom: 0.1rem !important;
    padding-left: 0.5rem !important;
    padding-right: 0.5rem !important;
    line-height: 1.2 !important;
}

/* 移除表格滚动条 */
.table-responsive {
    overflow-x: visible !important;
    overflow-y: visible !important;
    -ms-overflow-style: none; /* IE/Edge */
    scrollbar-width: none; /* Firefox */
}
.table-responsive::-webkit-scrollbar {
    display: none;
}
/* 禁止表格单元格换行 */
.table-responsive table,
.table-responsive table td,
.table-responsive table th {
    white-space: nowrap !important;
}

/* 表格列对齐方式，禁止换行 */
.table-fixed td {
    text-align: center;
    white-space: nowrap;
}
.table-fixed td:first-child { text-align: left; }
.table-fixed td:nth-child(2) { text-align: left; }
.table-fixed td:nth-child(3) { text-align: center; }
.table-fixed td:nth-child(4) { text-align: center; }
.table-fixed td:nth-child(5) { text-align: center; }
.table-fixed td:last-child { text-align: right; }

/* 针对移动设备的样式调整 */
@media (max-width: 767px) {
    /* 给主容器设置一个最小宽度，强制内容不换行且并排显示 */
    main.container {
        min-width: 1200px; /* 可以根据实际内容调整宽度 */
        /* 移除 Bootstrap 的 padding，如果需要占满屏幕宽度 */
        /* padding-left: 0; */
        /* padding-right: 0; */
    }
    /* 确保 row 和 col-6 正常工作 */
    .row {
        /* 可能不需要特殊处理 */
    }
    .col-6 {
        /* 可能不需要特殊处理 */
    }
    /* 恢复 table-responsive 的默认 overflow，因为外部容器控制宽度 */
    .table-responsive {
       overflow-x: visible !important; /* 允许内容溢出到宽容器中 */
    }
}