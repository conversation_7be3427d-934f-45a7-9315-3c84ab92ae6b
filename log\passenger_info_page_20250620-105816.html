<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="ja-<PERSON>" xml:lang="ja-<PERSON>">

	
	<!-- skey = 2025/06/20 11:58:16 rei22c x2aLRZ8egh dljdmx+3ce  --><head id="j_idt53">
    <base href="about:blank">
    <style>
        /* Hide elements that might try to load external resources, as a fallback. */
        img, svg, video, iframe, link[rel="stylesheet"], script { display: none !important; }
    </style>

		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
			<meta http-equiv="X-UA-Compatible" content="IE=EmulateIE9; IE=EmulateIE10" />
			<meta http-equiv="X-UA-Compatible" content="IE=7" />
			<meta http-equiv="X-UA-Compatible" content="IE=edge" />
		<meta name="description" content="" />
		<meta name="keywords" content="" />
		<meta name="format-detection" content="telephone=no" />
		<script type="text/javascript" src="/international_asw/dynatrace/ruxitagentjs_ICANVfhqrux_10273230920145353.js" data-dtconfig="rid=RID_1861316877|rpid=-1885015616|domain=aswbe-i.ana.co.jp|reportUrl=https://bf90419wvr.bf.dynatrace.com/bf|app=78bf0b58acf6ed13|cors=1|owasp=1|featureHash=ICANVfhqrux|xb=www^bs.google-analytics^bs.com^pana^bs.demdex^bs.net^pcollect^bs.tealiumiq^bs.com^pallnipponairways^bs.australia-3^bs.evergage^bs.com^pallnipponairwayscolt^bs.tt^bs.omtrdc^bs.net|rdnt=1|uxrgce=1|cuc=k8vpgh61|mel=100000|expw=1|dpvc=1|md=mdcc1=bAsw.PnrOutput.recordLocator,mdcc2=bAsw.PnrOutput.paymentDetailInfoList[0].fopCode,mdcc3=bAsw.PnrOutput.ticketedFlag,mdcc4=bAsw.PnrOutput.nhPnrFlag,mdcc5=bAsw.PnrOutput.itinerarySummary.departureDateYyyymmdd,mdcc6=bAsw.SearchCriteriaOutput.cffCodeInput,mdcc7=a#main ^rb div.section.sectionNoFlow ^rb table ^rb tbody ^rb tr ^rb td.flightNumber.lastCol ^rb span ^rb span:nth-child(2),mdcc8=cunique|lastModification=1750184416409|tp=500,50,0|agentUri=/international_asw/dynatrace/ruxitagentjs_ICANVfhqrux_10273230920145353.js"></script><link rel="shortcut icon" href="https://www.ana.co.jp/favicon.ico" />
		<title>お客様情報入力 | ANA</title>
		<noscript><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/css/asw_noscript_pc.css?99001fc" />
		</noscript>

		<!--  [ Adobe Target ] -->
		<script type="text/javascript" src="https://www.ana.co.jp/behavior/adobetarget/target_intbe.js"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/script/jquery-1.8.3.min.js?717d3c0"></script><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/ja/css/asw_initialize.css?99001fc" /><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/css/asw_screen_pc.css?99001fc" /><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/css/asw_common_pc.css?99001fc" /><link type="text/css" rel="stylesheet" href="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/css/asw_forminput_pc.css?99001fc" /><script type="text/javascript">
		var Asw = Asw || {};
		Asw.GlobalWord = Asw.GlobalWord || {};
		
		Asw.GlobalWord.toolTipCloseIconAltText = "閉じる";
		
		</script><script >bazadebezolkohpepadr="2048572912"</script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/akam/13/7a1abcfe"  defer></script></head><body>
		
		<noscript>当サイトをご利用になるためには、JavaScript対応のブラウザが必要です。設定でJavaScriptを有効にしてください。有効にできない場合はANA電話窓口へお問い合わせください。
		</noscript>
		<div id="preLoadingArea"></div>
	
	<div id="cmnHeader" class="platinum">
		<!--===== cmnHeader =====-->
		<div class="headerWrapper">
			<div class="header">
				<p class="logo"><a href="https://www.ana.co.jp/asw/AMCTopServlet?type=a" tabindex="-1" aria-hidden="true"><img src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/image/ana_logo_navy.png?717d3c0" alt="ANA Inspiration of JAPAN。リンク先はマイレージクラブトップページです。" height="28" width="274" /></a>
				</p>
					<ul class="headerMenu">
						<li><a href="https://www.ana.co.jp/asw/AMCTopServlet?type=a">ANAマイレージクラブトップページ</a>
						</li>
						<li><a href="https://www.ana.co.jp/other/int/meta/0005.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">お問い合わせ<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
						</li>
							<li class="btnLogoutArea"><a href="https://aswbe-i.ana.co.jp/rei22c/international_asw/rest/logout" role="button" class="btnBase btnLogout">ログアウト</a>
							</li>
					</ul>
			</div>
		</div>
			<div class="amcUserInfoWrapper">
				<div class="amcUserInfo">
					<div class="userNameArea">
						<table>
							<tr>	
									<td class="statusIcon"><img src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/image/icon_status_05.png?717d3c0" alt="プラチナサービスメンバー" height="46" width="60" />
									</td>
								<td class="userName">佐藤 直樹 <span>様</span>
								</td>
							</tr>					
						</table>
					</div>
						<div class="balance">
								<dl class="upgradePointBalance">
									<dt>アップグレードポイント残高
									</dt>
									<dd>
											<dl>
												<dt>(2025年度)
												</dt>
												<dd>
													<em>20
													</em>ポイント
												</dd>
											</dl>
									</dd>
								</dl>
							<dl class="mileBalance">
								<dt>マイル口座残高
								</dt>
								<dd><em>75,663</em><span class="currencyCode">マイル</span>
								</dd>
							</dl>
						</div>
				</div>
					<p class="nowTime">6月20日11時58分現在
					</p>
			</div>
		<!--===== /cmnHeader =====-->
	</div>
		<div id="sessionKeeperContainer" class="sessionKeepingContainer"><img src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/image/bg_modalWindow.png?717d3c0" alt="" height="0" width="0" />
			<div id="extendSessionExpiredDialog" class="modalExpire modalSmall modalContainer" role="dialog" aria-labelledby="extendSessionExpiredDialogTitle" aria-hidden="true">
<form id="sessionKeeperContainer:j_idt183" name="sessionKeeperContainer:j_idt183" method="post" action="https://aswbe-i.ana.co.jp/rei22c/international_asw/pages/award/reservation/award_mandatory_passenger_information_input.xhtml?aswcid=1&amp;rand=20250620115816x2aLRZ8egh" autocomplete="off" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="sessionKeeperContainer:j_idt183" value="sessionKeeperContainer:j_idt183" />
<input type="hidden" name="sessionKeeperContainer:j_idt183_operationTicket" value="dljdmx+3ce76cdd3313e70fb33c4387a9d83db2~SThu3AoiYCrLP0GaMTotBXQDWcC3VTIfoBbCVqr3!1750388288308.aere-xml-controller-67d4778877-zwdk2" /><input type="hidden" name="sessionKeeperContainer:j_idt183_cmnPageTicket" value="4" />
					<h1 id="extendSessionExpiredDialogTitle" class="dialogTitle">まもなくセッションが終了します</h1>
					<div id="extendSessionExpiredDialogMessage" class="dialogMessage" tabindex="-1">このままお手続きを進める場合は、［延長］ボタンを押してください。</div>
					<p class="modalButton"><input id="sessionKeeperContainer:cmnSessionKeepingButton" type="submit" name="sessionKeeperContainer:cmnSessionKeepingButton" value="延長" class="btnBase btnModal btnMainStream" onclick="mojarra.ab(this,event,'action',0,0,{'onevent':function(data) {Asw.doCommonAjaxCallback(data,false, event); Asw.SessionKeeper.doSessionKeep(data);},'onerror':function(data) {Asw.doCommonAjaxErrorCallback(data); }});return false" />
					</p><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:0" value="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" autocomplete="off" />
</form>
			</div>
			<div id="showSessionExpiredDialog" class="modalExpire modalSmall modalContainer" role="dialog" aria-labelledby="showSessionExpiredDialogTitle" aria-hidden="true">
				<h1 id="showSessionExpiredDialogTitle" class="dialogTitle">セッションが終了しました</h1>
				<div id="showSessionExpiredDialogMessage" class="dialogMessage" tabindex="-1">一定時間操作されなかったため、お手続きを進めることができません。お手数ですが、再度トップページからお手続きください。</div>
				<p class="modalButton">
					<input type="submit" value="閉じる" class="btnBase btnModal btnMainStream" onclick="Asw.SessionKeeper.closeTimeoutDialog(); return false;" />
				</p>
			</div>
		</div>
		<!--===== dialogMessages =====--><div id="cmnModalMessages"></div>
		<div id="prebookConfirmDialog" class="modalContainer modalConfirm modalSmall " aria-hidden="true" role="dialog">
<form id="prebookConfirmDialog:j_idt403" name="prebookConfirmDialog:j_idt403" method="post" action="https://aswbe-i.ana.co.jp/rei22c/international_asw/pages/award/reservation/award_mandatory_passenger_information_input.xhtml?aswcid=1&amp;rand=20250620115816x2aLRZ8egh" autocomplete="off" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="prebookConfirmDialog:j_idt403" value="prebookConfirmDialog:j_idt403" />
<input type="hidden" name="prebookConfirmDialog:j_idt403_operationTicket" value="dljdmx+3ce76cdd3313e70fb33c4387a9d83db2~SThu3AoiYCrLP0GaMTotBXQDWcC3VTIfoBbCVqr3!1750388288308.aere-xml-controller-67d4778877-zwdk2" /><input type="hidden" name="prebookConfirmDialog:j_idt403_cmnPageTicket" value="4" /><div class="dialogMessage" tabindex="0"><p>この後、名・姓の修正や変更ができません。</p><p>「搭乗者選択」欄の氏名が「名」「姓」の順番で表示されていることを再確認ください。</p><p>例）<strong class="highlight">空野 太郎</strong> 様 → MR <strong class="highlight">TARO SORANO</strong></p><p>保安上の理由により、航空券とパスポートのお名前が同じ表記でない場合はご搭乗いただくことができませんので必ず再度ご確認ください。</p></div>
					
					<ul class="modalBtnHorizontal">
							
							<li><input type="submit" name="prebookConfirmDialog:j_idt419" value="戻る" aria-controls="prebookConfirmDialog" class="btnBase btnModal btnCancel" onclick="Asw.Dialog.getInstance('prebookConfirmDialog').close(event, Asw.DialogSequencer.isActiveSequencerNotDisplayedLastDialog());  return false;" />
							</li>
						
						<li class="btnArrowNext"><input type="submit" name="prebookConfirmDialog:j_idt422" value="OK" aria-controls="prebookConfirmDialog" class="btnBase btnModal btnMainStream " onclick="Asw.Dialog.getInstance('prebookConfirmDialog').callOpener('NORMAL', event); return false;" />
						</li>
					</ul><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:1" value="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" autocomplete="off" />
</form>
		</div>
		<!--===== /dialogMessages =====--><div id="cmnContainer">
		<!--=========== .cmnContainer ============--><div id="cmnEmergencyNotice"></div><div id="cmnTitle" class="hgroup ">
		<h1 class="visuallyHidden">お客様情報入力</h1></div>

		<div id="cmnWrapper">
		<!--===== cmnWrapper =====-->
		<ol class="mainFlow" aria-label="完了までの手順、">
			<li><img src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/image/icon_flow_01.png?717d3c0" alt="1" height="20" width="28" />フライト検索</li><!--
			--><li><img src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/image/icon_flow_02_on.png?717d3c0" alt="2" height="20" width="28" /><span class="visuallyHidden">現在のステップ、</span>お客様情報入力</li><!--
			--><li><img src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/image/icon_flow_03.png?717d3c0" alt="3" height="20" width="28" />お支払い情報</li><!--
			--><li class="flowLast"><img src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/image/icon_flow4_04.png?717d3c0" alt="4" height="20" width="28" />完了</li>
		</ol>

	<div id="main">
	<!--=========== main =============-->
		<ul class="iconExample">
				<li class="requiredIconExample">
					<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="" height="7" width="7" /></span>必須項目です	
				</li>
		</ul>
<form id="j_idt526" name="j_idt526" method="post" action="https://aswbe-i.ana.co.jp/rei22c/international_asw/pages/award/reservation/award_mandatory_passenger_information_input.xhtml?aswcid=1&amp;rand=20250620115816x2aLRZ8egh" autocomplete="off" enctype="application/x-www-form-urlencoded">
<input type="hidden" name="j_idt526" value="j_idt526" />
<input type="hidden" name="j_idt526_operationTicket" value="dljdmx+3ce76cdd3313e70fb33c4387a9d83db2~SThu3AoiYCrLP0GaMTotBXQDWcC3VTIfoBbCVqr3!1750388288308.aere-xml-controller-67d4778877-zwdk2" /><input type="hidden" name="j_idt526_cmnPageTicket" value="4" />

<div class="section">
	<h2>搭乗者選択<a href="https://www.ana.co.jp/other/int/meta/0086.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="特典のご利用に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a></h2>
	<table class="passengerInfo">
		<thead>
			<tr>
				
				<th class="name">氏名</th>
				
				<th class="dateOfBirthWide" id="dateOfBirthHeader">生年月日</th>
				
				<th class="gender lastCol" id="genderHeader">性別<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span></th>
				
			</tr>
		</thead>
		<tbody>
				
				<tr>
					<th class="parent_01" id="parentHeader01">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:0:passenger" type="checkbox" name="passengers:0:passenger" checked="checked" data-number="1" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:0:passenger">NAOKI SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader01">1963年06月05日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader01">				
							<span class="formWrapper">
							<label for="passengers:0:gender" class="visuallyHidden">NAOKI SATOの性別</label><select id="passengers:0:gender" name="passengers:0:gender" size="1">	<option value="">選択してください</option>
	<option value="M" selected="selected">男性</option>
	<option value="F">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_02" id="parentHeader02">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:1:passenger" type="checkbox" name="passengers:1:passenger" data-number="2" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:1:passenger">REO SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader02">1995年05月18日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader02">				
							<span class="formWrapper">
							<label for="passengers:1:gender" class="visuallyHidden">REO SATOの性別</label><select id="passengers:1:gender" name="passengers:1:gender" size="1">	<option value="">選択してください</option>
	<option value="M" selected="selected">男性</option>
	<option value="F">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_03" id="parentHeader03">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:2:passenger" type="checkbox" name="passengers:2:passenger" data-number="3" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:2:passenger">ERI SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader03">1963年08月14日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader03">				
							<span class="formWrapper">
							<label for="passengers:2:gender" class="visuallyHidden">ERI SATOの性別</label><select id="passengers:2:gender" name="passengers:2:gender" size="1">	<option value="">選択してください</option>
	<option value="M">男性</option>
	<option value="F" selected="selected">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_04" id="parentHeader04">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:3:passenger" type="checkbox" name="passengers:3:passenger" data-number="4" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:3:passenger">TAISHI SATO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader04">1992年12月03日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader04">				
							<span class="formWrapper">
							<label for="passengers:3:gender" class="visuallyHidden">TAISHI SATOの性別</label><select id="passengers:3:gender" name="passengers:3:gender" size="1">	<option value="">選択してください</option>
	<option value="M" selected="selected">男性</option>
	<option value="F">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
				
				<tr>
					<th class="parent_05" id="parentHeader05">
						<div class="passengerInfoInputArea">
							<span><input id="passengers:4:passenger" type="checkbox" name="passengers:4:passenger" data-number="5" disabled="disabled" />
							</span>
						</div>
						
						<div class="passengerInfoInputText">
							<span>
								<label for="passengers:4:passenger">HIROMI FUJIMOTO 
								</label>
							</span>
						</div>
					</th>
					
					<td headers="dateOfBirthHeader parentHeader05">1961年11月23日
					</td>
					
					<td class="lastCol" headers="genderHeader parentHeader05">				
							<span class="formWrapper">
							<label for="passengers:4:gender" class="visuallyHidden">HIROMI FUJIMOTOの性別</label><select id="passengers:4:gender" name="passengers:4:gender" size="1">	<option value="">選択してください</option>
	<option value="M">男性</option>
	<option value="F" selected="selected">女性</option>
	<option value="X">特定しない(X)</option>
	<option value="U">公開しない(U)</option>
</select>
							</span>
					</td>
					
				</tr>
		</tbody>
	</table>
</div>
		<div class="section">
			<h2 class="midstream">連絡先情報
			</h2>
			<h3>代表者
			</h3>
	
	
	
	<div class="formInput">
		<fieldset>
			<legend><label>代表者</label></legend>
			<dl id="contactEmailAddressInfo">
				<dt class="hasRequiredIcon">メールアドレス<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span><a href="https://www.ana.co.jp/other/int/meta/0057.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="メールの宛先と内容に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a>
				</dt>
				<dd>
					<ul class="verticalList">
						<li class="nonMarginTop"><label for="representativeEmail" class="visuallyHidden">代表者メールアドレス</label><input id="representativeEmail" type="text" name="representativeEmail" value="<EMAIL>" class="widthLarge" placeholder="例：<EMAIL>" />
						</li>
						<li>
							<span class="supplementTxt" id="confirmMailAddressForRepresentative">確認のため再度ご入力ください。
							</span><label for="confirmrepresentativeEmail" class="visuallyHidden">確認用メールアドレス</label><input id="confirmrepresentativeEmail" type="text" name="confirmrepresentativeEmail" value="<EMAIL>" class="widthLarge jsNotPasteInput" placeholder="例：<EMAIL>" aria-describedby="confirmMailAddressForRepresentative" />
						</li>
					</ul>
				</dd>
					<dd style="display:none;"><input id="updateAlwaysInfo" type="checkbox" name="updateAlwaysInfo" /><label for="updateAlwaysInfo">ログイン中の会員情報へこのメールアドレスを登録</label>
					</dd>
			</dl>
			<dl>
				<dt>電話番号<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span>
				</dt>
				<dd class="js-countryWrap">
					<ul>
						<li><label for="representativeTelType" class="visuallyHidden">電話番号種別</label><select id="representativeTelType" name="representativeTelType" class="basicWidth js-typeSelect" size="1" onchange="changeTelType(this,'representativeTel')">	<option value="M1" selected="selected">携帯</option>
	<option value="B1">会社</option>
	<option value="H1">自宅</option>
</select>
						</li>
						<li><label for="representativeTelCountry" class="visuallyHidden">電話番号の国/地区選択</label><select id="representativeTelCountry" name="representativeTelCountry" class="js-countrySelect" size="1">	<option value="" data-sms-possible="true" data-country-code="---">選択してください</option>
	<option value="JP" selected="selected" data-sms-possible="true">日本</option>
	<option value="-" data-sms-possible="true" data-country-code="---">-----------</option>
	<option value="IS" data-sms-possible="true">アイスランド</option>
	<option value="IE" data-sms-possible="true">アイルランド</option>
	<option value="AZ" data-sms-possible="true">アゼルバイジャン</option>
	<option value="AF" data-sms-possible="true">アフガニスタン</option>
	<option value="US" data-sms-possible="true">アメリカ合衆国/カナダ</option>
	<option value="AE" data-sms-possible="true">アラブ首長国連邦</option>
	<option value="DZ" data-sms-possible="true">アルジェリア</option>
	<option value="AR" data-sms-possible="true">アルゼンチン</option>
	<option value="AW" data-sms-possible="true">アルバ</option>
	<option value="AL" data-sms-possible="true">アルバニア</option>
	<option value="AM" data-sms-possible="true">アルメニア</option>
	<option value="AO" data-sms-possible="true">アンゴラ</option>
	<option value="AG" data-sms-possible="true">アンティグア・バーブーダ</option>
	<option value="AD" data-sms-possible="true">アンドラ</option>
	<option value="YE" data-sms-possible="true">イエメン</option>
	<option value="GB" data-sms-possible="true">イギリス</option>
	<option value="IL" data-sms-possible="true">イスラエル</option>
	<option value="IT" data-sms-possible="true">イタリア</option>
	<option value="IQ" data-sms-possible="true">イラク</option>
	<option value="IR" data-sms-possible="true">イラン</option>
	<option value="IN" data-sms-possible="true">インド</option>
	<option value="ID" data-sms-possible="true">インドネシア</option>
	<option value="UG" data-sms-possible="true">ウガンダ</option>
	<option value="UA" data-sms-possible="true">ウクライナ</option>
	<option value="UZ" data-sms-possible="true">ウズベキスタン</option>
	<option value="UY" data-sms-possible="true">ウルグアイ</option>
	<option value="EC" data-sms-possible="true">エクアドル</option>
	<option value="EG" data-sms-possible="true">エジプト</option>
	<option value="EE" data-sms-possible="true">エストニア</option>
	<option value="SZ" data-sms-possible="true">エスワティニ</option>
	<option value="ET" data-sms-possible="true">エチオピア</option>
	<option value="ER" data-sms-possible="true">エリトリア</option>
	<option value="SV" data-sms-possible="true">エルサルバドル</option>
	<option value="AU" data-sms-possible="true">オーストラリア</option>
	<option value="AT" data-sms-possible="true">オーストリア</option>
	<option value="OM" data-sms-possible="true">オマーン</option>
	<option value="NL" data-sms-possible="true">オランダ</option>
	<option value="GH" data-sms-possible="true">ガーナ</option>
	<option value="CV" data-sms-possible="true">カーボベルデ</option>
	<option value="GY" data-sms-possible="true">ガイアナ</option>
	<option value="KZ" data-sms-possible="true">カザフスタン</option>
	<option value="QA" data-sms-possible="true">カタール</option>
	<option value="CA" data-sms-possible="true">カナダ/アメリカ合衆国</option>
	<option value="GA" data-sms-possible="true">ガボン</option>
	<option value="CM" data-sms-possible="true">カメルーン</option>
	<option value="KR" data-sms-possible="true">韓国</option>
	<option value="GM" data-sms-possible="true">ガンビア</option>
	<option value="KH" data-sms-possible="true">カンボジア</option>
	<option value="KP" data-sms-possible="true">北朝鮮</option>
	<option value="MK" data-sms-possible="true">北マケドニア</option>
	<option value="GN" data-sms-possible="true">ギニア</option>
	<option value="GW" data-sms-possible="true">ギニアビサウ</option>
	<option value="CY" data-sms-possible="true">キプロス</option>
	<option value="CU" data-sms-possible="true">キューバ</option>
	<option value="GR" data-sms-possible="true">ギリシャ</option>
	<option value="KI" data-sms-possible="true">キリバス</option>
	<option value="KG" data-sms-possible="true">キルギス</option>
	<option value="GT" data-sms-possible="true">グアテマラ</option>
	<option value="GU" data-sms-possible="true">グアム</option>
	<option value="KW" data-sms-possible="true">クウェート</option>
	<option value="GD" data-sms-possible="true">グレナダ</option>
	<option value="HR" data-sms-possible="true">クロアチア</option>
	<option value="KY" data-sms-possible="true">ケイマン諸島</option>
	<option value="KE" data-sms-possible="true">ケニア</option>
	<option value="CI" data-sms-possible="true">コートジボワール</option>
	<option value="CR" data-sms-possible="true">コスタリカ</option>
	<option value="KM" data-sms-possible="true">コモロ</option>
	<option value="CO" data-sms-possible="true">コロンビア</option>
	<option value="CG" data-sms-possible="true">コンゴ共和国</option>
	<option value="CD" data-sms-possible="true">コンゴ民主共和国</option>
	<option value="SA" data-sms-possible="true">サウジアラビア</option>
	<option value="ZM" data-sms-possible="true">ザンビア</option>
	<option value="SM" data-sms-possible="true">サンマリノ</option>
	<option value="SL" data-sms-possible="true">シエラレオネ</option>
	<option value="DJ" data-sms-possible="true">ジブチ</option>
	<option value="JM" data-sms-possible="true">ジャマイカ</option>
	<option value="GE" data-sms-possible="true">ジョージア</option>
	<option value="SY" data-sms-possible="true">シリア</option>
	<option value="SG" data-sms-possible="true">シンガポール</option>
	<option value="ZW" data-sms-possible="true">ジンバブエ</option>
	<option value="CH" data-sms-possible="true">スイス</option>
	<option value="SE" data-sms-possible="true">スウェーデン</option>
	<option value="SD" data-sms-possible="true">スーダン</option>
	<option value="ES" data-sms-possible="true">スペイン</option>
	<option value="SR" data-sms-possible="true">スリナム</option>
	<option value="LK" data-sms-possible="true">スリランカ</option>
	<option value="SK" data-sms-possible="true">スロバキア</option>
	<option value="SI" data-sms-possible="true">スロベニア</option>
	<option value="SC" data-sms-possible="true">セイシェル</option>
	<option value="GQ" data-sms-possible="true">赤道ギニア</option>
	<option value="SN" data-sms-possible="true">セネガル</option>
	<option value="RS" data-sms-possible="true">セルビア</option>
	<option value="KN" data-sms-possible="true">セントクリストファー・ネイビス</option>
	<option value="LC" data-sms-possible="true">セントルシア</option>
	<option value="SB" data-sms-possible="true">ソロモン諸島</option>
	<option value="TH" data-sms-possible="true">タイ</option>
	<option value="TW" data-sms-possible="true">台湾</option>
	<option value="TJ" data-sms-possible="true">タジキスタン</option>
	<option value="TZ" data-sms-possible="true">タンザニア</option>
	<option value="CZ" data-sms-possible="true">チェコ</option>
	<option value="TD" data-sms-possible="true">チャド</option>
	<option value="CF" data-sms-possible="true">中央アフリカ共和国</option>
	<option value="CN" data-sms-possible="true">中国</option>
	<option value="TN" data-sms-possible="true">チュニジア</option>
	<option value="CL" data-sms-possible="true">チリ</option>
	<option value="TV" data-sms-possible="true">ツバル</option>
	<option value="DK" data-sms-possible="true">デンマーク</option>
	<option value="DE" data-sms-possible="true">ドイツ</option>
	<option value="TG" data-sms-possible="true">トーゴ</option>
	<option value="DO" data-sms-possible="true">ドミニカ共和国</option>
	<option value="DM" data-sms-possible="true">ドミニカ国</option>
	<option value="TT" data-sms-possible="true">トリニダード・トバゴ</option>
	<option value="TM" data-sms-possible="true">トルクメニスタン</option>
	<option value="TR" data-sms-possible="true">トルコ</option>
	<option value="TO" data-sms-possible="true">トンガ</option>
	<option value="NG" data-sms-possible="true">ナイジェリア</option>
	<option value="NR" data-sms-possible="true">ナウル</option>
	<option value="NA" data-sms-possible="true">ナミビア</option>
	<option value="NI" data-sms-possible="true">ニカラグア</option>
	<option value="NE" data-sms-possible="true">ニジェール</option>
	<option value="NZ" data-sms-possible="true">ニュージーランド</option>
	<option value="NP" data-sms-possible="true">ネパール</option>
	<option value="NO" data-sms-possible="true">ノルウェー</option>
	<option value="BH" data-sms-possible="true">バーレーン</option>
	<option value="HT" data-sms-possible="true">ハイチ</option>
	<option value="PK" data-sms-possible="true">パキスタン</option>
	<option value="VA" data-sms-possible="true">バチカン</option>
	<option value="PA" data-sms-possible="true">パナマ</option>
	<option value="VU" data-sms-possible="true">バヌアツ</option>
	<option value="BS" data-sms-possible="true">バハマ</option>
	<option value="PG" data-sms-possible="true">パプアニューギニア</option>
	<option value="BM" data-sms-possible="true">バミューダ</option>
	<option value="PW" data-sms-possible="true">パラオ</option>
	<option value="PY" data-sms-possible="true">パラグアイ</option>
	<option value="BB" data-sms-possible="true">バルバドス</option>
	<option value="PS" data-sms-possible="true">パレスチナ</option>
	<option value="HU" data-sms-possible="true">ハンガリー</option>
	<option value="BD" data-sms-possible="true">バングラデシュ</option>
	<option value="TL" data-sms-possible="true">東ティモール</option>
	<option value="FJ" data-sms-possible="true">フィジー</option>
	<option value="PH" data-sms-possible="true">フィリピン</option>
	<option value="FI" data-sms-possible="true">フィンランド</option>
	<option value="BT" data-sms-possible="true">ブータン</option>
	<option value="PR" data-sms-possible="true">プエルトリコ</option>
	<option value="BR" data-sms-possible="true">ブラジル</option>
	<option value="FR" data-sms-possible="true">フランス</option>
	<option value="PF" data-sms-possible="true">フランス領ポリネシア</option>
	<option value="BG" data-sms-possible="true">ブルガリア</option>
	<option value="BF" data-sms-possible="true">ブルキナファソ</option>
	<option value="BN" data-sms-possible="true">ブルネイ</option>
	<option value="BI" data-sms-possible="true">ブルンジ</option>
	<option value="VN" data-sms-possible="true">ベトナム</option>
	<option value="BJ" data-sms-possible="true">ベナン</option>
	<option value="VE" data-sms-possible="true">ベネズエラ</option>
	<option value="BY" data-sms-possible="true">ベラルーシ</option>
	<option value="BZ" data-sms-possible="true">ベリーズ</option>
	<option value="PE" data-sms-possible="true">ペルー</option>
	<option value="BE" data-sms-possible="true">ベルギー</option>
	<option value="PL" data-sms-possible="true">ポーランド</option>
	<option value="BA" data-sms-possible="true">ボスニア・ヘルツェゴビナ</option>
	<option value="BW" data-sms-possible="true">ボツワナ</option>
	<option value="BO" data-sms-possible="true">ボリビア</option>
	<option value="PT" data-sms-possible="true">ポルトガル</option>
	<option value="HK" data-sms-possible="true">香港</option>
	<option value="HN" data-sms-possible="true">ホンジュラス</option>
	<option value="MH" data-sms-possible="true">マーシャル諸島</option>
	<option value="MO" data-sms-possible="true">マカオ</option>
	<option value="MG" data-sms-possible="true">マダガスカル</option>
	<option value="MW" data-sms-possible="true">マラウイ</option>
	<option value="ML" data-sms-possible="true">マリ</option>
	<option value="MT" data-sms-possible="true">マルタ</option>
	<option value="MY" data-sms-possible="true">マレーシア</option>
	<option value="FM" data-sms-possible="true">ミクロネシア連邦</option>
	<option value="ZA" data-sms-possible="true">南アフリカ</option>
	<option value="MM" data-sms-possible="true">ミャンマー</option>
	<option value="MX" data-sms-possible="true">メキシコ</option>
	<option value="MU" data-sms-possible="true">モーリシャス</option>
	<option value="MR" data-sms-possible="true">モーリタニア</option>
	<option value="MZ" data-sms-possible="true">モザンビーク</option>
	<option value="MC" data-sms-possible="true">モナコ</option>
	<option value="MV" data-sms-possible="true">モルディヴ</option>
	<option value="MD" data-sms-possible="true">モルドバ</option>
	<option value="MA" data-sms-possible="true">モロッコ</option>
	<option value="MN" data-sms-possible="true">モンゴル</option>
	<option value="ME" data-sms-possible="true">モンテネグロ</option>
	<option value="JO" data-sms-possible="true">ヨルダン</option>
	<option value="LA" data-sms-possible="true">ラオス人民民主共和国</option>
	<option value="LV" data-sms-possible="true">ラトビア</option>
	<option value="LT" data-sms-possible="true">リトアニア</option>
	<option value="LY" data-sms-possible="true">リビア</option>
	<option value="LI" data-sms-possible="true">リヒテンシュタイン</option>
	<option value="LR" data-sms-possible="true">リベリア</option>
	<option value="RO" data-sms-possible="true">ルーマニア</option>
	<option value="LU" data-sms-possible="true">ルクセンブルク</option>
	<option value="RW" data-sms-possible="true">ルワンダ</option>
	<option value="LS" data-sms-possible="true">レソト</option>
	<option value="LB" data-sms-possible="true">レバノン</option>
	<option value="RU" data-sms-possible="true">ロシア</option>
</select>
						</li>
						<li class="numberInputWrap">
							<span id="numberInputInfo1" class="supplementTxt marginTop">先頭に0(ゼロ)がある場合、0(ゼロ)を抜いてください(一部、国・地域除く)。携帯電話番号の場合は、携帯電話を契約された国を選択ください。</span>
							<div class="numberRowWrap">
								<div class="block">
									
									<span class="visuallyHidden">国番号</span>
									<span class="viewInput widthMini js-countryCode"></span>
								</div>
								<div class="block"><label for="representativeTel" class="visuallyHidden">電話番号</label><input id="representativeTel" type="text" name="representativeTel" value="9042504176" class="widthMiddle" placeholder="例：9012345678" maxlength="30" aria-describedby="numberInputInfo1" />
								</div>
							</div>
						</li>
					</ul>
				</dd>
			</dl>
		</fieldset>
	</div>
	<div class="titleWrapper">
		<h3>
			<span class="text">eメール・SMSに関するご案内<a href="https://www.ana.co.jp/other/int/meta/0057.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="eメール・SMSに関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a></span>
		</h3>
	</div>
	<div class="formInput">
	<h4 class="formAreaTitle">主に搭乗・運航に関わるeメールの送付先設定（米国へ通知いたします）<a href="https://www.ana.co.jp/other/int/meta/0629.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="米国入国用eメール・SMS・電話番号に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a>
	</h4>
	<div class="paxSettingArea js-mailFormWrap onePax is-open">
			<div class="settingArea">
					<dl>
						<dt class="breakWord">NAOKI SATO
						</dt>
						<dd>
							<fieldset>
								<legend>NAOKI SATOのeメール送付先および米国到着時に提供するメールアドレス</legend>代表者と同じ
							</fieldset>
						</dd>
					</dl>
			</div>
	</div>
	<h4 class="formAreaTitle hasTopLine">主に搭乗・運航に関わるSMSの送付先および米国で受信できる電話番号の設定（米国へ通知いたします）<a href="https://www.ana.co.jp/other/int/meta/0629.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" aria-label="米国入国用eメール・SMS・電話番号に関するご案内(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)" target="_blank" class="informationIconLarge jsRollOverInLink"><span class="informationIcon"><img src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/image/icon_info_large_02.png?512eb1d" alt="インフォメーション" height="19" width="18" class="jsRollOverImg" /></span><span class="externalLinkIcon imageTypePC4"></span></a>
	</h4>
	<div class="paxSettingArea js-smsFormWrap onePax is-open">
			<div class="settingArea">
					<dl>
						<dt class="breakWord">NAOKI SATO<span class="requiredIcon"><img src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/image/icon_required.png?0f9cdd3" alt="必須項目です" height="7" width="7" /></span>
						</dt>
						<dd>
							<fieldset>
								<legend>NAOKI SATOの1つ目の電話番号</legend>
									<ul class="verticalList js-formItem">
										<li><input type="radio" id="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSameAsRepresentativeSms:radioItem" name="contactsSms:0:recipientSmsEachPersonSelection:radioGroup" value="0" checked="checked" /><label for="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSameAsRepresentativeSms:radioItem">代表者電話番号と同じ</label>
										</li>
										<li><input type="radio" id="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSmsPerPassenger:radioItem" name="contactsSms:0:recipientSmsEachPersonSelection:radioGroup" value="1" /><label for="contactsSms:0:recipientSmsEachPersonSelection:selectEachPersonSmsPerPassenger:radioItem">電話番号を指定</label>
											<div class="indentArea js-formImputArea">
												<span id="confirmTelNumberForRepresentative:0" class="supplementTxt marginTop">先頭に0(ゼロ)がある場合、0(ゼロ)を抜いてください(一部、国・地域除く)。携帯電話番号の場合は、携帯電話を契約された国を選択ください。</span>
												<ul class="countryNumberInput js-countryWrap">
													<li><label for="contactsSms:0:recipientSmsEachPersonSelection:passengerSmsCountry" class="visuallyHidden">電話番号の国/地区選択</label><select id="contactsSms:0:recipientSmsEachPersonSelection:passengerSmsCountry" name="contactsSms:0:recipientSmsEachPersonSelection:passengerSmsCountry" class="countrySelect js-countrySelect" size="1">	<option value="" data-country-code="---">選択してください</option>
	<option value="JP">日本</option>
	<option value="-" data-country-code="---">-----------</option>
	<option value="IE">アイルランド</option>
	<option value="AZ">アゼルバイジャン</option>
	<option value="AF">アフガニスタン</option>
	<option value="US">アメリカ合衆国/カナダ</option>
	<option value="AE">アラブ首長国連邦</option>
	<option value="DZ">アルジェリア</option>
	<option value="AR">アルゼンチン</option>
	<option value="AW">アルバ</option>
	<option value="AL">アルバニア</option>
	<option value="AM">アルメニア</option>
	<option value="AG">アンティグア・バーブーダ</option>
	<option value="AD">アンドラ</option>
	<option value="YE">イエメン</option>
	<option value="GB">イギリス</option>
	<option value="IL">イスラエル</option>
	<option value="IT">イタリア</option>
	<option value="IQ">イラク</option>
	<option value="IR">イラン</option>
	<option value="IN">インド</option>
	<option value="ID">インドネシア</option>
	<option value="UG">ウガンダ</option>
	<option value="UA">ウクライナ</option>
	<option value="UZ">ウズベキスタン</option>
	<option value="UY">ウルグアイ</option>
	<option value="EC">エクアドル</option>
	<option value="EG">エジプト</option>
	<option value="EE">エストニア</option>
	<option value="ER">エリトリア</option>
	<option value="SV">エルサルバドル</option>
	<option value="AU">オーストラリア</option>
	<option value="AT">オーストリア</option>
	<option value="OM">オマーン</option>
	<option value="NL">オランダ</option>
	<option value="GH">ガーナ</option>
	<option value="GY">ガイアナ</option>
	<option value="KZ">カザフスタン</option>
	<option value="QA">カタール</option>
	<option value="CA">カナダ/アメリカ合衆国</option>
	<option value="GA">ガボン</option>
	<option value="CM">カメルーン</option>
	<option value="KR">韓国</option>
	<option value="GM">ガンビア</option>
	<option value="KH">カンボジア</option>
	<option value="GN">ギニア</option>
	<option value="GW">ギニアビサウ</option>
	<option value="CY">キプロス</option>
	<option value="CU">キューバ</option>
	<option value="GR">ギリシャ</option>
	<option value="KG">キルギス</option>
	<option value="GT">グアテマラ</option>
	<option value="GU">グアム</option>
	<option value="KW">クウェート</option>
	<option value="GD">グレナダ</option>
	<option value="HR">クロアチア</option>
	<option value="KY">ケイマン諸島</option>
	<option value="KE">ケニア</option>
	<option value="CI">コートジボワール</option>
	<option value="CR">コスタリカ</option>
	<option value="KM">コモロ</option>
	<option value="CO">コロンビア</option>
	<option value="CD">コンゴ民主共和国</option>
	<option value="SA">サウジアラビア</option>
	<option value="ZM">ザンビア</option>
	<option value="SL">シエラレオネ</option>
	<option value="JM">ジャマイカ</option>
	<option value="GE">ジョージア</option>
	<option value="SY">シリア</option>
	<option value="SG">シンガポール</option>
	<option value="ZW">ジンバブエ</option>
	<option value="CH">スイス</option>
	<option value="SE">スウェーデン</option>
	<option value="SD">スーダン</option>
	<option value="ES">スペイン</option>
	<option value="SR">スリナム</option>
	<option value="LK">スリランカ</option>
	<option value="SK">スロバキア</option>
	<option value="SI">スロベニア</option>
	<option value="SN">セネガル</option>
	<option value="RS">セルビア</option>
	<option value="KN">セントクリストファー・ネイビス</option>
	<option value="LC">セントルシア</option>
	<option value="TH">タイ</option>
	<option value="TW">台湾</option>
	<option value="TJ">タジキスタン</option>
	<option value="TZ">タンザニア</option>
	<option value="CZ">チェコ</option>
	<option value="TD">チャド</option>
	<option value="CF">中央アフリカ共和国</option>
	<option value="CN">中国</option>
	<option value="TN">チュニジア</option>
	<option value="CL">チリ</option>
	<option value="DK">デンマーク</option>
	<option value="DE">ドイツ</option>
	<option value="TG">トーゴ</option>
	<option value="DO">ドミニカ共和国</option>
	<option value="TT">トリニダード・トバゴ</option>
	<option value="TR">トルコ</option>
	<option value="NG">ナイジェリア</option>
	<option value="NI">ニカラグア</option>
	<option value="NE">ニジェール</option>
	<option value="NZ">ニュージーランド</option>
	<option value="NP">ネパール</option>
	<option value="NO">ノルウェー</option>
	<option value="HT">ハイチ</option>
	<option value="PK">パキスタン</option>
	<option value="PA">パナマ</option>
	<option value="BS">バハマ</option>
	<option value="PG">パプアニューギニア</option>
	<option value="BM">バミューダ</option>
	<option value="PW">パラオ</option>
	<option value="PY">パラグアイ</option>
	<option value="BB">バルバドス</option>
	<option value="PS">パレスチナ</option>
	<option value="HU">ハンガリー</option>
	<option value="BD">バングラデシュ</option>
	<option value="TL">東ティモール</option>
	<option value="FJ">フィジー</option>
	<option value="PH">フィリピン</option>
	<option value="FI">フィンランド</option>
	<option value="BT">ブータン</option>
	<option value="PR">プエルトリコ</option>
	<option value="BR">ブラジル</option>
	<option value="FR">フランス</option>
	<option value="PF">フランス領ポリネシア</option>
	<option value="BG">ブルガリア</option>
	<option value="BF">ブルキナファソ</option>
	<option value="BN">ブルネイ</option>
	<option value="BI">ブルンジ</option>
	<option value="VN">ベトナム</option>
	<option value="BJ">ベナン</option>
	<option value="VE">ベネズエラ</option>
	<option value="BY">ベラルーシ</option>
	<option value="PE">ペルー</option>
	<option value="BE">ベルギー</option>
	<option value="PL">ポーランド</option>
	<option value="BA">ボスニア・ヘルツェゴビナ</option>
	<option value="BW">ボツワナ</option>
	<option value="HK">香港</option>
	<option value="MH">マーシャル諸島</option>
	<option value="MO">マカオ</option>
	<option value="MG">マダガスカル</option>
	<option value="MW">マラウイ</option>
	<option value="ML">マリ</option>
	<option value="MT">マルタ</option>
	<option value="MY">マレーシア</option>
	<option value="ZA">南アフリカ</option>
	<option value="MM">ミャンマー</option>
	<option value="MX">メキシコ</option>
	<option value="MU">モーリシャス</option>
	<option value="MR">モーリタニア</option>
	<option value="MZ">モザンビーク</option>
	<option value="MC">モナコ</option>
	<option value="MV">モルディヴ</option>
	<option value="MA">モロッコ</option>
	<option value="MN">モンゴル</option>
	<option value="ME">モンテネグロ</option>
	<option value="JO">ヨルダン</option>
	<option value="LA">ラオス人民民主共和国</option>
	<option value="LV">ラトビア</option>
	<option value="LT">リトアニア</option>
	<option value="LY">リビア</option>
	<option value="LR">リベリア</option>
	<option value="RO">ルーマニア</option>
	<option value="LU">ルクセンブルク</option>
	<option value="RW">ルワンダ</option>
	<option value="LB">レバノン</option>
	<option value="RU">ロシア</option>
</select>
													</li>
													<li>
														
														<div class="numberRowWrap">
															<div class="block">
																<span class="visuallyHidden">国番号</span>
																<span class="countryCode viewInput js-countryCode"></span>
															</div>
															
															<div class="block"><label for="contactsSms:0:recipientSmsEachPersonSelection:flightStatusNotificationContactPointSmsDescription" class="visuallyHidden">電話番号</label><input id="contactsSms:0:recipientSmsEachPersonSelection:flightStatusNotificationContactPointSmsDescription" type="text" name="contactsSms:0:recipientSmsEachPersonSelection:flightStatusNotificationContactPointSmsDescription" class="telNumber" placeholder="例：9012345678" maxlength="30" aria-describedby="confirmTelNumberForRepresentative:0" />
															</div>
														</div>
													</li>
												</ul>
											</div>
										</li>
									</ul>
							</fieldset>
						</dd>
					</dl>
			</div>
	</div>
	</div>
	
	<div class="formInput articleLine">
		<h3>滞在中の連絡先(任意)
		</h3>
		<dl>
			
			<dt><label for="accommodationOne">現地連絡先1</label>
			</dt>
			<dd><input id="accommodationOne" type="text" name="accommodationOne" class="widthLarge" placeholder="例：09012345678 ABC HOTEL" maxlength="28" title="例：09012345678 ABC HOTEL" />
			</dd>
		</dl>
		<dl>
			
			<dt><label for="accommodationTwo">現地連絡先2</label>
			</dt>
			<dd><input id="accommodationTwo" type="text" name="accommodationTwo" class="widthLarge" placeholder="例：09012345678 ABC HOTEL" maxlength="28" title="例：09012345678 ABC HOTEL" />
			</dd>
		</dl>
	</div>
		</div>
		
		<p class="btnAreaSubmit btnArrowNext "><input id="next" type="submit" name="next" value="次へ" class="btnBase btnMainStream btnVerticalMain btnWidthVariable" onclick="return Asw.Dialog.getInstance('prebookConfirmDialog').toggle(event);return Asw.LoadingWindow.open(&quot;NORMAL&quot;, event)" />
		</p><input type="hidden" name="javax.faces.ViewState" id="j_id1:javax.faces.ViewState:2" value="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" autocomplete="off" />
</form><div id="cmnDynamicMessages" class="infoBoxSection">
				<dl class="infoBox importantInfo jsAccordionSwitch">
					<dt><strong>重要なご案内</strong></dt>
					<dd>
						<ul class="jsAccordionSwitchList" id="informationMessages">
								<li>個人情報の利用目的<a href="https://www.ana.co.jp/other/int/meta/0012.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="ANAプライバシーポリシー(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">ANAプライバシーポリシー<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>を必ずお読みいただき、内容に同意いただいた上で、お進みください。</li>
								<li><a href="https://www.ana.co.jp/other/int/meta/0058.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="Secure Flight Programについて(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">米国Secure Flightプログラム<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>に基づき、パスポート名、生年月日、性別などの情報は事前に米国へ通知いたします。</li>
								<li>入力したeメールアドレス、電話番号は入国に必要な情報として米国に通知される可能性があります。</li>
									<li class="jsHiddenFlg toggleContents">お名前のスペルがパスポート名と一致しているかご確認ください。異なる場合はインターネットではお取り扱いできません。<a href="https://www.ana.co.jp/other/int/meta/intbe0001.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="ANAお問い合わせ先(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">ANA<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>へ名前の変更をお申し出ください。</li>
									<li class="jsHiddenFlg toggleContents">二重国籍のお客様は、ご搭乗時に使用されるパスポートのお名前でご予約ください。<br />パスポートごとにお名前が異なる場合は、お客様ご自身で入国管理事務所・外務省・大使館などで各種条件をご確認のうえ、使用されるパスポートを決めてください。<br />なお、入国可否については国によって異なるため、お客様ご自身で各入国管理事務所などでご確認ください。</li>
									<li class="jsHiddenFlg toggleContents">旅行開始日時点で12歳以上の場合は大人、2歳以上12歳未満の場合は小児、2歳未満の場合は幼児の扱いとなります。なお、小児は大人と同マイル数が必要です。</li>
									<li class="jsHiddenFlg toggleContents">運航に関する情報をお客様へ通知するため、提携航空会社にもお客様のメールアドレスが提供されます。あらかじめご了承ください。</li>
									<li class="jsHiddenFlg toggleContents"><dl><dt>携帯電話へのメール送信をご希望のお客様へ</dt><dd>ドメイン指定受信をご利用の場合は、ana.co.jpとamadeus.comからのメールが受信できるようあらかじめ設定をお願いいたします。</dd></dl></li>
									<li class="jsHiddenFlg toggleContents">SMSはスマートフォンの番号に配信されます。一部の国ではSMSでのご案内をご利用いただけません。詳細は<a href="https://www.ana.co.jp/other/int/meta/0737.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank" aria-label="ショートメッセージ(SMS)でのご案内対象外の国について(別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。)">ショートメッセージ(SMS)でのご案内対象外の国について<span class="externalLinkIcon iconMarginRight"><img src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/image/icon_new_tab.png?512eb1d" width="10" height="10" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" /></span></a>をご覧ください。</li>
									<li class="jsHiddenFlg toggleContents">ログインされている会員ご本人が搭乗しない場合は、再度フライト検索画面より「ログインされている会員ご本人は搭乗しない」のチェックボックスを選択してお手続きください。</li>
						</ul>
							<a href="#" class="toggleSwitch jsTriggerSwitch" role="button" aria-controls="informationMessages" aria-expanded="false"><img src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/image/toggle_open.png?7bec38a" alt="開閉ボタン" height="25" width="25" class="jsRollOver" />
							</a>
					</dd>
				</dl></div>
	<!--=========== /main =============-->
	</div>

	<!--=========== Script ============-->
	<script type="text/javascript">
	$(window).on('load', function() {
		// アコーディオンのメソッドと設定
		Asw.AccordionInfo('.jsAccordionSwitch', {
			'triggerSwitch': '.jsAccordionSwitch .jsTriggerSwitch',
			'listClass': '.jsAccordionSwitchList',
			'visibilityHidden': 'toggleContents'
		});
	});
	</script>
	<!--=========== Script ============-->
	<script type="text/javascript">
		//プラットフォーム判別（サマリーエリア追従用として）
		var Asw = Asw || {};
	
	  	Asw.ClientInfo = Asw.ClientInfo  || {};
	  	Asw.ClientInfo.DeviceType = Asw.ClientInfo.DeviceType || {};
	
	  	Asw.ClientInfo.DeviceType.PC = 'PC';
	  	Asw.ClientInfo.DeviceType.SP = 'SP';
	  	Asw.ClientInfo.DeviceType.TC = 'TC';
	
	  	Asw.ClientInfo.deviceType = Asw.ClientInfo.DeviceType.PC;
	
	 	if((navigator.userAgent.indexOf('Android') > 0 && navigator.userAgent.indexOf('Mobile') == -1) || navigator.userAgent.indexOf('iPad') > 0){
	    	Asw.ClientInfo.deviceType = Asw.ClientInfo.DeviceType.TC;
	  	}
	</script>
	<!--=========== /Script ============--><div id="summaryArea">
			<div class="flightSummaryArea">
						<div class="flightSummarySection">
							<h2>区間 1
							</h2>
							<p class="flightDate"><em>6</em>月<em class="hasLeft">28</em>日（土）
							</p>
							<div class="flightSummaryRoute">
									<div class="flightSummaryRouteSection">
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation1_1">
													<span>東京(成田)
													</span>
												</p>
												<p class="time">
													<span>17:00
													</span>
												</p>
											</div>
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation1_1">
													<span>シカゴ(ORD)
													</span>
												</p>
												<p class="time">
													<span>14:55
													</span>
												</p>
											</div>
										<p class="flightNumberClass">
												<span>NH012</span><span><img src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/image/flight_ana_summaryarea.png?717d3c0" alt="ANA運航便" height="11" width="41" /></span>
												<br />特典ビジネス
										</p>
									</div>
							</div>
						</div>
						<div class="flightSummarySection">
							<h2 class="midstream">区間 2
							</h2>
							<p class="flightDate"><em>7</em>月<em class="hasLeft">23</em>日（水）
							</p>
							<div class="flightSummaryRoute">
									<div class="flightSummaryRouteSection">
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation2_1">
													<span>ホノルル(オアフ島)
													</span>
												</p>
												<p class="time">
													<span>11:35
													</span>
												</p>
											</div>
											<div>
											<p class="airportLocation heightLine-flightSummaryAirportLocation2_1">
													<span>東京(成田)
													</span>
												</p>
												<p class="time">
													<span>14:50
																	<span>翌日
																	</span>
													</span>
												</p>
											</div>
										<p class="flightNumberClass">
												<span>NH183</span><span><img src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/image/flight_ana_summaryarea.png?717d3c0" alt="ANA運航便" height="11" width="41" /></span>
												<br />特典エコノミー
										</p>
									</div>
							</div>
						</div>
					<div class="total">
						<p class="passenger">大人 x1
						</p>
						<div class="totalArea">
					  		<dl class="mealAmountWrap">
									<dt>有料機内食サービス
									</dt>
									<dd><em class="mealAmount">0</em><span class="currencyCode">円</span>
									</dd>
							</dl>
									<div>
										<div class="mileageAndTotal">
												<dl class="requiredMileage">
													<dt>必要マイル
													</dt>
													<dd><em class="requiredMileage">74,000</em><span class="currencyCode">マイル</span>
													</dd>
												</dl>
												<dl>
													<dt>総額
													</dt>
													<dd><em class="awardTotalPayment">68,130</em>円
													</dd>
												</dl>
												<p class="includeFareAndFuelCost">※各種税金、燃油特別付加運賃等を含みます。
												</p>
										</div>
									</div>
						</div>
					</div>
				
			</div></div>
		<!--===== /cmnWrapper =====-->
		</div>
			
		<span id="message" class="visuallyHidden" role="alert" aria-live="assertive"></span>
		<span id="politeMessage" class="visuallyHidden" role="log" aria-live="polite"></span><script type="text/javascript">
		var Asw = Asw || {};
		Asw.BaseOutput = {"sessionID":"x2aLRZ8egh","language":"ja","siteCatalystLanguageCode":"J","operationDate":"20250620","operationDateTime":"20250620115816","office":{"officeCode":"TYONH08ZZ","country2letterCode":"JP"},"bookingType":"A","pageID":"A04_P01","siteCatalystPageName":"INT_BE_AWARD_J_A04特典予約_P01旅客情報入力（必須情報）","device":{"deviceType":"PC"}};
	</script><div id="promotionArea">
		<!--===== promotionArea =====-->
		<!--===== /promotionArea =====--></div>
		<!--=========== /cmnContainer ============--></div><div id="cmnBaloonArea" aria-live="polite"></div><div id="cmnPopupArea" aria-live="polite"></div>

	

	<div id="cmnFooterWrapper">
		<!--=========== .cmnFooterWrapper ============-->
		<div id="cmnFooter">
			<!--===== cmnFooter =====-->
			<ul>
				<li><a href="https://www.ana.co.jp/other/int/meta/0012.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">プライバシーポリシー<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
					<li><a href="https://www.ana.co.jp/other/int/meta/0670.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">利用者情報の外部送信について<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
					</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/0052.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">サイト利用規約<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/be0238.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">推奨環境<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/0133.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">サイトマップ<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
				<li><a href="https://www.ana.co.jp/other/int/meta/0732.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">運送約款<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
				</li>
					<li class="lastChild"><a href="https://www.ana.co.jp/other/int/meta/0556.html?CONNECTION_KIND=jp&amp;LANG=j&amp;BOOKING_TYPE=a&amp;TIER_LEVEL=plt" target="_blank">ウェブアクセシビリティについて<span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
					</li>
			</ul>
			<p class="blankText" aria-hidden="true"><img src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="" height="10" width="10" />外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。</p>
			<p class="copyright"><span lang="en" xml:lang="en">Copyright&#169;ANA</span>
			</p>
			<p class="starAllianceLogo"><a href="http://www.staralliance.com/ja/" target="_blank"><img src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/image/star_alliance_logo.png?0fe73b5" alt="A STAR ALLIANCE MEMBER" height="18" width="175" /><span class="externalLinkIcon"><img src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/image/icon_new_tab_white.png?512eb1d" alt="別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。" height="10" width="10" /></span></a>
			</p>
			<!--===== cmnFooter =====-->
		</div>
		<!--=========== /cmnFooterWrapper ============-->
	</div>

		
		<div id="cmnLoadingForAjax"><img src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/image/bg_modalWindow.png?717d3c0" alt="" height="0" width="0" />
			<div class="loadingArea">
				<p class="loadingImage"><img src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/image/loading.gif?717d3c0" alt="リクエストを処理しています...しばらくお待ちください。" height="33" width="33" />
				</p>
			</div>
		</div>
		
		
		<div id="popupAltSentence" style="display: none" aria-hidden="true">別ウィンドウで開く。外部サイトの場合はアクセシビリティガイドラインに対応していない可能性があります。</div>
	<div id="cmnSiteCatalyst" aria-hidden="true"><div id="cmnSiteCatalystParamArea"><script type="text/javascript">
				// ▼SiteCatalyst
				// SiteCatalyst code version: H.2.
				// Copyright 1997-2005 Omniture, Inc. More info available at http://www.omniture.com
				SiteCatalystReportSuites   = "INT";
				SiteCatalystCharSet        = "UTF-8";
				SiteCatalystChannel        = "BE_AWARD_J";
				SiteCatalystPageName       = "INT_BE_AWARD_J_A04\u7279\u5178\u4E88\u7D04_P01\u65C5\u5BA2\u60C5\u5831\u5165\u529B\uFF08\u5FC5\u9808\u60C5\u5831\uFF09";
				SiteCatalystERR            = "";
				SiteCatalystEventNum       = "";
				SiteCatalystPurchase       = "FALSE";
				SiteCatalystProductPrice   = "";
				SiteCatalystCurrencyCode   = "";
				SiteCatalystSeatAvailabilityINT   = "";
				SiteCatalystDepartDate     = "";
				SiteCatalystDepart         = "";
				SiteCatalystArrive         = "";
				SiteCatalystFlightNo       = "";
				SiteCatalystClass          = "";
				SiteCatalystFare           = "";
				SiteCatalystPurchaseID     = "";
				
				SiteCatalystLocater        = "";
				SiteCatalystPayment        = "";
				SiteCatalystIncome         = "";
				
				SiteCatalystPax            = "";
				SiteCatalystManipulateDay  = "";
				SiteCatalystCompartmentClass   = "";
				SiteCatalystOptionInfo     = "";
				SiteCatalystOutboundUpsellPrice   = "";
				SiteCatalystInboundUpsellPrice    = "";
				
				SiteCatalystSearchInput    = "";
				SiteCatalystSegment        = "";
				SiteCatalystOfficeCode     = "";
				SiteCatalystPassengerCount = "";
				SiteCatalystPassenger      = "";
				SiteCatalystAncillaryService = "";
				// End SiteCatalyst code version: H.2.
				// ▲SiteCatalyst
			</script></div><script type="text/javascript"><!-- if(navigator.appVersion.indexOf('MSIE')>=0)document.write(unescape('%3C')+'\!-'+'-') // --></script>
		<!--/DO NOT REMOVE/-->
		

		
		<!-- Google Tag Manager -->
		<script type="text/javascript" charset="Shift_JIS" src="https://www.ana.co.jp/common/js/GTMINTBE.js"></script>
		<!-- End Google Tag Manager -->
		<!-- Yahoo Tag Manager -->
		<script type="text/javascript" charset="Shift_JIS" src="https://www.ana.co.jp/common/js/YTMINTBE.js"></script>
		<!-- End Yahoo Tag Manager -->
		

	</div>

	

	<div class="mboxDefault"></div><script type="text/javascript">mboxCreate("ASW_common_A04_P01");</script>
			<script type="text/javascript" src="https://www.ana.co.jp/common/js/tealium/tealium.js"></script>
			<script type="text/javascript" src="//cdn.evgnet.com/beacon/allnipponairways/nonamcprd/scripts/evergage.min.js"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22c/international_asw/javax.faces.resource/jsf.js.xhtml?ln=javax.faces"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/script/jquery.mousewheel.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/script/jquery.jscrollpane.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/script/jquery-ui-1.10.4.custom.min.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/script/jquery.ui.touch-punch.min.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/script/asw-common.js?beb6a50"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/script/asw-common-pc.js?fd88d7d"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/script/asw-format.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/script/mask.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/script/loading.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/script/asw-loadingwindow-pc.js?9e233c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/script/asw-loadingwindow.js?51db600"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/script/dialog-pc.js?25d36a4"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/script/asw-dialog-sequencer.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/script/asw-event.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/script/asw-date-select.js?717d3c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/script/asw-calendar-pc.js?9b86b8a"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/script/asw-calendar.js?db624c0"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/script/heightLine.js?0e7a1ce"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/script/asw-summaryarea.js?b21c6d7"></script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/script/asw-fingerprints.js?99001fc"></script><script type="text/javascript">
			Asw.init("https://aswbe-i.ana.co.jp/rei22c/international_asw", "ja");
			Asw.getCalendarHolidayList = function() {return [20250721,20250811,20250915,20250923,20251013,20251103,20251123,20251124,20260101,20260112,20260211,20260223,20260320,20260429,20260503,20260504,20260505,20260506,20260720,20260811,20260921,20260922,20260923,20261012,20261103,20261123,20270101,20270111,20270211,20270223,20270321,20270322,20270429,20270503,20270504,20270505,20270719,20270811,20270920,20270923,20271011,20271103,20271123,20280101,20280110,20280211,20280223,20280320,20280429,20280503,20280504,20280505,20280717,20280811,20280918,20280922,20281009,20281103,20281123,20290101,20290108,20290211,20290212,20290223,20290320,20290429,20290430,20290503,20290504,20290505,20290716,20290811,20290917,20290923,20290924,20291008,20291103,20291123];}
			Asw.ClientInfo.deviceType = "PC";
			Asw.ClientInfo.mobileDeviceType = "UNKNOWN";
			Asw.ClientInfo.osVersion = "";
			Asw.Format.init("https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/{1}/{2}/{3}?{0}");
			Asw.overwriteTargetAttributeValue("false");
		</script><script type="text/javascript">
		
		var Asw = Asw || {};
		Asw.ContactInput = Asw.ContactInput || {};
		Asw.ContactInput.Const = {
			/** data-属性名「data-type」 @constant @type {String} @default 'data-type' */
			DATA_ATTRIBUTE_NAME_TYPE: 'data-type',
			/** data-属性名「data-sms-possible」 @constant @type {String} @default 'data-sms-possible' */
			DATA_ATTRIBUTE_NAME_SMS_POSSIBLE: 'data-sms-possible',
			/** data-属性名「data-country-code」 @constant @type {String} @default 'data-country-code' */
			DATA_ATTRIBUTE_NAME_COUNTRY_CODE: 'data-country-code',
			/** data-type属性値「sms」 @constant @type {String} @default 'sms' */
			DATA_TYPE_VALUE_SMS: 'sms'
		};

		
		Asw.ContactInput.countryInfoMap = {"IS":{"internationalCallingCode":"+354","smsPossible":false},"IE":{"internationalCallingCode":"+353","smsPossible":true},"AZ":{"internationalCallingCode":"+994","smsPossible":true},"AF":{"internationalCallingCode":"+93","smsPossible":true},"US":{"internationalCallingCode":"+1","smsPossible":true},"AE":{"internationalCallingCode":"+971","smsPossible":true},"DZ":{"internationalCallingCode":"+213","smsPossible":true},"AR":{"internationalCallingCode":"+54","smsPossible":true},"AW":{"internationalCallingCode":"+297","smsPossible":true},"AL":{"internationalCallingCode":"+355","smsPossible":true},"AM":{"internationalCallingCode":"+374","smsPossible":true},"AO":{"internationalCallingCode":"+244","smsPossible":false},"AG":{"internationalCallingCode":"+1","smsPossible":true},"AD":{"internationalCallingCode":"+376","smsPossible":true},"YE":{"internationalCallingCode":"+967","smsPossible":true},"GB":{"internationalCallingCode":"+44","smsPossible":true},"IL":{"internationalCallingCode":"+972","smsPossible":true},"IT":{"internationalCallingCode":"+39","smsPossible":true},"IQ":{"internationalCallingCode":"+964","smsPossible":true},"IR":{"internationalCallingCode":"+98","smsPossible":true},"IN":{"internationalCallingCode":"+91","smsPossible":true},"ID":{"internationalCallingCode":"+62","smsPossible":true},"UG":{"internationalCallingCode":"+256","smsPossible":true},"UA":{"internationalCallingCode":"+380","smsPossible":true},"UZ":{"internationalCallingCode":"+998","smsPossible":true},"UY":{"internationalCallingCode":"+598","smsPossible":true},"EC":{"internationalCallingCode":"+593","smsPossible":true},"EG":{"internationalCallingCode":"+20","smsPossible":true},"EE":{"internationalCallingCode":"+372","smsPossible":true},"SZ":{"internationalCallingCode":"+268","smsPossible":false},"ET":{"internationalCallingCode":"+251","smsPossible":false},"ER":{"internationalCallingCode":"+291","smsPossible":true},"SV":{"internationalCallingCode":"+503","smsPossible":true},"AU":{"internationalCallingCode":"+61","smsPossible":true},"AT":{"internationalCallingCode":"+43","smsPossible":true},"OM":{"internationalCallingCode":"+968","smsPossible":true},"NL":{"internationalCallingCode":"+31","smsPossible":true},"GH":{"internationalCallingCode":"+233","smsPossible":true},"CV":{"internationalCallingCode":"+238","smsPossible":false},"GY":{"internationalCallingCode":"+592","smsPossible":true},"KZ":{"internationalCallingCode":"+7","smsPossible":true},"QA":{"internationalCallingCode":"+974","smsPossible":true},"CA":{"internationalCallingCode":"+1","smsPossible":true},"GA":{"internationalCallingCode":"+241","smsPossible":true},"CM":{"internationalCallingCode":"+237","smsPossible":true},"KR":{"internationalCallingCode":"+82","smsPossible":true},"KH":{"internationalCallingCode":"+855","smsPossible":true},"GM":{"internationalCallingCode":"+220","smsPossible":true},"KP":{"internationalCallingCode":"+850","smsPossible":false},"MK":{"internationalCallingCode":"+389","smsPossible":false},"GN":{"internationalCallingCode":"+224","smsPossible":true},"GW":{"internationalCallingCode":"+245","smsPossible":true},"CY":{"internationalCallingCode":"+357","smsPossible":true},"CU":{"internationalCallingCode":"+53","smsPossible":true},"GR":{"internationalCallingCode":"+30","smsPossible":true},"KI":{"internationalCallingCode":"+686","smsPossible":false},"KG":{"internationalCallingCode":"+996","smsPossible":true},"GT":{"internationalCallingCode":"+502","smsPossible":true},"GU":{"internationalCallingCode":"+1","smsPossible":true},"KW":{"internationalCallingCode":"+965","smsPossible":true},"KY":{"internationalCallingCode":"+1","smsPossible":true},"GD":{"internationalCallingCode":"+1","smsPossible":true},"HR":{"internationalCallingCode":"+385","smsPossible":true},"KE":{"internationalCallingCode":"+254","smsPossible":true},"CI":{"internationalCallingCode":"+225","smsPossible":true},"CR":{"internationalCallingCode":"+506","smsPossible":true},"SA":{"internationalCallingCode":"+966","smsPossible":true},"KM":{"internationalCallingCode":"+269","smsPossible":true},"CO":{"internationalCallingCode":"+57","smsPossible":true},"CG":{"internationalCallingCode":"+242","smsPossible":false},"CD":{"internationalCallingCode":"+243","smsPossible":true},"ZM":{"internationalCallingCode":"+260","smsPossible":true},"SM":{"internationalCallingCode":"+378","smsPossible":false},"SL":{"internationalCallingCode":"+232","smsPossible":true},"DJ":{"internationalCallingCode":"+253","smsPossible":false},"JM":{"internationalCallingCode":"+1","smsPossible":true},"CH":{"internationalCallingCode":"+41","smsPossible":true},"GE":{"internationalCallingCode":"+995","smsPossible":true},"SE":{"internationalCallingCode":"+46","smsPossible":true},"SY":{"internationalCallingCode":"+963","smsPossible":true},"ES":{"internationalCallingCode":"+34","smsPossible":true},"SG":{"internationalCallingCode":"+65","smsPossible":true},"LK":{"internationalCallingCode":"+94","smsPossible":true},"SK":{"internationalCallingCode":"+421","smsPossible":true},"SI":{"internationalCallingCode":"+386","smsPossible":true},"ZW":{"internationalCallingCode":"+263","smsPossible":true},"SD":{"internationalCallingCode":"+249","smsPossible":true},"KN":{"internationalCallingCode":"+1","smsPossible":true},"SR":{"internationalCallingCode":"+597","smsPossible":true},"LC":{"internationalCallingCode":"+1","smsPossible":true},"SC":{"internationalCallingCode":"+248","smsPossible":false},"SN":{"internationalCallingCode":"+221","smsPossible":true},"SB":{"internationalCallingCode":"+677","smsPossible":false},"RS":{"internationalCallingCode":"+381","smsPossible":true},"TH":{"internationalCallingCode":"+66","smsPossible":true},"TW":{"internationalCallingCode":"+886","smsPossible":true},"TJ":{"internationalCallingCode":"+992","smsPossible":true},"TZ":{"internationalCallingCode":"+255","smsPossible":true},"CZ":{"internationalCallingCode":"+420","smsPossible":true},"TD":{"internationalCallingCode":"+235","smsPossible":true},"CF":{"internationalCallingCode":"+236","smsPossible":true},"TN":{"internationalCallingCode":"+216","smsPossible":true},"CL":{"internationalCallingCode":"+56","smsPossible":true},"TV":{"internationalCallingCode":"+688","smsPossible":false},"DK":{"internationalCallingCode":"+45","smsPossible":true},"DE":{"internationalCallingCode":"+49","smsPossible":true},"TG":{"internationalCallingCode":"+228","smsPossible":true},"DO":{"internationalCallingCode":"+1","smsPossible":true},"DM":{"internationalCallingCode":"+1","smsPossible":false},"TT":{"internationalCallingCode":"+1","smsPossible":true},"TM":{"internationalCallingCode":"+993","smsPossible":false},"TR":{"internationalCallingCode":"+90","smsPossible":true},"TO":{"internationalCallingCode":"+676","smsPossible":false},"NG":{"internationalCallingCode":"+234","smsPossible":true},"NR":{"internationalCallingCode":"+674","smsPossible":false},"NA":{"internationalCallingCode":"+264","smsPossible":false},"NI":{"internationalCallingCode":"+505","smsPossible":true},"BH":{"internationalCallingCode":"+973","smsPossible":false},"NE":{"internationalCallingCode":"+227","smsPossible":true},"PK":{"internationalCallingCode":"+92","smsPossible":true},"JP":{"internationalCallingCode":"+81","smsPossible":true},"BS":{"internationalCallingCode":"+1","smsPossible":true},"NZ":{"internationalCallingCode":"+64","smsPossible":true},"BM":{"internationalCallingCode":"+1","smsPossible":true},"NP":{"internationalCallingCode":"+977","smsPossible":true},"BB":{"internationalCallingCode":"+246","smsPossible":true},"NO":{"internationalCallingCode":"+47","smsPossible":true},"HT":{"internationalCallingCode":"+509","smsPossible":true},"BD":{"internationalCallingCode":"+880","smsPossible":true},"VA":{"internationalCallingCode":"+39","smsPossible":false},"PA":{"internationalCallingCode":"+507","smsPossible":true},"VU":{"internationalCallingCode":"+678","smsPossible":false},"PG":{"internationalCallingCode":"+675","smsPossible":true},"PW":{"internationalCallingCode":"+680","smsPossible":true},"PY":{"internationalCallingCode":"+595","smsPossible":true},"PS":{"internationalCallingCode":"+970","smsPossible":true},"BR":{"internationalCallingCode":"+55","smsPossible":true},"HU":{"internationalCallingCode":"+36","smsPossible":true},"TL":{"internationalCallingCode":"+670","smsPossible":true},"BG":{"internationalCallingCode":"+359","smsPossible":true},"BF":{"internationalCallingCode":"+226","smsPossible":true},"BN":{"internationalCallingCode":"+673","smsPossible":true},"FJ":{"internationalCallingCode":"+679","smsPossible":true},"PH":{"internationalCallingCode":"+63","smsPossible":true},"FI":{"internationalCallingCode":"+358","smsPossible":true},"BT":{"internationalCallingCode":"+975","smsPossible":true},"BY":{"internationalCallingCode":"+375","smsPossible":true},"BZ":{"internationalCallingCode":"+501","smsPossible":false},"PR":{"internationalCallingCode":"+1","smsPossible":true},"BE":{"internationalCallingCode":"+32","smsPossible":true},"BA":{"internationalCallingCode":"+387","smsPossible":true},"FR":{"internationalCallingCode":"+33","smsPossible":true},"BO":{"internationalCallingCode":"+591","smsPossible":false},"PF":{"internationalCallingCode":"+689","smsPossible":true},"BI":{"internationalCallingCode":"+257","smsPossible":true},"VN":{"internationalCallingCode":"+84","smsPossible":true},"BJ":{"internationalCallingCode":"+229","smsPossible":true},"VE":{"internationalCallingCode":"+58","smsPossible":true},"PE":{"internationalCallingCode":"+51","smsPossible":true},"PL":{"internationalCallingCode":"+48","smsPossible":true},"BW":{"internationalCallingCode":"+267","smsPossible":true},"PT":{"internationalCallingCode":"+351","smsPossible":false},"HK":{"internationalCallingCode":"+852","smsPossible":true},"HN":{"internationalCallingCode":"+504","smsPossible":false},"MH":{"internationalCallingCode":"+692","smsPossible":true},"MO":{"internationalCallingCode":"+853","smsPossible":true},"MG":{"internationalCallingCode":"+261","smsPossible":true},"MW":{"internationalCallingCode":"+265","smsPossible":true},"ML":{"internationalCallingCode":"+223","smsPossible":true},"MT":{"internationalCallingCode":"+356","smsPossible":true},"MY":{"internationalCallingCode":"+60","smsPossible":true},"FM":{"internationalCallingCode":"+691","smsPossible":false},"MM":{"internationalCallingCode":"+95","smsPossible":true},"MX":{"internationalCallingCode":"+52","smsPossible":true},"MU":{"internationalCallingCode":"+230","smsPossible":true},"MR":{"internationalCallingCode":"+222","smsPossible":true},"MZ":{"internationalCallingCode":"+258","smsPossible":true},"MC":{"internationalCallingCode":"+377","smsPossible":true},"MV":{"internationalCallingCode":"+960","smsPossible":true},"MD":{"internationalCallingCode":"+373","smsPossible":false},"RO":{"internationalCallingCode":"+40","smsPossible":true},"MA":{"internationalCallingCode":"+212","smsPossible":true},"MN":{"internationalCallingCode":"+976","smsPossible":true},"ME":{"internationalCallingCode":"+382","smsPossible":true},"JO":{"internationalCallingCode":"+962","smsPossible":true},"LA":{"internationalCallingCode":"+856","smsPossible":true},"RU":{"internationalCallingCode":"+7","smsPossible":true},"LV":{"internationalCallingCode":"+371","smsPossible":true},"LT":{"internationalCallingCode":"+370","smsPossible":true},"LY":{"internationalCallingCode":"+218","smsPossible":true},"GQ":{"internationalCallingCode":"+240","smsPossible":false},"LI":{"internationalCallingCode":"+423","smsPossible":false},"LR":{"internationalCallingCode":"+231","smsPossible":true},"CN":{"internationalCallingCode":"+86","smsPossible":true},"LU":{"internationalCallingCode":"+352","smsPossible":true},"ZA":{"internationalCallingCode":"+27","smsPossible":true},"RW":{"internationalCallingCode":"+250","smsPossible":true},"LS":{"internationalCallingCode":"+266","smsPossible":false},"LB":{"internationalCallingCode":"+961","smsPossible":true}};

		$(function () {
			contactInfoInit();
		});
		
		function contactInfoInit() {
			
			
			Asw.get('representativeTelType').find('option[value="M1"]').attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_TYPE,
					Asw.ContactInput.Const.DATA_TYPE_VALUE_SMS);
			Asw.get('representativeTelCountry').children('option').each(function(i,option){
				var countryInfo = Asw.ContactInput.countryInfoMap[option.value];
				if (countryInfo === void 0) {
					
					return true;
				}
				$(option).attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_SMS_POSSIBLE, countryInfo.smsPossible);
				$(option).attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_COUNTRY_CODE, countryInfo.internationalCallingCode);
			});
			
			var smsCountrySelectSelector = 'select.js-countrySelect:not(#representativeTelCountry)'
			$(smsCountrySelectSelector).eq(0).children('option').each(function(i,option){
				var countryInfo = Asw.ContactInput.countryInfoMap[option.value];
				if (countryInfo === void 0) {
					
					return true;
				}
				$(smsCountrySelectSelector + ' option:nth-child(' + (i+1) + ')').attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_COUNTRY_CODE,
						countryInfo.internationalCallingCode);
			});
			
			var emergencyCountrySelectSelector = 'select.passengerEmergencyCountry.js-countrySelect:not(#representativeTelCountry)'
			$(emergencyCountrySelectSelector).eq(0).children('option').each(function(i,option){
				var countryInfo = Asw.ContactInput.countryInfoMap[option.value];
				if (countryInfo === void 0) {
					
					return true;
				}
				$(emergencyCountrySelectSelector + ' option:nth-child(' + (i+1) + ')').attr(Asw.ContactInput.Const.DATA_ATTRIBUTE_NAME_COUNTRY_CODE,
						countryInfo.internationalCallingCode);
			});
		}
	</script><script type="text/javascript">
		var isInit = true;
		function changeTelType(_this, telNoInputId) {
			var $telNoInput = Asw.get(telNoInputId);
			var $telNoKind = Asw.get("representativeTelType");
			var telNo = Asw.getValueComparedToPlaceholder(telNoInputId);
			var telNoCountry = Asw.get("representativeTelCountry").val();

			if(true && !isInit) {
				if(telInputCheck(telNo, telNoCountry)) {
					if (_this.value == "M1") {
						telNo = exceptDomesticPrefix("09042504176", telNoCountry);
					} else if (_this.value == "B1") {
						telNo = exceptDomesticPrefix("03-5405-2172", telNoCountry);
					} else {
						telNo = exceptDomesticPrefix("090-4250-4176", telNoCountry);
					}
					$telNoInput.val(telNo);
				}
			}
			isInit = false;
		}
		
		function telInputCheck(telNo, telNoCountry) {
	        checkResult = false;
	        
	        var amcHndyTelNo = exceptDomesticPrefix("09042504176", telNoCountry);
	        var amcCmpTelNo = exceptDomesticPrefix("03-5405-2172", telNoCountry);
	        var amcTelNo = exceptDomesticPrefix("090-4250-4176", telNoCountry);
	
	        if(telNo == "" || telNo == amcHndyTelNo || telNo == amcCmpTelNo || telNo == amcTelNo) {
	        	checkResult = true;
	        }
	        return checkResult;
    	}
    	
    	function exceptDomesticPrefix(telNo, telNoCountry) {
    		if(telNoCountry == 'JP') {
    			if(telNo.substr(0, 1) === '0') {
    				return telNo.slice(1);
    			}
    		}
    		return telNo;
    	}
	</script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/script/asw-customerinfomationform.js?6e91953"></script><script type="text/javascript">
		$(document).ready(function(){
				Asw.enableTextWhenCheckBox('.cascadedSwitch');
				Asw.notPasteInput('.jsNotPasteInput');
			$(function(){
				Asw.alignHeightToParentTdElement( '.passengerInfoInputArea' );
				
					Asw.alignHeightToParentTdElement( '.passengerInfoInputText' );
				
				
				$('select.js-countrySelect').trigger('change');
			});
		});
	</script><script type="text/javascript" src="https://aswbe-i.ana.co.jp/rei22c/international_asw/contents/cmn/script/asw-session-keeper.js?6ec0730"></script><script type="text/javascript">
			$(document).ready(function() {
				Asw.SessionKeeper.initAndStart("sessionKeeperContainer", "520", "570", "PC");
			});
			</script><script type="text/javascript">
		Asw.addAjaxCallback(function(data) {
			// アコーディオンのメソッドと設定 
			if($('.jsAccordionSwitch .jsTriggerSwitch').length > 0){
				var events = $._data($('.jsAccordionSwitch .jsTriggerSwitch').get(0), "events");
				if(events === undefined || events.click === undefined) {
					Asw.AccordionInfo('.jsAccordionSwitch', {
						'triggerSwitch': '.jsAccordionSwitch .jsTriggerSwitch',
						'listClass': '.jsAccordionSwitchList',
						'visibilityHidden': 'toggleContents'
					});
				}
			}
		});
	</script><script type="text/javascript">
	Asw.Price = Asw.Price || {};
	// 整数の区切り文字
	Asw.Price.separateString = ',';
	
	// 整数の区切り桁数
	Asw.Price.separateDigitNumber = 3;
	
	// 小数点
	Asw.Price.decimalPointString = '.';
	
	// 小数点以降の有効桁数
	Asw.Price.significantDigit = 0;
	
	// 表示金額の単位
	Asw.Price.currencyUnit = '円';
	</script><script type="text/javascript">
			$(document).ready(function() {
				$('#preLoadingArea').css('visibility', 'hidden');
			});
		</script><noscript><img src="https://aswbe-i.ana.co.jp/akam/13/pixel_7a1abcfe?a=dD03YWI5ZDk3ZGY3ZDA3NDI4NmY2ZDc4ZmNiZmUyZWI1ZmRkZDI3Y2RkJmpzPW9mZg==" style="visibility: hidden; position: absolute; left: -999px; top: -999px;" /></noscript><script type="text/javascript"  src="/Q1XS4o/Cn_ls/KfDO7/xA/t9OfhmEwSiGQaG/NG4dCQE/bw/QJBChQIQEB"></script></body>
</html>